from datetime import datetime, timedelta
import json
from typing import Optional

from celery import shared_task
from django.conf import settings
import pytz


# Create your task(s) here.
@shared_task
def update_customer_status():
    from helpers.custom_filters import get_current_month_range
    from requisition.models import Company
    from sales_app.helper.enums import CustomerStatus, TransactionStatusChoices
    from sales_app.models import Customer, SalesTransaction

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    companies = Company.objects.all()
    if not companies.exists():
        return "NO AVAILABLE COMPANIES YET."
    for company in companies:
        start_date, end_date = get_current_month_range(TODAY)
        duration = int(((end_date + timedelta(days=1)) - start_date).days)
        company_transaction_count = SalesTransaction.objects.filter(
            company=company,
            status=TransactionStatusChoices.SUCCESSFUL,
            created_at__range=[start_date, end_date + timedelta(days=1)],
        ).count()
        company_average_sales = round(company_transaction_count / duration)
        company_customers = Customer.objects.filter(company=company)
        if not company_customers.exists():
            pass
        for customer in company_customers:
            customer_transaction_count = SalesTransaction.objects.filter(
                company=company,
                customer=customer,
                status=TransactionStatusChoices.SUCCESSFUL,
                created_at__range=[start_date, end_date + timedelta(days=1)],
            ).count()
            customer.status = (
                CustomerStatus.ACTIVE
                if customer_transaction_count >= company_average_sales
                else CustomerStatus.INACTIVE
            )
            customer.save()
    return "SUCCESSFULLY UPDATED COMPANIES CUSTOMER INFORMATION."


# @shared_task
# def send_customer_link_sms(phone: str, payment_link: str):
#     # shortener = pyshorteners.Shortener(api_key=settings.BITLY_ACCESS_TOKEN)
#     # short_url = shortener.bitly.short(payment_link)

#     headers = {
#         "Authorization": f"Api_key {}",
#         "Content-Type": "application/json",
#     }
#     payload = json.dumps(
#         {
#             "receiver": phone,
#             "template": "533c3c50-c16f-4d42-946b-b8ac53016a71",
#             "place_holders": {"url": payment_link},
#         }
#     )
#     response = request(
#         "POST",
#         "https://whispersms.xyz/transactional/send",
#         headers=headers,
#         data=payload,
#     )
#     return response.json()


@shared_task
def update_sales_transaction(
    amount: float,
    inflow_id: str,
    batch_id: Optional[str] = None,
    rrn: Optional[str] = None,
):
    """
    NOTE [params]:∫
    - inflow_id: this is the local transaction ID for transfer or card event(s).
    - batch_id: this used when payment has to do with bank transfer(s).
    - rrn: this is used when payment has to do with POS card transaction(s).
    """
    from account.enums import (
        AccountType,
        DebitCreditEntry,
        TransactionType,
    )
    from account.models import (
        AccountSystem,
        Transaction,
        TransactionMetaData,
        Wallet,
    )
    from sales_app.helper.enums import (
        CashBookChoices,
        MeansOfPaymentChoices,
        TransactionStatusChoices,
    )
    from sales_app.models import (
        CashBook,
        CardTransaction,
        SalesTransaction,
    )

    if batch_id is not None:
        payment_channel = MeansOfPaymentChoices.TRANSFER
        sales_transaction = (
            SalesTransaction.objects.filter(
                batch_id=batch_id,
            )
            .last()
        )
        if sales_transaction.means_of_payment == MeansOfPaymentChoices.SPLIT:
            transfer_duplicate = SalesTransaction.objects.filter(
                payment_reference=batch_id,
                means_of_payment=MeansOfPaymentChoices.TRANSFER,
            ).last()
            if transfer_duplicate is not None:
                transfer_duplicate.amount_paid = amount
                transfer_duplicate.paid = True
                transfer_duplicate.paid_at = datetime.now(
                    tz=pytz.timezone(settings.TIME_ZONE)
                )
                transfer_duplicate.status = TransactionStatusChoices.SUCCESSFUL
                transfer_duplicate.save()
    if rrn is not None:
        payment_channel = MeansOfPaymentChoices.CARD
        sales_transaction = (
            SalesTransaction.objects.filter(
                card_rrn=rrn,
            )
            .last()
        )
        card_transaction = CardTransaction.objects.filter(rrn=rrn).last()
        if card_transaction is not None:
            amount_payable = float(sales_transaction.total_sales_amount)
            # Check for duplicate fund transaction.
            fund_transaction = Transaction.objects.filter(
                bank_deposit_ref=rrn
            ).last()
            if fund_transaction is None:
                account_details = AccountSystem.objects.filter(
                    company=sales_transaction.company,
                    branch=sales_transaction.branch,
                    account_type=AccountType.SALES,
                ).last()
                if account_details is not None:
                    meta_data_instance = TransactionMetaData.objects.create(
                        user=sales_transaction.company.user,
                        fund_wallet_payload=json.dumps(card_transaction.payload),
                    )
                    transaction_instance = Transaction.objects.create(
                        user=sales_transaction.company.user,
                        company_name=sales_transaction.company.company_name,
                        beneficiary_account_number=account_details.account_number,
                        beneficiary_account_name=account_details.account_name,
                        bank_code=rrn,
                        source_account_name="LibertyPay",
                        source_account_number="**********",
                        user_full_name=sales_transaction.company.user.full_name,
                        user_email=sales_transaction.company.user.email,
                        transaction_type=TransactionType.CARD,
                        amount=amount_payable,
                        total_amount_received=card_transaction.amount,
                        bank_deposit_ref=rrn,
                        status=TransactionStatusChoices.PENDING,
                        narration="LibertyPay card settlement",
                        payout_type=account_details.account_type,
                        company_id=account_details.company.id,
                        date_credited=card_transaction.created_at,
                    )
                    meta_data_instance.transaction = transaction_instance
                    meta_data_instance.save()
                    # Actual wallet funding
                    Wallet.fund_wallet(
                        account_id=account_details.id,
                        transaction_id=transaction_instance.id,
                    )
                    card_transaction.is_settled = True
                    card_transaction.save()
    if sales_transaction is not None:
        sales_transaction.status = TransactionStatusChoices.SUCCESSFUL
        sales_transaction.amount_paid = amount
        sales_transaction.paid = True
        sales_transaction.paid_at = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        sales_transaction.save()
        # Register the sales transaction payment.
        CashBook.objects.create(
            company=sales_transaction.company,
            branch=sales_transaction.branch,
            sales_transaction=sales_transaction,
            transaction_type=DebitCreditEntry.CREDIT,
            amount=amount,
            cash_book_type=CashBookChoices.CASH_IN_BANK,
            payment_channel=payment_channel,
            reference=inflow_id,
            status=TransactionStatusChoices.SUCCESSFUL,
        )
        return "SUCCESSFULLY UPDATED THE SALES TRANSACTION."
    return f"SALES TRANSACTION NOT FOUND FOR batchID: {batch_id} / card: {rrn}."


@shared_task
def check_sales_withdrawal_status():
    from account.enums import TransactionStatus
    from account.tasks import wema_verify_send_money
    
    from sales_app.models import WalletWithdrawal

    sales_withdrawal = WalletWithdrawal.objects.filter(
        status=TransactionStatus.IN_PROGRESS,
        refunded=False,
    )
    for withdrawal in sales_withdrawal:
        wema_verify_send_money.apply_async(
            queue="sales",
            args=[
                str(withdrawal.id)
            ]
        )
    return "SUCCESSFULLY CHECKED SALES WITHDRAWAL STATUS."
