import os
import re


# Helper function(s).
def upload_path(instance, filename):
    """Set file upload path.
    Args:
        instance and filename to be saved.
    """
    filename, ext = os.path.splitext(filename)
    filename = f"{filename}{ext}"

    if ext == ".jpg" or ext == ".jpeg" or ext == ".png":
        return f"images/{instance.company.company_name}/{filename}"
    else:
        return f"documents/{instance.company.company_name}/{filename}"


def validate_subdomain(subdomain):
    # Regex pattern for a valid subdomain name
    pattern = r"^(?!-)[A-Za-z0-9-]{1,63}(?<!-)$"

    # Check if the subdomain matches the pattern
    if re.match(pattern, subdomain):
        # Additional check to ensure it does not contain spaces, underscores, or periods
        if " " in subdomain or "_" in subdomain or "." in subdomain:
            return False
        # Check if the subdomain is not all numeric
        if not subdomain.isdigit():
            return True
    return False
