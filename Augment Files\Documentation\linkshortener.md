# Link Shortener App Documentation

## Overview

The Link Shortener app provides URL shortening services for the Paybox360 platform, creating compact, trackable links for use in communications, notifications, and marketing materials. It enables the creation of branded short links with analytics capabilities.

## Core Features

### 1. URL Shortening

- Long URL conversion to short links
- Custom alias support
- QR code generation
- Link expiration settings

### 2. Link Analytics

- Click tracking and counting
- Geographic location data
- Device and browser statistics
- Referrer tracking

### 3. Link Management

- Link organization and categorization
- Bulk link creation
- Link editing and deactivation
- Link search and filtering

### 4. Branded Links

- Custom domain support
- Brand-specific link formats
- Visual customization
- Consistent branding across channels

### 5. Integration Services

- API access for other apps
- Webhook notifications
- Batch processing
- Export capabilities

## How It Works

1. **Link Creation**: Long URLs are submitted for shortening
2. **Processing**: System generates a unique short code
3. **Storage**: Link mapping is stored in the database
4. **Redirection**: When short link is accessed, user is redirected to original URL
5. **Tracking**: Click and usage data is recorded for analytics
6. **Reporting**: Link performance data is compiled into reports

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `ShortLink` | Link mapping | `original_url`, `short_code`, `expires_at` | User, Company |
| `LinkClick` | Click tracking | `timestamp`, `ip_address`, `user_agent` | ShortLink |
| `LinkCategory` | Organization | `name`, `description` | Company |
| `CustomDomain` | Branded domains | `domain_name`, `is_active`, `verification_status` | Company |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `generate_short_code` | Creates unique codes | `utils.py` | Called during link creation |
| `process_redirect` | Handles redirections | `views.py` | Used when links are accessed |
| `track_link_click` | Records usage data | `services.py` | Called during redirections |
| `generate_qr_code` | Creates QR codes | `services.py` | Used for offline sharing |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/linkshortener/links/` | Manage short links | POST/GET | `ShortLinkViewSet` |
| `/linkshortener/analytics/` | View link statistics | GET | `LinkAnalyticsView` |
| `/linkshortener/categories/` | Manage categories | POST/GET | `LinkCategoryViewSet` |
| `/linkshortener/domains/` | Manage custom domains | POST/GET | `CustomDomainViewSet` |
| `/{short_code}/` | Access shortened URL | GET | `RedirectView` |

### Integration Points

- **Notification App**: For SMS and email links
- **Marketing Services**: For campaign tracking
- **Core App**: For user authentication
- **QR Code Services**: For code generation

## Key Considerations

### 1. Link Reliability

- **Responsible App**: Link Shortener app
- **Key Functions**:
  - High-availability redirection service
  - Error handling for invalid links
  - Backup systems for critical links

### 2. Performance Optimization

- **Responsible App**: Link Shortener app
- **Key Functions**:
  - Caching of frequently accessed links
  - Database indexing for quick lookups
  - Load balancing for high-traffic links

### 3. Security Measures

- **Responsible App**: Link Shortener app
- **Key Functions**:
  - Malicious URL scanning
  - Rate limiting to prevent abuse
  - Private link protection

### 4. Analytics Accuracy

- **Responsible App**: Link Shortener app
- **Key Functions**:
  - Bot traffic filtering
  - Unique visitor identification
  - Cross-device tracking

### 5. Custom Domain Management

- **Responsible App**: Link Shortener app
- **Key Functions**:
  - Domain verification process
  - SSL certificate management
  - DNS configuration assistance