from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
import pytz

from account.models import Transaction
from sales_app.models import SalesTransaction


class Command(BaseCommand):
    help = "UPDATE EXISTING SALES WALLET TRANSACTION(S)."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        wallet_transactions = Transaction.objects.filter(
            date_created__date__gte="2025-03-13",
            transaction_type="CARD",
            status="SUCCESSFUL",
        )
        for wallet_transaction in wallet_transactions:
            sales_transaction = SalesTransaction.objects.filter(
                card_rrn=wallet_transaction.bank_deposit_ref
            ).last()
            # Update the wallet transaction value(s).
            Transaction.objects.filter(
                bank_deposit_ref=sales_transaction.card_rrn
            ).update(
                amount=sales_transaction.total_sales_amount,
                balance_after=(
                    float(wallet_transaction.balance_before) + float(sales_transaction.total_sales_amount)
                ),
            )

        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        print(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
