import ast
import calendar
import datetime
import uuid

import pandas as pd

from typing import Tuple, Optional
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import models
from django.db.models import Sum, Count, Case, When, IntegerField, DecimalField, FloatField

from django.utils import timezone
from lxml.html.builder import Q
from collections import Counter

from core.tasks import send_email
from core.exceptions import InvalidRequestException
from requisition.models import IndentProduct, Budget, ProcurementPurchaseOrder, ProcurementPurchaseInvoice, ProcurementReturn, Asset, PurchaseIndent, \
    Requisition, Team
from stock_inventory.models import Product, Supplier

User = get_user_model()
login_url = f"{settings.SUPPLIER_INVITE_FRONTEND_URL}/login"


def create_ident_products(products, team, supplier, instance=None):
    indent_products = list()
    total_cost = 0

    if instance:
        for ip in instance.products.all():
            total_cost += ip.estimated_amount
            indent_products.append(ip)

    for product_data in products:
        """
        Expected data in list:
        {
            "id": "uuid-jjhsd-bvhsdsd-hsdhsb", # Not required (except for update: changing price and quantity)
            "product_id": "uuid-jjhsd-bvhsdsd-hsdhsb",
            "amount": 100,
            "quantity": 3,
            "is_asset": true,
            "depreciation_method": "STRAIGHT_LINE",
            "useful_life": 2, # This is number of months
            "residual_value": 1000,
            "total_estimated_units": 5, # Required for unit of production depreciation method
            "units_produced": 10000, # Required for unit of production depreciation method
            "depreciation_rate": 0.2, # Required for double balance depreciation method
        }
        """
        product_id = product_data.get("product_id")
        indent_id = product_data.get("id")
        amount = product_data.get("amount")
        quantity = product_data.get("quantity")

        # Asset Items
        is_asset = product_data.get("is_asset")
        depreciation_method = product_data.get("depreciation_method")
        useful_life = product_data.get("useful_life")
        residual_value = product_data.get("residual_value")
        total_estimated_units = product_data.get("total_estimated_units", 0)
        units_produced = product_data.get("units_produced", 0)
        depreciation_rate = product_data.get("depreciation_rate", 0.0)

        if indent_id:
            # GET INDENT PRODUCT AND UPDATE
            try:
                ip = IndentProduct.objects.get(id=indent_id)
                new_cost = total_cost + amount - ip.estimated_amount
                success, detail = purchase_ident_with_budget_check(team, new_cost)
                if success is False:
                    return indent_products, new_cost
                ip.estimated_amount = amount
                ip.quantity = quantity
                ip.save()
            except IndentProduct.DoesNotExist:
                pass

        else:

            product = Product.objects.filter(id=product_id, company=team.company)
            if product:
                # Create IndentProduct
                prd = product.last()
                ip = IndentProduct.objects.create(product=prd, estimated_amount=amount, quantity=quantity)
                total_cost += amount
                indent_products.append(ip)

                if is_asset:
                    # Create Asset Item
                    asset_id = str(team.company.company_name).upper()[:4] + "/ASSET/" + str(uuid.uuid4()).replace("-", "")[:10]
                    Asset.objects.create(
                        product=prd, team=team, supplier=supplier, depreciation_method=depreciation_method, useful_life=useful_life,
                        purchase_cost=amount, indent_product=ip, residual_value=residual_value, purchase_date=datetime.datetime.now(),
                        total_estimated_units=total_estimated_units, units_produced=units_produced, depreciation_rate=depreciation_rate,
                        asset_no=asset_id
                    )

    return indent_products, total_cost


def purchase_ident_with_budget_check(team, total_cost):
    message = "Success"
    success = False
    allocated_amount = available_for_allocation = 0
    # Check remaining allocation against total cost
    current_date = timezone.now()
    # Ensure budget period is within current date
    budgets = Budget.objects.filter(team=team, is_active=True, start_date__lte=current_date, end_date__gte=current_date)
    if budgets:
        current_active_budget = budgets.first()
        allocation = Budget.get_current_allocated_amount(budget_instance=current_active_budget, team_instance=team)

        allocated_amount = float(allocation.get("total_active_allocation"))
        amount_disbursed = float(allocation.get("total_request_amount_made_so_far"))
        available_for_allocation = allocated_amount - amount_disbursed

    if float(total_cost) > allocated_amount:
        message = "Total estimated product price cannot be greater than allocated budget amount"
        return success, message

    if float(total_cost) > available_for_allocation:
        message = "Total estimated product price cannot be greater than available budget balance"
        return success, message

    return True, message


def extract_from_excel_or_csv(file):
    try:
        if file.name.endswith('.csv'):
            df = pd.read_csv(file)
        elif file.name.endswith(('.xls', '.xlsx')):
            df = pd.read_excel(file)
        else:
            raise InvalidRequestException({"message": "Unsupported file type"})

        json_data = df.to_dict(orient='records')
        return json_data

    except Exception as err:
        print(str(err))
        raise InvalidRequestException({"message": "An error occurred while extracting from file, please try again later"})


def add_retrieve_procurement_history(history_item, new_data):
    """
    Expected history item
    {'created_on': '2024-09-12', 'message': 'This was added'}{'created_on': '2024-10-12', 'message': 'This was modified'}
    {'created_on': '2024-11-12', 'message': 'This was deleted'}
    """
    data_list = list()
    history_str = ""
    # Convert history item back to list of object
    if history_item:
        dict_strings = history_item.replace('}{', '}|{').split('|')
        data_list = [ast.literal_eval(str(item)) for item in dict_strings]

    data_list.append(new_data)
    # Convert list back to string
    if data_list:
        history_str = ''.join(str(data) for data in data_list)

    return history_str


def get_week_start_and_end_datetime(date_time):
    week_start = date_time - datetime.timedelta(days=date_time.weekday())
    week_end = week_start + datetime.timedelta(days=6)
    week_start = datetime.datetime.combine(week_start.date(), datetime.time.min)
    week_end = datetime.datetime.combine(week_end.date(), datetime.time.max)
    return week_start, week_end


def get_month_start_and_end_datetime(date_time):
    month_start = date_time.replace(day=1)
    month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])
    month_start = datetime.datetime.combine(month_start.date(), datetime.time.min)
    month_end = datetime.datetime.combine(month_end.date(), datetime.time.max)
    return month_start, month_end


def get_year_start_and_end_datetime(date_time):
    year_start = date_time.replace(day=1, month=1, year=date_time.year)
    year_end = date_time.replace(day=31, month=12, year=date_time.year)
    year_start = datetime.datetime.combine(year_start.date(), datetime.time.min)
    year_end = datetime.datetime.combine(year_end.date(), datetime.time.max)
    return year_start, year_end


def get_purchase_order_data(query, user_id):
    purchase_order = dict()
    weekly = []
    monthly = []
    yearly = []
    current_date = timezone.now()
    for delta in range(6, -1, -1):
        total_week_po_count = total_week_po_amount = pen_week_po_count = pen_week_po_amount = can_week_po_count = can_week_po_amount = rej_week_po_count = rej_week_po_amount = 0
        total_month_po_count = total_month_po_amount = pen_month_po_count = pen_month_po_amount = can_month_po_count = can_month_po_amount = rej_month_po_count = rej_month_po_amount = 0
        total_year_po_count = total_year_po_amount = pen_year_po_count = pen_year_po_amount = can_year_po_count = can_year_po_amount = rej_year_po_count = rej_year_po_amount = 0

        week_date = current_date - relativedelta(weeks=delta)
        month_date = current_date - relativedelta(months=delta)
        year_date = current_date - relativedelta(years=delta)
        week_start, week_end = get_week_start_and_end_datetime(week_date)
        month_start, month_end = get_month_start_and_end_datetime(month_date)
        year_start, year_end = get_year_start_and_end_datetime(year_date)
        purchase_orders = ProcurementPurchaseOrder.objects.filter(query, created_at__gte=week_start, created_at__lte=week_end)
        if purchase_orders:
            pending = purchase_orders.filter(status="UNDER_REVIEW")
            cancelled = purchase_orders.filter(status="CANCELLED")
            rejected = purchase_orders.filter(status="DENIED")
            total_week_po_count = purchase_orders.count()
            total_week_po_amount = purchase_orders.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
            pen_week_po_count = pending.count()
            pen_week_po_amount = pending.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
            can_week_po_count = cancelled.count()
            can_week_po_amount = cancelled.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
            rej_week_po_count = rejected.count()
            rej_week_po_amount = rejected.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
        weekly.append({
            "week": week_start.strftime("%d %b"), "total_count": total_week_po_count, "total_amount": total_week_po_amount, "pending_count": pen_week_po_count,
            "pending_amount": pen_week_po_amount, "cancelled_count": can_week_po_count, "cancelled_amount": can_week_po_amount, "rejected_count": rej_week_po_count,
            "rejected_amount": rej_week_po_amount
        })
        month_purchase_orders = ProcurementPurchaseOrder.objects.filter(query, created_at__gte=month_start, created_at__lte=month_end)
        if month_purchase_orders:
            pending = month_purchase_orders.filter(status="UNDER_REVIEW")
            cancelled = month_purchase_orders.filter(status="CANCELLED")
            rejected = month_purchase_orders.filter(status="DENIED")
            total_month_po_count = month_purchase_orders.count()
            total_month_po_amount = month_purchase_orders.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
            pen_month_po_count = pending.count()
            pen_month_po_amount = pending.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
            can_month_po_count = cancelled.count()
            can_month_po_amount = cancelled.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
            rej_month_po_count = rejected.count()
            rej_month_po_amount = rejected.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0

        monthly.append({
            "month": month_start.strftime("%b"), "total_count": total_month_po_count, "total_amount": total_month_po_amount, "pending_count": pen_month_po_count,
            "pending_amount": pen_month_po_amount, "cancelled_count": can_month_po_count, "cancelled_amount": can_month_po_amount, "rejected_count": rej_month_po_count,
            "rejected_amount": rej_month_po_amount
        })
        year_purchase_orders = ProcurementPurchaseOrder.objects.filter(query, created_at__gte=year_start, created_at__lte=year_end)
        if year_purchase_orders:
            pending = year_purchase_orders.filter(status="UNDER_REVIEW")
            cancelled = year_purchase_orders.filter(status="CANCELLED")
            rejected = year_purchase_orders.filter(status="DENIED")
            total_year_po_count = year_purchase_orders.count()
            total_year_po_amount = year_purchase_orders.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
            pen_year_po_count = pending.count()
            pen_year_po_amount = pending.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
            can_year_po_count = cancelled.count()
            can_year_po_amount = cancelled.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0
            rej_year_po_count = rejected.count()
            rej_year_po_amount = rejected.aggregate(Sum("initial_cost"))["initial_cost__sum"] or 0

        yearly.append({
            "year": year_start.strftime("%Y"), "total_count": total_year_po_count, "total_amount": total_year_po_amount, "pending_count": pen_year_po_count,
            "pending_amount": pen_year_po_amount, "cancelled_count": can_year_po_count, "cancelled_amount": can_year_po_amount, "rejected_count": rej_year_po_count,
            "rejected_amount": rej_year_po_amount
        })
    purchase_order['weekly'] = weekly
    purchase_order['monthly'] = monthly
    purchase_order['yearly'] = yearly
    cache_key = f"purchase_order_data_{user_id}"
    cache.set(key=cache_key, value=purchase_order, timeout=600)
    return purchase_order


def get_previous_next_item(model_to_check: models.Model, identifier, query_item=None) -> Tuple[Optional[models.Model], Optional[models.Model]]:
    query = Q()
    if query_item:
        query = query_item
    previous_item = next_item = None
    try:
        item = model_to_check.objects.filter(query, id=identifier).last()
        prev_item = model_to_check.objects.filter(query, created_at__lt=item.created_at).order_by('created_at')
        if prev_item:
            previous_item = prev_item.first().id
        nxt_item = model_to_check.objects.filter(query, created_at__gt=item.created_at).order_by('created_at')
        if nxt_item:
            next_item = nxt_item.first().id
        return previous_item, next_item
    except Exception:
        return previous_item, next_item


def format_phone_number(phone_number):
    return f"234{phone_number[-10:]}"


def get_supplier_dashboard_data(supplier_id):
    # Supplier's Dashboard/Analytics
    dashboard_analysis = dict()
    supplier_orders = ProcurementPurchaseOrder.objects.filter(indent__supplier_id=supplier_id)
    returned_po = ProcurementReturn.objects.filter(purchase_order__indent__supplier_id=supplier_id)
    accepted_purchase_orders = supplier_orders.filter(status="ACCEPTED")
    orders_pending_fulfilment = accepted_purchase_orders.filter(delivery_status__in=["PARTLY_DELIVERED", "NOT_DELIVERED"])
    rejected_po = supplier_orders.filter(status="DENIED")
    cancelled_orders = supplier_orders.filter(status="CANCELLED")
    pending_payments = ProcurementPurchaseInvoice.objects.filter(purchase_order__indent__supplier_id=supplier_id, paid=False)

    # - Total PO (count and amount)
    total_po_count = supplier_orders.count()
    total_po_amount = supplier_orders.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
    # - Pending Fulfilment (Count and amount)
    pending_fulfilment_count = orders_pending_fulfilment.count()
    pending_fulfilment_amount = orders_pending_fulfilment.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
    # - Pending Payment (Count and amount)
    pending_payment_count = pending_payments.count()
    pending_payment_amount = pending_payments.aggregate(Sum("total"))["total__sum"] or 0
    # - Rejected PO (Count and amount)
    rejected_count = rejected_po.count()
    reject_amount = rejected_po.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
    # - Cancelled PO (count and amount)
    cancelled_count = cancelled_orders.count()
    cancelled_amount = cancelled_orders.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
    # - Last four accepted PO (total amount, goods count, po no, date)
    l4_accepted_po = [{
        "order_no": item.order_no, "amount": item.accepted_cost, "date": item.created_at, "goods": item.indent.products.count()
    } for item in accepted_purchase_orders[:4]]
    # - Last 4 pending Payment (amount, invoice no) # Invoice
    l4_pending_payment = [{"invoice_no": item.po_invoice_number, "amount": item.total} for item in pending_payments[:4]]  # This will be determined by invoice
    # - Last 4 Pending Fulfilment (total amount, goods count, po no)
    l4_pending_fulfilment = [{"order_no": item.order_no, "amount": item.accepted_cost, "goods": item.indent.products.count()} for item in orders_pending_fulfilment[:4]]
    # - Analysis (showing totalPO received, pending fulfilment, pending payments, rejects) - all in count and amount
    analysis_graph = get_supplier_dashboard_graph(supplier_orders, orders_pending_fulfilment)
    # - Count of ordered product, grouped by categories
    category_list = list()
    cat = list()
    for purchase_order in accepted_purchase_orders:
        products = purchase_order.indent.products.all()
        for prod in products:
            category = str(prod.product.category.name).lower()
            category_list.append(category)

    if category_list:
        cat = [{"name": item, "count": count} for item, count in Counter(category_list).items()]
    # - Goods trend (count of supplied goods and rejected goods)
    goods_trend = list()
    current_date = timezone.now()
    for delta in range(12, -1, -1):
        supplied_trend = rejected_trend = perfect_trend = 0
        month_date = current_date - relativedelta(months=delta)
        month_start, month_end = get_month_start_and_end_datetime(month_date)
        delivered_orders = accepted_purchase_orders.filter(created_at__gte=month_start, created_at__lte=month_end, delivery_status="FULLY_DELIVERED")
        if delivered_orders:
            supplied_trend = delivered_orders.count()
            rejected_trend = returned_po.count()  # To be determined by returned items
            perfect_trend = supplied_trend - rejected_trend
        goods_trend.append({"month": month_start.strftime("%b"), "supplied_trend": supplied_trend, "rejected_trend": rejected_trend, "perfect_trend": perfect_trend})

    # Dashboard Results
    dashboard_analysis["purchase_order_count"] = total_po_count
    dashboard_analysis["purchase_order_amount"] = total_po_amount
    dashboard_analysis["pending_payment_count"] = pending_payment_count
    dashboard_analysis["pending_payment_amount"] = pending_payment_amount
    dashboard_analysis["rejected_count"] = rejected_count
    dashboard_analysis["rejected_amount"] = reject_amount
    dashboard_analysis["pending_fulfilment_count"] = pending_fulfilment_count
    dashboard_analysis["pending_fulfilment_amount"] = pending_fulfilment_amount
    dashboard_analysis["cancelled_purchase_order_count"] = cancelled_count
    dashboard_analysis["cancelled_purchase_order_amount"] = cancelled_amount
    dashboard_analysis["last_four_purchase_order"] = l4_accepted_po
    dashboard_analysis["last_four_pending_payment"] = l4_pending_payment
    dashboard_analysis["last_four_pending_fulfilment"] = l4_pending_fulfilment
    dashboard_analysis["analytics"] = analysis_graph
    dashboard_analysis["products_per_supply"] = cat
    dashboard_analysis["goods_trend"] = goods_trend
    cache_key = f"supplier_dashboard_data_{supplier_id}"
    cache.set(key=cache_key, value=dashboard_analysis, timeout=600)
    return dashboard_analysis


def get_supplier_dashboard_graph(supplier_orders, orders_pending_fulfilment):
    analysis = dict()
    monthly = list()
    weekly = list()
    yearly = list()
    current_date = timezone.now()
    for delta in range(12, -1, -1):
        weekly_total_supply_count = weekly_pending_fulfilment_count = weekly_pending_payment_count = weekly_rejected_goods_count = 0
        weekly_total_supply_amount = weekly_pending_fulfilment_amount = weekly_pending_payment_amount = weekly_rejected_goods_amount = 0

        monthly_total_supply_count = monthly_pending_fulfilment_count = monthly_pending_payment_count = monthly_rejected_goods_count = 0
        monthly_total_supply_amount = monthly_pending_fulfilment_amount = monthly_pending_payment_amount = monthly_rejected_goods_amount = 0

        yearly_total_supply_count = yearly_pending_fulfilment_count = yearly_pending_payment_count = yearly_rejected_goods_count = 0
        yearly_total_supply_amount = yearly_pending_fulfilment_amount = yearly_pending_payment_amount = yearly_rejected_goods_amount = 0

        week_date = current_date - relativedelta(weeks=delta)
        month_date = current_date - relativedelta(months=delta)
        year_date = current_date - relativedelta(years=delta)
        week_start, week_end = get_week_start_and_end_datetime(week_date)
        month_start, month_end = get_month_start_and_end_datetime(month_date)
        year_start, year_end = get_year_start_and_end_datetime(year_date)
        weekly_total_supply = supplier_orders.filter(created_at__gte=week_start, created_at__lte=week_end)
        if weekly_total_supply:
            weekly_total_supply_count = weekly_total_supply.count()
            weekly_total_supply_amount = weekly_total_supply.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
        weekly_pending_fulfilment = orders_pending_fulfilment.filter(created_at__gte=week_start, created_at__lte=week_end)
        if weekly_pending_fulfilment:
            weekly_pending_fulfilment_count = weekly_pending_fulfilment.count()
            weekly_pending_fulfilment_amount = weekly_pending_fulfilment.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
        weekly_pending_payment = [] # Dependent on Invoice
        if weekly_pending_payment:
            weekly_pending_payment_count = 0
            weekly_pending_payment_amount = 0
        weekly_rejected_goods = [] # Dependent on returns/rejects
        if weekly_rejected_goods:
            weekly_rejected_goods_count = 0
            weekly_rejected_goods_amount = 0
        weekly.append({
            "week": week_start.strftime("%d %b"), "total_supply_count": weekly_total_supply_count, "total_supply_amount": weekly_total_supply_amount,
            "pending_fulfilment_count": weekly_pending_fulfilment_count, "pending_fulfilment_amount": weekly_pending_fulfilment_amount,
            "pending_payment_count": weekly_pending_payment_count, "pending_payment_amount": weekly_pending_payment_amount, "rejected_goods_count": weekly_rejected_goods_count,
            "rejected_goods_amount": weekly_rejected_goods_amount
        })
        monthly_total_supply = supplier_orders.filter(created_at__gte=month_start, created_at__lte=month_end)
        if monthly_total_supply:
            monthly_total_supply_count = monthly_total_supply.count()
            monthly_total_supply_amount = monthly_total_supply.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
        monthly_pending_fulfilment = orders_pending_fulfilment.filter(created_at__gte=month_start, created_at__lte=month_end)
        if monthly_pending_fulfilment:
            monthly_pending_fulfilment_count = monthly_pending_fulfilment.count()
            monthly_pending_fulfilment_amount = monthly_pending_fulfilment.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
        monthly_pending_payment = [] # Dependent on Invoice
        if monthly_pending_payment:
            monthly_pending_payment_count = 0
            monthly_pending_payment_amount = 0
        monthly_rejected_goods = [] # Dependent on returns/rejects
        if monthly_rejected_goods:
            monthly_rejected_goods_count = 0
            monthly_rejected_goods_amount = 0
        monthly.append({
            "month": month_start.strftime("%b"), "total_supply_count": monthly_total_supply_count, "total_supply_amount": monthly_total_supply_amount,
            "pending_fulfilment_count": monthly_pending_fulfilment_count, "pending_fulfilment_amount": monthly_pending_fulfilment_amount,
            "pending_payment_count": monthly_pending_payment_count, "pending_payment_amount": monthly_pending_payment_amount, "rejected_goods_count": monthly_rejected_goods_count,
            "rejected_goods_amount": monthly_rejected_goods_amount
        })
        yearly_total_supply = supplier_orders.filter(created_at__gte=year_start, created_at__lte=year_end)
        if yearly_total_supply:
            yearly_total_supply_count = yearly_total_supply.count()
            yearly_total_supply_amount = yearly_total_supply.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
        yearly_pending_fulfilment = orders_pending_fulfilment.filter(created_at__gte=year_start, created_at__lte=year_end)
        if yearly_pending_fulfilment:
            yearly_pending_fulfilment_count = yearly_pending_fulfilment.count()
            yearly_pending_fulfilment_amount = yearly_pending_fulfilment.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
        yearly_pending_payment = [] # Dependent on Invoice
        if yearly_pending_payment:
            yearly_pending_payment_count = 0
            yearly_pending_payment_amount = 0
        yearly_rejected_goods = [] # Dependent on returns/rejects
        if yearly_rejected_goods:
            yearly_rejected_goods_count = 0
            yearly_rejected_goods_amount = 0
        yearly.append({
            "year": year_start.strftime("%Y"), "total_supply_count": yearly_total_supply_count, "total_supply_amount": yearly_total_supply_amount,
            "pending_fulfilment_count": yearly_pending_fulfilment_count, "pending_fulfilment_amount": yearly_pending_fulfilment_amount,
            "pending_payment_count": yearly_pending_payment_count, "pending_payment_amount": yearly_pending_payment_amount, "rejected_goods_count": yearly_rejected_goods_count,
            "rejected_goods_amount": yearly_rejected_goods_amount
        })
    analysis['weekly'] = weekly
    analysis['monthly'] = monthly
    analysis['yearly'] = yearly
    return analysis


def send_fund_to_vendor_bank(po, account_instance, user, escrow):
    from account.models import Transaction
    amount = float(po.accepted_cost)
    payout = Transaction.vfd_funds_transfer(
        bank_code=po.indent.supplier.bank_code, bank_name=po.indent.supplier.bank_name, account_name=po.indent.supplier.bank_account_name,
        account_number=po.indent.supplier.bank_account_number, narration=f"Payment for Purchase Order with ID: {po.order_no}", amount=amount, account=account_instance, user=user,
        company_owner=po.indent.team.company.user
    )

    if isinstance(payout, str):
        from accounting.utils import categorize_transaction
        # Categorize Transaction
        try:
            payout_transaction = Transaction.objects.get(transaction_ref=payout)
            categorize_transaction(
                payout_transaction,
                source="spend_management",
                transaction_type="supplier_management"
            )
        except Exception:
            pass
        # Subtract amount from the escrow balance
        escrow.balance -= amount
        escrow.save()
        po.indent.approval_status = "APPROVE_ONLY"
        po.indent.approved_by = user
        po.indent.save()
        po.status = "ACCEPTED"
        po.save()

        # Raise Invoice
        goods_count = po.indent.products.count()
        ProcurementPurchaseInvoice.objects.create(
            status="RELEASED", purchase_order=po, pi_requester=po.indent.requested_by, pi_approver=po.indent.approved_by, procured_for=po.indent.team,
            vendor=po.indent.supplier, no_of_items=goods_count, no_shipped=goods_count, accepted=True, allocation_balance=escrow, paid=True
        )

    ...


def is_supplier_check(email):
    try:
        supplier = Supplier.objects.get(email__iexact=email)
    except Supplier.DoesNotExist:
        raise InvalidRequestException({"message": "Supplier detail not found"})

    return supplier


def get_asset_dashboard(team_id):
    team_assets = Asset.objects.filter(team_id=team_id)

    aggregates = team_assets.aggregate(
        total_asset_amount=Sum("purchase_cost") or 0,
        total_salvage_cost=Sum("residual_value") or 0,
        total_net_income=Sum("net_income") or 0,
        total_asset_count=Count("id")
    )

    total_asset_count = aggregates["total_asset_count"] or 0
    total_asset_amount = aggregates["total_asset_amount"] or 0
    total_salvage_cost = aggregates["total_salvage_cost"] or 0
    total_net_income = aggregates["total_net_income"] or 0

    last_7_days = datetime.datetime.now() - datetime.timedelta(days=7)
    status_counts = team_assets.aggregate(
        newly_added=Count(Case(When(created_at__gt=last_7_days, then=1))),
        retired=Count(Case(When(depreciated=True, then=1))),
        active=Count(Case(When(depreciated=False, then=1))),
    )

    categories = ["PROPERTY", "VEHICLE", "ELECTRONICS", "HOUSEHOLD", "COLLECTIBLES", "OTHERS", "MACHINERY"]
    category_data = team_assets.values("asset_category").annotate(
        count=Count("id"),
        amount=Sum("purchase_cost")
    )

    category_summary = {cat: {"count": 0, "amount": 0} for cat in categories}
    for data in category_data:
        cat = data["asset_category"]
        if cat in category_summary:
            category_summary[cat]["count"] = data["count"]
            category_summary[cat]["amount"] = data["amount"]

    divisor = 1
    if total_asset_count > 0:
        divisor = total_asset_count

    overview = [
        {
            "category_name": cat,
            "amount": category_summary[cat]["amount"],
            "count": category_summary[cat]["count"],
            "rate": round((category_summary[cat]["count"] / divisor) * 100, 2)
        }
        for cat in categories
    ]

    asset_status = [
        {"status": "newly_added", "count": status_counts["newly_added"], "rate": round((status_counts["newly_added"] / divisor) * 100, 2)},
        {"status": "retired", "count": status_counts["retired"], "rate": round((status_counts["retired"] / divisor) * 100, 2)},
        {"status": "active", "count": status_counts["active"], "rate": round((status_counts["active"] / divisor) * 100, 2)},
    ]

    first_10_assets = team_assets.order_by("-purchase_cost")[:10].values("product__name", "purchase_cost")  # Based on highest value
    first_10_result = [{"name": item["product__name"], "amount": item["purchase_cost"]} for item in first_10_assets]

    last_10_items = [{
        "asset_no": item.asset_no,
        "asset_name": item.product.name,
        "product_category": item.product.category.name,
        "asset_category": item.asset_category,
        "purchase_date": item.purchase_date,
        "purchase_cost": item.purchase_cost,
        "supplier_name": item.supplier.name,
        "assigned_to": item.team.team_name

    } for item in team_assets.order_by("-created_at")[:10]]

    asset_analysis = {
        "total_asset_count": total_asset_count,
        "total_asset_amount": total_asset_amount,
        "total_salvage_amount": total_salvage_cost,
        "return_on_asset": (total_net_income / total_asset_count) if total_asset_count else 0,
        "asset_overview": overview,
        "asset_status": asset_status,
        "asset_value": first_10_result,
        "last_ten": last_10_items
    }

    cache_key = f"asset_dashboard_{team_id}"
    cache.set(key=cache_key, value=asset_analysis, timeout=300)

    return asset_analysis


def onboard_supplier(vendor, company, approve_status=False, user=None):
    approval_status = company.auto_approve_suppliers
    created_by_user = user
    if not user:
        try:
            if company.user:
                created_by_user = company.user
            else:
                created_by_user = company.teams.first().members.first().member
        except Exception as err:
            raise InvalidRequestException({"message": "User not found", "error": err})
    if approve_status:
        approval_status = True
    business_name = str(vendor.get("business_name")).title()
    business_email = str(vendor.get("business_email"))
    first_name = vendor.get("first_name")
    last_name = vendor.get("last_name")
    address = vendor.get("address")
    state = vendor.get("state")
    country = vendor.get("country")
    bank_code = vendor.get("bank_code")
    bank_name = vendor.get("bank_name")
    account_no = vendor.get("account_no")
    account_name = vendor.get("account_name")
    phone_no = vendor.get("phone_number")

    phone_number = format_phone_number(phone_no)
    person_names = first_name + " " + last_name

    supplier = Supplier.objects.create(
        company=company,
        created_by=created_by_user,
        name=business_name,
        email=business_email,
        phone_number=phone_number,
        address=address,
        bank_name=bank_name,
        bank_account_name=account_name,
        bank_account_number=account_no,
        contact_person_name=person_names,
        state=state,
        is_approved=approval_status,
        is_active=approval_status,
        bank_code=bank_code,
        country=country,
    )
    if approval_status:
        # Create Login for supplier
        random_password = User.objects.make_random_password()
        supplier_user = User.objects.create(
            email=business_email, is_supplier=True, first_name=business_name
        )
        supplier_user.set_password(random_password)
        supplier_user.save()
        supplier.approved_at = datetime.datetime.now()
        supplier.save()

        # Send generated password to supplier
        send_email.delay(
            recipient=business_email,
            subject="Vendor Account Approved",
            login_url=login_url,
            supplier_name=business_name,
            template_dir="supplier_approval.html",
            email=business_email,
            password=random_password,
        )

    return supplier


def get_spend_overview(team_id):
    current_date = timezone.now()
    twenty_days_ago = current_date - timezone.timedelta(days=20)
    week_start, week_end = get_week_start_and_end_datetime(current_date)
    this_month_start, this_month_end = get_month_start_and_end_datetime(current_date)
    year_start, year_end = get_year_start_and_end_datetime(current_date)
    po_metric = dict()

    purchase_indents = PurchaseIndent.objects.aggregate(
        pi_request_count=Count(Case(When(team_id=team_id, created_at=current_date, then="id"), output_field=IntegerField())),
        pi_request_amount=Sum(Case(When(team_id=team_id, created_at=current_date, then="estimated_cost"), output_field=FloatField())) or 0,

        weekly_pi_request_count=Count(Case(When(team_id=team_id, created_at__range=[week_start, week_end], then="id"), output_field=IntegerField())),
        weekly_pi_request_amount=Sum(Case(When(team_id=team_id, created_at__range=[week_start, week_end],
                                               then="estimated_cost"), output_field=FloatField())),

        monthly_pi_request_count=Count(Case(When(
            team_id=team_id, created_at__range=[this_month_start, this_month_end], then="id"), output_field=IntegerField()
        )),
        monthly_pi_request_amount=Sum(Case(When(
            team_id=team_id, created_at__range=[this_month_start, this_month_end], then="estimated_cost"), output_field=FloatField())),

        yearly_pi_request_count=Count(Case(When(team_id=team_id, created_at__range=[year_start, year_end], then="id"), output_field=IntegerField())),
        yearly_pi_request_amount=Sum(Case(When(
            team_id=team_id, created_at__range=[year_start, year_end], then="estimated_cost"), output_field=FloatField())),

        approved_pi_request_count=Count(Case(When(
            team_id=team_id, created_at=current_date,
            approval_status__in=["APPROVE_ONLY", "APPROVE_WITH_PO", "APPROVE_WITH_OTP", "APPROVE_WITH_INVOICE"], then="id"
        ), output_field=IntegerField())),
        approved_pi_request_amount=Sum(Case(When(
            team_id=team_id, created_at=current_date,
            approval_status__in=["APPROVE_ONLY", "APPROVE_WITH_PO", "APPROVE_WITH_OTP", "APPROVE_WITH_INVOICE"], then="estimated_cost"
        ), output_field=FloatField())),

        weekly_approved_pi_request_count=Count(Case(When(
            team_id=team_id, created_at__range=[week_start, week_end],
            approval_status__in=["APPROVE_ONLY", "APPROVE_WITH_PO", "APPROVE_WITH_OTP", "APPROVE_WITH_INVOICE"], then="id"
        ), output_field=IntegerField())),
        weekly_approved_pi_request_amount=Sum(Case(When(
            team_id=team_id, created_at__range=[week_start, week_end],
            approval_status__in=["APPROVE_ONLY", "APPROVE_WITH_PO", "APPROVE_WITH_OTP", "APPROVE_WITH_INVOICE"], then="estimated_cost"
        ), output_field=FloatField())),

        monthly_approved_pi_request_count=Count(Case(When(
            team_id=team_id, created_at__range=[this_month_start, this_month_end],
            approval_status__in=["APPROVE_ONLY", "APPROVE_WITH_PO", "APPROVE_WITH_OTP", "APPROVE_WITH_INVOICE"], then="id"
        ), output_field=IntegerField())),
        monthly_approved_pi_request_amount=Sum(Case(When(
            team_id=team_id, created_at__range=[this_month_start, this_month_end],
            approval_status__in=["APPROVE_ONLY", "APPROVE_WITH_PO", "APPROVE_WITH_OTP", "APPROVE_WITH_INVOICE"], then="estimated_cost"
        ), output_field=FloatField())),

        yearly_approved_pi_request_count=Count(Case(When(
            team_id=team_id, created_at__range=[year_start, year_end],
            approval_status__in=["APPROVE_ONLY", "APPROVE_WITH_PO", "APPROVE_WITH_OTP", "APPROVE_WITH_INVOICE"], then="id"
        ), output_field=IntegerField())),
        yearly_approved_pi_request_amount=Sum(Case(When(
            team_id=team_id, created_at__range=[year_start, year_end],
            approval_status__in=["APPROVE_ONLY", "APPROVE_WITH_PO", "APPROVE_WITH_OTP", "APPROVE_WITH_INVOICE"], then="estimated_cost"
        ), output_field=FloatField())),

    )

    purchase_indents["pi_request_amount"] = purchase_indents["pi_request_amount"] or 0
    purchase_indents["weekly_pi_request_amount"] = purchase_indents["weekly_pi_request_amount"] or 0
    purchase_indents["monthly_pi_request_amount"] = purchase_indents["monthly_pi_request_amount"] or 0
    purchase_indents["yearly_pi_request_amount"] = purchase_indents["yearly_pi_request_amount"] or 0
    purchase_indents["approved_pi_request_amount"] = purchase_indents["approved_pi_request_amount"] or 0
    purchase_indents["weekly_approved_pi_request_amount"] = purchase_indents["weekly_approved_pi_request_amount"] or 0
    purchase_indents["monthly_approved_pi_request_amount"] = purchase_indents["monthly_approved_pi_request_amount"] or 0
    purchase_indents["yearly_approved_pi_request_amount"] = purchase_indents["yearly_approved_pi_request_amount"] or 0

    purchase_orders = ProcurementPurchaseOrder.objects.aggregate(
        total_po_count=Count(Case(When(indent__team_id=team_id, then="id"), output_field=IntegerField())),
        total_po_amount=Sum(Case(When(indent__team_id=team_id, then="accepted_cost"), output_field=FloatField())),
        pending_po_count=Count(Case(When(indent__team_id=team_id, status="UNDER_REVIEW", then="id"), output_field=IntegerField())),
        pending_po_amount=Sum(Case(When(indent__team_id=team_id, status="UNDER_REVIEW", then="accepted_cost"), output_field=FloatField())),
        closed_po_count=Count(Case(When(
            indent__team_id=team_id, status__in=["ACCEPTED", "DENIED", "CANCELLED"], then="id"), output_field=IntegerField())),
        closed_po_amount=Sum(Case(When(
            indent__team_id=team_id, status__in=["ACCEPTED", "DENIED", "CANCELLED"], then="accepted_cost"
        ), output_field=FloatField())),
        overdue_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__lte=twenty_days_ago, status="UNDER_REVIEW", then="id"
        ), output_field=IntegerField())),
        overdue_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__lte=twenty_days_ago, status="UNDER_REVIEW", then="accepted_cost"), output_field=FloatField())),

        today_po_count=Count(Case(When(indent__team_id=team_id, created_at=current_date, then="id"), output_field=IntegerField())),
        today_po_amount=Sum(Case(When(indent__team_id=team_id, created_at=current_date, then="accepted_cost"), output_field=FloatField())),
        this_week_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[week_start, week_end], then="id"), output_field=IntegerField())),
        this_week_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[week_start, week_end], then="accepted_cost"), output_field=FloatField())),
        this_month_po_count=Count(
            Case(When(indent__team_id=team_id, created_at__range=[this_month_start, this_month_end], then="id"), output_field=IntegerField())),
        this_month_po_amount=Sum(
            Case(When(
                indent__team_id=team_id, created_at__range=[this_month_start, this_month_end], then="accepted_cost"
            ), output_field=FloatField())),
        this_year_po_count=Count(
            Case(When(indent__team_id=team_id, created_at__range=[year_start, year_end], then="id"), output_field=IntegerField())),
        this_year_po_amount=Sum(
            Case(When(indent__team_id=team_id, created_at__range=[year_start, year_end], then="accepted_cost"), output_field=FloatField())),

        today_accepted_po_count=Count(Case(When(
            indent__team_id=team_id, created_at=current_date, status="ACCEPTED", then="id"), output_field=IntegerField())),
        today_accepted_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at=current_date, status="ACCEPTED", then="accepted_cost"), output_field=FloatField())),
        this_week_accepted_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[week_start, week_end], status="ACCEPTED", then="id"
        ), output_field=IntegerField())),
        this_week_accepted_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[week_start, week_end], status="ACCEPTED", then="accepted_cost"
        ), output_field=FloatField())),
        this_month_accepted_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[this_month_start, this_month_end], status="ACCEPTED", then="id"
        ), output_field=IntegerField())),
        this_month_accepted_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[this_month_start, this_month_end], status="ACCEPTED", then="accepted_cost"
        ), output_field=FloatField())),
        this_year_accepted_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[year_start, year_end], status="ACCEPTED", then="id"
        ), output_field=IntegerField())),
        this_year_accepted_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[year_start, year_end], status="ACCEPTED", then="accepted_cost"
        ), output_field=FloatField())),

        today_supplied_po_count=Count(Case(When(
            indent__team_id=team_id, created_at=current_date, delivery_status="FULLY_DELIVERED", then="id"), output_field=IntegerField())),
        today_supplied_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at=current_date, delivery_status="FULLY_DELIVERED", then="accepted_cost"
        ), output_field=FloatField())),
        this_week_supplied_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[week_start, week_end], delivery_status="FULLY_DELIVERED", then="id"
        ), output_field=IntegerField())),
        this_week_supplied_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[week_start, week_end], delivery_status="FULLY_DELIVERED", then="accepted_cost"
        ), output_field=FloatField())),
        this_month_supplied_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[this_month_start, this_month_end], delivery_status="FULLY_DELIVERED", then="id"
        ), output_field=IntegerField())),
        this_month_supplied_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[this_month_start, this_month_end], delivery_status="FULLY_DELIVERED", then="accepted_cost"
        ), output_field=FloatField())),
        this_year_supplied_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[year_start, year_end], delivery_status="FULLY_DELIVERED", then="id"
        ), output_field=IntegerField())),
        this_year_supplied_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[year_start, year_end], delivery_status="FULLY_DELIVERED", then="accepted_cost"
        ), output_field=FloatField())),

        today_pending_po_count=Count(Case(When(
            indent__team_id=team_id, created_at=current_date, delivery_status="NOT_DELIVERED", then="id"), output_field=IntegerField())),
        today_pending_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at=current_date, delivery_status="NOT_DELIVERED", then="accepted_cost"
        ), output_field=FloatField())),
        this_week_pending_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[week_start, week_end], delivery_status="NOT_DELIVERED", then="id"
        ), output_field=IntegerField())),
        this_week_pending_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[week_start, week_end], delivery_status="NOT_DELIVERED", then="accepted_cost"
        ), output_field=FloatField())),
        this_month_pending_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[this_month_start, this_month_end], delivery_status="NOT_DELIVERED", then="id"
        ), output_field=IntegerField())),
        this_month_pending_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[this_month_start, this_month_end], delivery_status="NOT_DELIVERED", then="accepted_cost"
        ), output_field=FloatField())),
        this_year_pending_po_count=Count(Case(When(
            indent__team_id=team_id, created_at__range=[year_start, year_end], delivery_status="NOT_DELIVERED", then="id"
        ), output_field=IntegerField())),
        this_year_pending_po_amount=Sum(Case(When(
            indent__team_id=team_id, created_at__range=[year_start, year_end], delivery_status="NOT_DELIVERED", then="accepted_cost"
        ), output_field=FloatField())),

    )
    purchase_orders["today_po_amount"] = purchase_orders["today_po_amount"] or 0
    purchase_orders["this_week_po_amount"] = purchase_orders["this_week_po_amount"] or 0
    purchase_orders["this_month_po_amount"] = purchase_orders["this_month_po_amount"] or 0
    purchase_orders["this_year_po_amount"] = purchase_orders["this_year_po_amount"] or 0
    purchase_orders["today_accepted_po_amount"] = purchase_orders["today_accepted_po_amount"] or 0
    purchase_orders["this_week_accepted_po_amount"] = purchase_orders["this_week_accepted_po_amount"] or 0
    purchase_orders["this_month_accepted_po_amount"] = purchase_orders["this_month_accepted_po_amount"] or 0
    purchase_orders["this_year_accepted_po_amount"] = purchase_orders["this_year_accepted_po_amount"] or 0
    purchase_orders["today_supplied_po_amount"] = purchase_orders["today_supplied_po_amount"] or 0
    purchase_orders["this_week_supplied_po_amount"] = purchase_orders["this_week_supplied_po_amount"] or 0
    purchase_orders["this_month_supplied_po_amount"] = purchase_orders["this_month_supplied_po_amount"] or 0
    purchase_orders["this_year_supplied_po_amount"] = purchase_orders["this_year_supplied_po_amount"] or 0
    purchase_orders["today_pending_po_amount"] = purchase_orders["today_pending_po_amount"] or 0
    purchase_orders["this_week_pending_po_amount"] = purchase_orders["this_week_pending_po_amount"] or 0
    purchase_orders["this_month_pending_po_amount"] = purchase_orders["this_month_pending_po_amount"] or 0
    purchase_orders["this_year_pending_po_amount"] = purchase_orders["this_year_pending_po_amount"] or 0

    purchase_orders["total_po_amount"] = purchase_orders["total_po_amount"] or 0
    purchase_orders["pending_po_amount"] = purchase_orders["pending_po_amount"] or 0
    purchase_orders["closed_po_amount"] = purchase_orders["closed_po_amount"] or 0
    purchase_orders["overdue_po_amount"] = purchase_orders["overdue_po_amount"] or 0

    po_metric["total_po_count"] = purchase_orders["total_po_count"]
    po_metric["total_po_amount"] = purchase_orders["total_po_amount"]
    po_metric["pending_po_count"] = purchase_orders["pending_po_count"]
    po_metric["pending_po_amount"] = purchase_orders["pending_po_amount"]
    po_metric["closed_po_count"] = purchase_orders["closed_po_count"]
    po_metric["closed_po_amount"] = purchase_orders["closed_po_amount"]
    po_metric["overdue_po_count"] = purchase_orders["overdue_po_count"]
    po_metric["overdue_po_amount"] = purchase_orders["overdue_po_amount"]

    procurement_info = {**purchase_indents, **purchase_orders}
    top_supplier = list()

    to_pop_out = [
        "total_po_count", "total_po_amount", "pending_po_count", "pending_po_amount", "closed_po_count", "closed_po_amount", "overdue_po_count",
        "overdue_po_amount"
    ]
    for item in to_pop_out:
        procurement_info.pop(item)

    try:
        team = Team.objects.get(id=team_id)
        supplier = Supplier.objects.annotate(delivery_amount=Sum(Case(When(
            company_id=team.company_id, purchaseindent__procurementpurchaseorder__status="ACCEPTED",
            purchaseindent__procurementpurchaseorder__delivery_status__in=["PARTLY_DELIVERED", "FULLY_DELIVERED"],
            then="purchaseindent__procurementpurchaseorder__accepted_cost"
        ), output_field=FloatField())),
            delivery_count=Count(Case(When(
                company_id=team.company_id, purchaseindent__procurementpurchaseorder__status="ACCEPTED",
                purchaseindent__procurementpurchaseorder__delivery_status__in=["PARTLY_DELIVERED", "FULLY_DELIVERED"],
                then="purchaseindent__procurementpurchaseorder__id"
            ), output_field=IntegerField()))
        ).order_by("delivery_amount")[:5]
        top_supplier = [
            {"id": item.id, "supplier_name": item.name, "total_po_delivery_amount": item.delivery_amount or 0, "count": item.delivery_count}
            for item in supplier
        ]
    except Team.DoesNotExist:
        pass

    recent_deliveries = [{
        "supplier_name": item.indent.supplier.name if item.indent.supplier else None,
        "amount": item.accepted_cost,
        "po_number": item.order_no,
        "delivery_date": item.indent.actual_date_of_delivery if item.indent.actual_date_of_delivery else item.indent.expected_date_of_delivery,
        "invoice_no": item.procurementpurchaseinvoice_set.last().po_invoice_number if item.procurementpurchaseinvoice_set else ""
    } for item in ProcurementPurchaseOrder.objects.filter(
        indent__team_id=team_id, delivery_status="FULLY_DELIVERED").order_by("-confirm_delivery_date")[:5]]

    # Budget and Invoice
    dashboard_data = dict()
    budget_invoices = list()
    for delta in range(11, -1, -1):
        budget_data = dict()
        invoice_data = dict()
        month_date = current_date - relativedelta(months=delta)
        month_start, month_end = get_month_start_and_end_datetime(month_date)
        budgets = Budget.objects.aggregate(
            total_budget_amount=Sum(
                Case(When(team_id=team_id, start_date__gte=month_start, end_date__lte=month_end, then="budget_amount"), output_field=DecimalField()))
        )
        spend_amount = Requisition.objects.aggregate(
            spent_amount=Sum(Case(
                When(team_id=team_id, created_at__range=[month_start, month_end], is_disbursed=True, status__in=["APPROVED", "SUCCESSFUL"],
                     then="request_amount"), output_field=DecimalField()))
        )
        invoices_amount = ProcurementPurchaseInvoice.objects.aggregate(
            paid_invoices=Sum(
                Case(When(procured_for=team_id, created_at__range=[month_start, month_end], paid=True, status="FULLFILLED", then="total"),
                     output_field=DecimalField())
            ),
            pending_invoices=Sum(
                Case(When(procured_for=team_id, created_at__range=[month_start, month_end], paid=False, status__in=["IN_PROGRESS", "PENDING"],
                          then="total"), output_field=DecimalField())
            ),
            overdue_invoices=Sum(
                Case(When(procured_for=team_id, created_at__range=[month_start, month_end], paid=False, status__in=["IN_PROGRESS", "PENDING"],
                          then="total"), Due_date__gt=current_date, output_field=DecimalField())
            ),
        )
        budget_data["budget_amount"] = budgets.get("total_budget_amount") or 0
        budget_data["spent_amount"] = spend_amount.get("spent_amount") or 0
        invoice_data["paid_invoices"] = invoices_amount.get("paid_invoices") or 0
        invoice_data["pending_invoices"] = invoices_amount.get("pending_invoices") or 0
        invoice_data["overdue_invoices"] = invoices_amount.get("overdue_invoices") or 0

        budget_invoices.append({"month": month_start.strftime("%b"), "budget": budget_data, "invoices": invoice_data})

    dashboard_data["procurement_activities"] = procurement_info
    dashboard_data["top_supplier"] = top_supplier
    dashboard_data["purchase_order_metric"] = po_metric
    dashboard_data["budget_and_invoices"] = budget_invoices
    dashboard_data["recently_delivered"] = recent_deliveries

    cache_key = f"procurement_dashboard_{team_id}"
    cache.set(key=cache_key, value=dashboard_data, timeout=300)

    return dashboard_data




