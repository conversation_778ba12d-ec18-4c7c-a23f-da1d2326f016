from django.db.models.signals import post_save
from django.dispatch import receiver

from cart_management.models import OrderPipeline, OrderStage
from requisition.models import Company
from stock_inventory import models
from stock_inventory.tasks import company_product_price_variation


# Register your signal(s) here.
@receiver(post_save, sender=Company)
def create_company_default_branch(sender, instance, created, **kwargs):
    if created:
        models.Branch.create_company_branch(
            user=instance.user,
            company=instance,
            name="Head Office",
            address="Location",
            vat=0.0,
            is_super_branch=True,
        )


@receiver(post_save, sender=models.Branch)
def create_branch_pipeline_and_stages(sender, instance, created, **kwargs):
    if created:
        # Create a pipeline for the new branch
        pipeline = OrderPipeline.objects.create(
            name=f"{instance.name} Pipeline",
            company=instance.company,
            branch=instance,
            is_default=True,
        )
        # Define the stages
        stages = [
            "New Order",
            "Processing",
            "Fulfilled",
            "Ready For Delivery",
            "Completed",
            "Cancelled",
            "Refunded",
        ]
        # Create stages for the pipeline
        for position, stage_name in enumerate(stages, start=1):
            OrderStage.objects.create(
                name=stage_name, position=position, pipeline=pipeline
            )


@receiver(post_save, sender=models.PriceTag)
def add_price_tag_to_price_variation(sender, instance, created, **kwargs):
    if created:
        company_product_price_variation.delay(price_tag_id=instance.id)


@receiver(post_save, sender=models.Product)
def add_product_to_price_variation(sender, instance, created, **kwargs):
    if created:
        company_product_price_variation.delay(product_id=instance.id)
