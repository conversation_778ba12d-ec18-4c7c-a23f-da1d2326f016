from django.contrib import admin
from import_export.admin import ImportExportModelAdmin

from invoicing import models, resources


# Register your model(s) here.
class InvoiceResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.InvoiceResource
    autocomplete_fields = [
        "company",
        "branch",
        "customer"
    ]
    search_fields = [
        "company__company_name",
        "company__user__email",
        "branch__name",
        "reference",
        "customer__email",
        "customer__phone",
    ]
    list_filter = [
        "paid",
        "due",
        "received_part_payment",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class InvoiceItemResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.InvoiceItemResource
    autocomplete_fields = [
        "invoice",
    ]
    search_fields = [
        "invoice__company__company_name",
        "invoice__company__user__email",
        "invoice__branch__name",
        "invoice__reference",
        "invoice__customer__email",
        "invoice__customer__phone",
    ]
    list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TaxResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TaxResource
    search_fields = []
    list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.Invoice, InvoiceResourceAdmin)
admin.site.register(models.InvoiceItem, InvoiceItemResourceAdmin)
admin.site.register(models.Tax, TaxResourceAdmin)
