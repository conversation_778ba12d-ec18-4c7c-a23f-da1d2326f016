from datetime import datetime, timedelta
import random

from django.conf import settings
from django.core.management.base import (
    BaseCommand,
    CommandError,
    CommandParser,
)
import pytz

from core.models import User
from helpers.enums import ChannelTypes 
from requisition.models import Company
from sales_app import models
from sales_app.helper.enums import TransactionStatusChoices
from stock_inventory.models import Product


customers = [
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "08081111111",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "<PERSON><PERSON><PERSON>oli",
        "email": "<EMAIL>",
        "phone": "08082222222",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "08083333333",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "phone": "08084444444",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "Dare Oguntuga",
        "email": "<EMAIL>",
        "phone": "08085555555",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "Edmund Giwa",
        "email": "<EMAIL>",
        "phone": "08086666666",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "Emeka Nwaoma",
        "email": "<EMAIL>",
        "phone": "08087777777",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "Tobi Taiwo",
        "email": "<EMAIL>",
        "phone": "08088888888",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "Adeyemi Ogunnaike",
        "email": "<EMAIL>",
        "phone": "08089999999",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "Loretta Ojeah",
        "email": "<EMAIL>",
        "phone": "080878954321",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "Victor Olaniyi",
        "email": "<EMAIL>",
        "phone": "08080896542",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
    {
        "name": "Chukwuka Ibejih",
        "email": "<EMAIL>",
        "phone": "08086573421",
        "address": "27, Alara Street, Sabo - Yaba, Lagos, Nigeria",
    },
]


def get_random_product(company: Company):
    count = Product.objects.filter(company=company).count()
    random_index = random.randint(0, count - 1)
    random_item = Product.objects.filter(company=company)[random_index]
    return random_item


class Command(BaseCommand):
    help = "CREATES SALES TRANSACTION RECORD(S) FOR THE SPECIFIED COMPANY."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("user_id")
        parser.add_argument("company_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        user = User.objects.filter(id=kwargs["user_id"]).first()
        if user is None:
            raise CommandError("PROVIDE A VALID USER ID.")
        company = Company.objects.filter(id=kwargs["company_id"]).first()
        if company is None:
            raise CommandError("PROVIDE A VALID COMPANY ID.")
        company_branches = models.Branch.objects.filter(company=company)
        if not company_branches.exists():
            raise CommandError("COMPANY HAS NO BRANCH RECORDs.")
        super_branch = company_branches.filter(is_super_branch=True).first()
        if super_branch is None:
            pass
        counter = 0
        duration = 30
        START_DATE = START_TIME - timedelta(days=duration)
        # select and create a customer.
        for customer in customers:
            models.Customer.create_record(
                company=company,
                branch=(
                    super_branch if super_branch is not None else company_branches[0]
                ),
                created_by=user,
                name=customer["name"],
                email=customer["email"],
                phone=customer["phone"],
                address=customer["address"],
            )
        company_customers = models.Customer.objects.filter(company=company)[:10]
        for _ in range(duration):
            for branch in company_branches:
                for customer in company_customers:
                    sales = []
                    while len(sales) < random.choice(range(10)):
                        sales_item = get_random_product(company=company)
                        sales_items = {
                            "item": sales_item,
                            "quantity": random.choice([5, 10, 15, 20, 25]),
                            "amount": random.choice([1000, 1200, 1500, 1800, 2000]),
                        }
                        sales.append(sales_items)
                    sales_transaction = models.SalesTransaction.register(
                        company=company,
                        branch=branch,
                        sales=sales,
                        created_by=user,
                        device=random.choice(
                            [
                                ChannelTypes.INSTANT_WEB,
                                ChannelTypes.MOBILE,
                                ChannelTypes.POS,
                                ChannelTypes.USSD,
                                ChannelTypes.WEB,
                            ]
                        ),
                        customer=customer,
                    )
                    models.SalesTransaction.objects.filter(
                        batch_id=sales_transaction["batch_id"]
                    ).update(
                        created_at=START_DATE + timedelta(days=counter),
                        updated_at=START_DATE + timedelta(days=counter),
                        status=TransactionStatusChoices.SUCCESSFUL,
                    )
            counter += 1
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        print(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
