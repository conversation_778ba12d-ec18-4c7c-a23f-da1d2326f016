from datetime import date

from rest_framework.serializers import ValidationError

from helpers.redis_storage import RedisStore


redis_manager = RedisStore()


# Create your utility function(s) here.
def invoice_number():
    """
    Deprecated (2024-08-28).
    """
    from invoicing.models import Invoice

    month = date.today().strftime("%b").upper()
    invoices = Invoice.objects.all().count()
    base_number = redis_manager.get_data("base_number_counter")
    if base_number is None:
        base_number = invoices + 1
    else:
        base_number = int(base_number)
        base_number += 1
    redis_manager.set_data_no_expiration(key="base_number_counter", value=base_number)
    number_value = str(base_number).zfill(10)
    return f"INV-{month}-{number_value}"


def get_tax_rate(tax_id: str):
    from invoicing.models import Tax

    try:
        tax = Tax.objects.get(id=tax_id)
    except Tax.DoesNotExist:
        raise ValidationError({"errors": "invalid tax ID supplied."})
    return tax.rate
