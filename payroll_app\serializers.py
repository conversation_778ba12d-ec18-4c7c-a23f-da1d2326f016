from django.contrib.auth import get_user_model
from django.db.models import Sum
from django.db.models.functions import Lower
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.core.validators import FileExtensionValidator
from payroll_app.apis.employee_verification import validate_utility_bill_with_chagpt
from payroll_app.services import valid_uuid_check
from rest_framework import serializers
from account.models import DebitCreditRecordOnAccount, Transaction
from payroll_app.apis.func import check_employee_onboarding_percentage, round_amount
from payroll_app.models import (
    ALL_SALARY_COMPONENT_CALCULATION_TYPE,
    COMPONENT_FREQUENCY,
    DEDUCTION_CALCULATION_TYPE,
    DEPENDENCY_OPERATOR,
    OPERATOR_TYPE,
    Beneficiary,
    BenefitComponentSettings,
    CompanyAnnouncement,
    CompanyDepartmentSettings, 
    CompanyDetailsData,
    CompanyEmployeeAccountDetails,
    CompanyEmployeeCertifications,
    CompanyEmployeeEducationDetails,
    CompanyEmployeeExperienceDetails, 
    CompanyEmployeeList,
    CompanyEmployeeOnboardingForm, 
    CompanyPayGradeSettings, 
    CompanyPayGroupSettings, 
    CompanyPayrollSettings,
    CompanyTaxBand,
    InstantWagePayroll,
    ManageDepartmentRole,
    ManagePermissionsRole, 
    OneClickTransaction,
    CustomComponentSettings, 
    OtherDeductionSettings,
    OtherDependencySettings, 
    PayrollTable,
    DEDUCTION_TYPE,
    PensionFundAdminSettings,
    SalaryComponentSettings,
    SALARY_COMPONENT_CALCULATION_TYPE,
)
from requisition.models import Company

User = get_user_model()

class PFAPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid PFA ID.',
        'incorrect_type': 'Incorrect type. Expected a valid PFA ID, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', False)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        # if not data is None:
        #     return None
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)

class DepartmentCustomPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid Department ID.',
        'incorrect_type': 'Incorrect type. Expected a valid Department ID, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', False)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)

class PayGradeCustomPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid PayGrade ID.',
        'incorrect_type': 'Incorrect type. Expected a valid PayGrade ID, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', False)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)

class CreatePayrollPINSerializer(serializers.Serializer):
    requisition_pin = serializers.CharField()
    requisition_pin_retry = serializers.CharField()

    def validate(self, values):
        if not "requisition_pin" in values or not "requisition_pin_retry" in values:
            raise serializers.ValidationError(
                {"error_code": "24", "message": "You must supply requisition_pin and requisition_pin_retry keys"}
            )

        if values["requisition_pin"].isnumeric():
            pass
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "You must supply an integer"}
            )
        return values


class ChangePayrollPINSerializer(serializers.Serializer):
    old_pin = serializers.CharField()
    pin = serializers.CharField()
    repeat_pin = serializers.CharField()

    def validate(self, values):
        if not "old_pin" in values or not "pin" in values or not "repeat_pin" in values:
            raise serializers.ValidationError(
                {"error_code": "24", "message": "You must supply old_pin, pin and repeat_pin keys"}
            )

        if values["pin"].isnumeric():
            pass
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "You must supply an integer"}
            )
        return values


class AddPayrollSerializer(serializers.Serializer):
    payroll_pin = serializers.CharField(required=True, allow_null=False)
    payroll_date = serializers.DateField(required=True, allow_null=False)
    # is_recurring = serializers.BooleanField(required=True)
    narration = serializers.CharField(required=True, allow_null=False)
    payroll_month = serializers.CharField(required=True, allow_null=False)
    payroll_year = serializers.CharField(required=True, allow_null=False)

class AddSelectedPayrollSerializer(serializers.Serializer):
    payroll_pin = serializers.CharField(required=True, allow_null=False)
    payroll_date = serializers.DateField(required=True, allow_null=False)
    # is_recurring = serializers.BooleanField(required=True)
    narration = serializers.CharField(required=True, allow_null=False)
    payroll_month = serializers.CharField(required=True, allow_null=False)
    payroll_year = serializers.CharField(required=True, allow_null=False)
    employees = serializers.ListField(required=True, allow_null=False)
    multiple_payroll = serializers.BooleanField(required=False, default=False)

    def validate(self, attrs):
        employees = attrs.get("employees")
        if len(employees) <= 0:
            raise serializers.ValidationError({"employees": "employees field cannot be empty"})
        return attrs


class RunApprovalPayrollSerializer(serializers.Serializer):
    payroll_pin = serializers.CharField(required=True, allow_null=False)


class EditPayrollDetailSerializer(serializers.Serializer):
    phone_number = serializers.CharField(required=True, allow_null=False)
    gross_amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    net_amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    pension_amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    tax_amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    other_amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    other_deductions = serializers.FloatField(min_value=1, required=True, allow_null=False)
    payable_amount = serializers.FloatField(min_value=1, required=True, allow_null=False)


class PayrollApprovalSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = (
            "id",
            "payroll_admin",
            # "employee",
            "phone_number",
            "gross_amount",
            "net_amount",
            "first_name",
            "last_name",
            "email",
            "bulk_id",
            "salary_grade",
            "pension_amount",
            "employer_pension_amount",
            "employee_voluntary_pension_amount",
            "tax_amount",
            "other_amount",
            "other_deductions",
            "payable_amount",
            "hmo_amount",
            "payroll_type",
            "status",
        )

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["is_active"] = True
        serialized_data["phone_no"] = instance.phone_number
        serialized_data["company_name"] = instance.company.company_name
        if instance.status == "APPROVAL":
            serialized_data["status"] = "PENDING APPROVAL"
        elif instance.status == "DISBURSE":
            serialized_data["status"] = "PENDING DISBURSE"
        elif instance.status == "DISBURSED":
            serialized_data["status"] = "PAYMENT SUCCESSFUL"
        else:
            serialized_data["status"] = "OMITTED"

        return serialized_data


class PayrollSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = ["bulk_id"]

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        bulk_id = serialized_data["bulk_id"]
        all_data = []
        payroll_all = PayrollTable.objects.filter(bulk_id=bulk_id)
        print(payroll_all)
        for payroll in payroll_all:
            all_data.append({
                "id": payroll.id,
                "phone_number": payroll.phone_number,
                "gross_amount": payroll.gross_amount,
                "net_amount": payroll.net_amount,
                "bulk_id": payroll.bulk_id,
                # "payroll_user": payroll.payroll_user,
                "date_created": payroll.date_created,
            })
        serialized_data["payroll_data"] = all_data
        # representation["request_status"] = instance.get_request_history_status

        return serialized_data




class PayrollSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = ["bulk_id"]

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        bulk_id = serialized_data["bulk_id"]
        all_data = []
        payroll_all = PayrollTable.objects.filter(bulk_id=bulk_id)
        print(payroll_all)
        for payroll in payroll_all:
            all_data.append({
                "id": payroll.id,
                "phone_number": payroll.phone_number,
                "gross_amount": payroll.gross_amount,
                "net_amount": payroll.net_amount,
                "bulk_id": payroll.bulk_id,
                # "payroll_user": payroll.payroll_user,
                "date_created": payroll.date_created,
            })
        serialized_data["payroll_data"] = all_data
        # representation["request_status"] = instance.get_request_history_status

        return serialized_data


class PayrollUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = [
            "id",
            "phone_number",
            "gross_amount",
            "life_insurance_amount",
            "net_amount",
            "bulk_id",
            "first_name",
            "last_name",
            "email",
            "gender",
            "salary_grade",
            "pension_amount",
            "tax_amount",
            "other_amount",
            "other_deductions",
            "payable_amount",
            "hmo_amount",
            "instant_wage_deduction",
            "bank_name",
            "account_number",
            "account_name",
            "bank_code",
            "status",
            "basic_amount",
            "housing_amount",
            "transport_amount",
            "payroll_type",
            "payroll_disburse",
            "payroll_status",
            "payroll_date",
            "date_created",
            "date_updated",
            "payroll_admin",
            "payroll_user",
            "company"
        ]
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )

        serialized_data["payroll_admin"] = instance.payroll_admin.email if instance.payroll_admin else None
        serialized_data["payroll_user"] = instance.payroll_user.email if instance.payroll_user else None
        serialized_data["company"] = instance.company.company_name if instance.company else None

        if instance.company:
            get_company = CompanyPayrollSettings.objects.filter(company=instance.company).last()
            company_data = CompanyDetailsData.objects.filter(company=instance.company, is_deleted=False, bulk_id=instance.bulk_id).first()
            payroll_date = company_data.payroll_date
            if get_company:
                serialized_data["company_logo"] = get_company.company_logo
                serialized_data["company_address"] = get_company.company_address
            else:
                serialized_data["company_logo"] = None
                serialized_data["company_address"] = None

            serialized_data["payroll_date"] = payroll_date if company_data else None
            

        else:
            serialized_data["company_logo"] = None
            serialized_data["company_address"] = None
        return serialized_data


class EditCompanySerializer(serializers.Serializer):
    COMPANY_TYPE = (
        ("COOPERATE", "COOPERATE"),
        ("PERSONAL", "PERSONAL"),
    )
    company_name = serializers.CharField(required=False, allow_null=False)
    company_type = serializers.ChoiceField(required=False, choices=COMPANY_TYPE)
    company_id = serializers.CharField(required=False, allow_null=False)


class VerifyCompanySerializer(serializers.Serializer):
    COMPANY_TYPE = (
        ("COOPERATE", "COOPERATE"),
        ("PERSONAL", "PERSONAL"),
    )
    company_rc_number = serializers.CharField(allow_blank=True)
    company_type = serializers.ChoiceField(required=True, choices=COMPANY_TYPE)
    extra_kwargs = {"company_rc_number": {"required": True, "allow_null": True}}

    def validate(self, values):
        if values["company_type"] == "COOPERATE":
            if values["company_rc_number"] == "" or values["company_rc_number"] == None:
                raise serializers.ValidationError(
                    {"error_code": "24", "company_rc_number": "company_rc_number cannot be empty"}
                )

        return values


class EmployeeMultipleSerializer(serializers.Serializer):
    GENDER = [
        ("MALE", "MALE"),
        ("FEMALE", "FEMALE"),
    ]
    employee_email = serializers.EmailField()
    employee_gross_amount = serializers.FloatField()
    employee_net_amount = serializers.FloatField()

    employee_pension_amount = serializers.FloatField(required=False, default=0)
    employee_tax_amount = serializers.FloatField(required=False, default=0)
    employee_other_amount = serializers.FloatField(required=False, default=0)
    employee_other_deductions = serializers.FloatField(required=False, default=0)
    employee_hmo_amount = serializers.FloatField(required=False, default=0)

    employee_department = DepartmentCustomPrimaryKeyRelatedField(queryset=CompanyDepartmentSettings.objects.all())
    employee_salary_grade = PayGradeCustomPrimaryKeyRelatedField(queryset=CompanyPayGradeSettings.objects.all())
    employee_gender = serializers.ChoiceField(required=False, choices=GENDER, allow_null=True, allow_blank=True)
    employee_bank_name = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    employee_bank_code = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    employee_account_number = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    employee_account_name = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    employee_first_name = serializers.CharField()
    employee_last_name = serializers.CharField()
    employee_payable_amount = serializers.FloatField()
    employee_phone_number = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    def validate(self, values):
        company_id = self.context.get("company_id")

        employee_email = values.get("employee_email")
        employee_first_name = values.get("employee_first_name")
        employee_last_name = values.get("employee_last_name")

        employee_department = values.get("employee_department")
        employee_salary_grade = values.get("employee_salary_grade")

        values["employee_email"] = employee_email.lower()
        values["employee_first_name"] = employee_first_name.title()
        values["employee_last_name"] = employee_last_name.title()
        
        if employee_department:
            print(employee_department.company.id, company_id)
            if str(employee_department.company.id) != str(company_id):
                raise serializers.ValidationError(
                    {"data": f"{employee_department.department_name} does not belong to this company"}
                )
        if employee_salary_grade:
            if str(employee_salary_grade.company.id) != str(company_id):
                raise serializers.ValidationError(
                    {"data": f"{employee_salary_grade.pay_grade_name} does not belong to this company"}
                )
       
        return values


class CreateEmployeeSerializer(serializers.Serializer):
    data = serializers.ListSerializer(child=EmployeeMultipleSerializer())

    def validate(self, values):
        if len(values["data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "data": "data cannot be empty"}
            )
        return values


class CompanyMembersSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ("phone_no", "email", "first_name", "last_name", "account_no", "account_name", "state", "lga",
                  "nearest_landmark", "payroll_id", "gender")


class ListCompanyEmployeeSerializer(serializers.ModelSerializer):
    employee = CompanyMembersSerializer(read_only=True)

    class Meta:
        model = CompanyEmployeeList
        fields = (
            "id",
            "employer",
            "employee",
            "company",
            "employee_gross_amount",
            "employee_net_amount",
            "employee_salary_grade",
            "employee_pension_amount",
            "employer_pension_amount",
            "employee_tax_amount",
            "employee_other_amount",
            "employee_other_deductions",
            "employee_payable_amount",
            "employee_hmo_amount",
            "employee_bank_name",
            "employee_account_number",
            "employee_gender",
            "employee_first_name",
            "employee_last_name",
            "employee_phone_number",
            "employee_voluntary_pension_amount",
            "can_disburse",
            "can_approve",
            "can_add_member",
            "can_edit_member",
            "can_delete_member",
            "can_delete_payroll",
            "can_run_payroll",
            "employee_email",
            "employee_status",
            "employee_start_date",
            "is_work_anniversary_enabled", 
            "is_birthday_enabled", 
            "employee_staff_id",

            "employee_other_name",
            "employee_marital_status",
            "employee_religion",
            "employee_blood_group",
            "employee_genotype",
        )

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        # if instance.employee:
        #     # serialized_data["is_active"] = instance.employee.is_active
        #     # serialized_data["employee_first_name"] = instance.employee.first_name
        #     # serialized_data["employee_last_name"] = instance.employee.last_name
        #     # serialized_data["employee_gender"] = instance.employee.gender
        #     # serialized_data["employee_phone_no"] = instance.employee.phone_no
        # else:
        #     # # serialized_data["is_active"] = ""
        #     # serialized_data["first_name"] = ""
        #     # serialized_data["last_name"] = ""
        #     # serialized_data["gender"] = ""
        #     # serialized_data["phone_no"] = ""
        #     pass
        employee_data_percentage = check_employee_onboarding_percentage(instance)
        serialized_data["percentage_completed"] = employee_data_percentage
        serialized_data["email"] = instance.employee_email
        serialized_data["status"] = instance.employee_status

        return serialized_data
    
class ListCompanyEmployeeDataSerializer(serializers.ModelSerializer):

    class Meta:
        model = CompanyEmployeeList
        fields = (
            "id",
            "employee_first_name",
            "employee_last_name",
            "employee_start_date",
            "employee_department",
            "created_at",

        )

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["email"] = instance.employee_email
        serialized_data["department_name"] = instance.employee_department.department_name if instance.employee_department else ""

        return serialized_data

class EmployeePFAMembersSerializer(serializers.ModelSerializer):
    class Meta:
        model = PensionFundAdminSettings
        fields = ("id", "pfa_name")

class EditEmployeePermissionSerializer(serializers.ModelSerializer):
    pension_fund_admin = PFAPrimaryKeyRelatedField(queryset=PensionFundAdminSettings.objects.all())
    class Meta:
        model = CompanyEmployeeList
        fields = (
            "employee_gross_amount",
            "employee_life_insurance_amount",
            "employee_net_amount",
            "employee_pension_amount",
            "employer_pension_amount",
            "employee_tax_amount",
            "employee_other_amount",
            "employee_other_deductions",
            "employee_payable_amount",
            "employee_voluntary_pension_amount",
            "employee_hmo_amount",
            "employee_bank_name",
            "employee_account_number",
            "employee_staff_id",
            "employee_staff_id_reporting_line",
            "employee_job_title",
            "employee_position",
            "employee_birth_date",
            "employee_bank_code",
            "employee_account_name",
            "employee_bvn_number",
            "employee_swift_code",
            "employee_department",
            "employee_contract_type",
            "can_disburse",
            "can_approve",
            "can_add_member",
            "can_edit_member",
            "can_delete_member",
            "can_delete_payroll",
            "can_run_payroll",
            "employee_first_name",
            "employee_last_name",
            "employee_phone_number",
            "employee_alternate_email",
            "employee_birth_date",
            "employee_gender",
            "employee_staff_id",
            "employee_staff_id_reporting_line",
            "employee_job_title",
            "employee_department",
            "employee_contract_type",
            "employee_state",
            "employee_city",
            "employee_town",
            "employee_bus_stop",
            "employee_street_name",
            "employee_house_no",
            "employee_postal_code",
            "employee_profile_picture",
            "pension_pin",
            "pension_fund_admin",
            "pay_schedule",
            "pay_run",
            "employee_type",
            "staff_type",

            "employee_other_name",
            "employee_marital_status",
            "employee_religion",
            "employee_blood_group",
            "employee_genotype",
        )

    def validate(self, attrs):
        employee_instance = self.context.get("employee_instance")
        if "employee_first_name" in attrs:
            attrs["employee_first_name"] = attrs.get("employee_first_name").title() if attrs.get("employee_first_name") else None
        if "employee_last_name" in attrs:
            attrs["employee_last_name"] = attrs.get("employee_last_name").title() if attrs.get("employee_last_name") else None

        # if "employee_other_deductions" in attrs and "employee_other_amount" in attrs:
        #     employee_other_deductions = attrs.get("employee_other_deductions")
        #     employee_other_amount = attrs.get("employee_other_amount")

        #     net_amount = employee_instance.employee_net_amount
        #     try:
        #         employee_instance.employee_payable_amount = round_amount((net_amount + employee_other_amount) - employee_other_deductions)
        #         if employee_instance.employee_payable_amount < 0:
        #             raise serializers.ValidationError(
        #             {"error_code": "34", "message": f"{employee_instance.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
        #         )
        #         else:
        #             employee_instance.save()
        #     except ValueError:
        #         raise serializers.ValidationError(
        #         {"error_code": "24", "message": "check the entered values"}
        #     )
        
        # else:
            # raise serializers.ValidationError(
            #     {"error_code": "24", "data": "employee_other_deduction and employee_other_amount is required"}
            # )
        return attrs
    
class ViewEditEmployeeSerializer(serializers.ModelSerializer):
    pension_fund_admin = PFAPrimaryKeyRelatedField(queryset=PensionFundAdminSettings.objects.all())
    class Meta:
        model = CompanyEmployeeList
        fields = (
            "employee_bank_name",
            "employee_account_number",
            "employee_job_title",
            "employee_position",
            "employee_bank_code",
            "employee_account_name",
            "employee_bvn_number",
            "employee_swift_code",
            "can_disburse",
            "can_approve",
            "can_add_member",
            "can_edit_member",
            "can_delete_member",
            "can_delete_payroll",
            "can_run_payroll",
            "employee_first_name",
            "employee_last_name",
            "employee_phone_number",
            "employee_alternate_email",
            "employee_birth_date",
            "employee_gender",
            "employee_job_title",
            "employee_department",
            "employee_contract_type",
            "employee_state",
            "employee_city",
            "employee_town",
            "employee_bus_stop",
            "employee_street_name",
            "employee_house_no",
            "employee_postal_code",
            "employee_profile_picture",
            "pension_pin",
            "pension_fund_admin",
            "pay_schedule",
            "pay_run",
            "employee_type",
            "staff_type",

            "employee_other_name",
            "employee_marital_status",
            "employee_religion",
            "employee_blood_group",
            "employee_genotype",
        )

    def validate(self, attrs):
        employee_instance = self.context.get("employee_instance")
        if "employee_first_name" in attrs:
            attrs["employee_first_name"] = attrs.get("employee_first_name").title() if attrs.get("employee_first_name") else None
        if "employee_last_name" in attrs:
            attrs["employee_last_name"] = attrs.get("employee_last_name").title() if attrs.get("employee_last_name") else None

        # if "employee_other_deductions" in attrs and "employee_other_amount" in attrs:
        #     employee_other_deductions = attrs.get("employee_other_deductions")
        #     employee_other_amount = attrs.get("employee_other_amount")

        #     net_amount = employee_instance.employee_net_amount
        #     try:
        #         employee_instance.employee_payable_amount = round_amount((net_amount + employee_other_amount) - employee_other_deductions)
        #         if employee_instance.employee_payable_amount < 0:
        #             raise serializers.ValidationError(
        #             {"error_code": "34", "message": f"{employee_instance.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
        #         )
        #         else:
        #             employee_instance.save()
        #     except ValueError:
        #         raise serializers.ValidationError(
        #         {"error_code": "24", "message": "check the entered values"}
        #     )
        
        # else:
            # raise serializers.ValidationError(
            #     {"error_code": "24", "data": "employee_other_deduction and employee_other_amount is required"}
            # )
        return attrs


class DisbursePayrollSerializer(serializers.Serializer):
    PAYROLL_TYPE = (
        ("ACCOUNT", "ACCOUNT"),
        ("BUDDY", "BUDDY"),
    )
    payroll_pin = serializers.CharField(required=True, allow_null=False)
    payroll_choice = serializers.ChoiceField(required=True, allow_blank=False, choices=PAYROLL_TYPE)


class TransactionHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "transaction_type",
            "amount",
            "status",
            "balance_before",
            "balance_after",
            "beneficiary_account_name",
            "source_account_name",
            "date_created",
        ]

class PayrollTransactionHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = [
            "first_name",
            "last_name",
            "email",
            "payable_amount",
            "hmo_amount",
            "instant_wage_deduction",
            "other_deductions",
            "tax_amount",
            "other_amount",
            "pension_amount",
            "payroll_type",
            "date_created",
            "date_updated",
        ]
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["amount"] = instance.payable_amount
        return serialized_data


class PayrollHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = (
            "bulk_id",
        )

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        all_pay = (
            PayrollTable.objects.filter(bulk_id=instance, status="DISBURSED", payroll_deleted=False).order_by("-date_created")
        )
        employee_count = all_pay.count()
        total_amount = all_pay.aggregate(Sum("payable_amount"))["payable_amount__sum"]
        payroll_data = []
        for pay_data in all_pay:
            this_payroll_employee =  pay_data.employee
            if this_payroll_employee:
                employee_department = this_payroll_employee.employee_department.department_name if pay_data.employee.employee_department else None
            else:
                employee_department = None

            payroll_data.append(
                {
                    "id": pay_data.id,
                    "phone_number": pay_data.phone_number,
                    "gross_amount": pay_data.gross_amount,
                    "net_amount": pay_data.net_amount,
                    "bulk_id": pay_data.bulk_id,
                    "salary_grade": pay_data.salary_grade,
                    "pension_amount": pay_data.pension_amount,
                    "tax_amount": pay_data.tax_amount,
                    "other_amount": pay_data.other_amount,
                    "other_deductions": pay_data.other_deductions,
                    "payable_amount": pay_data.payable_amount,
                    "status": pay_data.status,
                    "full_name": pay_data.employee.full_name if pay_data.employee else None,
                    "department": employee_department
                }
            )
        payroll_det = CompanyDetailsData.objects.filter(bulk_id=instance, is_deleted=False).first()
        if payroll_det:
            date_completed = payroll_det.payroll_date_completed
            payroll_month = payroll_det.payroll_month.upper()
            payroll_year = payroll_det.payroll_year
        else:
            payroll_month = None
            date_completed = None
            payroll_year = None 

        del serialized_data["bulk_id"]
        serialized_data["status"] = "Payment Successful"
        serialized_data["date_completed"] = date_completed
        serialized_data["payroll_month"] = payroll_month
        serialized_data["bulk_id"] = instance
        serialized_data["payroll_year"] = payroll_year
        serialized_data["employee_count"] = employee_count
        serialized_data["total_amount"] = total_amount
        serialized_data["payroll_history_data"] = payroll_data

        return serialized_data


class PayrollTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = ["id", "payable_amount", "payroll_user", "pension_amount", "tax_amount"]


class ListCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = (
            ("__all__")
        )


class PayrollTypeSerializer(serializers.Serializer):
    PAYROLL_TYPE = (
        ("DISBURSED", "DISBURSED"),
        ("APPROVAL", "APPROVAL"),
        ("DISBURSE", "DISBURSE"),
    )
    payroll_type = serializers.ChoiceField(required=True, allow_blank=False, choices=PAYROLL_TYPE)


# class WalletSystemSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = WalletSystem
#         fields = ["wallet_id", "available_balance", "date_created"]

class OmitSerializer(serializers.Serializer):
    data = serializers.ListField(required=True)

    def validate(self, values):
        if len(values["data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "data": "data cannot be empty"}
            )
        return values


class EmployeeProfileSerializer(serializers.ModelSerializer):
    employee = CompanyMembersSerializer(read_only=True)
    pension_fund_admin = EmployeePFAMembersSerializer(read_only=True)
    class Meta:
        model = CompanyEmployeeList
        fields = (
            "id",
            "employer",
            "employee",
            "company",
            "employee_first_name",
            "employee_last_name",
            "employee_phone_number",
            "employee_alternate_email",
            "employee_birth_date",
            "employee_gender",
            "employee_staff_id",
            "employee_staff_id_reporting_line",
            "employee_job_title",
            "employee_department",
            "employee_contract_type",
            "employee_state",
            "employee_city",
            "employee_town",
            "employee_bus_stop",
            "employee_street_name",
            "employee_house_no",
            "employee_postal_code",
            "employee_profile_picture",
            "pension_fund_admin",
            "pension_pin",
            "pay_schedule",
            "pay_run",
            "employee_type",
            "staff_type",
            "employee_pay_grade",
            "employee_role",
            "employee_department_role",

            "employee_other_name",
            "employee_marital_status",
            "employee_religion",
            "employee_blood_group",
            "employee_genotype",
            
            "employee_gross_amount",
            "employee_life_insurance_amount",
            "employee_net_amount",
            "employee_pension_amount",
            "employer_pension_amount",
            "employee_tax_amount",
            "employee_other_amount",
            "employee_other_deductions",
            "employee_payable_amount",
            "employee_voluntary_pension_amount",
            "employee_hmo_amount",
        )
    
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        department_name = instance.employee_department.department_name if instance.employee_department else ""
        pay_grade_name = instance.employee_pay_grade.pay_grade_name if instance.employee_pay_grade else ""
        role_name = instance.employee_role.role_name if instance.employee_role else ""
        department_role_name = instance.employee_department_role.role_name if instance.employee_department_role else ""
        
        serialized_data["department_name"] = department_name
        serialized_data["pay_grade_name"] = pay_grade_name
        serialized_data["role_name"] = role_name
        serialized_data["department_role_name"] = department_role_name
        return serialized_data


class InstantWageRequestSerializer(serializers.Serializer):
    REQUEST_TYPE = (
        ("REJECTED", "REJECTED"),
        ("SUCCESSFUL", "SUCCESSFUL"),
    )
    request_type = serializers.ChoiceField(required=True, choices=REQUEST_TYPE)


class EditInstantWageRequestSerializer(serializers.Serializer):
    REQUEST_TYPE = (
        ("OFF", "OFF"),
    )
    request_type = serializers.ChoiceField(required=True, choices=REQUEST_TYPE)

class UserWalletHistorySerializer(serializers.ModelSerializer):

    class Meta:
        model = DebitCreditRecordOnAccount
        fields = [
            "id",
            "entry",
            "requisition_type",
            "amount",
            "balance_before",
            "balance_after",
            "type_of_trans",
            "date_credited",
            "last_updated",
            "date_created"
        ]

class PayOutSerializer(serializers.Serializer):
    PAYROLL_TYPE = (
        ("ACCOUNT", "ACCOUNT"),
        ("BUDDY", "BUDDY"),
    )

    DISBURSE_TYPE = (
        ("PERSONAL_PAYROLL", "PERSONAL_PAYROLL"),
        ("INSTANT_WAGE", "INSTANT_WAGE"),
        # ("PAYROLL", "PAYROLL"),
        # ("NON_CORP_PAYROLL", "NON_CORP_PAYROLL"),
    )
    disburse_pin = serializers.CharField(required=True, allow_null=False)
    payout_choice = serializers.ChoiceField(required=True, allow_blank=False, choices=PAYROLL_TYPE)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    account_number = serializers.CharField(required=True, allow_null=True)
    account_name = serializers.CharField(required=True, allow_null=True)
    bank_name = serializers.CharField(required=True, allow_null=True)
    bank_code = serializers.CharField(required=True, allow_null=True)
    disburse_type = serializers.ChoiceField(required=True, allow_blank=False, choices=DISBURSE_TYPE)
    narration = serializers.CharField(required=False, allow_blank=True, allow_null=True)

class CompanyPayOutSerializer(serializers.Serializer):
    PAYROLL_TYPE = (
        ("ACCOUNT", "ACCOUNT"),
        ("BUDDY", "BUDDY"),
    )

    DISBURSE_TYPE = (
        # ("PERSONAL_PAYROLL", "PERSONAL_PAYROLL"),
        # ("INSTANT_WAGE", "INSTANT_WAGE"),
        ("PAYROLL", "PAYROLL"),
        ("NON_CORP_PAYROLL", "NON_CORP_PAYROLL"),
    )
    disburse_pin = serializers.CharField(required=True, allow_null=False)
    payout_choice = serializers.ChoiceField(required=True, allow_blank=False, choices=PAYROLL_TYPE)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    account_number = serializers.CharField(required=True, allow_null=True)
    account_name = serializers.CharField(required=True, allow_null=True)
    bank_name = serializers.CharField(required=True, allow_null=True)
    bank_code = serializers.CharField(required=True, allow_null=True)
    disburse_type = serializers.ChoiceField(required=True, allow_blank=False, choices=DISBURSE_TYPE)
    narration = serializers.CharField(required=False, allow_blank=True, allow_null=True)

class DisbursePayrollRecordSerializer(serializers.Serializer):
    payroll_pin = serializers.CharField(required=True, allow_null=False)
    payroll_date = serializers.DateField(required=True, allow_null=False)
    narration = serializers.CharField(required=True, allow_null=False)
    payroll_month = serializers.CharField(required=True, allow_null=False)
    payroll_year = serializers.CharField(required=True, allow_null=False)


class AcceptInstantWageSerializer(serializers.Serializer):
    INSTANT_WAGE_TYPE = (
        ("DEFAULT", "DEFAULT"),
        ("SETTINGS", "SETTINGS"),
    )
    instant_wage_type = serializers.ChoiceField(required=True, allow_blank=False, choices=INSTANT_WAGE_TYPE)
    settings = serializers.IntegerField(required=True, min_value=0)

class CompanyEmployeeListSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeList
        fields = ["employee_first_name", "employee_last_name", "employee_birth_date", "employee_start_date"]

class CompanyAnnouncementSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyAnnouncement
        fields = ["announcement_type", "announcement_title", "announcement_body", "announcement_date"]

class InstantWageWithdrawSerializer(serializers.Serializer):
    WITHDRAW_CHOICE = (
        ("WALLET", "WALLET"),
        # ("ACCOUNT", "ACCOUNT"),
    )
    user_pin = serializers.CharField(required=True, allow_null=False)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    withdraw_choice = serializers.ChoiceField(required=True, allow_blank=False, choices=WITHDRAW_CHOICE)

class TurnOnOffInstantWageForCompanyEmployeeSerializer(serializers.Serializer):
    TOGGLE_TYPE = (
        ("ON", "ON"),
        ("OFF", "OFF"),
        ("RESET", "RESET"),
        ("RESUME", "RESUME"),
    )
    toggle_type = serializers.ChoiceField(required=True, allow_blank=False, choices=TOGGLE_TYPE)

class BeneficiarySerializer(serializers.Serializer):
    account_name = serializers.CharField(required=True, allow_null=False)
    account_number = serializers.CharField(required=True, allow_null=False)
    bank_name = serializers.CharField(required=True, allow_null=False)
    bank_code = serializers.CharField(required=True, allow_null=False)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    narration = serializers.CharField(required=True, allow_null=False)

class CreateBeneficiarySerializer(serializers.Serializer):
    beneficiary_data = serializers.ListSerializer(child=BeneficiarySerializer())
    disburse_pin = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        if len(values["beneficiary_data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "beneficiary_data": "beneficiary_data cannot be empty"}
            )
        return values
    
class AllBeneficiarySerializer(serializers.ModelSerializer):
    class Meta:
        model = Beneficiary
        fields = [
            "id",
            "account_number",
            "account_name",
            "bank_code",
            "narration",
            "amount",
            "bank_name",
        ]

class OneClickHistorySerializer(serializers.ModelSerializer):

    class Meta:
        model = OneClickTransaction
        fields = ("__all__")

class CompanyPayrollSettingSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyPayrollSettings
        fields = [
            "company_name",
            "company_address",
            "company_logo",
            "company_official_email",
            "pay_schedule",
            "pay_run",
            "employee_type",
            "country",
            "state",
            "business_name_number",
            "industry",
            "contact_name",
            "contact_phone_number",
            "contact_role",
            "company_email",
            "authorize_instant_wage",
            "automate_payroll",
            "standard_tax",
            "hr_email",
            "pension",
            "employee_contribution_pension_amount",
            "employer_contribution_pension_amount",
            "payroll_notification"
        ]

class CompanyPensionSettingSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyPayrollSettings
        fields = [
            "automate_payroll",
            "pension",
            "employee_contribution_pension_amount",
            "employer_contribution_pension_amount"
        ]

class AllCompanyPayrollSettingSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyPayrollSettings
        fields = [
            "employer__email",
            "company_name",
            "company_address",
            "company_logo",
            "pay_schedule",
            "pay_run",
            "employee_type"
        ]

class PayGroupSerializer(serializers.Serializer):
    pay_group_name = serializers.CharField(required=True, allow_null=False)
    pay_group_description = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        company_uuid = self.context["company_uuid"]
        pay_group_name = values["pay_group_name"]
        pay_group  = CompanyPayGroupSettings.objects.filter(
            company__id=company_uuid, 
            pay_group_name=pay_group_name,
            is_deleted=False
            ).exists()
        if pay_group:
            raise serializers.ValidationError(
                {"message": f"{pay_group_name} already exist"}
            )
        
        return values

class MultiplePayGroupSerializer(serializers.Serializer):
    pay_group_data = serializers.ListSerializer(child=PayGroupSerializer())
    def validate(self, values):
        if len(values["pay_group_data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "pay_group_data": "pay_group_data cannot be empty"}
            )
        return values
    
class AllPayGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyPayGroupSettings
        fields = [
            "id",
            "pay_group_name",
            "pay_group_description"
        ]


class AllPayGradeSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyPayGradeSettings
        fields = [
            "id",
            "pay_group",
            "pay_grade_name",
            "amount",
            "pay_schedule"
        ]

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        if serialized_data["pay_group"]:
            serialized_data["pay_group_id"] = instance.pay_group.id
            serialized_data["pay_group_name"] = instance.pay_group.pay_group_name
        else:
            serialized_data["pay_group_id"] = None
            serialized_data["pay_group_name"] = None
        del serialized_data["pay_group"]
        return serialized_data
        
class PayGradeSerializer(serializers.Serializer):
    PAY_SCHEDULE = [
        ("WEEKLY", "WEEKLY"),
        ("BI-WEEKLY", "BI-WEEKLY"),
        ("MONTHLY", "MONTHLY"),
    ]   
    pay_grade_name = serializers.CharField(required=True, allow_null=False)
    amount = serializers.FloatField(required=True, allow_null=False)
    pay_schedule = serializers.ChoiceField(required=True, choices=PAY_SCHEDULE)

class AllOtherDependencySerializer(serializers.ModelSerializer):
    class Meta:
        model = OtherDependencySettings
        fields = ["id", "calculation_type", "amount", "custom_name", "dependency_operator", "operator_type", "is_active"]



class OtherDependencySerializer(serializers.Serializer):
    custom_name = serializers.ListField(required=True, allow_null=False)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    operator_type = serializers.ChoiceField(required=True, choices=OPERATOR_TYPE)
    calculation_type = serializers.ChoiceField(required=True, allow_blank=True, choices=DEDUCTION_CALCULATION_TYPE)
    dependency_operator = serializers.ChoiceField(required=True,  choices=DEPENDENCY_OPERATOR)
    def validate(self, values):
        if "dependency_operator" not in values:
            raise serializers.ValidationError(
                {"message": f"dependency_operator is required"}
            )
        if "custom_name" not in values:
            raise serializers.ValidationError(
                {"message": f"custom_name is required"}
            )
        if "amount" not in values:
            raise serializers.ValidationError(
                {"message": f"amount is required"}
            )
        if "operator_type" not in values:
            raise serializers.ValidationError(
                {"message": f"operator_type is required"}
            )
        if "calculation_type" not in values:
            raise serializers.ValidationError(
                {"message": f"calculation_type is required"}
            )
        if values["dependency_operator"] != "NULL":
            if values["calculation_type"] == "CUSTOM_PERCENTAGE" and values["amount"] > 100:
                raise serializers.ValidationError(
                        {"error_code": "34", "amount": "amount cannot be greater than 100"}
                    )
            if values["calculation_type"] == "CUSTOM_PERCENTAGE" and len(values["custom_name"]) < 1:
                raise serializers.ValidationError(
                    {"error_code": "34", "custom_name": "custom_name cannot be empty"}
                )
            if values["calculation_type"] == "PERCENTAGE_NET" and values["amount"] > 100:
                raise serializers.ValidationError(
                    {"error_code": "34", "amount": "amount cannot be greater than 100"}
                ) 
            if values["operator_type"] == "NULL":
                raise serializers.ValidationError(
                    {"error_code": "34", "operator_type": "operator_type cannot be NULL"}
                ) 
            if len(values["custom_name"]) >= 1:
                company_uuid = self.context.get("company_uuid")
                custom_name = values["custom_name"]
                for custom_id in custom_name:
                    component = SalaryComponentSettings.objects.filter(
                        id=custom_id,
                        company__id=company_uuid, 
                        is_deleted=False
                        ).last()
                    if not component:
                        raise serializers.ValidationError(
                            {"message": f"{custom_id} component unavailable"}
                        )
        else:
            if values["operator_type"] != "NULL":
                raise serializers.ValidationError(
                    {"error_code": "34", "operation_type": "operator_type must be NULL"}
                )
            if values["calculation_type"] == "CUSTOM_PERCENTAGE" and values["amount"] > 100:
                raise serializers.ValidationError(
                        {"error_code": "34", "amount": "amount cannot be greater than 100"}
                    )
            if values["calculation_type"] == "CUSTOM_PERCENTAGE" and len(values["custom_name"]) < 1:
                raise serializers.ValidationError(
                    {"error_code": "34", "custom_name": "custom_name cannot be empty"}
                )
            if values["calculation_type"] == "PERCENTAGE_NET" and values["amount"] > 100:
                raise serializers.ValidationError(
                    {"error_code": "34", "amount": "amount cannot be greater than 100"}
                )
            if len(values["custom_name"]) >= 1:
                company_uuid = self.context.get("company_uuid")
                custom_name = values["custom_name"]
                for custom_id in custom_name:
                    component = SalaryComponentSettings.objects.filter(
                        id=custom_id,
                        company__id=company_uuid, 
                        is_deleted=False
                        ).last()
                    if not component:
                        raise serializers.ValidationError(
                            {"message": f"{custom_id} component unavailable"}
                        )
            
        return values

class OtherDeductionSerializer(serializers.Serializer):
    deduction_name = serializers.CharField(required=True, allow_null=False)
    custom_name = serializers.ListField(required=True, allow_null=False)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    deduction_type = serializers.ChoiceField(required=True, choices=DEDUCTION_TYPE)
    calculation_type = serializers.ChoiceField(required=True, choices=DEDUCTION_CALCULATION_TYPE)
    other_dependency = serializers.ListSerializer(required=False, child=OtherDependencySerializer())
    
    def validate(self, values):
        company_uuid = self.context.get("company_uuid")
        request_user = self.context.get("request_user")
        deduction_name = values["deduction_name"]
        deduction_type = values["deduction_type"]
        calculation_type = values["calculation_type"]
        custom_name = values["custom_name"]
        amount = values["amount"]
        company_ins = Company.objects.get(id=company_uuid)
        
        this_deduction = OtherDeductionSettings.objects.filter(
            company__id=company_uuid, 
            deduction_type=deduction_type,
            deduction_name=deduction_name,
            is_deleted=False
            ).exists()
        if this_deduction:
            raise serializers.ValidationError(
                {"message": f"{deduction_name} already exist"}
            )
        
        if values["calculation_type"] == "CUSTOM_PERCENTAGE":
            if values["amount"] > 100:
                raise serializers.ValidationError(
                    {"error_code": "34", "amount": "amount cannot be greater than 100"}
                )
            if len(values["custom_name"]) < 1:
                raise serializers.ValidationError(
                    {"error_code": "34", "custom_name": "custom_name cannot be empty"}
                )
            else:
                for custom_id in custom_name:
                    component = SalaryComponentSettings.objects.filter(
                        id=custom_id,
                        company__id=company_uuid, 
                        is_deleted=False
                        ).last()
                    if not component:
                        raise serializers.ValidationError(
                            {"message": f"{custom_id} component unavailable"}
                        )
        if values["calculation_type"] == "PERCENTAGE_NET" and values["amount"] > 100:
            raise serializers.ValidationError(
                {"error_code": "34", "amount": "amount cannot be greater than 100"}
            ) 
        # if values["calculation_type"] == "GROSS_INCOME" and values["amount"] > 100:
        #     raise serializers.ValidationError(
        #         {"error_code": "34", "amount": "amount cannot be greater than 100"}
        #     ) 
        if "other_dependency" in values.keys():
            if len(values["other_dependency"]) < 1:
                raise serializers.ValidationError(
                    {"error_code": "34", "other_dependency": "other_dependency cannot be empty"}
                )
            else:
                deduction = OtherDeductionSettings.objects.filter(
                    company__id=company_uuid, 
                    deduction_type=deduction_type,
                    deduction_name=deduction_name,
                    is_deleted=False
                    ).exists()
                if deduction:
                    raise serializers.ValidationError(
                        {"message": f"{deduction_name} already exist"}
                    )
                else:
                    deduction_ins = OtherDeductionSettings.objects.create(
                        company=company_ins,
                        employer=company_ins.user,
                        deduction_name=deduction_name,
                        deduction_type=deduction_type,
                        calculation_type=calculation_type,
                        amount=amount,
                        added_by=request_user
                    )
                    deduction_ins.custom_name.add(*custom_name)
                    
                    other_dependency = values["other_dependency"]
                    for this_dependency in other_dependency:
                        custom_name = this_dependency["custom_name"]
                        amount = this_dependency["amount"]
                        calculation_type = this_dependency["calculation_type"]
                        operator_type = this_dependency["operator_type"]
                        dependency_operator = this_dependency["dependency_operator"]

                        dependency_ins = OtherDependencySettings.objects.create(
                            deduction=deduction_ins,
                            amount=amount,
                            calculation_type=calculation_type,
                            operator_type=operator_type,
                            dependency_operator=dependency_operator,
                            added_by=request_user 
                        )
                        dependency_ins.custom_name.add(*custom_name)
        else:
            if len(values["custom_name"]) < 1:
                deduction = OtherDeductionSettings.objects.filter(
                    company__id=company_uuid, 
                    deduction_type=deduction_type,
                    deduction_name=deduction_name,
                    is_deleted=False
                    ).exists()
                if deduction:
                    raise serializers.ValidationError(
                        {"message": f"{deduction_name} already exist"}
                    )
                else:
                    deduction_ins = OtherDeductionSettings.objects.create(
                        company=company_ins,
                        employer=company_ins.user,
                        deduction_name=deduction_name,
                        deduction_type=deduction_type,
                        calculation_type=calculation_type,
                        amount=amount,
                        added_by=request_user
                    )
            else:
                dependency = []
                custom_name = values["custom_name"]
                for custom_id in custom_name:
                    if not valid_uuid_check(custom_id):
                        raise serializers.ValidationError({"message":"invalid custom_name_id"})
                    component = SalaryComponentSettings.objects.filter(
                        id=custom_id,
                        company__id=company_uuid, 
                        is_deleted=False
                        ).last()
                    if component:
                        dependency.append(component)
                    else:
                        raise serializers.ValidationError(
                            {"message": f"{custom_id} component unavailable"}
                        ) 
                deduction_ins = OtherDeductionSettings.objects.create(
                    company=company_ins,
                    employer=company_ins.user,
                    deduction_name=deduction_name,
                    deduction_type=deduction_type,
                    calculation_type=calculation_type,
                    amount=amount,
                    added_by=request_user
                )
                deduction_ins.custom_name.add(*custom_name)
        return values

class AllOtherDeductionSerializer(serializers.ModelSerializer):
    other_dependency = serializers.ListSerializer(required=False, allow_null=False, child=OtherDependencySerializer())
    class Meta:
        model = OtherDeductionSettings
        fields = [
            "id",
            "deduction_name",
            'deduction_type',
            "calculation_type",
            "amount",
            "custom_name",
            "is_active",
            "other_dependency"
        ]
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )

        custom_name = []
        for comp in serialized_data["custom_name"]:
            custom_name.append(comp)

        serialized_data["custom_name"] = custom_name

        dependency_data = []
        all_dependency = OtherDependencySettings.objects.filter(deduction=instance, is_deleted=False)
        for dependency in all_dependency:
            custom_name = []
            all_dep = dependency.custom_name.all()
            for comp in all_dep:
                custom_name.append(comp.id)
            dependency_data.append(
                {
                    "id": dependency.id,
                    "calculation_type": dependency.calculation_type,
                    "amount": dependency.amount,
                    "custom_name": custom_name,
                    "dependency_operator": dependency.dependency_operator,
                    "operator_type": dependency.operator_type,
                    "is_active": dependency.is_active
                }
            )
        serialized_data["other_dependency"] = dependency_data
        return serialized_data
    
    def validate(self, values):
        company_uuid = self.context.get("company_uuid")
        deduction_ins = self.context.get("deduction_ins")
        request_user = self.context.get("request_user")
        if "deduction_name" and "deduction_type" in values.keys():
            deduction_name=values["deduction_name"]
            deduction_type=deduction_ins.deduction_type
            get_deduction = OtherDeductionSettings.objects.filter(company__id=company_uuid, 
                                                                  deduction_type=deduction_type, 
                                                                  deduction_name=deduction_name, 
                                                                  is_deleted=False).last()
            if get_deduction:
                raise serializers.ValidationError(
                    {"error_code": "34", "deduction": f"{deduction_name} already exist"}
                )
        if "other_dependency" not in values.keys():
            if "calculation_type" in values.keys():
                if values["calculation_type"] == "CUSTOM_PERCENTAGE":
                    if "amount" in values.keys():
                        if values["amount"] > 100:
                            raise serializers.ValidationError(
                                {"error_code": "34", "amount": "amount cannot be greater than 100"}
                            )
                    else:
                        raise serializers.ValidationError(
                                {"error_code": "34", "amount": "amount is required"}
                            )
                    if "custom_name" in values.keys():
                        if len(values["custom_name"]) < 1:
                            raise serializers.ValidationError(
                                {"error_code": "34", "custom_name": "custom_name cannot be empty"}
                            )
                    else:
                        raise serializers.ValidationError(
                                {"error_code": "34", "custom_name": "custom_name is required"}
                            )
                if values["calculation_type"] == "PERCENTAGE_NET":
                    if "custom_name" in values.keys():
                        if len(values["custom_name"]) >= 1:
                            raise serializers.ValidationError(
                                {"error_code": "34", "custom_name": "custom_name must be empty"}
                            )
                    if "amount" in values.keys():
                        if values["amount"] > 100:
                            raise serializers.ValidationError(
                                {"error_code": "34", "amount": "amount cannot be greater than 100"}
                            )
                    else:
                        raise serializers.ValidationError(
                                {"error_code": "34", "amount": "amount is required"}
                            )
                # if values["calculation_type"] == "GROSS_INCOME":
                #     if "custom_name" in values.keys():
                #         if len(values["custom_name"]) >= 1:
                #             raise serializers.ValidationError(
                #                 {"error_code": "34", "custom_name": "custom_name must be empty"}
                #             )
                        
                #     if "amount" in values.keys():
                #         if values["amount"] > 100:
                #             raise serializers.ValidationError(
                #                 {"error_code": "34", "amount": "amount cannot be greater than 100"}
                #             )
                #     else:
                #         raise serializers.ValidationError(
                #                 {"error_code": "34", "amount": "amount is required"}
                #             )
                        
        else:            
            if "calculation_type" in values.keys():
                if values["calculation_type"] == "CUSTOM_PERCENTAGE":
                    if "amount" in values.keys():
                        if values["amount"] > 100:
                            raise serializers.ValidationError(
                                {"error_code": "34", "amount": "amount cannot be greater than 100"}
                            )
                    else:
                        raise serializers.ValidationError(
                                {"error_code": "34", "amount": "amount is required"}
                            )
                    if "custom_name" in values.keys():
                        if len(values["custom_name"]) < 1:
                            raise serializers.ValidationError(
                                {"error_code": "34", "custom_name": "custom_name cannot be empty"}
                            )
                    else:
                        raise serializers.ValidationError(
                                {"error_code": "34", "custom_name": "custom_name is required"}
                            )
                if values["calculation_type"] == "PERCENTAGE_NET":
                    if "custom_name" in values.keys():
                        if len(values["custom_name"]) >= 1:
                            raise serializers.ValidationError(
                                {"error_code": "34", "custom_name": "custom_name must be empty"}
                            )
                    if "amount" in values.keys():
                        if values["amount"] > 100:
                            raise serializers.ValidationError(
                                {"error_code": "34", "amount": "amount cannot be greater than 100"}
                            )
                    else:
                        raise serializers.ValidationError(
                                {"error_code": "34", "amount": "amount is required"}
                            )
                # if values["calculation_type"] == "GROSS_INCOME":
                #     if "custom_name" in values.keys():
                #         if len(values["custom_name"]) >= 1:
                #             raise serializers.ValidationError(
                #                 {"error_code": "34", "custom_name": "custom_name must be empty"}
                #             )
                        
                #     if "amount" in values.keys():
                #         if values["amount"] > 100:
                #             raise serializers.ValidationError(
                #                 {"error_code": "34", "amount": "amount cannot be greater than 100"}
                #             )
                #     else:
                #         raise serializers.ValidationError(
                #                 {"error_code": "34", "amount": "amount is required"}
                #             )
    
            if len(values["other_dependency"]) < 1:
                raise serializers.ValidationError(
                    {"error_code": "34", "other_dependency": "other_dependency cannot be empty"}
                )
            
            other_dependency = values["other_dependency"]
                
            all_dependency = OtherDependencySettings.objects.filter(deduction=deduction_ins)
            for dependency in all_dependency:
                dependency.is_deleted = True
                dependency.is_active = False
                dependency.save()

            
            for this_dependency in other_dependency:
                custom_name = this_dependency["custom_name"]
                amount = this_dependency["amount"]
                calculation_type = this_dependency["calculation_type"]
                operator_type = this_dependency["operator_type"]
                dependency_operator = this_dependency["dependency_operator"]
                
                dependency_ins = OtherDependencySettings.objects.create(
                    deduction=deduction_ins,
                    amount=amount,
                    calculation_type=calculation_type,
                    operator_type=operator_type,
                    dependency_operator=dependency_operator
                )
                dependency_ins.custom_name.add(*custom_name)

        return values

class AllDepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyDepartmentSettings
        fields = [
            "id",
            "department_name",
            "department_head_name"
        ]

class DepartmentSerializer(serializers.Serializer):
    department_name = serializers.CharField(required=True, allow_null=False)
    department_head_name = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    def validate(self, values):
        company_uuid = self.context["company_uuid"]
        department_name = values["department_name"]
        department  = CompanyDepartmentSettings.objects.filter(
            company__id=company_uuid, 
            department_name=department_name,
            is_deleted=False
            ).exists()
        if department:
            raise serializers.ValidationError(
                {"message": f"{department_name} already exist"}
            )
        return values
        

class MultipleDepartmentSerializer(serializers.Serializer):
    department_data = serializers.ListSerializer(child=DepartmentSerializer())
    def validate(self, values):
        if len(values["department_data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "department_data": "department_data cannot be empty"}
            )
        return values

class AllSalaryComponentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalaryComponentSettings
        fields = [
            "id",
            "salary_name",
            "calculation_type",
            "amount",
            "is_active",
        ]

class SalaryComponentSerializer(serializers.Serializer):
    salary_name = serializers.CharField(required=True, allow_null=False)
    calculation_type = serializers.ChoiceField(required=True, choices=ALL_SALARY_COMPONENT_CALCULATION_TYPE)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    is_active = serializers.BooleanField(required=True, allow_null=False)

    def validate(self, values):
        if values["calculation_type"] == "PERCENTAGE_NET" and values["amount"] > 100:
            raise serializers.ValidationError(
                {"error_code": "34", "amount": "percentage cannot be greater than 100"}
            )

        return values
    
class AllCustomComponentSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomComponentSettings
        fields = [
            "id",
            "custom_component_name",
            "calculation_type",
            "amount",
            "custom_name",
            "is_active",
            "frequency",
            "part_of_employee_structure", 
            "calculate_pay_schedule_basis",
            "show_on_payslip"
        ]
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        custom_name = []
        for comp in serialized_data["custom_name"]:
            comp_name = SalaryComponentSettings.objects.filter(id=comp).first()
            custom_name.append(comp_name.salary_name)

        serialized_data["custom_name"] = custom_name

        return serialized_data
        

class CustomComponentSerializer(serializers.Serializer):
    custom_component_name = serializers.CharField(required=True, allow_null=False)
    calculation_type = serializers.ChoiceField(required=True, choices=SALARY_COMPONENT_CALCULATION_TYPE)
    frequency = serializers.ChoiceField(required=True, choices=COMPONENT_FREQUENCY)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    custom_name = serializers.ListField(required=True, allow_null=False)
    is_active = serializers.BooleanField(required=True, allow_null=False)
    part_of_employee_structure = serializers.BooleanField(required=True, allow_null=False)
    calculate_pay_schedule_basis = serializers.BooleanField(required=True, allow_null=False)
    show_on_payslip = serializers.BooleanField(required=True, allow_null=False)

    def validate(self, values):
        if values["calculation_type"] == "PERCENTAGE_NET" and values["amount"] > 100:
            raise serializers.ValidationError(
                {"error_code": "34", "amount": "percentage cannot be greater than 100"}
            )
        if values["calculation_type"] == "CUSTOM_PERCENTAGE" and values["amount"] > 100:
            raise serializers.ValidationError(
                {"error_code": "34", "amount": "percentage cannot be greater than 100"}
            )
        return values
    
class AllBenefitComponentSerializer(serializers.ModelSerializer):
    class Meta:
        model = BenefitComponentSettings
        fields = [
            "id",
            "benefit_name",
            "calculation_type",
            "frequency",
            "amount",
            "custom_name",
            "is_active",
        ]
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        custom_name = []
        for comp in serialized_data["custom_name"]:
            comp_name = SalaryComponentSettings.objects.filter(id=comp).first()
            custom_name.append(comp_name.salary_name)

        serialized_data["custom_name"] = serialized_data["custom_name"]
        serialized_data["custom_data"] = custom_name

        return serialized_data
        

class BenefitComponentSerializer(serializers.Serializer):
    benefit_name = serializers.CharField(required=True, allow_null=False)
    calculation_type = serializers.ChoiceField(required=True, choices=SALARY_COMPONENT_CALCULATION_TYPE)
    frequency = serializers.ChoiceField(required=True, choices=COMPONENT_FREQUENCY)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    custom_name = serializers.ListField(required=True, allow_null=False)
    is_active = serializers.BooleanField(required=True, allow_null=False)

    def validate(self, values):
        if values["calculation_type"] == "PERCENTAGE_NET" and values["amount"] > 100:
            raise serializers.ValidationError(
                {"error_code": "34", "amount": "percentage cannot be greater than 100"}
            )
        if values["calculation_type"] == "CUSTOM_PERCENTAGE" and values["amount"] > 100:
            raise serializers.ValidationError(
                {"error_code": "34", "amount": "percentage cannot be greater than 100"}
            )
        return values
    
class EmployeeOnboardingSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeOnboardingForm
        fields = [
            "id",
            "employee_first_name",
            "employee_last_name",
            "employee_phone_number",
            "employee_alternate_email",
            "employee_birth_date",
            "employee_gender",
            "employee_staff_id",
            "employee_staff_id_reporting_line",
            "employee_state",
            "employee_city",
            "employee_town",
            "employee_bus_stop",
            "employee_street_name",
            "employee_house_no",
            "employee_postal_code",
            "employee_first_next_of_kin_firstname",
            "employee_first_next_of_kin_lastname",
            "employee_first_next_of_kin_phone_number",
            "employee_first_next_of_kin_relationship",
            "employee_first_next_of_kin_address",
            "employee_second_next_of_kin_firstname",
            "employee_second_next_of_kin_lastname",
            "employee_second_next_of_kin_phone_number",
            "employee_second_next_of_kin_relationship",
            "employee_second_next_of_kin_address"
        ]
    def validate(self, values):
        if "employee_first_name" in values:
            values["employee_first_name"] = values.get("employee_first_name").title()
        if "employee_last_name" in values:
            values["employee_last_name"] = values.get("employee_last_name").title()
        if "employee_first_next_of_kin_firstname" in values:
            values["employee_first_next_of_kin_firstname"] = values.get("employee_first_next_of_kin_firstname").title()
        if "employee_first_next_of_kin_lastname" in values:
            values["employee_first_next_of_kin_lastname"] = values.get("employee_first_next_of_kin_lastname").title()
        if "employee_second_next_of_kin_firstname" in values:
            values["employee_second_next_of_kin_firstname"] = values.get("employee_second_next_of_kin_firstname").title()
        if "employee_second_next_of_kin_lastname" in values:
            values["employee_second_next_of_kin_lastname"] = values.get("employee_second_next_of_kin_lastname").title()
        if "employee_phone_number" in values:
            if len(values["employee_phone_number"]) != 11:
                raise serializers.ValidationError({
                    "error": "24", "message" : "employee_phone_number must at 11 digits"
                })
            else:
                values["employee_phone_number"] = User.format_number_from_back_add_234(values["employee_phone_number"])
        if "employee_first_next_of_kin_phone_number" in values:
            if len(values["employee_first_next_of_kin_phone_number"]) != 11:
                raise serializers.ValidationError({
                    "error": "24", "message" : "employee_first_next_of_kin_phone_number must at 11 digits"
                })
            else:
                values["employee_first_next_of_kin_phone_number"] = User.format_number_from_back_add_234(values["employee_first_next_of_kin_phone_number"])
        if "employee_second_next_of_kin_phone_number" in values:
            if len(values["employee_second_next_of_kin_phone_number"]) != 11:
                raise serializers.ValidationError({
                    "error": "24", "message" : "employee_second_next_of_kin_phone_number must at 11 digits"
                })
            else:
                values["employee_second_next_of_kin_phone_number"] = User.format_number_from_back_add_234(values["employee_second_next_of_kin_phone_number"])
        
        return values
    
class EmployeeFamilyContactSerializer(serializers.ModelSerializer):
    employee = CompanyMembersSerializer(read_only=True)

    class Meta:
        model = CompanyEmployeeList
        fields = (
            "id",
            "employer",
            "employee",
            "company",
            "employee_first_next_of_kin_firstname",
            "employee_first_next_of_kin_lastname",
            "employee_first_next_of_kin_phone_number",
            "employee_first_next_of_kin_relationship",
            "employee_first_next_of_kin_address",
            "employee_second_next_of_kin_firstname",
            "employee_second_next_of_kin_lastname",
            "employee_second_next_of_kin_phone_number",
            "employee_second_next_of_kin_relationship",
            "employee_second_next_of_kin_address",
        )
    def validate(self, values):
        if "employee_first_next_of_kin_firstname" in values:
            values["employee_first_next_of_kin_firstname"] = values.get("employee_first_next_of_kin_firstname").title()
        if "employee_first_next_of_kin_lastname" in values:
            values["employee_first_next_of_kin_lastname"] = values.get("employee_first_next_of_kin_lastname").title()
        if "employee_second_next_of_kin_firstname" in values:
            values["employee_second_next_of_kin_firstname"] = values.get("employee_second_next_of_kin_firstname").title()
        if "employee_second_next_of_kin_lastname" in values:
            values["employee_second_next_of_kin_lastname"] = values.get("employee_second_next_of_kin_lastname").title()
        if "employee_first_next_of_kin_phone_number" in values:
            if len(values["employee_first_next_of_kin_phone_number"]) != 11:
                raise serializers.ValidationError({
                    "error": "24", "message" : "employee_first_next_of_kin_phone_number must at 11 digits"
                })
            else:
                values["employee_first_next_of_kin_phone_number"] = User.format_number_from_back_add_234(values["employee_first_next_of_kin_phone_number"])
        if "employee_second_next_of_kin_phone_number" in values:
            if len(values["employee_second_next_of_kin_phone_number"]) != 11:
                raise serializers.ValidationError({
                    "error": "24", "message" : "employee_second_next_of_kin_phone_number must at 11 digits"
                })
            else:
                values["employee_second_next_of_kin_phone_number"] = User.format_number_from_back_add_234(values["employee_second_next_of_kin_phone_number"])
        
        return values

class EditEmployeeFamilyContactSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeList
        fields = (
            "employee_first_next_of_kin_firstname",
            "employee_first_next_of_kin_lastname",
            "employee_first_next_of_kin_phone_number",
            "employee_first_next_of_kin_relationship",
            "employee_first_next_of_kin_address",
            "employee_second_next_of_kin_firstname",
            "employee_second_next_of_kin_lastname",
            "employee_second_next_of_kin_phone_number",
            "employee_second_next_of_kin_relationship",
            "employee_second_next_of_kin_address",
        )

class EmployeeAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeAccountDetails
        fields = (
            "id",
            "bank_name",
            "account_name",
            "account_number",
            "bvn_number",
            "bank_code",
            "swift_code",
        )

class EditEmployeeAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeAccountDetails
        fields = (
            "bank_name",
            "account_name",
            "account_number",
            "bvn_number",
            "bank_code",
            "swift_code",
        )

class CreateEmployeeAccountSerializer(serializers.Serializer):
    bank_name = serializers.CharField(required=True, allow_null=True)
    account_name = serializers.CharField(required=True, allow_null=True)
    account_number = serializers.CharField(required=True, allow_null=True)
    bvn_number = serializers.CharField(required=True, allow_null=True)
    bank_code = serializers.CharField(required=True, allow_null=True)
    swift_code = serializers.CharField(required=True, allow_null=True)

class AddPayrollSettingsLogoSerializer(serializers.Serializer):
    company_logo = serializers.FileField(required=True, allow_null=True)

class AddEmployeeProfilePictureSerializer(serializers.Serializer):
    profile_picture = serializers.FileField(required=True, allow_null=True)

class EmployeeEducationSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeEducationDetails
        exclude = [
            "is_deleted",
            "created_at",
            "updated_at"
        ]

class EmployeeExperienceSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeExperienceDetails
        exclude = [
            "is_deleted",
            "created_at",
            "updated_at"
        ]

class EmployeeCertificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeCertifications
        exclude = [
            "is_deleted",
            "created_at",
            "updated_at"
        ]

class EmployeeDepartmentSerializer(serializers.Serializer):
    employee_id = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        company_uuid = self.context["company_uuid"]
        department_id= self.context["department_id"]
        employee_id= values["employee_id"]
        try:
            employee_ins = CompanyEmployeeList.objects.filter(id=employee_id, company__id=company_uuid, is_deleted=False).last()
        except ValueError:
            raise serializers.ValidationError(
                {"message": f"{employee_id} invalid format"}
            )

        if not employee_ins:
            raise serializers.ValidationError(
                {"message": f"{employee_id} does not exist"}
            )
        else:
            department_ins = CompanyDepartmentSettings.objects.filter(
                id = department_id,
                company__id=company_uuid, 
                is_deleted=False
                ).last()
            if not department_ins:
                raise serializers.ValidationError(
                    {"message": f"{department_id} does not exist"}
                )
        return values

class MultipleEmployeeDepartmentSerializer(serializers.Serializer):
    employee_department_data = serializers.ListSerializer(child=EmployeeDepartmentSerializer())
    def validate(self, values):
        if len(values["employee_department_data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employee_department_data": "employee_department_data cannot be empty"}
            )
        return values
    
class EmployeeRoleSerializer(serializers.Serializer):
    employee_id = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        company_uuid = self.context["company_uuid"]
        role_id = self.context["role_id"]
        employee_id = values["employee_id"]
        try:
            employee_ins = CompanyEmployeeList.objects.filter(id=employee_id, company__id=company_uuid, is_deleted=False).last()
        except ValueError:
            raise serializers.ValidationError(
                {"message": f"{employee_id} invalid format"}
            )

        if not employee_ins:
            raise serializers.ValidationError(
                {"message": f"{employee_id} does not exist"}
            )
        else:
            role_ins = ManagePermissionsRole.objects.filter(
                id = role_id,
                company__id=company_uuid, 
                is_deleted=False
                ).last()
            if not role_ins:
                raise serializers.ValidationError(
                    {"message": f"{role_id} does not exist"}
                )
        return values
    
class MultipleEmployeeRoleSerializer(serializers.Serializer):
    employee_role_data = serializers.ListSerializer(child=EmployeeRoleSerializer())
    def validate(self, values):
        if len(values["employee_role_data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employee_role_data": "employee_role_data cannot be empty"}
            )
        return values
    
class EmployeeEmailSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, allow_null=False)

    def validate(self, values):
        email = values["email"]
        values["email"] = email.lower()
        return values
    
class CreateEmployeeInviteSerializer(serializers.Serializer):
    all_employees = serializers.ListSerializer(child=EmployeeEmailSerializer())
    def validate(self, values):
        if len(values["all_employees"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "all_employees": "all_employees cannot be empty"}
            )
        return values
    
class EmployeeApprovalInfoSerializer(serializers.Serializer):
    APPROVAL_STATUS = (
        ("ACCEPT", "ACCEPT"),
        ("REJECT", "REJECT"),
        ("DELETE", "DELETE"),
    )
    approval_status = serializers.ChoiceField(required=False, choices=APPROVAL_STATUS)
    
class TaxBandSerializer(serializers.Serializer):
    start_band = serializers.FloatField(min_value=1, required=True, allow_null=False)
    end_band = serializers.FloatField(min_value=1, required=True, allow_null=False)
    tax_rate = serializers.FloatField(min_value=1, required=True, allow_null=False)
    deduction_amount = serializers.FloatField(min_value=1, required=True, allow_null=False)

    def validate(self, attrs):
        start_band = attrs.get("start_band")
        end_band = attrs.get("end_band")
        tax_rate = attrs.get("tax_rate")
        deduction_amount = attrs.get("deduction_amount")
        company_uuid = self.context.get("company_uuid")
        request_user = self.context.get("request_user")
        if start_band > end_band:
            raise serializers.ValidationError(
                {"error_code": "34", "start_band": "start_band cannot be greater than end_band"}
            )
        if tax_rate > 100:
            raise serializers.ValidationError(
                {"error_code": "34", "tax_rate": "tax_rate cannot be greater than 100"}
            )
        get_tax_band = CompanyTaxBand.objects.filter(company__id=company_uuid, start_band=start_band, end_band=end_band).last()
        if get_tax_band:
            raise serializers.ValidationError(
                {"error_code": "34", "tax_band": "tax_band already exist"}
            )
        company_ins = Company.objects.filter(id=company_uuid).last()
        CompanyTaxBand.objects.create(
            company=company_ins,
            start_band=start_band,
            end_band=end_band,
            tax_rate=tax_rate,
            deduction_amount=deduction_amount,
            added_by=request_user
        )
        return attrs
    
class EditTaxBandSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyTaxBand
        fields = ["start_band", "end_band", "tax_rate", "deduction_amount"]

    def validate(self, attrs):

        company_uuid = self.context.get("company_uuid")
        request_user = self.context.get("request_user")
        tax_band_id = self.context.get("tax_band_id")
        if "start_band" in  attrs and "end_band" in attrs:
            start_band = attrs.get("start_band")
            end_band = attrs.get("end_band")
            if start_band <=0 or end_band <=0:
                raise serializers.ValidationError(
                    {"error_code": "34", "message": "start_band and end_band must be greater than 0"}
                )
            if attrs.get("start_band") > attrs.get("end_band"):
                raise serializers.ValidationError(
                    {"error_code": "34", "start_band": "start_band cannot be greater than end_band"}
                )
            get_tax_band = CompanyTaxBand.objects.filter(company__id=company_uuid, start_band=start_band,end_band=end_band).last()
            if get_tax_band:
                raise serializers.ValidationError(
                    {"error_code": "34", "tax_band": "tax_band already exist"}
                )
        else:
            get_tax_ins = CompanyTaxBand.objects.filter(company__id=company_uuid, id=tax_band_id).first()
            if "start_band" in attrs:
                if attrs.get("start_band") > get_tax_ins.end_band:
                    raise serializers.ValidationError(
                        {"error_code": "34", "start_band": "start_band cannot be greater than end_band"}
                    )
                if attrs.get("start_band") <= 0:
                    raise serializers.ValidationError(
                        {"error_code": "34", "start_band": "start_band must be greater than 0"}
                    )
            if "end_band" in attrs:
                if attrs.get("end_band") > get_tax_ins.start_band:
                    raise serializers.ValidationError(
                        {"error_code": "34", "start_band": "start_band cannot be greater than end_band"}
                    )
                if attrs.get("end_band") <= 0:
                    raise serializers.ValidationError(
                        {"error_code": "34", "end_band": "end_band must be greater than 0"}
                    )
            if "tax_rate" in attrs:
                if attrs.get("tax_rate") > 100:
                    raise serializers.ValidationError(
                        {"error_code": "34", "tax_rate": "tax_rate cannot be greater than 100"}
                    )
        
        return attrs

class ApplyDeductionBonusSerializer(serializers.Serializer):
    APPLY_TO = (
        ("ONE", "ONE"),
        ("ALL", "ALL"),
    )
    APPLY_TYPE = (
        ("BONUS", "BONUS"),
        ("DEDUCTION", "DEDUCTION"),
        ("HMO", "HMO"),
        ("LIFE_INSURANCE", "LIFE_INSURANCE"),
        ("PENSION", "PENSION"),
    )
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)
    employee_id = serializers.CharField(required=False)
    apply_to = serializers.ChoiceField(required=True, choices=APPLY_TO)
    apply_type = serializers.ChoiceField(required=True, choices=APPLY_TYPE)
    def validate(self, attrs):
        company_uuid = self.context.get("company_uuid")
        apply_to = attrs.get("apply_to")
        apply_type = attrs.get("apply_type")
        amount = attrs.get("amount")
        if apply_to == "ONE":
            if not attrs.get("employee_id"):
                raise serializers.ValidationError(
                    {"error_code": "34", "message": "employee_id is required"}
                )
            else:
                employee_id = attrs.get("employee_id")
                get_employee = CompanyEmployeeList.objects.filter(company__id=company_uuid, id=employee_id, is_deleted=False).first()
                if get_employee:
                    net_amount = get_employee.employee_net_amount
                    life_insurance = get_employee.employee_life_insurance_amount
                    hmo_amount = get_employee.employee_hmo_amount
                    pension_amount = get_employee.employee_pension_amount
                    if apply_type == "BONUS":
                        get_employee.employee_other_amount = amount
                        get_employee.employee_payable_amount = round_amount((net_amount + amount) - get_employee.employee_other_deductions - life_insurance - hmo_amount - pension_amount)
                        get_employee.save()
                    elif apply_type == "DEDUCTION":
                        get_employee.employee_other_deductions = amount
                        get_employee.employee_payable_amount = round_amount((net_amount - amount) - get_employee.employee_other_deductions)
                        get_employee.employee_payable_amount = round_amount((net_amount + get_employee.employee_other_amount) - amount  - life_insurance - hmo_amount - pension_amount)
                        if get_employee.employee_payable_amount < 0:
                            raise serializers.ValidationError(
                                {"error_code": "34", "message": f"{get_employee.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
                            )
                        else:
                            get_employee.save()
                    elif apply_type == "HMO":
                        get_employee.employee_hmo_amount = amount
                        get_employee.employee_payable_amount = round_amount((net_amount + get_employee.employee_other_amount) - amount - life_insurance - pension_amount - get_employee.employee_other_deductions)
                        if get_employee.employee_payable_amount < 0:
                            raise serializers.ValidationError(
                                {"error_code": "34", "message": f"{get_employee.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
                            )
                        else:
                            get_employee.save()
                    elif apply_type == "LIFE_INSURANCE":
                        get_employee.employee_life_insurance_amount = amount
                        get_employee.employee_payable_amount = round_amount((net_amount + get_employee.employee_other_amount) - amount - pension_amount - hmo_amount - get_employee.employee_other_deductions)
                        if get_employee.employee_payable_amount < 0:
                            raise serializers.ValidationError(
                                {"error_code": "34", "message": f"{get_employee.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
                            )
                        else:
                            get_employee.save()
                    elif apply_type == "PENSION":
                        get_employee.employee_pension_amount = amount
                        get_employee.employee_payable_amount = round_amount((net_amount + get_employee.employee_other_amount) - amount - life_insurance - hmo_amount - get_employee.employee_other_deductions)
                        if get_employee.employee_payable_amount < 0:
                            raise serializers.ValidationError(
                                {"error_code": "34", "message": f"{get_employee.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
                            )
                        else:
                            get_employee.save()
                    else:
                        raise serializers.ValidationError(
                            {"error_code": "34", "message": "invalid apply_type choice"}
                        )
                else:
                    raise serializers.ValidationError(
                        {"error_code": "34", "message": "employee does not exist"}
                    )
        elif apply_to == "ALL":
            get_employees = CompanyEmployeeList.objects.filter(company__id=company_uuid, is_deleted=False)
            if get_employees:
                if apply_type == "BONUS":
                    for employee in get_employees:
                        net_amount = employee.employee_net_amount
                        life_insurance = employee.employee_life_insurance_amount
                        hmo_amount = employee.employee_hmo_amount
                        pension_amount = employee.employee_pension_amount
                        employee.employee_other_amount = amount
                        employee.employee_payable_amount = round_amount((net_amount + amount) - employee.employee_other_deductions - life_insurance - hmo_amount - pension_amount)
                        employee.save()
                elif apply_type == "DEDUCTION":
                    for employee in get_employees:
                        net_amount = employee.employee_net_amount
                        life_insurance = employee.employee_life_insurance_amount
                        hmo_amount = employee.employee_hmo_amount
                        pension_amount = employee.employee_pension_amount
                        employee.employee_other_deductions = amount
                        employee.employee_payable_amount = round_amount((net_amount - amount) - employee.employee_other_deductions)
                        employee.employee_payable_amount = round_amount((net_amount +  employee.employee_other_amount) - amount - life_insurance - hmo_amount - pension_amount)
                        if employee.employee_payable_amount < 0:
                            raise serializers.ValidationError(
                                {"error_code": "34", "message": f"{employee.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
                            )
                        else:
                            pass
                    for employee in get_employees:
                        net_amount = employee.employee_net_amount
                        life_insurance = employee.employee_life_insurance_amount
                        hmo_amount = employee.employee_hmo_amount
                        pension_amount = employee.employee_pension_amount
                        employee.employee_other_deductions = amount
                        employee.employee_payable_amount = round_amount((net_amount - amount) - employee.employee_other_deductions)
                        employee.employee_payable_amount = round_amount((net_amount +  employee.employee_other_amount) - amount - life_insurance - hmo_amount - pension_amount)
                        employee.save()
                elif apply_type == "HMO":
                    for employee in get_employees:
                        net_amount = employee.employee_net_amount
                        life_insurance = employee.employee_life_insurance_amount
                        hmo_amount = employee.employee_hmo_amount
                        pension_amount = employee.employee_pension_amount
                        employee.employee_hmo_amount = amount
                        employee.employee_payable_amount = round_amount((net_amount + employee.employee_other_amount) - amount - life_insurance - pension_amount - employee.employee_other_deductions)
                        if employee.employee_payable_amount < 0:
                            raise serializers.ValidationError(
                                {"error_code": "34", "message": f"{employee.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
                            )
                        else:
                            pass
                    for employee in get_employees:
                        net_amount = employee.employee_net_amount
                        life_insurance = employee.employee_life_insurance_amount
                        hmo_amount = employee.employee_hmo_amount
                        pension_amount = employee.employee_pension_amount
                        employee.employee_hmo_amount = amount
                        employee.employee_payable_amount = round_amount((net_amount + employee.employee_other_amount) - amount - life_insurance - pension_amount - employee.employee_other_deductions)
                        employee.save()
                elif apply_type == "LIFE_INSURANCE":
                    for employee in get_employees:
                        net_amount = employee.employee_net_amount
                        life_insurance = employee.employee_life_insurance_amount
                        hmo_amount = employee.employee_hmo_amount
                        pension_amount = employee.employee_pension_amount
                        employee.employee_life_insurance_amount = amount
                        employee.employee_payable_amount = round_amount((net_amount + employee.employee_other_amount) - amount - pension_amount - hmo_amount - employee.employee_other_deductions)
                        if employee.employee_payable_amount < 0:
                            raise serializers.ValidationError(
                                {"error_code": "34", "message": f"{employee.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
                            )
                        else:
                            pass
                    for employee in get_employees:
                        net_amount = employee.employee_net_amount
                        life_insurance = employee.employee_life_insurance_amount
                        hmo_amount = employee.employee_hmo_amount
                        pension_amount = employee.employee_pension_amount
                        employee.employee_life_insurance_amount = amount
                        employee.employee_payable_amount = round_amount((net_amount + employee.employee_other_amount) - amount - pension_amount - hmo_amount - employee.employee_other_deductions)
                        employee.save()
                elif apply_type == "PENSION":
                    for employee in get_employees:
                        net_amount = employee.employee_net_amount
                        life_insurance = employee.employee_life_insurance_amount
                        hmo_amount = employee.employee_hmo_amount
                        pension_amount = employee.employee_pension_amount
                        employee.employee_pension_amount = amount
                        employee.employee_payable_amount = round_amount((net_amount + employee.employee_other_amount) - amount - life_insurance - hmo_amount - employee.employee_other_deductions)
                        if employee.employee_payable_amount < 0:
                            raise serializers.ValidationError(
                                {"error_code": "34", "message": f"{employee.employee_email} payable_amount cannot be less than 0. please check deduction and try again"}
                            )
                        else:
                            pass
                    for employee in get_employees:
                        net_amount = employee.employee_net_amount
                        life_insurance = employee.employee_life_insurance_amount
                        hmo_amount = employee.employee_hmo_amount
                        pension_amount = employee.employee_pension_amount
                        employee.employee_pension_amount = amount
                        employee.employee_payable_amount = round_amount((net_amount + employee.employee_other_amount) - amount - life_insurance - hmo_amount - employee.employee_other_deductions)
                        employee.save()
                else:
                    pass
            else:
                raise serializers.ValidationError(
                        {"error_code": "34", "message": "no employee to apply changes to"}
                )
        else:
            raise serializers.ValidationError(
                {"error_code": "34", "message": "invalid apply_to choice"}
           )    
        
        return attrs      

class SearchEmployeeSerializer(serializers.ModelSerializer):
    employee = CompanyMembersSerializer(read_only=True)
    class Meta:
        model = CompanyEmployeeList
        fields = (
            "id",
            "employer",
            "employee",
            "company",
            "employee_gross_amount",
            "employee_net_amount",
            "employee_salary_grade",
            "employee_pension_amount",
            "employee_tax_amount",
            "employee_other_amount",
            "employee_other_deductions",
            "employee_payable_amount",
            "employee_hmo_amount",
            "employee_bank_name",
            "employee_account_number",
            "employee_gender",
            "employee_first_name",
            "employee_last_name",
            "employee_phone_number",
            "can_disburse",
            "can_approve",
            "can_add_member",
            "can_edit_member",
            "can_delete_member",
            "can_delete_payroll",
            "can_run_payroll",
            "employee_email",
            "employee_status",
        )

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["email"] = instance.employee_email
        serialized_data["status"] = instance.employee_status

        return serialized_data
    
class SearchPayrollApprovalSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = (
            "id",
            "payroll_admin",
            "phone_number",
            "gross_amount",
            "net_amount",
            "first_name",
            "last_name",
            "email",
            "bulk_id",
            "salary_grade",
            "pension_amount",
            "tax_amount",
            "other_amount",
            "other_deductions",
            "payable_amount",
            "hmo_amount",
            "status",
        )

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["is_active"] = instance.payroll_user.is_active
        serialized_data["phone_no"] = instance.phone_number
        serialized_data["company_name"] = instance.company.company_name
        if instance.status == "APPROVAL":
            serialized_data["status"] = "PENDING APPROVAL"
        elif instance.status == "DISBURSE":
            serialized_data["status"] = "PENDING DISBURSE"
        elif instance.status == "DISBURSED":
            serialized_data["status"] = "PAYMENT SUCCESSFUL"
        else:
            serialized_data["status"] = "OMITTED"

        return serialized_data
    
    
class SearchPayrollApprovalSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = (
            "id",
            "payroll_admin",
            "phone_number",
            "gross_amount",
            "net_amount",
            "first_name",
            "last_name",
            "email",
            "bulk_id",
            "salary_grade",
            "pension_amount",
            "tax_amount",
            "other_amount",
            "other_deductions",
            "payable_amount",
            "hmo_amount",
            "status",
        )

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["is_active"] = instance.payroll_user.is_active
        serialized_data["phone_no"] = instance.phone_number
        serialized_data["company_name"] = instance.company.company_name
        if instance.status == "APPROVAL":
            serialized_data["status"] = "PENDING APPROVAL"
        elif instance.status == "DISBURSE":
            serialized_data["status"] = "PENDING DISBURSE"
        elif instance.status == "DISBURSED":
            serialized_data["status"] = "PAYMENT SUCCESSFUL"
        else:
            serialized_data["status"] = "OMITTED"

        return serialized_data
    
    
class SearchPayrollDisburseSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollTable
        fields = (
            "id",
            "payroll_admin",
            "phone_number",
            "gross_amount",
            "net_amount",
            "first_name",
            "last_name",
            "email",
            "bulk_id",
            "salary_grade",
            "pension_amount",
            "tax_amount",
            "other_amount",
            "other_deductions",
            "payable_amount",
            "hmo_amount",
            "status",
        )

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["is_active"] = instance.payroll_user.is_active
        serialized_data["phone_no"] = instance.phone_number
        serialized_data["company_name"] = instance.company.company_name
        if instance.status == "APPROVAL":
            serialized_data["status"] = "PENDING APPROVAL"
        elif instance.status == "DISBURSE":
            serialized_data["status"] = "PENDING DISBURSE"
        elif instance.status == "DISBURSED":
            serialized_data["status"] = "PAYMENT SUCCESSFUL"
        else:
            serialized_data["status"] = "OMITTED"

        return serialized_data
    
class EditEmployeeProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeList
        fields = (
            "employee_first_name",
            "employee_last_name",
            "employee_phone_number",
            "employee_alternate_email",
            "employee_birth_date",
            "employee_gender",
            "employee_staff_id",
            "employee_staff_id_reporting_line",
            "employee_job_title",
            "employee_department",
            "employee_contract_type",
            "employee_state",
            "employee_city",
            "employee_town",
            "employee_bus_stop",
            "employee_street_name",
            "employee_house_no",
            "employee_postal_code",
            # "employee_profile_picture",
            "pension_fund_admin",
            "pension_pin",
            "pay_schedule",
            "pay_run",
            "employee_type",
            "staff_type",
            "employee_pay_grade",
            "employee_role",
            "employee_department_role"
        )

class AllPensionFundAdminSerializer(serializers.ModelSerializer):
    class Meta:
        model = PensionFundAdminSettings
        fields = [
            "id",
            "pfa_name",
            "pfa_account_number",
            "pfa_bank_name",
            "pfa_account_name",
            "pfa_bank_code",
            "pfa_sort_code",
            "pfa_email",
            "company_code",
        ]

class PensionFundAdminSerializer(serializers.Serializer):
    pfa_name = serializers.CharField(required=True, allow_null=False)
    pfa_account_number = serializers.CharField(required=True, allow_null=False)
    pfa_bank_name = serializers.CharField(required=True, allow_null=False)
    pfa_account_name = serializers.CharField(required=True, allow_null=False)
    pfa_bank_code = serializers.CharField(required=True, allow_null=False)
    pfa_sort_code = serializers.CharField(required=True, allow_null=False)
    pfa_email = serializers.CharField(required=True, allow_null=False)
    company_code = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        company_uuid = self.context["company_uuid"]
        pfa_name = values["pfa_name"]
        pfa  = PensionFundAdminSettings.objects.filter(
            company__id=company_uuid, 
            pfa_name=pfa_name,
            is_deleted=False
            ).exists()
        if pfa:
            raise serializers.ValidationError(
                {"message": f"{pfa_name} already exist"}
            )
        return values
    
class MultiplePensionFundAdminSerializer(serializers.Serializer):
    pfa_data = serializers.ListSerializer(child=PensionFundAdminSerializer())
    def validate(self, values):
        pfa_name = [pfa_datum['pfa_name'] for pfa_datum in values]
        if len(pfa_name) != len(set(pfa_name)):
            raise serializers.ValidationError("duplicate pfa_name found in the list.")
        
        if len(values["pfa_data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "pfa_data": "pfa_data cannot be empty"}
            )
        return values
    
class EmployeePensionFundAdminSerializer(serializers.Serializer):
    pfa_id = serializers.CharField(required=True, allow_null=False)
    employee_id = serializers.CharField(required=True, allow_null=False)
    pfa_pin = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        company_uuid = self.context["company_uuid"]
        pfa_id= values["pfa_id"]
        employee_id= values["employee_id"]
        try:
            employee_ins = CompanyEmployeeList.objects.filter(id=employee_id, company__id=company_uuid, is_deleted=False).last()
        except ValueError:
            raise serializers.ValidationError(
                {"message": f"{employee_id} invalid format"}
            )

        if not employee_ins:
            raise serializers.ValidationError(
                {"message": f"{employee_id} does not exist"}
            )
        else:
            pfa_ins = PensionFundAdminSettings.objects.filter(
                id = pfa_id,
                company__id=company_uuid, 
                is_deleted=False
                ).last()
            if not pfa_ins:
                raise serializers.ValidationError(
                    {"message": f"{pfa_id} does not exist"}
                )
        return values
    
class MultipleEmployeePensionFundAdminSerializer(serializers.Serializer):
    employee_department_data = serializers.ListSerializer(child=EmployeePensionFundAdminSerializer())
    def validate(self, values):
        if len(values["employee_department_data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employee_department_data": "employee_department_data cannot be empty"}
            )
        return values


class AdminResetEmployeePayrollPINSerializer(serializers.Serializer):
    pin = serializers.CharField()
    repeat_pin = serializers.CharField()

    def validate(self, values):
        if not "pin" in values or not "repeat_pin" in values:
            raise serializers.ValidationError(
                {"error_code": "24", "message": "You must supply pin and repeat_pin keys"}
            )

        if values["pin"].isnumeric():
            pass
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "You must supply an integer"}
            )
        return values

class AdminRedeployEmployeeDepartmentSerializer(serializers.Serializer):
    department_id = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        company_uuid = self.context["company_uuid"]
        department_id= values["department_id"]
       
        department_ins = CompanyDepartmentSettings.objects.filter(
            id = department_id,
            company__id=company_uuid, 
            is_deleted=False
            ).last()
        if not department_ins:
            raise serializers.ValidationError(
                {"message": "department does not exist"}
            )
        return values
    
class AdminPromoteEmployeeSerializer(serializers.Serializer):
    pay_grade_id = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        company_uuid = self.context["company_uuid"]
        pay_grade_id= values["pay_grade_id"]
       
        pay_grade_ins = CompanyPayGradeSettings.objects.filter(
            id = pay_grade_id,
            company__id=company_uuid, 
            is_deleted=False
            ).last()
        if not pay_grade_ins:
            raise serializers.ValidationError(
                {"message": f"{pay_grade_id} does not exist"}
            )
        return values
    
class AddEmployeePensionFundAdminSerializer(serializers.Serializer):
    pfa_id = serializers.CharField(required=True, allow_null=False)
    pfa_pin = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        pfa_id= values["pfa_id"]
       
        pfa_ins = PensionFundAdminSettings.objects.filter(
            id = pfa_id,
            company__isnull=True,
            is_deleted=False
            ).last()
        if not pfa_ins:
            raise serializers.ValidationError(
                {"message": f"{pfa_id} does not exist"}
            )
        return values

class PFADetailSerializer(serializers.ModelSerializer):        
    class Meta:
        model = PensionFundAdminSettings
        fields = [
            "id",
            "pfa_name",
            "pfa_account_number",
            "pfa_bank_name",
            "pfa_account_name",
            "pfa_bank_code",
            "pfa_sort_code",
            "pfa_email",
            "company_code",
        ]

class ListEmployeePFASerializer(serializers.ModelSerializer):
    pension_fund_admin = PFADetailSerializer(read_only=True)
    class Meta:
        model = CompanyEmployeeList
        fields = ("pension_fund_admin",)

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        get_pfa = PensionFundAdminSettings.objects.filter(id=instance["pension_fund_admin"]).last()
        
        serialized_data["id"] = get_pfa.id
        serialized_data["pfa_name"] = get_pfa.pfa_name
        serialized_data["pfa_account_number"] = get_pfa.pfa_account_number
        serialized_data["pfa_bank_name"] = get_pfa.pfa_bank_name
        serialized_data["pfa_account_name"] = get_pfa.pfa_account_name
        serialized_data["pfa_bank_code"] = get_pfa.pfa_bank_code
        serialized_data["pfa_sort_code"] = get_pfa.pfa_sort_code
        serialized_data["pfa_email"] = get_pfa.pfa_email
        serialized_data["company_code"] = get_pfa.company_code

        del serialized_data["pension_fund_admin"]
        return serialized_data
    
class AllPFAEmployeeListSerializer(serializers.ModelSerializer):
    pension_fund_admin = PFADetailSerializer(read_only=True)
    class Meta:
        model = CompanyEmployeeList
        fields = ("pension_fund_admin",)

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        company_uuid = self.context.get("company_uuid")

        get_pfa = PensionFundAdminSettings.objects.filter(id=instance["pension_fund_admin"]).last()
        get_pfa_base = CompanyEmployeeList.objects.filter(company__id=company_uuid, pension_fund_admin=get_pfa, is_active=True)
        
        total_employees = get_pfa_base.count() or 0
        total_pension_funds = get_pfa_base.aggregate(Sum("employee_pension_amount"))["employee_pension_amount__sum"] or 0
        
        serialized_data["pfa_name"] = get_pfa.pfa_name
        serialized_data["total_employees"] = total_employees
        serialized_data["total_pension_funds"] = total_pension_funds
        serialized_data["employees"] = get_pfa_base.values("employee_first_name", "employee_last_name", "employee_pension_amount").order_by(Lower("employee_last_name"))

        del serialized_data["pension_fund_admin"]
        return serialized_data

class DepartmentDetailSerializer(serializers.ModelSerializer):        
    class Meta:
        model = CompanyDepartmentSettings
        fields = [
            "id",
            "department_name",
        ]

class AllDepartmentEmployeeListSerializer(serializers.ModelSerializer):
    employee_department = DepartmentDetailSerializer(read_only=True)
    class Meta:
        model = CompanyEmployeeList
        fields = ("employee_department",)

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        company_uuid = self.context.get("company_uuid")

        get_department = CompanyDepartmentSettings.objects.filter(id=instance["employee_department"]).last()
        get_department_base = CompanyEmployeeList.objects.filter(company__id=company_uuid, employee_department=get_department, is_active=True)
        
        total_employees = get_department_base.count() or 0
        
        serialized_data["department_name"] = get_department.department_name
        serialized_data["total_employees"] = total_employees
        serialized_data["employees"] = get_department_base.values("employee_first_name", "employee_last_name").order_by(Lower("employee_last_name"))

        del serialized_data["employee_department"]
        return serialized_data


class AddEmployeeWorkTypeSerializer(serializers.Serializer):
    employees = serializers.JSONField()
    work_type = serializers.CharField(required=True)

    def validate(self, values):
        if len(values["employees"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employees": "employees cannot be empty"}
            )
        return values
    
class AddEmployeeStaffTypeSerializer(serializers.Serializer):
    employees = serializers.JSONField()
    staff_type = serializers.CharField(required=True)

    def validate(self, values):
        if len(values["employees"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employees": "employees cannot be empty"}
            )
        return values
    
class FetchPermissionsSerializer(serializers.ModelSerializer):

    class Meta:
        model = ManagePermissionsRole
        fields = (
            "id",
            "company",
            "role_name",
            "role_description",
            "can_disburse", 
            "can_approve", 
            "can_add_member", 
            "can_edit_member", 
            "can_delete_member",
            "can_delete_payroll",
            "can_run_payroll",
            "can_edit_payroll_settings",
            "can_create_leave_type",
            "can_edit_leave_type",
            "can_delete_leave_type",
            "can_create_leave_policy",
            "can_edit_leave_policy",
            "can_delete_leave_policy",
            "can_approve_leave",
            "can_create_role",
            "can_edit_role",
            "can_delete_role",
            "can_create_role",
            "can_create_department",
            "can_edit_department",
            "can_delete_department",
            "can_create_department_role",
            "can_edit_department_role",
            "can_delete_department_role",
            "can_deploy_department_role",
            "can_deploy_department",
            "can_deploy_employee_role"
        )
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["company_name"] = instance.company.company_name if instance.company else ""
        return serialized_data
    
class CreatePermissionRoleSerializer(serializers.Serializer):
    role_name = serializers.CharField(required=True, allow_null=False)
    role_description = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    can_disburse = serializers.BooleanField(default=False) 
    can_approve = serializers.BooleanField(default=False)
    can_add_member = serializers.BooleanField(default=False) 
    can_edit_member = serializers.BooleanField(default=False) 
    can_delete_member = serializers.BooleanField(default=False)
    can_delete_payroll = serializers.BooleanField(default=False)
    can_run_payroll = serializers.BooleanField(default=False)
    can_edit_payroll_settings = serializers.BooleanField(default=False)
    can_create_leave_type = serializers.BooleanField(default=False)
    can_edit_leave_type = serializers.BooleanField(default=False)
    can_delete_leave_type = serializers.BooleanField(default=False)
    can_create_leave_policy = serializers.BooleanField(default=False)
    can_edit_leave_policy = serializers.BooleanField(default=False)
    can_delete_leave_policy = serializers.BooleanField(default=False)
    can_approve_leave = serializers.BooleanField(default=False)
    can_create_role = serializers.BooleanField(default=False)
    can_edit_role = serializers.BooleanField(default=False)
    can_delete_role = serializers.BooleanField(default=False)
    can_create_department = serializers.BooleanField(default=False)
    can_edit_department = serializers.BooleanField(default=False)
    can_delete_department = serializers.BooleanField(default=False)
    can_create_department_role = serializers.BooleanField(default=False)
    can_edit_department_role = serializers.BooleanField(default=False)
    can_delete_department_role = serializers.BooleanField(default=False)
    can_deploy_department_role = serializers.BooleanField(default=False)
    can_deploy_department = serializers.BooleanField(default=False)

    def validate(self, attrs):
        company_id = self.context.get("company_id")
        role_name = attrs.get("role_name")
        role_exist = ManagePermissionsRole.role_exist(company_id=company_id, role_name=role_name)
        if role_exist:
            raise serializers.ValidationError({"message": f"{role_name} already exists"})
        return attrs
    
class EditPermissionRoleSerializer(serializers.ModelSerializer):    
    class Meta:
        model = ManagePermissionsRole
        fields = (
            "role_name",
            "role_description",
            "can_disburse", 
            "can_approve", 
            "can_add_member", 
            "can_edit_member", 
            "can_delete_member",
            "can_create_leave_type",
            "can_edit_leave_type",
            "can_delete_leave_type",
            "can_create_leave_policy",
            "can_edit_leave_policy",
            "can_delete_leave_policy",
            "can_edit_payroll_settings",
            "can_approve_leave",
            "can_create_role",
            "can_edit_role",
            "can_delete_role",
            "can_create_department",
            "can_edit_department",
            "can_delete_department",
            "can_create_department_role",
            "can_edit_department_role",
            "can_delete_department_role",
            "can_deploy_department_role",
            "can_deploy_department",
            "can_deploy_employee_role"
        )

    # def validate(self, attrs):
    #     company_id = self.context.get("company_id")
    #     role_name = attrs.get("role_name")
    #     role_exist = ManagePermissionsRole.role_exist(company_id=company_id, role_name=role_name)
    #     if role_exist:
    #         raise serializers.ValidationError({"message": f"{role_name} already exists"})
    #     return attrs

class FetchDepartmentRoleSerializer(serializers.ModelSerializer):

    class Meta:
        model = ManageDepartmentRole
        fields = (
            "id",
            "company",
            "role_name",
            "role_description",
        )
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["company_name"] = instance.company.company_name if instance.company else ""
        return serialized_data
    
class CreateDepartmentRoleSerializer(serializers.Serializer):
    role_name = serializers.CharField(required=True, allow_null=False)
    role_description = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    def validate(self, attrs):
        company_id = self.context.get("company_id")
        role_name = attrs.get("role_name")
        role_exist = ManageDepartmentRole.role_exist(company_id=company_id, role_name=role_name)
        if role_exist:
            raise serializers.ValidationError({"message": f"{role_name} already exists"})
        return attrs
    
class EditDepartmentRoleSerializer(serializers.ModelSerializer):    
    class Meta:
        model = ManageDepartmentRole
        fields = (
            "role_name",
            "role_description",
        )

    # def validate(self, attrs):
    #     company_id = self.context.get("company_id")
    #     role_name = attrs.get("role_name")
    #     role_exist = ManageDepartmentRole.role_exist(company_id=company_id, role_name=role_name)
    #     if role_exist:
    #         raise serializers.ValidationError({"message": f"{role_name} already exists"})
    #     return attrs

class DepartmentEmployeeSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeList
        fields = (
            "id",
            "employee_first_name",
            "employee_last_name",
            "employee_email",
            "employee_department_role",
        )
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["employee_department_role"] = instance.employee_department_role.role_name if instance.employee_department_role else "Member"
        return serialized_data

class ListDepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyDepartmentSettings
        fields = [
            "id",
            "department_name",
            "department_head_name"
        ]
    
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        
        department_employee = CompanyEmployeeList.objects.filter(company=instance.company, employee_department=instance)
        this_data = DepartmentEmployeeSerializer(department_employee, many=True)
        serialized_data["department_employee"] = this_data.data
        serialized_data["number_of_employee"] = len(this_data.data)
        
        return serialized_data
    
class DepartmentModuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyDepartmentSettings
        fields = [
            "id",
            "department_name",
            "department_head_name"
        ]

class AdminEmployeeDepartmentRoleSerializer(serializers.Serializer):
    role_id = serializers.CharField(required=True, allow_null=False)

    def validate(self, attrs):
        company_uuid = self.context.get("company_uuid")
        role_id = attrs.get("role_id")
       
        department_ins = ManageDepartmentRole.objects.filter(
            id = role_id,
            company__id=company_uuid, 
            is_deleted=False
            ).first()
        if not department_ins:
            raise serializers.ValidationError(
                {"message": "department role does not exist"}
            )
        return attrs
    
class EmployeeDepartmentRoleSerializer(serializers.Serializer):
    employee_id = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        company_uuid = self.context.get("company_uuid")
        role_id = self.context.get("role_id")
        employee_id= values.get("employee_id")
        try:
            employee_ins = CompanyEmployeeList.objects.filter(id=employee_id, company__id=company_uuid, is_deleted=False).first()
        except ValueError:
            raise serializers.ValidationError(
                {"message": f"{employee_id} invalid format"}
            )

        if not employee_ins:
            raise serializers.ValidationError(
                {"message": f"{employee_id} does not exist"}
            )
        else:
            department_ins = ManageDepartmentRole.objects.filter(
                id = role_id,
                company__id=company_uuid, 
                is_deleted=False
                ).last()
            if not department_ins:
                raise serializers.ValidationError(
                    {"message": f"{role_id} does not exist"}
                )
        return values

class MultipleEmployeeDepartmentRoleSerializer(serializers.Serializer):
    employee_department_data = serializers.ListSerializer(child=EmployeeDepartmentRoleSerializer())
    def validate(self, values):
        if len(values["employee_department_data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employee_department_data": "employee_department_data cannot be empty"}
            )
        return values
    
class EmployeeUserRoleSerializer(serializers.Serializer):
    employee_id = serializers.CharField(required=True, allow_null=False)

    def validate(self, values):
        company_uuid = self.context.get("company_uuid")
        role_id = self.context.get("role_id")
        employee_id = values.get("employee_id")
        try:
            employee_ins = CompanyEmployeeList.objects.filter(id=employee_id, company__id=company_uuid, is_deleted=False).first()
        except ValueError:
            raise serializers.ValidationError(
                {"message": f"{employee_id} invalid format"}
            )

        if not employee_ins:
            raise serializers.ValidationError(
                {"message": f"{employee_id} does not exist"}
            )
        else:
            role_ins = ManagePermissionsRole.objects.filter(
                id = role_id,
                company__id=company_uuid, 
                is_deleted=False
                ).last()
            if not role_ins:
                raise serializers.ValidationError(
                    {"message": f"{role_id} does not exist"}
                )
        return values
    
class MultipleEmployeeUserRoleSerializer(serializers.Serializer):
    employee_role_data = serializers.ListSerializer(child=EmployeeUserRoleSerializer())
    def validate(self, values):
        if len(values["employee_role_data"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employee_role_data": "employee_role_data cannot be empty"}
            )
        return values
    
class ListCompanyEmployeePermissionSerializer(serializers.ModelSerializer):

    class Meta:
        model = CompanyEmployeeList
        fields = (
            "id",
            "employee_role",
            "employee_first_name",
            "employee_last_name",
            "employee_email",
        )

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["role_name"] = instance.employee_role.role_name if  instance.employee_role else ""
        serialized_data["status"] = instance.employee_status

        return serialized_data
    
class AddEmployeeExistingPayrollSerializer(serializers.Serializer):
    payroll_pin = serializers.CharField(required=True, allow_null=False)
    employees = serializers.ListField(required=True, allow_null=False)

class EmployeeAddBonusSerializer(serializers.Serializer):
    email = serializers.CharField(required=True, allow_null=False)
    amount = serializers.FloatField(min_value=1, required=True, allow_null=False)

class AddBonusExistingPayrollSerializer(serializers.Serializer):
    employees = serializers.ListSerializer(child=EmployeeAddBonusSerializer())
    def validate(self, values):
        if len(values["employees"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employees": "employees data cannot be empty"}
            )
        return values
    
class EmployeeInvitedOnboardingSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeOnboardingForm
        fields = [
            "id",
            "employee_first_name",
            "employee_last_name",
            "employee_phone_number",
            "employee_alternate_email",
            "employee_birth_date",
            "employee_gender",
            "employee_staff_id",
            "employee_staff_id_reporting_line",
            "employee_state",
            "employee_city",
            "employee_town",
            "employee_bus_stop",
            "employee_street_name",
            "employee_house_no",
            "employee_postal_code",
            "employee_first_next_of_kin_firstname",
            "employee_first_next_of_kin_lastname",
            "employee_first_next_of_kin_phone_number",
            "employee_first_next_of_kin_relationship",
            "employee_first_next_of_kin_address",
            "employee_second_next_of_kin_firstname",
            "employee_second_next_of_kin_lastname",
            "employee_second_next_of_kin_phone_number",
            "employee_second_next_of_kin_relationship",
            "employee_second_next_of_kin_address"
        ]

class OnboardUserSerializer(serializers.Serializer):
    DEVICE_TYPE_CHOICES = (
        ("TERMINAL", "TERMINAL"),
        ("MOBILE", "MOBILE"),
        ("WEB", "WEB")
    )
    GENDER_CHOICES = [
        ("Male", "MALE"),
        ("Female", "FEMALE")
    ]
    phone_number = serializers.CharField(max_length=25, required=True)
    username = serializers.CharField(max_length=25, required=True)
    first_name = serializers.CharField(max_length=50, required=True)
    last_name = serializers.CharField(max_length=50, required=True)
    email = serializers.EmailField(required=True)
    state = serializers.CharField(max_length=150, required=True)
    lga = serializers.CharField(max_length=150, required=True)
    nearest_landmark = serializers.CharField(max_length=250, required=True)
    street = serializers.CharField(max_length=150, required=True)
    gender = serializers.ChoiceField(required=False, choices=GENDER_CHOICES)
    device_type = serializers.ChoiceField(required=False, choices=DEVICE_TYPE_CHOICES, allow_blank=True, allow_null=True)

    def validate(self, attrs):
        email = attrs.get("email")
        attrs["email"] = email.lower()
        return attrs
    
class OnboardPhoneUserSerializer(serializers.Serializer):
    DEVICE_TYPE_CHOICES = (
        ("TERMINAL", "TERMINAL"),
        ("MOBILE", "MOBILE"),
        ("WEB", "WEB")
    )
    GENDER_CHOICES = [
        ("Male", "MALE"),
        ("Female", "FEMALE")
    ]
    phone_number = serializers.CharField(max_length=25, required=True)
    username = serializers.CharField(max_length=25, required=True)
    first_name = serializers.CharField(max_length=50, required=True)
    last_name = serializers.CharField(max_length=50, required=True)
    state = serializers.CharField(max_length=150, required=True)
    lga = serializers.CharField(max_length=150, required=True)
    nearest_landmark = serializers.CharField(max_length=250, required=True)
    street = serializers.CharField(max_length=150, required=True)
    gender = serializers.ChoiceField(required=False, choices=GENDER_CHOICES)
    device_type = serializers.ChoiceField(required=False, choices=DEVICE_TYPE_CHOICES, allow_blank=True, allow_null=True)

    
class EmployeeFromSavingsSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, allow_null=False)
    payable_amount = serializers.FloatField(min_value=0, required=True, allow_null=False)
    net_amount = serializers.FloatField(min_value=0, required=True, allow_null=False)
    gross_amount = serializers.FloatField(min_value=0, required=False, allow_null=True)
    deduction_amount = serializers.FloatField(min_value=0, required=False, allow_null=True)
    bonus_amount = serializers.FloatField(min_value=0, required=False, allow_null=True)

    def validate(self, attrs):
        email = attrs.get("email")
        attrs["email"] = email.lower()
        return attrs

class EditEmployeeDataFromSavingsSerializer(serializers.Serializer):
    employees = serializers.ListSerializer(child=EmployeeFromSavingsSerializer())
    def validate(self, values):
        if len(values["employees"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employees": "employees data cannot be empty"}
            )
        return values

class ResendPensionSerializer(serializers.Serializer):
    bulk_id = serializers.CharField(max_length=150, required=True)
    all_payable_id = serializers.ListField(required=True, allow_null=False)


class UniquePayrollSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyDetailsData
        fields = ["id", "bulk_id", "date_created", "narration", "payroll_month"]

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        payroll_qs = PayrollTable.objects.filter(
            company=instance.company, bulk_id=instance.bulk_id, payroll_deleted=False)
        approval = payroll_qs.filter(status="APPROVAL")
        disburse = payroll_qs.filter(status="DISBURSE")
        omitted = payroll_qs.filter(status="OMITTED")
        # print(approval, disburse, omitted)
        if disburse:
            payroll_status = "DISBURSE"
            amount_processed = (
                    disburse.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
                )
        else:
            if approval:
                payroll_status = "APPROVAL"
                amount_processed = (
                        approval.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
                    )
            else:
                payroll_status = "OMITTED"
                amount_processed = (
                        omitted.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
                    )

        serialized_data["payroll_status"] = payroll_status
        serialized_data["amount_processed"] = amount_processed
        return serialized_data
    
class CompanyPayrollCalculatorSerializer(serializers.Serializer):
    net_amount = serializers.FloatField(default=0, required=False)
    other_deductions = serializers.FloatField(default=0, required=False)
    other_bonus = serializers.FloatField(default=0, required=False)
    life_insurance = serializers.FloatField(default=0, required=False)
    hmo = serializers.FloatField(default=0, required=False)
    voluntary_pension = serializers.FloatField(default=0, required=False)

class ManualPensionSerializer(serializers.Serializer):
    pension_list = serializers.ListField(required=True, allow_null=False)
    company_name =  serializers.CharField(max_length=150, required=True)
    payroll_date = serializers.DateField(required=True)
    employer_code = serializers.CharField(max_length=150, required=True)
    company_email = serializers.EmailField()
    hr_email = serializers.EmailField()

class CompanyTransactionHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "transaction_type",
            "transaction_ref",
            "amount",
            "status",
            "balance_before",
            "balance_after",
            "date_created",
            "company_name",
            "beneficiary_account_number",
            "beneficiary_account_name",
            "source_account_name",
            "source_account_number",
            "narration",
            "beneficiary_wallet_id",
            "source_wallet_id",
            "wallet_id"
        ]

class CompanyPensionTransactionHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "transaction_type",
            "transaction_ref",
            "amount",
            "status",
            "balance_before",
            "balance_after",
            "date_created",
            "company_name",
            "beneficiary_account_number",
            "beneficiary_account_name",
            "source_account_name",
            "source_account_number",
            "narration",
            "beneficiary_wallet_id",
            "source_wallet_id",
            "wallet_id"
        ]
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["session_id"] = instance.session_id
        return serialized_data

class VerifyUtilityBillSerializer(serializers.Serializer):
    utility_bill = serializers.FileField(required=True, allow_null=True, validators=[FileExtensionValidator(allowed_extensions=['jpg', 'png', 'jpeg', 'gif', 'webp'])])

    def validate(self, attrs):
        utility_bill = attrs.get("utility_bill")
        if utility_bill is None:
            raise serializers.ValidationError(
                {"message": "employee must upload an utility bill"}
            )
        
        image_data = validate_utility_bill_with_chagpt(utility_bill)
        # print(image_data, "IMAGE DATA")

        utility_status = image_data.get("status")
        utility_message = image_data.get("message")
        verification_image_data = image_data.get("image_data")
        utility_date = image_data.get("Date")
        utility_address = image_data.get("Address")

        if utility_status is False:
            raise serializers.ValidationError(
                {"message": utility_message}
            )

        attrs["utility_status"] = utility_status
        attrs["utility_date"] = utility_date
        attrs["utility_message"] = utility_message
        attrs["verification_image_data"] = verification_image_data
        attrs["utility_address"] = utility_address

        return attrs
    
class VerifyGuarantorUtilityBillSerializer(serializers.Serializer):
    utility_bill = serializers.FileField(required=True, allow_null=True, validators=[FileExtensionValidator(allowed_extensions=['jpg', 'png', 'jpeg', 'gif', 'webp'])])
    guarantor_state = serializers.CharField(max_length=150, required=True)
    guarantor_city = serializers.CharField(max_length=150, required=True)
    guarantor_town = serializers.CharField(max_length=150, required=True)
    guarantor_bus_stop = serializers.CharField(max_length=150, required=True)
    guarantor_street_name = serializers.CharField(max_length=150, required=True)

    def validate(self, attrs):
        utility_bill = attrs.get("utility_bill")
        if utility_bill is None:
            raise serializers.ValidationError(
                {"message": "guarantor must upload an utility bill"}
            )
        
        image_data = validate_utility_bill_with_chagpt(utility_bill)

        utility_status = image_data.get("status")
        utility_message = image_data.get("message")
        verification_image_data = image_data.get("image_data")
        utility_date = image_data.get("Date")
        utility_address = image_data.get("Address")

        if utility_status is False:
            raise serializers.ValidationError(
                {"message": utility_message}
            )

        attrs["utility_status"] = utility_status
        attrs["utility_date"] = utility_date
        attrs["utility_message"] = utility_message
        attrs["verification_image_data"] = verification_image_data
        attrs["utility_address"] = utility_address

        return attrs


class EmployeeVerificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyEmployeeList
        fields = (
            "id",
            "employee_guarantor_phone_number",
            "is_phone_number_verified",
            "is_guarantor_phone_number_verified",
            "employee_guarantor_state", 
            "employee_guarantor_city", 
            "employee_guarantor_town", 
            "employee_guarantor_bus_stop", 
            "employee_guarantor_street_name", 
            "employee_guarantor_house_no",
            "employee_utility_bill",
            "guarantor_utility_bill"
        )