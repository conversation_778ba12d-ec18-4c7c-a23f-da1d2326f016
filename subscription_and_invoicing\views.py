from django.shortcuts import get_object_or_404
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status, generics, filters
from django_filters.rest_framework import DjangoFilterBackend
from django.db import transaction
from django.http import Http404
from django.utils.timezone import now
from rest_framework.authtoken.models import Token
from datetime import datetime
from cart_management.views import CustomDashboardPagination
from core.auth.custom_auth import CustomUserAuthentication
from helpers.reusable_functions import get_account_details
from requisition.models import Company
# from subscription_and_invoicing.filters import InvoiceFilter
from subscription_and_invoicing import models as subscription
from performance_sales_metrics_dashboard import models as perf
from account import models as acc
from requisition import serializers as ser
from requisition import models as req
import requests

from subscription_and_invoicing.models import ModuleSubscription, CompanySubscription, AccessPath, SubscriptionPlan
from subscription_and_invoicing.serializers import (
    CreateInvoiceSerializer,
    InvoiceSerializer,
    InvoiceTransactionSerializer,
    InvoiceListSerializer,
    InvoiceUpdateSerializer, UserCompanyDetailsSerializer, GetInvoiceSerializer, ModuleSerializer,
    SubscriptionPlanSerializer
)
import logging
from datetime import timedelta
from django.contrib.auth import get_user_model
from decimal import Decimal
from django.conf import settings
from account.helpers.core_banking import CoreBankingService
from account.models import CoreBankingCallback
from core.models import PaystackPayment
from sales_app.helper.enums import MeansOfPaymentChoices
from django.db.models import Sum, Count, F, Avg, ExpressionWrapper, fields, Q
from rest_framework.exceptions import AuthenticationFailed
from core.helpers.apis.request_cls import LibertyPayPlus
from subscription_and_invoicing.service import SubscriptionManager

User = get_user_model()
logger = logging.getLogger(__name__)


class CreateInvoiceAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = CreateInvoiceSerializer(
            data=request.data,
            context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        
        return Response(
            serializer.validated_data,
            status=status.HTTP_201_CREATED
        )

class SubscriptionPlanListView(APIView):
    """Returns all active subscription plans"""

    def get(self, request):
        plans = SubscriptionPlan.objects.all()
        serialized_data = SubscriptionPlanSerializer(plans, many=True).data
        return Response({"plans": serialized_data}, status=200)



# class TieSubscriptionToUserCompanyView(APIView):
#     def tie_subscription_to_company(self):
#         subscription_manager = SubscriptionManager(self.request.user)
#         response = subscription_manager.tie_paid_modules_to_subscription()
#         if "error" in response:
#             return Response(response, status=status.HTTP_400_BAD_REQUEST)
#         return Response(response, status=status.HTTP_200_OK)


class TieSubscriptionToUserCompanyView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        company_id = request.query_params.get("company_id") or request.data.get("company_id")

        if not company_id:
            return Response({"error": "company_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Ensure company_id is a valid UUID
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"error": "Invalid company_id"}, status=status.HTTP_404_NOT_FOUND)

        # Pass only the user to SubscriptionManager, as it does not accept company_id
        subscription_manager = SubscriptionManager(request.user)

        # Tie paid modules to subscription
        response = subscription_manager.tie_paid_modules_to_subscription()

        # If an error occurred, return a 400 response
        if "error" in response:
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(response, status=status.HTTP_200_OK)

class CreateUserAndCompany(APIView):
    # authentication_classes = [CustomUserAuthentication]
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        # Validate the incoming request data
        serializer = UserCompanyDetailsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        # result = subscription.UserCompanyDetails.create_user(validated_data)
        # user_instance=subscription.UserCompanyDetails.objects.get(id="725e9598-596c-4e4a-8e0c-921d82fb04a1")
        # user_instance.create_paybox_user()

        # if result.get("status") == "success":
        return Response(
            {
                "message": "User created successfully",
                "user_data": serializer.data
            },
            status=status.HTTP_200_OK
        )

class GetAllInvoiceAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    """
    APIView to retrieve all invoices, with optional filtering by payment_status.
    """
    def get(self, request):
        # Get the payment_status from the query params (if provided)
        payment_status = request.query_params.get('payment_status', None)

        # If payment_status is provided, filter by it, otherwise get all invoices
        if payment_status:
            all_invoices = subscription.Invoice.objects.filter(payment_status=payment_status)
        else:
            all_invoices = subscription.Invoice.objects.all()

        # Serialize the filtered or full invoice set
        serialized_invoices = GetInvoiceSerializer(all_invoices, many=True).data
        total_invoices_value = all_invoices.aggregate(amount=Sum('amount'))['amount'] or 0

        response_data = {
            "invoices": serialized_invoices,
            "total_invoices_count": all_invoices.count(),
            "total_invoices_value": total_invoices_value
        }

        return Response(response_data, status=status.HTTP_200_OK)


class InvoiceStatsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    """
    APIView to retrieve invoice statistics and lists.
    """
    def get(self, request):
        # Retrieve all invoices and calculate the total count
        total_invoice_count = subscription.Invoice.objects.count()

        # Retrieve paid invoices and calculate statistics
        paid_invoices = subscription.Invoice.objects.filter(payment_status__in=["paid", "paid_excess"])
        paid_total_amount = paid_invoices.aggregate(paid_amount=Sum('amount_due'))['paid_amount'] or 0
        paid_invoice_count = paid_invoices.count()

        # Calculate the average time taken to pay for invoices
        average_paid_time = (
            paid_invoices
            .annotate(duration=ExpressionWrapper(F('expiry_date') - F('start_date'), output_field=fields.DurationField()))
            .aggregate(avg_paid_time=Avg('duration'))['avg_paid_time']
        )
        average_paid_time_days = average_paid_time.days if average_paid_time else None

        # Retrieve overdue invoices and calculate statistics
        overdue_invoices = subscription.Invoice.objects.filter(expiry_date__lt=now(), payment_status__in=["unpaid", "part_payment"])
        overdue_total_amount = overdue_invoices.aggregate(overdue_amount=Sum('amount_due'))['overdue_amount'] or 0
        overdue_invoice_count = overdue_invoices.count()

        # Retrieve unpaid invoices and calculate statistics
        unpaid_invoices = subscription.Invoice.objects.filter(payment_status="unpaid")
        unpaid_total_amount = unpaid_invoices.aggregate(unpaid_amount=Sum('amount_due'))['unpaid_amount'] or 0
        unpaid_invoice_count = unpaid_invoices.count()

        # Prepare response data
        response_data = {
            "total_invoices": total_invoice_count,
            "paid_invoices": {
                "count": paid_invoice_count,
                "total_amount": paid_total_amount,
            },
            "overdue_invoices": {
                "invoice_count": overdue_invoice_count,
                "overdue_total_amount": overdue_total_amount,
            },
            "unpaid_invoices": {
                "invoice_count": unpaid_invoice_count,
                "unpaid_total_amount": unpaid_total_amount,
            },
            "average_paid_time": {
                "average_duration": str(average_paid_time) if average_paid_time else None,
                "average_days": average_paid_time_days,
            }
        }

        return Response(response_data, status=status.HTTP_200_OK)


class UpdateInvoiceAPIView(APIView):
    # permission_classes = [IsAuthenticated]

    def put(self, request, invoice_id):
        try:
            invoice = subscription.Invoice.objects.get(id=invoice_id)
        except subscription.Invoice.DoesNotExist:
            raise Http404("Invoice not found.")

        serializer = InvoiceUpdateSerializer(instance=invoice, data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CancelInvoiceAPIView(APIView):
    # permission_classes = [IsAuthenticated]

    def post(self, request, invoice_id):

        try:
            invoice = subscription.Invoice.objects.get(id=invoice_id)
        except subscription.Invoice.DoesNotExist:
            raise Http404("Invoice not found.")

        if request.user != invoice.sales_officer and not request.user.is_staff:
            return Response({"error": "You don't have permission to cancel this invoice"},
                            status=status.HTTP_403_FORBIDDEN)

        if invoice.paid == 'paid':
            return Response({"error": "Fully paid invoices cannot be cancelled"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                invoice.paid = 'cancelled'
                invoice.is_active = False
                invoice.save()

                transactions = subscription.InvoiceTransaction.objects.filter(invoice=invoice)
                for trans in transactions:
                    subscription.InvoiceTransaction.objects.create(
                        invoice=invoice,
                        amount=-trans.amount,
                        payment_method=f"Reversal of {trans.payment_method}",
                        is_processed=True,
                        excess_payment=0,
                        invoice_reference=f"REV-{trans.invoice_reference}"
                    )
                subscription.Commission.objects.filter(invoice_transaction__invoice=invoice).delete()

                serializer = InvoiceSerializer(invoice)
                return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": f"An error occurred while cancelling the invoice: {str(e)}"},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# class SubscriptionRenewalAPIView(APIView):
#     permission_classes = [IsAuthenticated]
#
#     def post(self, request):
#         company_id = request.data.get('company_id')
#         module_id = request.data.get('module_id')
#
#         try:
#             company = Company.objects.get(id=company_id)
#             module = SubscriptionModule.objects.get(id=module_id)
#         except (Company.DoesNotExist, SubscriptionModule.DoesNotExist):
#             return Response({"error": "Invalid company or module"}, status=status.HTTP_400_BAD_REQUEST)
#
#         latest_invoice = Invoice.objects.filter(
#             company=company,
#             module=module,
#             is_active=True
#         ).order_by('-expiry_date').first()
#
#         if latest_invoice and not latest_invoice.is_due_for_renewal:
#             return Response({"error": "Subscription is not due for renewal"}, status=status.HTTP_400_BAD_REQUEST)
#
#         new_invoice = Invoice.objects.create(
#             company=company,
#             module=module,
#             amount=module.price,
#             amount_due=module.price,
#             start_date=latest_invoice.expiry_date + timezone.timedelta(days=1) if latest_invoice else timezone.now().date(),
#             expiry_date=(latest_invoice.expiry_date if latest_invoice else timezone.now().date()) + timedelta(days=30 * module.subscription_duration),
#             sales_officer=request.user,
#             is_active=False,
#             paid='unpaid'
#         )
#
#         collection_method = generate_collection_method(new_invoice)
#
#         if latest_invoice:
#             latest_invoice.is_active = False
#             latest_invoice.save()
#
#         serializer = InvoiceSerializer(new_invoice)
#         return Response({
#             "invoice": serializer.data,
#             "collection_method": collection_method
#         }, status=status.HTTP_201_CREATED)


class SubscriptionDashboardAPIView(APIView):
    def get(self, request):
        # Top Summary Data
        total_subscriptions = subscription.SubscriptionModule.objects.count()
        subscription_income = subscription.Invoice.objects.aggregate(total_income=Sum('settled_amount'))[
                                  'total_income'] or 0
        active_subscriptions = subscription.Invoice.objects.filter(is_active=True).count()
        inactive_subscriptions = subscription.Invoice.objects.filter(is_active=False).count()

        # Charts Data
        subscription_growth = subscription.SubscriptionModule.objects.values('start_date').annotate(total=Count('id'))
        top_subscribed_modules = subscription.SubscriptionModule.objects.values('subscription_type__name').annotate(
            total=Count('id')).order_by('-total')
        churn_rate = self.calculate_churn_rate()
        net_revenue = subscription.Invoice.objects.aggregate(net_revenue=Sum('settled_amount'))['net_revenue'] or 0

        # Fetching the most recent 5 subscriptions
        recent_subscriptions = subscription.Invoice.objects.order_by('-start_date')[:5]

        # Subscription Table Data
        subscriptions = subscription.SubscriptionModule.objects.values(
            'id',
            'subscription_type__name',
            'invoice__company__company_name',
            'invoice__amount',
            'start_date',
            'invoice__expiry_date',
            'invoice__payment_status',
            'invoice__is_active'
        )

        # Constructing the Response
        data = {
            'summary': {
                'total_subscriptions': total_subscriptions,
                'subscription_income': subscription_income,
                'active_subscriptions': active_subscriptions,
                'inactive_subscriptions': inactive_subscriptions,
            },
            'charts': {
                'subscription_growth': list(subscription_growth),
                'top_subscribed_modules': list(top_subscribed_modules),
                'churn_rate': churn_rate,
                'net_revenue': net_revenue,
                # Corrected field reference here:
                'recent_subscriptions': list(
                    recent_subscriptions.values('company__company_name', 'amount', 'start_date')),
            },
            'subscriptions': list(subscriptions),
        }

        return Response(data)

    def calculate_churn_rate(self):
        total_customers = subscription.Invoice.objects.values('company').distinct().count()
        churned_customers = subscription.Invoice.objects.filter(payment_status='unpaid').values(
            'company').distinct().count()
        churn_rate = (churned_customers / total_customers) * 100 if total_customers > 0 else 0
        return churn_rate


class CompanyDetailsAPIView(APIView, CustomDashboardPagination):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # Fetch query parameters for search and filters
        company_name = request.GET.get('company_name', None)
        company_email = request.GET.get('company_email', None)
        date_created = request.GET.get('date_created', None)
        sales_officer_param = request.GET.get('sales_officer', None)
        subscription_status = request.GET.get('subscription_status', None)
        subscription_module_param = request.GET.get('subscription_module', None)

        query = Q()

        if company_name:
            query &= Q(company_name__icontains=company_name)

        if company_email:
            query &= Q(user__email__icontains=company_email)

        if date_created:
            try:
                date_created = datetime.strptime(date_created, "%Y-%m-%d")
                query &= Q(created_at__date=date_created)
            except ValueError:
                return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=status.HTTP_400_BAD_REQUEST)

        if sales_officer_param:
            query &= Q(invoice__sales_officer__referral_code=sales_officer_param)

        if subscription_status:
            query &= Q(invoice__payment_status__iexact=subscription_status)

        if subscription_module_param:
            query &= Q(subscriptionmodule__subscription_type__name__iexact=subscription_module_param)

        companies = req.Company.objects.filter(query)

        page = self.paginate_queryset(companies, request, view=self)
        if page is not None:
            companies = page

        company_data = []

        for company in companies:
            subscription_modules = subscription.SubscriptionModule.objects.filter(company=company, is_active=True)
            subscription_info = []

            for module in subscription_modules:
                subscription_info.append({
                    "subscription_type": module.subscription_type.name if module.subscription_type else "N/A",
                    "duration_in_months": module.duration_in_months,
                    "start_date": module.start_date,
                    "expiry_date": module.calculate_expiry_date(),
                    "is_active": module.is_active,
                })

            latest_invoice = subscription.Invoice.objects.filter(company=company).order_by('-start_date').first()

            sales_officer_name = None
            if latest_invoice and latest_invoice.sales_officer:
                sales_officer = perf.SalesOfficer.objects.filter(referral_code=latest_invoice.sales_officer).first()
                sales_officer_name = sales_officer.name if sales_officer else "Unknown Sales Officer"

            current_month = datetime.now().month
            amount_processed_monthly = acc.Transaction.objects.filter(
                company_name=company.company_name,
                date_created__month=current_month
            ).aggregate(total_amount=Sum('amount'))['total_amount'] or 0

            company_data.append({
                "company_name": company.company_name,
                "company_email": company.user.email if company.user else None,  # Assuming the user has an email field
                "cac_num": company.cac_num,
                "industry": company.industry,
                "size": company.size,
                "is_active": company.is_active,
                "company_wallet_type": company.company_wallet_type,
                "subscription_modules": subscription_info,
                "subscription_status": latest_invoice.payment_status if latest_invoice else "No invoice",
                "sales_officer": sales_officer_name,
                "date_created": company.created_at,
                "amount_processed_monthly": amount_processed_monthly,
            })

        return self.get_paginated_response(company_data)


class CompanyStatisticsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    """
    Returns:
    - Total number of companies
    - Total number of companies with active subscriptions
    - Total number of companies with inactive subscriptions
    """

    def get(self, request, *args, **kwargs):
        total_companies = req.Company.objects.count()

        companies_with_active_subscriptions = req.Company.objects.filter(
            subscriptionmodule__is_active=True
        ).distinct().count()

        companies_with_inactive_subscriptions = req.Company.objects.filter(
            Q(subscriptionmodule__is_active=False) |
            Q(subscriptionmodule__isnull=True)
        ).distinct().count()

        data = {
            "total_companies": total_companies,
            "companies_with_active_subscriptions": companies_with_active_subscriptions,
            "companies_with_inactive_subscriptions": companies_with_inactive_subscriptions,
        }

        return Response(data, status=status.HTTP_200_OK)


class FetchAllModules(APIView):
    def get(self, request):
        modules = subscription.Module.objects.all()
        serialized_data = ModuleSerializer(modules, many=True).data
        return Response({"modules": serialized_data}, status=200)


class CompanySubscriptionCountView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        default_company = user.default_company

        if not default_company:
            return Response({"error": "No default company found for user."}, status=400)

        total_subscriptions = CompanySubscription.objects.filter(company=default_company).count()
        active_subscriptions = CompanySubscription.objects.filter(company=default_company, status="active").count()
        expired_subscriptions = CompanySubscription.objects.filter(company=default_company, status="expired").count()

        return Response({
            "total_subscriptions": total_subscriptions,
            "active_subscriptions": active_subscriptions,
            "expired_subscriptions": expired_subscriptions
        }, status=200)

class CompanySubscriptionsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user

        default_company = user.default_company

        if not default_company:
            return Response({"error": "No default company found for user."}, status=400)

        module_name = request.GET.get("module_name")
        is_active = request.GET.get("is_active")

        if is_active is not None:
            is_active = is_active.lower() == "true"

        # Get all subscriptions for the default company
        subscriptions = CompanySubscription.objects.filter(company=default_company).prefetch_related('modulesubscription_set')

        data = []
        for sub in subscriptions:
            # Filter module subscriptions
            module_subscriptions = sub.modulesubscription_set.all()

            # Filter module subscriptions by module_name & is_active
            if module_name:
                module_subscriptions = module_subscriptions.filter(module__name=module_name)
            if is_active is not None:
                module_subscriptions = module_subscriptions.filter(is_active=is_active)

            # Skip subscriptions that have no matching modules after filtering
            if not module_subscriptions.exists():
                continue

            modules_data = [
                {
                    "module_id": mod.module.id,
                    "module_name": mod.module.name,
                    "module_code": mod.module.code,
                    "is_auto_renewal": mod.is_auto_renewal,
                    "promotional_offer": mod.promotional_offer.id if mod.promotional_offer else None,
                    "pending_balance": str(mod.pending_balance),
                    "is_active": mod.is_active,
                    "start_date": mod.start_date,
                    "end_date": mod.end_date,
                    "days_remaining": mod.days_remaining()
                }
                for mod in module_subscriptions
            ]

            data.append({
                "company": sub.company.company_name,
                "status": sub.status,
                "access_type": sub.access_type,
                "created_by": sub.created_by.email if sub.created_by else None,
                "created_at": sub.created_at,
                "updated_at": sub.updated_at,
                "is_active": sub.is_active(),
                "modules": modules_data
            })

        return Response({"subscriptions": data}, status=200)

class CompanySubscriptionCountsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        default_company = user.default_company

        if not default_company:
            return Response({"error": "No default company found for user."}, status=400)

        # Get all subscriptions for the company
        all_subscriptions = CompanySubscription.objects.filter(company=default_company)
        total_count = all_subscriptions.count()

        # Count active subscriptions (using the is_active method)
        active_count = sum(1 for sub in all_subscriptions if sub.is_active())

        # Count due subscriptions
        # Assuming "due" means subscriptions that have modules with pending_balance > 0
        due_subscriptions = set()
        for sub in all_subscriptions:
            module_subscriptions = sub.modulesubscription_set.all()
            for mod in module_subscriptions:
                if mod.pending_balance and float(mod.pending_balance) > 0:
                    due_subscriptions.add(sub.id)
                    break

        due_count = len(due_subscriptions)

        response_data = {
            "company": default_company.company_name,
            "total_subscriptions": total_count,
            "active_subscriptions": active_count,
            "due_subscriptions": due_count
        }

        return Response(response_data, status=200)
