# Requisition App Documentation

## Overview

The Requisition app is a spend management system that allows companies to manage, approve, and disburse funds for various business expenses. It provides a structured workflow for requesting funds, approving requests, and tracking expenses across teams within an organization.

## Core Features

### 1. Company Management

- Create and manage company profiles
- Verify company information
- Set up company wallets for fund disbursement
- Manage industry classifications

### 2. Team Management

- Create teams within a company
- Assign members to teams with specific roles (<PERSON><PERSON>, Disburser, Reviewer, etc.)
- Invite new team members via email

### 3. Requisition Workflow

- Create fund requests with detailed information
- Specify request categories, amounts, and reasons
- Upload supporting documents (invoices)
- Track request status (Pending, Approved, Declined)
- Receive notifications on request status changes

### 4. Disbursement

- Secure fund transfers to approved requisitions
- Transaction PIN protection for disbursements
- Bulk disbursement capabilities
- SMS notifications for disbursements

### 5. Budget Management

- Create and allocate budgets to teams
- Track budget utilization
- Set spending limits
- Monitor out-of-budget expenses

### 6. Expense Tracking

- Record and categorize expenses
- Upload and extract data from receipts
- Generate expense reports
- View expense dashboards

### 7. Procurement Management

- Create purchase indents
- Manage supplier relationships
- Track purchase orders
- Process invoices and returns

## How Requisition Works

1. **Setup**: Company owner creates an account, sets up company profile, and creates teams
2. **Roles**: Team members are assigned roles that determine their permissions
3. **Funding**: Company wallet is funded for disbursements
4. **Budget Creation**: Company owner creates budgets with specific amounts and time periods
5. **Team Allocation**: Budgets are allocated to teams with spending limits and categories
6. **Requisition**: Team members create requisition requests for funds
7. **Approval**: Authorized members review and approve/decline requests
8. **Disbursement**: Approved requests receive funds via secure transfers
9. **Tracking**: All transactions are recorded for reporting and auditing

## How Procurement Works

1. **Purchase Indent**: Team members create purchase indents specifying products, quantities, and suppliers
2. **Approval Workflow**: Authorized members review and approve purchase indents based on budget availability
3. **Purchase Order**: Approved indents generate purchase orders sent to suppliers
4. **Invoice Processing**: Supplier invoices are uploaded, verified, and processed
5. **Payment**: Funds are disbursed to suppliers based on approved invoices
6. **Delivery Tracking**: Product deliveries are tracked and confirmed
7. **Returns Management**: Any product returns or issues are documented and processed
8. **Expense Recording**: All procurement expenses are recorded against team budgets

## User Roles

- **Company Owner**: Full access to all features
- **Admin**: Manage teams and approve requisitions
- **Disburser**: Approve and disburse funds
- **Reviewer**: Review and comment on requisitions
- **Regular Member**: Create requisitions and track their status

## Getting Started

1. **Registration**: Create an account and set up your company profile
2. **Transaction PIN**: Set up your transaction PIN for secure disbursements
3. **Team Setup**: Create teams and invite members
4. **Budget Allocation**: Set up budgets for your teams
5. **Wallet Funding**: Fund your company wallet for disbursements

## Security Features

- Transaction PIN protection for disbursements
- Role-based access control
- Audit trails for all financial transactions
- Secure bank transfers

## Integration Points

### 1. Banking and Payment APIs

- **VFD Bank API**: Handles fund transfers to external bank accounts, account verification, and transaction processing
- **Liberty Pay API**: Manages financial accounts for companies and processes internal transfers
- **Mono API**: Provides bank account verification and financial data aggregation services
- **Paystack**: Processes payments and verifies transactions

### 2. Communication Services

- **WhisperSMS**: Sends SMS notifications for requisition approvals, disbursements, and other important updates
- **Mailgun**: Delivers email notifications and transaction receipts
- **Brevo CRM**: Manages marketing communications and user onboarding sequences

### 3. Document Processing

- **OpenAI API (ChatGPT)**: Analyzes and validates uploaded documents, extracts structured data from invoices
- **PaddleOCR**: Performs optical character recognition on uploaded receipts and invoices
- **Pytesseract**: Extracts text from images and PDFs for document verification
- **PDF2Image**: Converts PDF documents to images for OCR processing
- **Spacy NLP**: Performs natural language processing for text analysis in documents

### 4. URL Management

- **URL Shortener Service**: Creates shortened URLs for SMS notifications with approval links
- **Pyshorteners**: Generates compact links for notifications

### 5. Authentication and Verification

- **YouVerify API**: Verifies company registration information against official records
- **Google API**: Provides additional verification services and location data

### 6. Storage and File Management

- **AWS S3**: Stores uploaded documents, receipts, and other files
- **StorageManager**: Handles file uploads and retrievals

## Key Considerations

### 1. Ensure sufficient wallet balance before approving requisitions

- **Responsible App**: Account system and Requisition app
- **Key Functions**:
  - Wallet balance checks are performed in disbursement operations
  - Found in `requisition/views.py` where `SmsDisbursement` class handles disbursements
  - The `DisbursementSerializer` validates sufficient funds before processing
  - Account balances are managed through the `AccountSystem` and `Wallet` models from `account.models`

### 2. Set appropriate spending limits for teams

- **Responsible App**: Requisition app
- **Key Functions**:
  - Budget allocation is handled by `BudgetAllocationSerializer` in `requisition/serializers.py`
  - The `TrackBudget` class in `requisition/views.py` monitors budget utilization
  - Budget creation and editing is managed through `BudgetSerializer` and `EditBudgetSerializer`
  - Team budget linking is handled by `LinkTeamToBudgetSerializer`

### 3. Transaction PIN security

- **Responsible App**: Core app and Requisition app
- **Key Functions**:
  - PIN verification occurs in multiple views like `SmsDisbursement` in `requisition/views.py`
  - The `User.check_sender_payroll_pin()` method validates PINs before critical operations
  - PIN creation is handled by `CreateRequisitionTransactionPinSerializer`
  - OTP verification for PIN reset uses the `VerifyOtpSerializer` from `core.serializers`

### 4. Procurement workflow management

- **Responsible App**: Requisition app
- **Key Functions**:
  - Purchase order management in `ProcurementPurchaseOrderSerializerOut`
  - Supplier management through `AddSupplierSerializerIn` and related serializers
  - Invoice processing via `ProcurementPurchaseInvoiceSerializerIn`
  - Delivery confirmation handled by `ConfirmDeliverySerializerIn`

### 5. Document verification and processing

- **Responsible App**: Requisition app
- **Key Functions**:
  - Receipt data extraction in `ReadExpenseReceipt` view in `requisition/views.py`
  - The `process_invoice_file_2` function in `requisition/helpers/receipt_extract.py`
  - File uploads to AWS S3 via the `upload_file_aws_s3_bucket` task in `core.tasks`
  - Invoice image preview in `ProcurementPurchaseInoviceImagePreview` view

### 6. Notification system

- **Responsible App**: Core app
- **Key Functions**:
  - Email notifications through `send_email` task in `core.tasks`
  - SMS notifications for disbursements in `SmsDisbursement` view
  - Notification marking as read in `NotificationUpdateSerializer` from `core/views.py`

### 7. Role-based permissions

- **Responsible App**: Core app and Helpers
- **Key Functions**:
  - Custom permissions like `CanDisburse`, `CanEditBudget`, `CanEditTeam`, `CanInitiateTransfer` in `helpers/custom_permissions.py`
  - Authentication through `CustomUserAuthentication` in `core/auth/custom_auth.py`
  - User blacklisting via `IsUSerBlackListed` permission in `core/permissions.py`

This app streamlines expense management while providing transparency, control, and accountability for business spending across your organization.
