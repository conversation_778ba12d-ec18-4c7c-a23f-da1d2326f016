from django.db.models import Q
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from core.auth.custom_auth import CustomUserAuthentication
from helpers.custom_response import Response
from helpers.custom_permissions import (
    CompanyRequiredPermission,
    BranchRequiredPermission,
)
from helpers.reusable_functions import (
    Paginator,
    is_valid_string,
    is_valid_uuid,
)
from invoicing import models, serializers
from invoicing.helpers import enums
from requisition.models import Company, Team, TeamMember
from stock_inventory.models import Branch


invoice_types = [
    enums.InvoiceTypes.DUE,
    enums.InvoiceTypes.OVERDUE,
    enums.InvoiceTypes.PAID,
    enums.InvoiceTypes.PENDING,
]


# Create your view(s) here.
class InvoiceAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.InvoiceSerializer
    summary_serializer = serializers.CustomerInvoiceSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        invoice = models.InvoiceItem.generate_invoice(
            created_by=request.user, **serializer.validated_data
        )
        serializer = serializers.InvoiceDetailsSerializer(instance=invoice)
        data = {
            "details": "invoice registered successfully.",
            "invoice": serializer.data,
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        search = request.query_params.get("search")
        invoice_type = request.query_params.get("invoice_type")

        company_invoices = models.Invoice.objects.filter(company=company_id)
        if branch_id:
            company_invoices = company_invoices.filter(branch=branch_id)
        if search:
            company_invoices = company_invoices.filter(
                Q(reference__icontains=search)
                | Q(customer__name__icontains=search)
                | Q(customer__email__icontains=search)
                | Q(customer__phone__icontains=search)
            )
        if invoice_type and is_valid_string(invoice_type):
            if invoice_type.upper() not in invoice_types:
                return Response(
                    errors={"details": "invalid invoice type provided."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            company_invoices = company_invoices.filter(status=invoice_type.upper())
        paginated_response = Paginator.paginate(
            request=request, queryset=company_invoices
        )
        serializer = self.summary_serializer(instance=paginated_response, many=True)
        data = {
            "details": "success",
            "invoices": serializer.data,
            "count": len(serializer.data),
            "total_invoices": company_invoices.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class InvoiceItemsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.InvoiceRecordSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        reference = request.query_params.get("reference")
        invoice_items = models.Invoice.objects.filter(
            company=company_id, branch=branch_id
        )
        if reference and is_valid_string(reference):
            invoice_items = invoice_items.filter(reference=reference)
        serializer = self.serializer_class(instance=invoice_items, many=True)
        data = {
            "details": serializer.data,
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class TaxAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.TaxSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        tax = models.Tax.add(created_by=request.user, **serializer.validated_data)
        if not tax.get("status"):
            return Response(
                errors={"details": "Tax with this name already exists."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.serializer_class(instance=tax.get("tax"))
        return Response(
            data=serializer.data, status_code=201, status=status.HTTP_201_CREATED
        )

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")

        if company_id is None:
            return Response(
                errors={"details": "provide a valid company ID to view tax rate(s)."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(company_id):
            return Response(
                errors={"details": "invalid ID type provided for company."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        company = Company.retrieve_company(id=company_id)
        if company is None:
            return Response(
                errors={"details": "invalid company ID provided."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        company_rates = models.Tax.objects.filter(company=company)

        if branch_id:
            if not is_valid_uuid(branch_id):
                return Response(
                    errors={"details": "invalid ID type provided for branch."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            branch = Branch.retrieve_company_branch(id=branch_id, company=company)
            if branch is None:
                return Response(
                    errors={"details": "invalid branch ID provided."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            branch_rates = company_rates.filter(branch=branch)
            serializer = self.serializer_class(instance=branch_rates, many=True)
            data = {
                "details": "successfully fetched rates.",
                "rates": serializer.data,
                "count": len(serializer.data),
                "total_rates": branch_rates.count(),
            }
        else:
            serializer = self.serializer_class(instance=company_rates, many=True)
            data = {
                "details": "successfully fetched rates.",
                "rates": serializer.data,
                "count": len(serializer.data),
                "total_rates": company_rates.count(),
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanySummaryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        if company_id is None:
            return Response(
                errors={"details": "provide a valid company ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(company_id):
            return Response(
                errors={"details": "invalid ID type provided for company."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if Company.retrieve_company(id=company_id) is None:
            return Response(
                errors={"details": "invalid company ID provided."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        company_branches = Branch.objects.filter(company=company_id).count()
        company_teams = Team.objects.filter(company=company_id).count()
        company_invoices = models.Invoice.objects.filter(company=company_id)
        total_invoices = company_invoices.count()
        paid_invoices = company_invoices.filter(status=enums.InvoiceTypes.PAID).count()
        pending_invoices = company_invoices.filter(
            status=enums.InvoiceTypes.PENDING
        ).count()
        due_invoices = company_invoices.filter(status=enums.InvoiceTypes.DUE).count()
        data = {
            "branches": company_branches,
            "teams": company_teams,
            "total_invoices": total_invoices,
            "paid_invoices": paid_invoices,
            "pending_invoices": pending_invoices,
            "due_invoices": due_invoices,
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyBranchDetailsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        print(branch_id)

        if company_id is None or branch_id is None:
            return Response(
                errors={"details": "provide a valid company ID and branch ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(company_id) or not is_valid_uuid(branch_id):
            return Response(
                errors={"details": "invalid ID type provided for company or branch."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        company = Company.retrieve_company(id=company_id)
        branch = Branch.retrieve_company_branch(id=branch_id, company=company)
        if company is None or branch is None:
            return Response(
                errors={"details": "invalid company or branch ID provided."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_team_members = (
            TeamMember.objects.filter(
                team__company=company,
                team__branch=branch,
                is_active=True,
            )
            .order_by("member__id")
            .distinct("member")
        )
        members_details = [
            {
                "name": (
                    branch_member.member.full_name
                    if branch_member.member is not None
                    else ""
                ),
                "email": (
                    branch_member.member.email
                    if branch_member.member is not None
                    else ""
                ),
                "role": (
                    branch_member.role if branch_member.member is not None else ""
                ),
            }
            for branch_member in branch_team_members
        ]
        branch_invoices = models.Invoice.objects.filter(company=company, branch=branch)
        total_invoices = branch_invoices.count()
        paid_invoices = branch_invoices.filter(status=enums.InvoiceTypes.PAID).count()
        pending_invoices = branch_invoices.filter(
            status=enums.InvoiceTypes.PENDING
        ).count()
        due_invoices = branch_invoices.filter(status=enums.InvoiceTypes.DUE).count()
        if branch_invoices.exists():
            data = {
                "company": company.company_name,
                "branch": branch.name,
                "vat": branch.vat,
                "total_invoices": total_invoices,
                "paid_invoices": paid_invoices,
                "pending_invoices": pending_invoices,
                "due_invoices": due_invoices,
                "members": members_details,
                "last_update": branch_invoices.first().updated_at.date(),
            }
        else:
            data = {
                "company": company.company_name,
                "branch": branch.name,
                "vat": branch.vat,
                "total_invoices": total_invoices,
                "paid_invoices": paid_invoices,
                "pending_invoices": pending_invoices,
                "due_invoices": due_invoices,
                "members": members_details,
                "last_update": None,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyAnalyticsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"details": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        paid_and_due_analytics = models.Invoice.paid_and_due_analytics(
            company=company_id,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not paid_and_due_analytics.get("status"):
            return Response(
                errors=paid_and_due_analytics,
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.InvoiceMetricsSerializer(
                instance=paid_and_due_analytics.get("details"), many=True
            )
            data = {"details": "success", "analytics": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyBranchComparisonAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"details": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_comparison = models.Invoice.branch_comparison(
            company=company,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not branch_comparison.get("status"):
            return Response(
                errors=branch_comparison,
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.InvoiceMetricsSerializer(
                instance=branch_comparison.get("details"), many=True
            )
            data = {"details": "success", "comparison": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchAnalyticsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"details": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_analytics = models.Invoice.branch_analytics(
            company=company_id,
            branch=branch_id,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not branch_analytics.get("status"):
            return Response(
                errors=branch_analytics,
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.InvoiceMetricsSerializer(
                instance=branch_analytics.get("details"), many=True
            )
            data = {"details": "success", "analytics": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)