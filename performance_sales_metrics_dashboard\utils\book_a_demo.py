from performance_sales_metrics_dashboard.utils.google_calendar_api import check_free_busy_status
from performance_sales_metrics_dashboard.utils.parse_dates import get_time_max
from requisition.models import TeamMember
import json, random, threading
from django.utils import cache

# timeMin = "2024-12-01T17:06:02.000Z"
# start_time = "2024-11-30T10:00:00Z"

def book_demo(
    timeMin,
    start_time,
):
    
    try:
        timeMax = get_time_max(timeMin=timeMin)
        print("TIME_MAX::::", timeMax, "\n\n\n")
        return timeMax
    except Exception as e:
        print("An error while trying to calculate timeMax", str(e))
    
    try:
        sales_officers_instance = TeamMember.objects.all()
        # sales_officers_email = [member.email for member in TeamMember.objects.all()]

        sales_officer_emails = []
        for email in sales_officers_instance:
            sales_officer_emails.append(email.email)
        
        print("SALES_OFFICER_EMAILS::::", sales_officer_emails, "\n\n\n")

        sales_officer_ids = []
        for sales_officer_id in sales_officers_instance:
            sales_officer_ids.append(sales_officer_id.id)
        print("SALES_OFFICER_IDS::::", sales_officer_ids, "\n\n\n")

        calendarIds = []
        for sales_officer_email in sales_officer_emails:
            calendarIds.append({"id": sales_officer_email})
        print("CALENDAR_IDS::::", calendarIds, "\n\n\n")

        str_sales_officers_ids = []
        for i in sales_officer_ids:
            i_str = str(i)
            str_sales_officers_ids.append(i_str)
            continue
        print("DATA_TYPE_OF_ID::::", type(i_str))
        print("DATA_ID_LIST::::", str_sales_officers_ids, "\n\n\n")

        sales_officer_list = []
        for i, j in zip(str_sales_officers_ids, sales_officer_emails):
            sales_officer_list.append({"id": i, "email": j})
        print("SALES_OFFICERS_LIST::::", sales_officer_list)

        threads = []
        for email in sales_officer_emails:
            thread = threading.Thread(target=check_free_busy_status, args=(email, timeMax, timeMin))
            thread.start()
            threads.append(thread)
            dumped_threads = json.dumps(threads)
            return dumped_threads
                                                                                                      
        for thread in threads:
            cache.set(key="thread_results", value=thread, timeout=60*30)
            cached_thread_results = cache.get("thread_results")
            joint_thread = thread.join()
            dumped_thread = json.dumps(joint_thread)
            return dumped_thread

        print("JOINT_THREAD_RESULTS::::", joint_thread, "\n\n\n")
        print("DATA_TYPE_JOINT_THREAD_RESULTS::::", type(joint_thread), "\n\n\n")
        print("CACHED_THREAD_RESULTS::::", cached_thread_results, "\n\n\n")
        print("DATA_TYPE_CACHED_THREAD_RESULTS::::", type(cached_thread_results), "\n\n\n")
    except Exception as e:
        print("An error occured while trying to retrieve sales officers.\n", {"error": str(e)})

    # try:
    #     free_busy_status = check_free_busy_status(calendarIds, timeMax=timeMax, timeMin=timeMin)
    #     dumped_busy_status = json.dumps(free_busy_status)
    #     loaded_busy_status = json.loads(dumped_busy_status)

    #     calendar_status = loaded_busy_status["calendars"]
    #     dumped_calendar_status = json.dumps(calendar_status)
    #     calendar_keys = calendar_status.keys()
    #     calendar_values = calendar_status.values()

    #     free_calendars = []
    #     for email, details in loaded_busy_status["calendars"].items():
    #         if not details["busy"]:
    #             free_calendars.append(email)

    #     print("FREE_CALENDAR::::", free_calendars)
    #     num_of_free_sales_officers = len(free_calendars)
    #     print("NUM_OF_FREE_SALES_OFFICERS::::", len(free_calendars), "\n\n\n")

    #     chosen_sales_officer = random.randint(1, num_of_free_sales_officers)
    #     free_sales_officer = free_calendars[chosen_sales_officer]
    #     print("FREE_SALES_OFFICER::::", free_sales_officer, "\n\n\n")

    #     meeting_attendees = []
    #     meeting_attendees.append(free_sales_officer)
    #     print("MEETING_ATTENDEES::::", meeting_attendees, "\n\n\n")

    #     try:
    #         zoom_meeting = schedule_and_get_zoom_meeting(
    #             start_time=start_time,
    #             meeting_invitees=free_sales_officer,
    #         )

    #         meeting_uuid = zoom_meeting["uuid"]
    #         meeting_id = zoom_meeting["id"]
    #         meeting_host_id = zoom_meeting["host_id"]
    #         meeting_topic = zoom_meeting["topic"]
    #         meeting_time = zoom_meeting["start_time"]
    #         meeting_creation_date = zoom_meeting["created_at"]
    #         meeting_agenda = zoom_meeting["agenda"]
    #         meeting_start_url = zoom_meeting["start_url"]
    #         meeting_join_url = zoom_meeting["join_url"]
    #         meeting_password = zoom_meeting["password"]
    #         meeting_duration = zoom_meeting["duration"]

    #         zoom_meeting_details = {
    #             "meeting_uuid" : meeting_uuid,
    #             "meeting_id" : meeting_id,
    #             "meeting_host_id" : meeting_host_id,
    #             "meeting_topic" : meeting_topic,
    #             "meeting_time" : meeting_time,
    #             "meeting_creation_date" : meeting_creation_date,
    #             "meeting_agenda" : meeting_agenda,
    #             "meeting_start_url" : meeting_start_url,
    #             "meeting_join_url" : meeting_join_url,
    #             "meeting_password" : meeting_password,
    #             "meeting_duration" : meeting_duration,
    #             "meeting_attendees": meeting_attendees,
    #         }

    #         print("ZOOM_MEETING_DETAILS::::", zoom_meeting_details, "\n\n\n")
    #     except Exception as e:
    #         print("An error while attempting to schedule zoom meeeting.", str(e))

    # except Exception as e:
    #     print("An error occured.", str(e))

# book_demo(timeMin, start_time)