# Paybox360 Model Dependencies

This document provides a comprehensive overview of all models in the Paybox360 project, their dependencies, and creation criteria.

## Core App

### BaseModel

- **Description**: Abstract base class used by most models in the system
- **Fields**:
  - `id` (UUIDField, primary key)
  - `created_at` (DateTimeField, auto_now_add)
  - `updated_at` (DateTimeField, auto_now)
- **Dependencies**: None
- **Creation Criteria**: Not directly instantiated (abstract)

### User

- **Description**: Main user model for authentication and user management
- **Inherits From**: Django's AbstractUser
- **Fields**:
  - Standard Django user fields (username, email, password, etc.)
  - `phone_no` (CharField)
  - `gender` (CharField)
  - `date_of_birth` (DateField)
  - `bvn` (CharField)
  - `default_company` (ForeignKey to Company)
  - `default_branch` (ForeignKey to Branch)
  - `secret_question` (CharField)
  - `secret_answer` (CharField)
  - `is_blacklisted` (BooleanField)
  - `is_verified` (BooleanField)
  - `is_active` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
  - `BlackListEntry` (for blacklist status)
- **Third-Party Dependencies**:
  - Django's `AbstractUser`
  - `pytz` (for timezone handling)
- **Creation Criteria**:
  - Valid email address (unique)
  - Password (if authentication required)
  - First name and last name (optional but recommended)
  - Phone number (optional but recommended for KYC)

### ConstantTable

- **Description**: Stores system-wide configuration values
- **Inherits From**: BaseModel
- **Fields**:
  - `otp_expiry_time` (IntegerField)
  - `otp_length` (IntegerField)
  - `to_add_num_bvn` (IntegerField)
  - `send_sms_campaign` (BooleanField)
  - `priority_campaign_sms_charge` (FloatField)
  - `normal_campaign_sms_charge` (FloatField)
  - `company_verification_number` (IntegerField)
  - `multiple_payroll_limit` (IntegerField)
  - `minimum_wage_amount` (FloatField)
  - `sales_team_emails` (JSONField)
  - `asset_useful_life` (PositiveIntegerField)
- **Dependencies**: None
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Only one instance should exist (uses `get_constant_instance()` to ensure this)

### KycDetails

- **Description**: Stores Know Your Customer information for users
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `bvn` (CharField)
  - `bvn_first_name` (CharField)
  - `bvn_last_name` (CharField)
  - `bvn_middle_name` (CharField)
  - `bvn_date_of_birth` (DateField)
  - `bvn_phone_number` (CharField)
  - `bvn_enrollment_bank` (CharField)
  - `bvn_enrollment_branch` (CharField)
  - `bvn_email` (EmailField)
  - `bvn_gender` (CharField)
  - `bvn_image` (TextField)
  - `bvn_nationality` (CharField)
  - `bvn_marital_status` (CharField)
  - `bvn_state_of_origin` (CharField)
  - `bvn_state_of_residence` (CharField)
  - `bvn_lga_of_origin` (CharField)
  - `bvn_lga_of_residence` (CharField)
  - `bvn_address_line_1` (CharField)
  - `bvn_address_line_2` (CharField)
  - `bvn_registration_date` (DateField)
  - `bvn_nin` (CharField)
  - `bvn_watch_listed` (BooleanField)
  - `bvn_response_code` (CharField)
  - `is_verified` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance
  - BVN number (for verification)

### BlackListEntry

- **Description**: Manages blacklisted emails and phone numbers
- **Inherits From**: BaseModel
- **Fields**:
  - `general_black_list` (JSONField)
- **Dependencies**: None
- **Third-Party Dependencies**:
  - JSON handling for blacklist storage
- **Creation Criteria**:
  - Only one instance should exist (uses `get_entry_instance()` to ensure this)

### OTP

- **Description**: Manages One-Time Passwords for authentication
- **Inherits From**: BaseModel
- **Fields**:
  - `type` (CharField)
  - `recipient` (CharField)
  - `otp` (CharField)
  - `expiry_time` (DateTimeField)
  - `is_used` (BooleanField)
- **Dependencies**:
  - `BaseModel` (abstract base class)
- **Third-Party Dependencies**:
  - `pytz` (for timezone handling)
  - Django's password hashing
- **Creation Criteria**:
  - OTP type (e.g., "REGISTRATION")
  - Recipient (email or phone)
  - OTP length and expiry time

### Notification

- **Description**: Stores notifications for users
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `branch` (ForeignKey to Branch)
  - `notification_type` (CharField)
  - `title` (CharField)
  - `message` (TextField)
  - `is_read` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `BaseModel` (abstract base class)
  - `requisition.Company` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid notification type
  - Title and message content

### PaystackPayment

- **Description**: Stores Paystack payment information
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `reference` (CharField)
  - `amount` (DecimalField)
  - `email` (EmailField)
  - `status` (CharField)
  - `payload` (JSONField)
  - `is_verified` (BooleanField)
- **Dependencies**:
  - `BaseModel` (abstract base class)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**:
  - Paystack API integration
  - JSON handling for payload storage
- **Creation Criteria**:
  - Valid Company instance (optional)
  - Payment reference
  - Amount
  - Email of payer

### CampaignSenderId

- **Description**: Manages SMS campaign sender IDs
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `user` (ForeignKey to User)
  - `sender_id` (CharField)
  - `sample_message` (TextField)
  - `is_approved` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid User instance
  - Sender ID (must be approved)
  - Sample message

### CampaignBatch

- **Description**: Manages batches of SMS campaigns
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `sender_id` (ForeignKey to CampaignSenderId)
  - `batch_name` (CharField)
  - `batch_description` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `CampaignSenderId` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid CampaignSenderId instance
  - Batch name

### CampaignMessage

- **Description**: Stores individual SMS campaign messages
- **Inherits From**: BaseModel
- **Fields**:
  - `sender_id` (ForeignKey to CampaignSenderId)
  - `batch` (ForeignKey to CampaignBatch)
  - `recipient` (CharField)
  - `message` (TextField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `CampaignSenderId` (ForeignKey)
  - `CampaignBatch` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid CampaignSenderId instance
  - Valid CampaignBatch instance
  - Recipient phone number
  - Message content

### WaitList

- **Description**: Stores information about users on the waiting list
- **Inherits From**: BaseModel
- **Fields**:
  - `first_name` (CharField)
  - `last_name` (CharField)
  - `email` (EmailField)
  - `phone_number` (CharField)
  - `business_name` (CharField)
  - `business_type` (CharField)
  - `business_size` (CharField)
  - `is_contacted` (BooleanField)
- **Dependencies**: None
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Email (unique)
  - Phone number
  - Business information

### IPWhitelist

- **Description**: Manages IP addresses allowed to access certain APIs
- **Inherits From**: BaseModel
- **Fields**:
  - `ip_address` (CharField)
  - `description` (TextField)
  - `is_active` (BooleanField)
- **Dependencies**: None
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid IP address
  - Description

### CategoryList

- **Description**: Stores categories for various purposes
- **Inherits From**: BaseModel
- **Fields**:
  - `title` (CharField)
  - `description` (TextField)
  - `is_active` (BooleanField)
- **Dependencies**: None
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Title (unique)

### SalesTeamShift

- **Description**: Manages shifts for sales team members
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `branch` (ForeignKey to Branch)
  - `user` (ForeignKey to User)
  - `shift_start` (DateTimeField)
  - `shift_end` (DateTimeField)
  - `is_active` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
  - `User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Branch instance
  - Valid User instance
  - Shift start and end times

### OfflineApplication

- **Description**: Manages offline applications
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `first_name` (CharField)
  - `last_name` (CharField)
  - `email` (EmailField)
  - `phone_number` (CharField)
  - `address` (TextField)
  - `state` (CharField)
  - `lga` (CharField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance
  - Contact information

### Guarantor

- **Description**: Stores guarantor information for users
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `first_name` (CharField)
  - `last_name` (CharField)
  - `email` (EmailField)
  - `phone_number` (CharField)
  - `address` (TextField)
  - `relationship` (CharField)
  - `is_verified` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance
  - Guarantor contact information
  - Relationship to user

## Requisition App

### Company

- **Description**: Represents a company in the system
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company_name` (CharField)
  - `company_email` (EmailField)
  - `company_phone` (CharField)
  - `company_address` (TextField)
  - `company_logo` (TextField)
  - `industry` (CharField)
  - `company_size` (CharField)
  - `company_wallet_type` (CharField)
  - `overall_running_budget` (DecimalField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance (company owner)
  - Company name
  - Industry (optional)
  - Company size (optional)

### Team

- **Description**: Represents a team within a company
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `branch` (ForeignKey to Branch)
  - `team_name` (CharField)
  - `team_type` (CharField)
  - `team_description` (TextField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Company` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Branch instance (optional)
  - Team name
  - Team type

### TeamMember

- **Description**: Represents a member of a team
- **Inherits From**: BaseModel
- **Fields**:
  - `team` (ForeignKey to Team)
  - `user` (ForeignKey to User)
  - `role` (CharField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Team` (ForeignKey)
  - `User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Team instance
  - Valid User instance
  - Role assignment

### Requisition

- **Description**: Represents a requisition request
- **Inherits From**: BaseModel
- **Fields**:
  - `requester` (ForeignKey to User)
  - `approver` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `team` (ForeignKey to Team)
  - `team_member` (ForeignKey to TeamMember)
  - `amount` (DecimalField)
  - `reason` (TextField)
  - `status` (CharField)
  - `category` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey for requester)
  - `User` (ForeignKey for approver)
  - `Company` (ForeignKey)
  - `Team` (ForeignKey)
  - `TeamMember` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance (requester)
  - Valid Company instance
  - Valid Team instance
  - Valid TeamMember instance
  - Request amount
  - Request reason
  - Requisition category

### Budget

- **Description**: Represents a budget for a company or team
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `team` (ForeignKey to Team)
  - `budget_name` (CharField)
  - `budget_amount` (DecimalField)
  - `start_date` (DateField)
  - `end_date` (DateField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Company` (ForeignKey)
  - `Team` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Team instance (optional)
  - Budget name
  - Budget amount
  - Start and end dates

### BudgetAllocation

- **Description**: Represents budget allocation to teams
- **Inherits From**: BaseModel
- **Fields**:
  - `budget` (ForeignKey to Budget)
  - `team` (ForeignKey to Team)
  - `allocation_amount` (DecimalField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Budget` (ForeignKey)
  - `Team` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Budget instance
  - Valid Team instance
  - Allocation amount

### Category

- **Description**: Represents expense categories
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `category_name` (CharField)
  - `category_description` (TextField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Category name

### Expense

- **Description**: Represents an expense
- **Inherits From**: BaseModel
- **Fields**:
  - `requisition` (ForeignKey to Requisition)
  - `category` (ForeignKey to Category)
  - `amount` (DecimalField)
  - `description` (TextField)
  - `date` (DateField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Requisition` (ForeignKey)
  - `Category` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Requisition instance
  - Valid Category instance
  - Expense amount
  - Expense date

### Comment

- **Description**: Represents comments on requisitions
- **Inherits From**: BaseModel
- **Fields**:
  - `requisition` (ForeignKey to Requisition)
  - `user` (ForeignKey to User)
  - `comment` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Requisition` (ForeignKey)
  - `User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Requisition instance
  - Valid User instance
  - Comment text

### TeamMemberInvite

- **Description**: Represents invitations to join a team
- **Inherits From**: BaseModel
- **Fields**:
  - `team` (ForeignKey to Team)
  - `email` (EmailField)
  - `role` (CharField)
  - `status` (CharField)
  - `token` (CharField)
  - `expiry_date` (DateTimeField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Team` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Team instance
  - Email address
  - Role assignment
  - Expiry date

### CompanyVerificationInfo

- **Description**: Stores company verification information
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `rc_number` (CharField)
  - `company_name` (CharField)
  - `company_type` (CharField)
  - `company_address` (TextField)
  - `date_of_registration` (DateField)
  - `is_verified` (BooleanField)
- **Dependencies**:
  - `Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - RC number
  - Company registration information

### CompanyIndustry

- **Description**: Represents industry categories for companies
- **Inherits From**: BaseModel
- **Fields**:
  - `industry_name` (CharField)
  - `industry_description` (TextField)
  - `is_active` (BooleanField)
- **Dependencies**: None
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Industry name (unique)

### UserLinkedBanks

- **Description**: Stores bank accounts linked to users
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `bank_name` (CharField)
  - `account_number` (CharField)
  - `account_name` (CharField)
  - `bank_code` (CharField)
  - `is_default` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance
  - Bank account details

### MonoTopUpRecord

- **Description**: Records of top-ups via Mono
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `amount` (DecimalField)
  - `reference` (CharField)
  - `status` (CharField)
  - `payload` (JSONField)
  - `is_verified` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
  - `Company` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for payload storage
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance
  - Amount
  - Reference

## Stock Inventory App

### Branch

- **Description**: Represents a physical branch of a company
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `name` (CharField)
  - `address` (TextField)
  - `phone` (CharField)
  - `email` (EmailField)
  - `vat` (DecimalField)
  - `created_by` (ForeignKey to User)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Branch name
  - Location information

### Category

- **Description**: Represents product categories
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `name` (CharField)
  - `description` (TextField)
  - `created_by` (ForeignKey to User)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Category name
  - Description (optional)

### SubCategory

- **Description**: Represents product subcategories
- **Inherits From**: BaseModel
- **Fields**:
  - `category` (ForeignKey to Category)
  - `name` (CharField)
  - `description` (TextField)
  - `created_by` (ForeignKey to User)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Category` (ForeignKey)
  - `User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Category instance
  - Subcategory name
  - Description (optional)

### Product

- **Description**: Represents a product in inventory
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `category` (ForeignKey to Category)
  - `subcategory` (ForeignKey to SubCategory, optional)
  - `name` (CharField)
  - `description` (TextField)
  - `sku` (CharField)
  - `barcode` (CharField)
  - `cost_price` (DecimalField)
  - `selling_price` (DecimalField)
  - `reorder_level` (IntegerField)
  - `created_by` (ForeignKey to User)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `Category` (ForeignKey)
  - `SubCategory` (ForeignKey, optional)
  - `User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Category instance
  - Product name
  - SKU (optional but recommended)
  - Description (optional)

### Supplier

- **Description**: Represents a product supplier
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `name` (CharField)
  - `contact_person` (CharField)
  - `phone` (CharField)
  - `email` (EmailField)
  - `address` (TextField)
  - `created_by` (ForeignKey to User)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Supplier name
  - Contact information

### StockDetail

- **Description**: Represents stock details for a product
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `branch` (ForeignKey to Branch)
  - `category` (ForeignKey to Category)
  - `subcategory` (ForeignKey to SubCategory, optional)
  - `product` (ForeignKey to Product)
  - `supplier` (ForeignKey to Supplier, optional)
  - `quantity` (IntegerField)
  - `stock_price` (DecimalField)
  - `selling_price` (DecimalField)
  - `uploaded_by` (ForeignKey to User)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `Branch` (ForeignKey)
  - `Category` (ForeignKey)
  - `SubCategory` (ForeignKey, optional)
  - `Product` (ForeignKey)
  - `Supplier` (ForeignKey, optional)
  - `User` (ForeignKey for uploaded_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Branch instance
  - Valid Category instance
  - Valid Product instance
  - Valid User instance (uploader)
  - Quantity
  - Stock price
  - Selling price

### StockVariant

- **Description**: Represents variants of a product (e.g., size, color)
- **Inherits From**: BaseModel
- **Fields**:
  - `stock` (ForeignKey to StockDetail)
  - `variant_name` (CharField)
  - `variant_value` (CharField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `StockDetail` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid StockDetail instance
  - Variant name and value

### StockUniqueId

- **Description**: Represents unique identifiers for stock items
- **Inherits From**: BaseModel
- **Fields**:
  - `stock` (ForeignKey to StockDetail)
  - `unique_id` (CharField)
  - `is_sold` (BooleanField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `StockDetail` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid StockDetail instance
  - Unique ID

### PurchaseOrder

- **Description**: Represents a purchase order to a supplier
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `branch` (ForeignKey to Branch)
  - `supplier` (ForeignKey to Supplier)
  - `order_number` (CharField)
  - `order_date` (DateField)
  - `expected_delivery_date` (DateField)
  - `status` (CharField)
  - `total_amount` (DecimalField)
  - `created_by` (ForeignKey to User)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `Branch` (ForeignKey)
  - `Supplier` (ForeignKey)
  - `User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Branch instance
  - Valid Supplier instance
  - Order date
  - Expected delivery date

### PurchaseOrderItem

- **Description**: Represents an item in a purchase order
- **Inherits From**: BaseModel
- **Fields**:
  - `purchase_order` (ForeignKey to PurchaseOrder)
  - `product` (ForeignKey to Product)
  - `quantity` (IntegerField)
  - `unit_price` (DecimalField)
  - `total_price` (DecimalField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `PurchaseOrder` (ForeignKey)
  - `Product` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid PurchaseOrder instance
  - Valid Product instance
  - Quantity
  - Unit price

## Account App

### AccountServiceProvider

- **Description**: Represents an account service provider
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `branch` (ForeignKey to Branch)
  - `account_number` (CharField)
  - `account_name` (CharField)
  - `account_provider` (CharField)
  - `payload` (TextField)
- **Dependencies**:
  - `User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for payload storage
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance
  - Account provider
  - Account number and name

### AccountSystem

- **Description**: Represents a financial account in the system
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `branch` (ForeignKey to Branch)
  - `account_provider` (CharField)
  - `account_number` (CharField)
  - `account_name` (CharField)
  - `account_type` (CharField)
  - `bank_name` (CharField)
  - `bank_code` (CharField)
  - `is_test` (BooleanField)
  - `is_active` (BooleanField)
  - `request_status` (CharField)
  - `updated` (BooleanField)
  - `available_balance` (FloatField)
  - `payload` (TextField)
  - `service_provider` (ForeignKey to AccountServiceProvider)
- **Dependencies**:
  - `User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
  - `AccountServiceProvider` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for payload storage
  - VFD Bank API integration
- **Creation Criteria**:
  - Valid User instance
  - Account provider (e.g., VFD, WEMA)
  - Account number (unique)
  - Account type (e.g., SPEND_MGMT)

### Wallet

- **Description**: Represents a user's wallet
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `account` (ForeignKey to AccountSystem)
  - `balance` (FloatField)
  - `previous_balance` (FloatField)
  - `wallet_type` (CharField)
  - `is_active` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
  - `AccountSystem` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance
  - Valid AccountSystem instance
  - Initial balance
  - Wallet type

### DebitCreditRecordOnAccount

- **Description**: Records debit and credit transactions on accounts
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `account` (ForeignKey to AccountSystem)
  - `transaction_type` (CharField)
  - `amount` (FloatField)
  - `reference` (CharField)
  - `description` (TextField)
  - `status` (CharField)
  - `payload` (TextField)
- **Dependencies**:
  - `User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
  - `AccountSystem` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for payload storage
- **Creation Criteria**:
  - Valid User instance
  - Valid AccountSystem instance
  - Transaction type
  - Amount
  - Reference

### Transaction

- **Description**: Represents a financial transaction
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `account` (ForeignKey to AccountSystem)
  - `transaction_type` (CharField)
  - `amount` (FloatField)
  - `reference` (CharField)
  - `description` (TextField)
  - `status` (CharField)
  - `payout_type` (CharField)
  - `transfer_stage` (CharField)
  - `provider_fee` (FloatField)
  - `stamp_duty` (FloatField)
  - `internal_acct_bal` (FloatField)
  - `internal_acct_reversal_bal` (FloatField)
  - `internal_to_outwards_session_id` (CharField)
  - `user_uuid` (CharField)
  - `company_id` (CharField)
  - `float_to_internal` (BooleanField)
  - `internal_to_outwards` (BooleanField)
  - `is_reversed` (BooleanField)
  - `verification_ref` (CharField)
  - `payout_payload` (TextField)
  - `fund_wallet_payload` (TextField)
  - `payout_result` (TextField)
  - `float_verification_result` (TextField)
  - `internal_to_external_payload` (TextField)
  - `internal_to_external_result` (TextField)
  - `internal_verification_result` (TextField)
  - `commission_payload` (TextField)
  - `commission_result` (TextField)
  - `commission_ver_result` (TextField)
- **Dependencies**:
  - `User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
  - `AccountSystem` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for payload storage
- **Creation Criteria**:
  - Valid User instance
  - Valid AccountSystem instance
  - Transaction type
  - Amount
  - Reference

### Beneficiary

- **Description**: Represents a payment beneficiary
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `bank_name` (CharField)
  - `bank_code` (CharField)
  - `account_number` (CharField)
  - `account_name` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance
  - Bank name
  - Account number
  - Account name

### BulkTransfer

- **Description**: Represents a bulk transfer to multiple beneficiaries
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `account` (ForeignKey to AccountSystem)
  - `reference` (CharField)
  - `total_amount` (FloatField)
  - `status` (CharField)
  - `schedule_type` (CharField)
  - `schedule_date` (DateTimeField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
  - `AccountSystem` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance
  - Valid AccountSystem instance
  - Total amount
  - Schedule type and date (if scheduled)

### BulkTransferItem

- **Description**: Represents an item in a bulk transfer
- **Inherits From**: BaseModel
- **Fields**:
  - `bulk_transfer` (ForeignKey to BulkTransfer)
  - `beneficiary` (ForeignKey to Beneficiary)
  - `amount` (FloatField)
  - `reference` (CharField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `BulkTransfer` (ForeignKey)
  - `Beneficiary` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid BulkTransfer instance
  - Valid Beneficiary instance
  - Amount

### AccountCreationFailure

- **Description**: Records failed account creation attempts
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `account_provider` (CharField)
  - `account_type` (CharField)
  - `error_message` (TextField)
  - `payload` (TextField)
  - `is_resolved` (BooleanField)
- **Dependencies**:
  - `User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for payload storage
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance
  - Account provider
  - Account type
  - Error message

### TransactionMetaData

- **Description**: Stores metadata for transactions
- **Inherits From**: BaseModel
- **Fields**:
  - `transaction` (ForeignKey to Transaction)
  - `metadata` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Transaction` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for metadata storage
- **Creation Criteria**:
  - Valid Transaction instance
  - Metadata

### CoreBankingCallback

- **Description**: Stores callbacks from core banking systems
- **Inherits From**: BaseModel
- **Fields**:
  - `provider` (CharField)
  - `reference` (CharField)
  - `payload` (TextField)
  - `is_processed` (BooleanField)
- **Dependencies**: None
- **Third-Party Dependencies**:
  - JSON handling for payload storage
- **Creation Criteria**:
  - Provider name
  - Reference
  - Payload

## Clock App

### AttendanceSheet

- **Description**: Represents attendance sheets for companies
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `title` (CharField)
  - `description` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Title

### Shift

- **Description**: Represents work shifts for employees
- **Inherits From**: BaseModel
- **Fields**:
  - `employee` (ManyToManyField to CompanyEmployeeList)
  - `company` (ForeignKey to Company)
  - `title` (CharField)
  - `shift_resumption_time` (TimeField)
  - `shift_closing_time` (TimeField)
  - `attendance` (ForeignKey to AttendanceSheet)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `payroll_app.CompanyEmployeeList` (ManyToManyField)
  - `requisition.Company` (ForeignKey)
  - `AttendanceSheet` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid AttendanceSheet instance
  - Shift title
  - Resumption and closing times

### Location

- **Description**: Represents work locations
- **Inherits From**: BaseModel
- **Fields**:
  - `employee` (ForeignKey to CompanyEmployeeList)
  - `company` (ForeignKey to Company)
  - `location_type` (CharField)
  - `name` (CharField)
  - `latitude` (FloatField)
  - `longitude` (FloatField)
  - `radius` (FloatField)
  - `branch` (ForeignKey to Branch)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `payroll_app.CompanyEmployeeList` (ForeignKey)
  - `requisition.Company` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Location type (REMOTE or ONSITE)
  - Coordinates (latitude, longitude)
  - Radius (for geofencing)

### Record

- **Description**: Represents attendance records
- **Inherits From**: BaseModel
- **Fields**:
  - `employee` (ForeignKey to CompanyEmployeeList)
  - `company` (ForeignKey to Company)
  - `location` (ForeignKey to Location)
  - `shift` (ForeignKey to Shift)
  - `work_duration` (DurationField)
  - `clock_in_longitude` (FloatField)
  - `clock_in_latitude` (FloatField)
  - `clock_out_longitude` (FloatField)
  - `clock_out_latitude` (FloatField)
  - `location_type` (CharField)
  - `is_late` (BooleanField)
  - `lateness_duration` (DurationField)
  - `break_duration` (DurationField)
  - `is_break_exceeded` (BooleanField)
  - `break_excess` (DurationField)
  - `overtime_duration` (DurationField)
  - `clock_in_time` (TimeField)
  - `clock_out_time` (TimeField)
  - `week_day` (CharField)
  - `is_deleted` (BooleanField)
  - `attendance` (CharField)
- **Dependencies**:
  - `payroll_app.CompanyEmployeeList` (ForeignKey)
  - `requisition.Company` (ForeignKey)
  - `Location` (ForeignKey)
  - `Shift` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Employee instance
  - Valid Company instance
  - Valid Location instance
  - Clock in time and coordinates

### Clock

- **Description**: Represents clock in/out events
- **Inherits From**: BaseModel
- **Fields**:
  - `record` (OneToOneField to Record)
  - `company` (ForeignKey to Company)
  - `clock_in` (TimeField)
  - `clock_out` (TimeField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Record` (OneToOneField)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Record instance
  - Valid Company instance
  - Clock in time

### Break

- **Description**: Represents break periods
- **Inherits From**: BaseModel
- **Fields**:
  - `record` (ForeignKey to Record)
  - `company` (ForeignKey to Company)
  - `break_start_time` (DateTimeField)
  - `break_end_time` (DateTimeField)
  - `break_duration` (DurationField)
  - `start_break_longitude` (FloatField)
  - `start_break_latitude` (FloatField)
  - `end_break_longitude` (FloatField)
  - `end_break_latitude` (FloatField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Record` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Record instance
  - Valid Company instance
  - Break start time and coordinates

### OverTime

- **Description**: Represents overtime periods
- **Inherits From**: BaseModel
- **Fields**:
  - `record` (ForeignKey to Record)
  - `company` (ForeignKey to Company)
  - `overtime_start_time` (DateTimeField)
  - `overtime_end_time` (DateTimeField)
  - `overtime_duration` (DurationField)
  - `start_overtime_longitude` (FloatField)
  - `start_overtime_latitude` (FloatField)
  - `end_overtime_longitude` (FloatField)
  - `end_overtime_latitude` (FloatField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Record` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Record instance
  - Valid Company instance
  - Overtime start time and coordinates

### Task

- **Description**: Represents employee tasks
- **Inherits From**: BaseModel
- **Fields**:
  - `employee` (ForeignKey to CompanyEmployeeList)
  - `company` (ForeignKey to Company)
  - `task_name` (CharField)
  - `start_date` (DateTimeField)
  - `due_date` (DateTimeField)
  - `task_description` (CharField)
  - `status` (CharField)
  - `challenges` (CharField)
  - `achievement` (CharField)
  - `other_information` (CharField)
  - `priority` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `payroll_app.CompanyEmployeeList` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Employee instance
  - Valid Company instance
  - Task name and description
  - Start and due dates
  - Priority

### Screenshot

- **Description**: Represents screenshots taken during work
- **Inherits From**: BaseModel
- **Fields**:
  - `employee` (ForeignKey to CompanyEmployeeList)
  - `company` (ForeignKey to Company)
  - `screenshot` (TextField)
  - `timestamp` (DateTimeField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `payroll_app.CompanyEmployeeList` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Employee instance
  - Valid Company instance
  - Screenshot data
  - Timestamp

### CompanyBranchInvite

- **Description**: Represents invitations to join a branch
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `branch` (ForeignKey to Branch)
  - `email` (EmailField)
  - `token` (CharField)
  - `expiry_date` (DateTimeField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Branch instance
  - Email address
  - Expiry date

### CompanyMeeting

- **Description**: Represents company meetings
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `meeting_name` (CharField)
  - `meeting_link` (CharField)
  - `meeting_employee` (ManyToManyField to CompanyEmployeeList)
  - `meeting_date` (DateField)
  - `meeting_time` (TimeField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `payroll_app.CompanyEmployeeList` (ManyToManyField)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Meeting name
  - Meeting link
  - Meeting date and time

## Leave Management App

### LeaveType

- **Description**: Represents types of leave (e.g., sick leave, vacation)
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `title` (CharField)
  - `is_deleted` (BooleanField)
  - `created_by` (ForeignKey to User)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `core.User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Title
  - Created by (User instance)

### LeavePolicy

- **Description**: Represents leave policies for different grade levels
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `grade_level` (ForeignKey to CompanyPayGradeSettings)
  - `leave_type` (ForeignKey to LeaveType)
  - `monthly_allocation` (PositiveIntegerField)
  - `yearly_allocation` (PositiveIntegerField)
  - `is_deleted` (BooleanField)
  - `created_by` (ForeignKey to User)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `payroll_app.CompanyPayGradeSettings` (ForeignKey)
  - `LeaveType` (ForeignKey)
  - `core.User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Grade Level instance
  - Valid Leave Type instance
  - Monthly or yearly allocation

### LeaveRequest

- **Description**: Represents leave requests from employees
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `department` (ForeignKey to CompanyDepartmentSettings)
  - `employee` (ForeignKey to CompanyEmployeeList)
  - `leave_type` (ForeignKey to LeaveType)
  - `start_date` (DateField)
  - `end_date` (DateField)
  - `approved_start_date` (DateField)
  - `approved_end_date` (DateField)
  - `number_of_days` (PositiveIntegerField)
  - `reason` (CharField)
  - `rejection_reason` (CharField)
  - `other_information` (CharField)
  - `status` (CharField)
  - `leave_days` (PositiveIntegerField)
  - `leave_priority` (CharField)
  - `leave_document` (TextField)
  - `minute` (CharField)
  - `signed_by` (ForeignKey to CompanyEmployeeList)
  - `rejected_by` (ForeignKey to CompanyEmployeeList)
  - `is_taken` (BooleanField)
  - `is_deleted` (BooleanField)
  - `completed_date` (DateTimeField)
  - `leave_days_taken` (IntegerField)
  - `approved_leave_days` (IntegerField)
  - `leave_days_exceeded` (IntegerField)
  - `leave_days_remaining` (IntegerField)
  - `total_leave_days` (IntegerField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `payroll_app.CompanyDepartmentSettings` (ForeignKey)
  - `payroll_app.CompanyEmployeeList` (ForeignKey)
  - `LeaveType` (ForeignKey)
  - `payroll_app.CompanyEmployeeList` (ForeignKey for signed_by)
  - `payroll_app.CompanyEmployeeList` (ForeignKey for rejected_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Employee instance
  - Valid Leave Type instance
  - Start date and end date
  - Reason for leave
  - Priority

### LeaveRecord

- **Description**: Represents leave records for employees
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `department` (ForeignKey to CompanyDepartmentSettings)
  - `employee` (ForeignKey to CompanyEmployeeList)
  - `leave_request` (ForeignKey to LeaveRequest)
  - `leave_days_taken` (IntegerField)
  - `leave_days_exceeded` (IntegerField)
  - `leave_days_remaining` (IntegerField)
  - `total_leave_days` (IntegerField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `payroll_app.CompanyDepartmentSettings` (ForeignKey)
  - `payroll_app.CompanyEmployeeList` (ForeignKey)
  - `LeaveRequest` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Employee instance
  - Valid Leave Request instance
  - Leave days taken
  - Leave days remaining

## Payroll App

### CompanyEmployeeList

- **Description**: Represents employees in a company
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `employee` (ForeignKey to User)
  - `employee_id` (CharField)
  - `employee_first_name` (CharField)
  - `employee_last_name` (CharField)
  - `employee_email` (EmailField)
  - `employee_phone` (CharField)
  - `employee_address` (TextField)
  - `employee_state` (CharField)
  - `employee_lga` (CharField)
  - `employee_gender` (CharField)
  - `employee_date_of_birth` (DateField)
  - `employee_marital_status` (CharField)
  - `employee_branch` (ForeignKey to Branch)
  - `employee_department` (ForeignKey to CompanyDepartmentSettings)
  - `employee_pay_group` (ForeignKey to CompanyPayGroupSettings)
  - `employee_pay_grade` (ForeignKey to CompanyPayGradeSettings)
  - `date_joined` (DateField)
  - `work_type` (CharField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `core.User` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
  - `CompanyDepartmentSettings` (ForeignKey)
  - `CompanyPayGroupSettings` (ForeignKey)
  - `CompanyPayGradeSettings` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid User instance
  - Employee ID
  - First name and last name
  - Email and phone number
  - Department, pay group, and pay grade (optional)

### CompanyDepartmentSettings

- **Description**: Represents departments in a company
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `department_name` (CharField)
  - `department_head` (ForeignKey to CompanyEmployeeList)
  - `department_head_name` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `CompanyEmployeeList` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Department name
  - Department head (optional)

### CompanyPayGroupSettings

- **Description**: Represents pay groups in a company
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `pay_group_name` (CharField)
  - `pay_group_description` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Pay group name
  - Pay group description (optional)

### CompanyPayGradeSettings

- **Description**: Represents pay grades in a company
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `pay_grade_name` (CharField)
  - `pay_grade_description` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Pay grade name
  - Pay grade description (optional)

### PayrollTable

- **Description**: Represents payroll records
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `employee` (ForeignKey to CompanyEmployeeList)
  - `user` (ForeignKey to User)
  - `gross_salary` (DecimalField)
  - `net_salary` (DecimalField)
  - `payment_period` (CharField)
  - `payment_month` (CharField)
  - `payment_year` (CharField)
  - `payment_date` (DateField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `CompanyEmployeeList` (ForeignKey)
  - `core.User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Employee instance
  - Valid User instance
  - Gross salary
  - Payment period, month, and year

### SalaryComponentSettings

- **Description**: Represents salary components
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `component_name` (CharField)
  - `component_description` (TextField)
  - `component_type` (CharField)
  - `calculation_type` (CharField)
  - `calculation_percentage` (DecimalField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Component name
  - Component type
  - Calculation type and percentage

### BenefitComponentSettings

- **Description**: Represents benefit components
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `component_name` (CharField)
  - `component_description` (TextField)
  - `component_type` (CharField)
  - `calculation_type` (CharField)
  - `calculation_percentage` (DecimalField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Component name
  - Component type
  - Calculation type and percentage

### CompanyPayrollSettings

- **Description**: Represents company payroll settings
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `payment_date` (IntegerField)
  - `payment_method` (CharField)
  - `payment_currency` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Payment date
  - Payment method
  - Payment currency

## Finance System App

### CompanyManualBank

- **Description**: Represents manual bank accounts for companies
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `bank_name` (CharField)
  - `account_number` (CharField)
  - `account_name` (CharField)
  - `bank_code` (CharField)
  - `created_by` (ForeignKey to User)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `core.User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Bank name and code
  - Account number and name

### AccountTypeModule

- **Description**: Represents account types for chart of accounts
- **Inherits From**: BaseModel
- **Fields**:
  - `name` (CharField)
  - `description` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**: None
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Name (unique)
  - Description (optional)

### ChartOfAccount

- **Description**: Represents chart of accounts
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `account_name` (CharField)
  - `account_code` (CharField)
  - `account_type` (ForeignKey to AccountTypeModule)
  - `account_description` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `AccountTypeModule` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid AccountTypeModule instance
  - Account name
  - Account code

### Journal

- **Description**: Represents journal entries
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `entry_date` (DateField)
  - `debit_account` (ForeignKey to ChartOfAccount)
  - `credit_account` (ForeignKey to ChartOfAccount)
  - `amount` (DecimalField)
  - `description` (TextField)
  - `reference` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `ChartOfAccount` (ForeignKey for debit_account)
  - `ChartOfAccount` (ForeignKey for credit_account)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid ChartOfAccount instances for debit and credit
  - Entry date
  - Amount
  - Description

### FinancialPeriod

- **Description**: Represents financial periods
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `period_name` (CharField)
  - `start_date` (DateField)
  - `end_date` (DateField)
  - `is_closed` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Period name
  - Start and end dates

### FinancialStatement

- **Description**: Represents financial statements
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `statement_type` (CharField)
  - `period` (ForeignKey to FinancialPeriod)
  - `generated_date` (DateField)
  - `statement_data` (JSONField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `FinancialPeriod` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for statement data
- **Creation Criteria**:
  - Valid Company instance
  - Valid FinancialPeriod instance
  - Statement type
  - Generated date

### Asset

- **Description**: Represents company assets
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `asset_name` (CharField)
  - `asset_description` (TextField)
  - `asset_category` (CharField)
  - `purchase_date` (DateField)
  - `purchase_cost` (DecimalField)
  - `useful_life` (IntegerField)
  - `salvage_value` (DecimalField)
  - `depreciation_method` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Asset name and category
  - Purchase date and cost
  - Useful life

### Depreciation

- **Description**: Represents asset depreciation
- **Inherits From**: BaseModel
- **Fields**:
  - `asset` (ForeignKey to Asset)
  - `depreciation_date` (DateField)
  - `depreciation_amount` (DecimalField)
  - `accumulated_depreciation` (DecimalField)
  - `book_value` (DecimalField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Asset` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Asset instance
  - Depreciation date
  - Depreciation amount

## Performance Sales Metrics Dashboard App

### ProductVerticals

- **Description**: Represents product verticals for sales
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `team` (ForeignKey to Team)
  - `name` (CharField)
  - `description` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `requisition.Team` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Team instance
  - Name
  - Description (optional)

### SalesLead

- **Description**: Represents sales leads
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `team_member` (ForeignKey to TeamMember)
  - `first_name` (CharField)
  - `last_name` (CharField)
  - `email` (EmailField)
  - `phone` (CharField)
  - `product_verticals` (ManyToManyField to ProductVerticals)
  - `performance_data` (JSONField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `core.User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
  - `requisition.TeamMember` (ForeignKey)
  - `ProductVerticals` (ManyToManyField)
- **Third-Party Dependencies**:
  - JSON handling for performance data
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance
  - Valid TeamMember instance (optional)
  - Name and contact information

### SalesOfficer

- **Description**: Represents sales officers
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `sales_lead` (ForeignKey to SalesLead)
  - `first_name` (CharField)
  - `last_name` (CharField)
  - `email` (EmailField)
  - `phone` (CharField)
  - `product_vertical` (ForeignKey to ProductVerticals)
  - `metrics_data` (JSONField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `core.User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
  - `SalesLead` (ForeignKey)
  - `ProductVerticals` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for metrics data
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance
  - Valid SalesLead instance (optional)
  - Name and contact information

### Pipeline

- **Description**: Represents sales pipelines
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `team` (ForeignKey to Team)
  - `name` (CharField)
  - `description` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `requisition.Team` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Team instance
  - Name
  - Description (optional)

### Category

- **Description**: Represents lead categories
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `team` (ForeignKey to Team)
  - `name` (CharField)
  - `description` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `requisition.Team` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Team instance
  - Name
  - Description (optional)

### Stage

- **Description**: Represents pipeline stages
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `team` (ForeignKey to Team)
  - `pipeline` (ForeignKey to Pipeline)
  - `category` (ForeignKey to Category)
  - `name` (CharField)
  - `description` (TextField)
  - `position` (IntegerField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `requisition.Team` (ForeignKey)
  - `Pipeline` (ForeignKey)
  - `Category` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Team instance
  - Valid Pipeline instance
  - Valid Category instance
  - Name
  - Position

### Lead

- **Description**: Represents sales leads
- **Inherits From**: BaseModel
- **Fields**:
  - `sales_officer` (ForeignKey to SalesOfficer)
  - `pipeline` (ForeignKey to Pipeline)
  - `stage` (ForeignKey to Stage)
  - `product_verticals` (ManyToManyField to ProductVerticals)
  - `contact_first_name` (CharField)
  - `contact_last_name` (CharField)
  - `contact_email` (EmailField)
  - `contact_phone` (CharField)
  - `company_name` (CharField)
  - `company_email` (EmailField)
  - `company_phone` (CharField)
  - `company_address` (TextField)
  - `company_website` (URLField)
  - `company_size` (CharField)
  - `company_industry` (CharField)
  - `deal_value` (DecimalField)
  - `deal_currency` (CharField)
  - `expected_close_date` (DateField)
  - `probability` (IntegerField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `SalesOfficer` (ForeignKey)
  - `Pipeline` (ForeignKey)
  - `Stage` (ForeignKey)
  - `ProductVerticals` (ManyToManyField)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid SalesOfficer instance
  - Valid Pipeline instance
  - Valid Stage instance
  - Contact information (name, email, phone)
  - Company information (name, email, phone)

### Activity

- **Description**: Represents lead activities
- **Inherits From**: BaseModel
- **Fields**:
  - `lead` (ForeignKey to Lead)
  - `sales_officer` (ForeignKey to SalesOfficer)
  - `stage` (ForeignKey to Stage)
  - `activity_type` (CharField)
  - `subject` (CharField)
  - `description` (TextField)
  - `start_date` (DateTimeField)
  - `end_date` (DateTimeField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Lead` (ForeignKey)
  - `SalesOfficer` (ForeignKey)
  - `Stage` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Lead instance
  - Valid SalesOfficer instance
  - Valid Stage instance
  - Activity type
  - Subject
  - Start and end dates

### Task

- **Description**: Represents sales tasks
- **Inherits From**: BaseModel
- **Fields**:
  - `lead` (ForeignKey to Lead)
  - `sales_officer` (ForeignKey to SalesOfficer)
  - `task_name` (CharField)
  - `task_description` (TextField)
  - `due_date` (DateField)
  - `priority` (CharField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Lead` (ForeignKey)
  - `SalesOfficer` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Lead instance
  - Valid SalesOfficer instance
  - Task name
  - Due date
  - Priority

### BookADemo

- **Description**: Represents demo bookings
- **Inherits From**: BaseModel
- **Fields**:
  - `lead` (ForeignKey to Lead)
  - `sales_officer` (ForeignKey to SalesOfficer)
  - `demo_date` (DateField)
  - `demo_time` (TimeField)
  - `demo_link` (URLField)
  - `demo_notes` (TextField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Lead` (ForeignKey)
  - `SalesOfficer` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Lead instance
  - Valid SalesOfficer instance
  - Demo date and time
  - Demo link

## Subscription and Invoicing App

### Module

- **Description**: Represents system modules
- **Inherits From**: BaseModel
- **Fields**:
  - `name` (CharField)
  - `description` (TextField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**: None
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Name (unique)
  - Description (optional)

### AccessPath

- **Description**: Represents access paths for modules
- **Inherits From**: BaseModel
- **Fields**:
  - `module` (ForeignKey to Module)
  - `path` (CharField)
  - `description` (TextField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Module` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Module instance
  - Path (URL pattern)
  - Description (optional)

### SubscriptionPlan

- **Description**: Represents subscription plans
- **Inherits From**: BaseModel
- **Fields**:
  - `name` (CharField)
  - `description` (TextField)
  - `price` (DecimalField)
  - `duration` (IntegerField)
  - `duration_unit` (CharField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**: None
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Name (unique)
  - Price
  - Duration and duration unit

### CompanySubscription

- **Description**: Represents company subscriptions
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `subscription_plan` (ForeignKey to SubscriptionPlan)
  - `start_date` (DateField)
  - `end_date` (DateField)
  - `status` (CharField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `SubscriptionPlan` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid SubscriptionPlan instance
  - Start date
  - End date (calculated based on plan duration)

### ModuleSubscription

- **Description**: Represents module subscriptions
- **Inherits From**: BaseModel
- **Fields**:
  - `company_subscription` (ForeignKey to CompanySubscription)
  - `module` (ForeignKey to Module)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `CompanySubscription` (ForeignKey)
  - `Module` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid CompanySubscription instance
  - Valid Module instance

### Invoice

- **Description**: Represents subscription invoices
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `company_subscription` (ForeignKey to CompanySubscription)
  - `invoice_number` (CharField)
  - `amount` (DecimalField)
  - `due_date` (DateField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `CompanySubscription` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid CompanySubscription instance
  - Invoice number
  - Amount
  - Due date

### Payment

- **Description**: Represents invoice payments
- **Inherits From**: BaseModel
- **Fields**:
  - `invoice` (ForeignKey to Invoice)
  - `amount` (DecimalField)
  - `payment_date` (DateField)
  - `payment_method` (CharField)
  - `reference` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Invoice` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Invoice instance
  - Amount
  - Payment date
  - Payment method
  - Reference

## Sales App

### Sale

- **Description**: Represents sales transactions
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `branch` (ForeignKey to Branch)
  - `customer_name` (CharField)
  - `customer_phone` (CharField)
  - `customer_email` (EmailField)
  - `sale_date` (DateField)
  - `total_amount` (DecimalField)
  - `discount_amount` (DecimalField)
  - `tax_amount` (DecimalField)
  - `net_amount` (DecimalField)
  - `payment_method` (CharField)
  - `payment_status` (CharField)
  - `created_by` (ForeignKey to User)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `stock_inventory.Branch` (ForeignKey)
  - `core.User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid Branch instance
  - Customer information
  - Sale date
  - Payment method

### SalesItem

- **Description**: Represents items in a sale
- **Inherits From**: BaseModel
- **Fields**:
  - `sale` (ForeignKey to Sale)
  - `product` (ForeignKey to Product)
  - `quantity` (IntegerField)
  - `unit_price` (DecimalField)
  - `discount` (ForeignKey to Discount)
  - `discount_amount` (DecimalField)
  - `tax_amount` (DecimalField)
  - `total_amount` (DecimalField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Sale` (ForeignKey)
  - `stock_inventory.Product` (ForeignKey)
  - `Discount` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Sale instance
  - Valid Product instance
  - Quantity
  - Unit price

### Discount

- **Description**: Represents discounts
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `name` (CharField)
  - `description` (TextField)
  - `discount_type` (CharField)
  - `discount_value` (DecimalField)
  - `start_date` (DateField)
  - `end_date` (DateField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Name
  - Discount type and value
  - Start and end dates

## Invoicing App

### Invoice

- **Description**: Represents sales invoices
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `customer_name` (CharField)
  - `customer_email` (EmailField)
  - `customer_phone` (CharField)
  - `invoice_number` (CharField)
  - `invoice_date` (DateField)
  - `due_date` (DateField)
  - `total_amount` (DecimalField)
  - `discount_amount` (DecimalField)
  - `tax_amount` (DecimalField)
  - `net_amount` (DecimalField)
  - `status` (CharField)
  - `notes` (TextField)
  - `created_by` (ForeignKey to User)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `core.User` (ForeignKey for created_by)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Customer information
  - Invoice number
  - Invoice date and due date

### InvoiceItem

- **Description**: Represents items in an invoice
- **Inherits From**: BaseModel
- **Fields**:
  - `invoice` (ForeignKey to Invoice)
  - `product` (ForeignKey to Product)
  - `description` (TextField)
  - `quantity` (IntegerField)
  - `unit_price` (DecimalField)
  - `discount_amount` (DecimalField)
  - `tax_amount` (DecimalField)
  - `total_amount` (DecimalField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Invoice` (ForeignKey)
  - `stock_inventory.Product` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Invoice instance
  - Valid Product instance
  - Quantity
  - Unit price

### Payment

- **Description**: Represents invoice payments
- **Inherits From**: BaseModel
- **Fields**:
  - `invoice` (ForeignKey to Invoice)
  - `amount` (DecimalField)
  - `payment_date` (DateField)
  - `payment_method` (CharField)
  - `reference` (CharField)
  - `notes` (TextField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Invoice` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Invoice instance
  - Amount
  - Payment date
  - Payment method

## Cart Management App

### Cart

- **Description**: Represents shopping carts
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `core.User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance

### CartItem

- **Description**: Represents items in a cart
- **Inherits From**: BaseModel
- **Fields**:
  - `cart` (ForeignKey to Cart)
  - `product` (ForeignKey to Product)
  - `quantity` (IntegerField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `Cart` (ForeignKey)
  - `stock_inventory.Product` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Cart instance
  - Valid Product instance
  - Quantity

### Order

- **Description**: Represents orders
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `order_number` (CharField)
  - `order_date` (DateField)
  - `total_amount` (DecimalField)
  - `status` (CharField)
  - `shipping_address` (TextField)
  - `billing_address` (TextField)
  - `payment_method` (CharField)
  - `payment_status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `core.User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance
  - Order number
  - Order date
  - Shipping and billing addresses

## Instant Web App

### InstantWeb

- **Description**: Represents instant web stores
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `name` (CharField)
  - `description` (TextField)
  - `logo` (TextField)
  - `banner` (TextField)
  - `theme_color` (CharField)
  - `domain` (CharField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Name
  - Domain

### Category

- **Description**: Represents product categories in instant web
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `instant_web` (ForeignKey to InstantWeb)
  - `name` (CharField)
  - `description` (TextField)
  - `image` (TextField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `InstantWeb` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid InstantWeb instance
  - Name

### Item

- **Description**: Represents products in instant web
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `instant_web` (ForeignKey to InstantWeb)
  - `category` (ForeignKey to Category)
  - `product` (ForeignKey to Product)
  - `name` (CharField)
  - `description` (TextField)
  - `price` (DecimalField)
  - `discount_price` (DecimalField)
  - `image` (TextField)
  - `is_featured` (BooleanField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `InstantWeb` (ForeignKey)
  - `Category` (ForeignKey)
  - `stock_inventory.Product` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid InstantWeb instance
  - Valid Category instance
  - Valid Product instance
  - Name
  - Price

## Link Shortener App

### ShortLink

- **Description**: Represents shortened links
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `user` (ForeignKey to User)
  - `original_url` (URLField)
  - `short_code` (CharField)
  - `title` (CharField)
  - `expiry_date` (DateField)
  - `is_active` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `core.User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid User instance
  - Original URL
  - Title

### ClickAnalytics

- **Description**: Represents click analytics for short links
- **Inherits From**: BaseModel
- **Fields**:
  - `short_link` (ForeignKey to ShortLink)
  - `ip_address` (CharField)
  - `user_agent` (TextField)
  - `referrer` (URLField)
  - `country` (CharField)
  - `city` (CharField)
  - `device` (CharField)
  - `browser` (CharField)
  - `os` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `ShortLink` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid ShortLink instance
  - IP address
  - User agent

## Performance Management App

### PerformanceReview

- **Description**: Represents performance reviews
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `employee` (ForeignKey to User)
  - `reviewer` (ForeignKey to User)
  - `review_period` (CharField)
  - `start_date` (DateField)
  - `end_date` (DateField)
  - `status` (CharField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `core.User` (ForeignKey for employee)
  - `core.User` (ForeignKey for reviewer)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid User instances for employee and reviewer
  - Review period
  - Start and end dates

### Goal

- **Description**: Represents employee goals
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `employee` (ForeignKey to User)
  - `title` (CharField)
  - `description` (TextField)
  - `start_date` (DateField)
  - `end_date` (DateField)
  - `priority` (CharField)
  - `status` (CharField)
  - `progress` (IntegerField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `core.User` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid User instance
  - Title
  - Start and end dates
  - Priority

### Feedback

- **Description**: Represents performance feedback
- **Inherits From**: BaseModel
- **Fields**:
  - `company` (ForeignKey to Company)
  - `employee` (ForeignKey to User)
  - `reviewer` (ForeignKey to User)
  - `performance_review` (ForeignKey to PerformanceReview)
  - `feedback` (TextField)
  - `rating` (IntegerField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `requisition.Company` (ForeignKey)
  - `core.User` (ForeignKey for employee)
  - `core.User` (ForeignKey for reviewer)
  - `PerformanceReview` (ForeignKey)
- **Third-Party Dependencies**: None
- **Creation Criteria**:
  - Valid Company instance
  - Valid User instances for employee and reviewer
  - Valid PerformanceReview instance
  - Feedback text
  - Rating

## Dashboard App

### DashboardConfiguration

- **Description**: Represents dashboard configurations
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `name` (CharField)
  - `layout` (JSONField)
  - `is_default` (BooleanField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `core.User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for layout
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance
  - Name
  - Layout configuration

### Widget

- **Description**: Represents dashboard widgets
- **Inherits From**: BaseModel
- **Fields**:
  - `dashboard` (ForeignKey to DashboardConfiguration)
  - `widget_type` (CharField)
  - `title` (CharField)
  - `configuration` (JSONField)
  - `position` (JSONField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `DashboardConfiguration` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for configuration and position
- **Creation Criteria**:
  - Valid DashboardConfiguration instance
  - Widget type
  - Title
  - Configuration
  - Position

### Report

- **Description**: Represents custom reports
- **Inherits From**: BaseModel
- **Fields**:
  - `user` (ForeignKey to User)
  - `company` (ForeignKey to Company)
  - `name` (CharField)
  - `description` (TextField)
  - `report_type` (CharField)
  - `configuration` (JSONField)
  - `schedule` (CharField)
  - `last_run` (DateTimeField)
  - `is_deleted` (BooleanField)
- **Dependencies**:
  - `core.User` (ForeignKey)
  - `requisition.Company` (ForeignKey)
- **Third-Party Dependencies**:
  - JSON handling for configuration
- **Creation Criteria**:
  - Valid User instance
  - Valid Company instance
  - Name
  - Report type
  - Configuration

## Cross-App Creation Dependencies

Here's a hierarchical view of the model creation dependencies across the entire project:

1. **User** (core app)

   - Required for most other models
   - Email must be unique
   - Password required for authentication

2. **Company** (requisition app)

   - Requires a valid User (owner)
   - Company name must be unique
   - Required for most company-specific models

3. **Branch** (stock_inventory app)

   - Requires a valid Company
   - Required for inventory and team location

4. **Team** (requisition app)

   - Requires valid Company and Branch
   - Required for team-based models

5. **TeamMember** (requisition app)

   - Requires valid Team and User
   - Required for user role assignment

6. **AccountSystem** (account app)

   - Requires valid User and Company
   - Required for financial transactions

7. **Wallet** (account app)

   - Requires valid User and AccountSystem
   - Required for balance management

8. **CompanyEmployeeList** (payroll app)

   - Requires valid Company and User
   - Required for employee management

9. **Category/Product** (stock_inventory app)

   - Requires valid Company
   - Required for inventory management

10. **StockDetail** (stock_inventory app)

    - Requires valid Company, Branch, Category, Product, and User
    - Required for inventory tracking

11. **Record/Clock** (clock app)

    - Requires valid Employee, Company, and Location
    - Required for attendance tracking

12. **LeaveType/LeavePolicy** (leave_management app)

    - Requires valid Company and User
    - Required for leave management

13. **SalesLead/SalesOfficer** (performance_sales_metrics_dashboard app)

    - Requires valid User, Company, and TeamMember
    - Required for sales pipeline management

14. **Pipeline/Stage** (performance_sales_metrics_dashboard app)

    - Requires valid Company and Team
    - Required for lead management

15. **Lead** (performance_sales_metrics_dashboard app)

    - Requires valid SalesOfficer, Pipeline, and Stage
    - Required for customer acquisition tracking

16. **CompanySubscription** (subscription_and_invoicing app)
    - Requires valid Company and SubscriptionPlan
    - Controls feature access across the system

## Global Creation Criteria

For any model in the system, these criteria generally apply:

1. **Authentication**: User must be authenticated to create most models
2. **Authorization**: User must have appropriate permissions based on role
3. **Company Context**: Most models require a valid Company context
4. **Data Validation**: Field-specific validation rules must be satisfied
5. **Subscription Status**: Company must have an active subscription for the relevant module
6. **Soft Deletion**: Models with is_deleted=True are considered non-existent for most operations
7. **Active Status**: Models with is_active=False are considered inactive but still exist
