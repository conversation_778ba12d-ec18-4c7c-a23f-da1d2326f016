from datetime import datetime, timedelta, time
from django.utils import timezone
import calendar
import haversine as hs
from haversine import Unit
import math

def filter_dates(datetime):
    inception = datetime(2023, 1, 1)

    today = timezone.now()

    data = {
        "inception": inception,
        "today": today + timedelta(days=1)
    }

    return data

def calculate_distance(lat1, lon1, lat2, lon2):
    location_1 = (lat1, lon1)
    location_2 = (lat2, lon2)
    distance = hs.haversine(location_1, location_2, unit=Unit.METERS)
    return distance

# if __name__ == "__main__":
#     distance = calculate_distance(
#         6.5078744,	
#         3.3848194,
#         6.4945759,
#         3.3489769
#         )
#     print(distance)

#### get metres location from lon and lat
def get_distance(lon1, lat1, lon2, lat2):
    # distance between latitudes
    # and longitudes
    dLat = (lat2 - lat1) * math.pi / 180.0
    dLon = (lon2 - lon1) * math.pi / 180.0
 
    # convert to radians
    lat1 = (lat1) * math.pi / 180.0
    lat2 = (lat2) * math.pi / 180.0
 
    # apply formulae
    a = (pow(math.sin(dLat / 2), 2) + pow(math.sin(dLon / 2), 2) * math.cos(lat1) * math.cos(lat2))
    rad = 6371
    c = 2 * math.asin(math.sqrt(a))
    distance = rad * c * 1000
    return distance

### get the working days in a particular month
def get_workdays_in_month():
    now = timezone.localtime()  # Get the current date and time in the local timezone
    year = now.year
    month = now.month

    # Get the number of days in the month
    num_days_in_month = calendar.monthrange(year, month)[1]

    # Count the number of weekdays (workdays) in the month
    num_workdays = 0
    for day in range(1, num_days_in_month + 1):
        # Check if the day is a weekday (0: Monday, 6: Sunday)
        if calendar.weekday(year, month, day) < 5:
            num_workdays += 1

    return num_workdays

def add_minutes_to_time(input_time, minutes):
    # Define the time object
    original_time = input_time  # Example time: 8:00 AM

    # Create a timedelta object representing 10 minutes
    added_minutes = timedelta(minutes=minutes)

    # Add the timedelta to the original time
    new_time = datetime.combine(datetime.today(), original_time) + added_minutes

    # Extract the time portion from the datetime object
    new_time_only = new_time.time()

    return new_time_only

def sort_by_total_days_late(item):
    return item["Total Days Late"]

class AllDateFilter:
    filter_date = filter_dates(datetime=datetime)
    inception = filter_date.get("inception")
    today = filter_date.get("today")

    @classmethod
    def get_date_filter(cls, request):
        filter = request.query_params.get("filter")

        specific_day = request.query_params.get("specific_day")
        if specific_day is not None:
            specific_day = datetime.strptime(specific_day, "%Y-%m-%d")
            cls.date_filter = {"date_created__date": specific_day}
            cls.date_filter_two = {"created_at__date": specific_day}
            cls.date_filter_three = {"updated_at__date": specific_day}
            return {"date_filter": cls.date_filter,
                    "date_filter_two": cls.date_filter_two,
                    "date_filter_three": cls.date_filter_three}

        if filter is not None:
            cls.date_filter = {"date_created__date": timezone.now().date()}
            cls.date_filter_two = {"created_at__date": timezone.now().date()}
            cls.date_filter_three = {"updated_at__date": timezone.now().date()}
            return {"date_filter": cls.date_filter, 
                    "date_filter_two": cls.date_filter_two,
                    'date_filter_three': cls.date_filter_three}
        else:
            cls.date_filter = {"date_created__date__gte": cls.inception}
            cls.date_filter_two = {"created_at__date__gte": cls.inception}
            cls.date_filter_three = {"updated_at__date__gte": cls.inception}
            return {"date_filter": cls.date_filter, 
                    "date_filter_two": cls.date_filter_two,
                    'date_filter_three': cls.date_filter_three}