from django.core.management import BaseCommand
from account.enums import AccountType
from account.models import Wallet
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = "Create a VFD wallet for a user."

    def add_arguments(self, parser):
        parser.add_argument(
            "user_id",
            type=str,
            help="The unique identifier for the user.",
        )
        parser.add_argument(
            "account_type",
            type=str,
            choices=[account_type.value for account_type in AccountType],
            help="The type of account being created.",
        )

    def handle(self, *args, **kwargs):
        user_id = kwargs["user_id"]
        account_type_value = kwargs["account_type"]

        # Check if the user exists
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Error: User with ID '{user_id}' does not exist.")
            )
            return

        # Check if the account type exists
        try:
            account_type = AccountType(account_type_value)
        except ValueError:
            self.stdout.write(
                self.style.ERROR(
                    f"Error: Account type '{account_type_value}' does not exist."
                )
            )
            return

        result = Wallet.create_vfd_individual_wallet(
            user_id=user_id,
            account_type=account_type,
        )

        self.stdout.write(self.style.SUCCESS(f"Result: -> {result}."))
