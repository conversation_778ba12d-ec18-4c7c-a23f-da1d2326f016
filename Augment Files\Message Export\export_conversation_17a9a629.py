#!/usr/bin/env python
"""
Export Conversation 17a9a629-ba58-44e2-a9f6-90c6b548e7b4

This script exports the conversation with ID 17a9a629-ba58-44e2-a9f6-90c6b548e7b4 to a file.
"""

import os
import json
import datetime
from pathlib import Path

# Configuration
CONVERSATION_ID = "17a9a629-ba58-44e2-a9f6-90c6b548e7b4"
OUTPUT_DIR = "Augment Files/Conversations"
OUTPUT_FORMATS = ["json", "md", "txt"]  # Export in multiple formats

def ensure_directory_exists(directory):
    """Ensure the output directory exists."""
    Path(directory).mkdir(parents=True, exist_ok=True)

def find_conversation(conversation_id):
    """
    Find a conversation by its ID.
    
    This is a placeholder function. In a real implementation, this would query
    a database or API to retrieve the conversation data.
    """
    # Placeholder for demonstration purposes
    # In a real implementation, you would query your data source
    
    # Example conversation data structure for the specific ID
    conversation = {
        "id": conversation_id,
        "timestamp": datetime.datetime.now().isoformat(),
        "title": "Model Documentation Discussion",
        "participants": ["User", "Assistant"],
        "messages": [
            {
                "sender": "User",
                "content": "I need comprehensive documentation of model dependencies and creation criteria for all models in the project.",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=30)).isoformat()
            },
            {
                "sender": "Assistant",
                "content": "I'll help you create comprehensive model documentation. What specific aspects of the models would you like me to focus on?",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=29)).isoformat()
            },
            {
                "sender": "User",
                "content": "I'd like documentation that groups fields by field type, lists functions with their line numbers and where they're called, and documents third-party integrations with their purpose.",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=25)).isoformat()
            },
            {
                "sender": "Assistant",
                "content": "That sounds like a thorough approach. Would you prefer the documentation to be more technical or accessible to non-technical stakeholders?",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=24)).isoformat()
            },
            {
                "sender": "User",
                "content": "I'd like it to be simple and readable for non-technical people while still being understandable by technical people.",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=20)).isoformat()
            },
            {
                "sender": "Assistant",
                "content": "I understand. I'll create documentation that balances technical accuracy with readability for non-technical users. I'll focus on model dependencies, field groupings by type, function listings with line numbers and call references, and third-party integration documentation.",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=19)).isoformat()
            }
        ]
    }
    
    return conversation

def export_as_json(conversation, output_file):
    """Export conversation as JSON."""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(conversation, f, indent=2)
    return output_file

def export_as_text(conversation, output_file):
    """Export conversation as plain text."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"Conversation ID: {conversation['id']}\n")
        f.write(f"Title: {conversation.get('title', 'Untitled')}\n")
        f.write(f"Timestamp: {conversation['timestamp']}\n")
        f.write(f"Participants: {', '.join(conversation['participants'])}\n\n")
        
        for message in conversation['messages']:
            f.write(f"{message['sender']} ({message['timestamp']}):\n")
            f.write(f"{message['content']}\n\n")
    return output_file

def export_as_markdown(conversation, output_file):
    """Export conversation as Markdown."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"# {conversation.get('title', 'Conversation')} ({conversation['id']})\n\n")
        f.write(f"**Timestamp:** {conversation['timestamp']}\n")
        f.write(f"**Participants:** {', '.join(conversation['participants'])}\n\n")
        
        for message in conversation['messages']:
            f.write(f"### {message['sender']} ({message['timestamp']})\n\n")
            f.write(f"{message['content']}\n\n")
    return output_file

def main():
    """Main function to export the conversation."""
    # Ensure output directory exists
    ensure_directory_exists(OUTPUT_DIR)
    
    # Find the conversation
    try:
        conversation = find_conversation(CONVERSATION_ID)
    except Exception as e:
        print(f"Error finding conversation: {e}")
        return
    
    # Export the conversation in all specified formats
    for output_format in OUTPUT_FORMATS:
        output_file = os.path.join(OUTPUT_DIR, f"conversation_{CONVERSATION_ID}.{output_format}")
        
        try:
            if output_format == 'json':
                export_as_json(conversation, output_file)
            elif output_format == 'txt':
                export_as_text(conversation, output_file)
            elif output_format == 'md':
                export_as_markdown(conversation, output_file)
            
            print(f"Conversation exported to {output_file}")
        except Exception as e:
            print(f"Error exporting conversation to {output_format}: {e}")

if __name__ == "__main__":
    main()
