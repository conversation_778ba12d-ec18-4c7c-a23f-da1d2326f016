# Paybox360 App and Model Dependency Tree

## App-Level Dependency Tree

```
core
├── Used by all other apps (User model is fundamental)
│
requisition
├── Depends on: core
├── Used by: stock_inventory, payroll_app, performance_sales_metrics_dashboard,
│            clock_app, leave_management, finance_system, account, accounting
│
stock_inventory
├── Depends on: core, requisition
├── Used by: sales_app, instant_web, clock_app
│
account
├── Depends on: core, requisition, stock_inventory
├── Used by: finance_system, payroll_app
│
payroll_app
├── Depends on: core, requisition
├── Used by: clock_app, leave_management, performance_management
│
performance_sales_metrics_dashboard
├── Depends on: core, requisition
│
clock_app
├── Depends on: core, requisition, payroll_app, stock_inventory
│
leave_management
├── Depends on: core, requisition, payroll_app
│
finance_system
├── Depends on: core, requisition, account
│
accounting
├── Depends on: core, requisition
│
subscription_and_invoicing
├── Depends on: core, requisition
├── Used by: All apps (via middleware for access control)
│
sales_app
├── Depends on: core, requisition, stock_inventory
│
invoicing
├── Depends on: core, requisition, sales_app
│
cart_management
├── Depends on: core, requisition, stock_inventory
│
instant_web
├── Depends on: core, requisition, stock_inventory
│
linkshortener
├── Depends on: core
│
performance_management
├── Depends on: core, requisition, payroll_app
│
dashboard
├── Depends on: Multiple apps for data visualization
```

## Detailed Model-Level Dependency Tree

```
core
├── User
│   ├── Used by almost all models across all apps
│   ├── Referenced by: requisition.Company, stock_inventory.Branch, account.AccountSystem, etc.
│
├── BaseModel (abstract)
│   ├── Parent class for most models across the system
│
├── ConstantTable
│   ├── Used by various apps for system-wide configuration
│
├── KycDetails
│   ├── Depends on: User
│
├── BlackListEntry
│   ├── Used by: User (for blacklist status checks)
│
├── OTP
│   ├── Independent model for authentication
│
├── Notification
│   ├── Depends on: requisition.Company, stock_inventory.Branch
│
├── PaystackPayment
│   ├── Depends on: requisition.Company
│
├── CampaignSenderId, CampaignBatch, CampaignMessage
│   ├── Depends on: requisition.Company, User
│
requisition
├── Company
│   ├── Depends on: core.User
│   ├── Referenced by: Most models across all apps
│
├── Team
│   ├── Depends on: Company, stock_inventory.Branch
│   ├── Referenced by: TeamMember, performance_sales_metrics_dashboard models
│
├── TeamMember
│   ├── Depends on: Team, core.User
│   ├── Referenced by: performance_sales_metrics_dashboard.SalesLead
│
├── Budget, BudgetAllocation
│   ├── Depends on: Company, Team
│
├── Requisition
│   ├── Depends on: Company, Team, TeamMember, core.User
│
stock_inventory
├── Branch
│   ├── Depends on: requisition.Company
│   ├── Referenced by: requisition.Team, account.AccountSystem, clock_app.Location
│
├── Category, SubCategory
│   ├── Depends on: requisition.Company
│   ├── Referenced by: Product, StockDetail
│
├── Product
│   ├── Depends on: requisition.Company, Category, SubCategory
│   ├── Referenced by: StockDetail, sales_app.SalesItem, instant_web.Item
│
├── StockDetail
│   ├── Depends on: requisition.Company, Branch, Category, Product, core.User
│   ├── Referenced by: StockVariant, StockUniqueId
│
├── Supplier
│   ├── Depends on: requisition.Company
│   ├── Referenced by: StockDetail, PurchaseOrder
│
account
├── AccountSystem
│   ├── Depends on: core.User, requisition.Company, stock_inventory.Branch
│   ├── Referenced by: Wallet, Transaction, DebitCreditRecordOnAccount
│
├── Wallet
│   ├── Depends on: core.User, AccountSystem
│   ├── Referenced by: Transaction operations
│
├── Transaction
│   ├── Depends on: core.User, requisition.Company, AccountSystem
│
├── DebitCreditRecordOnAccount
│   ├── Depends on: core.User, requisition.Company, AccountSystem
│
├── Beneficiary
│   ├── Depends on: core.User, requisition.Company
│
payroll_app
├── CompanyEmployeeList
│   ├── Depends on: requisition.Company, core.User
│   ├── Referenced by: clock_app models, leave_management models
│
├── CompanyDepartmentSettings
│   ├── Depends on: requisition.Company
│   ├── Referenced by: CompanyEmployeeList, leave_management.LeaveRequest
│
├── CompanyPayGroupSettings, CompanyPayGradeSettings
│   ├── Depends on: requisition.Company
│   ├── Referenced by: CompanyEmployeeList, leave_management.LeavePolicy
│
├── PayrollTable
│   ├── Depends on: requisition.Company, core.User, CompanyEmployeeList
│
├── BenefitComponentSettings, SalaryComponentSettings
│   ├── Depends on: requisition.Company
│
performance_sales_metrics_dashboard
├── SalesLead
│   ├── Depends on: core.User, requisition.Company, requisition.TeamMember
│   ├── Referenced by: SalesOfficer
│
├── SalesOfficer
│   ├── Depends on: core.User, requisition.Company, SalesLead
│   ├── Referenced by: Lead, Activity, BookADemo
│
├── ProductVerticals
│   ├── Depends on: requisition.Company, requisition.Team
│   ├── Referenced by: SalesLead, SalesOfficer, Lead
│
├── Pipeline
│   ├── Depends on: requisition.Company, requisition.Team
│   ├── Referenced by: Stage, Lead
│
├── Stage
│   ├── Depends on: requisition.Company, requisition.Team, Pipeline, Category
│   ├── Referenced by: Lead, Activity
│
├── Lead
│   ├── Depends on: SalesOfficer, Pipeline, Stage
│   ├── Referenced by: Activity, Task, BookADemo
│
clock_app
├── Record
│   ├── Depends on: payroll_app.CompanyEmployeeList, requisition.Company, Location, Shift
│   ├── Referenced by: Clock, Break, OverTime
│
├── Clock
│   ├── Depends on: Record, requisition.Company
│
├── Break, OverTime
│   ├── Depends on: Record, requisition.Company
│
├── Location
│   ├── Depends on: payroll_app.CompanyEmployeeList, requisition.Company, stock_inventory.Branch
│   ├── Referenced by: Record
│
├── Task, Screenshot
│   ├── Depends on: payroll_app.CompanyEmployeeList, requisition.Company
│
├── Shift
│   ├── Depends on: payroll_app.CompanyEmployeeList, requisition.Company, AttendanceSheet
│   ├── Referenced by: Record
│
├── AttendanceSheet
│   ├── Depends on: requisition.Company
│   ├── Referenced by: Shift
│
leave_management
├── LeaveType
│   ├── Depends on: requisition.Company, core.User
│   ├── Referenced by: LeavePolicy, LeaveRequest
│
├── LeavePolicy
│   ├── Depends on: requisition.Company, payroll_app.CompanyPayGradeSettings, LeaveType
│
├── LeaveRequest
│   ├── Depends on: requisition.Company, payroll_app.CompanyDepartmentSettings,
│   │                payroll_app.CompanyEmployeeList, LeaveType
│   ├── Referenced by: LeaveRecord
│
├── LeaveRecord
│   ├── Depends on: requisition.Company, payroll_app.CompanyDepartmentSettings,
│                    payroll_app.CompanyEmployeeList, LeaveRequest
│
finance_system
├── CompanyManualBank
│   ├── Depends on: requisition.Company, core.User
│
├── AccountType
│   ├── Independent model
│   ├── Referenced by: ChartOfAccount
│
├── ChartOfAccount
│   ├── Depends on: requisition.Company, AccountType
│   ├── Referenced by: Journal
│
├── Journal
│   ├── Depends on: requisition.Company, ChartOfAccount
│
accounting
├── AccountType
│   ├── Depends on: core.BaseModel
│   ├── Referenced by: Account
│
├── Account
│   ├── Depends on: AccountType, requisition.Company, core.User
│   ├── Referenced by: JournalLine
│
├── JournalEntry
│   ├── Depends on: requisition.Company, core.User
│   ├── Referenced by: JournalLine
│
├── JournalLine
│   ├── Depends on: JournalEntry, Account
│
subscription_and_invoicing
├── CompanySubscription
│   ├── Depends on: requisition.Company, SubscriptionPlan
│   ├── Referenced by: ModuleSubscription
│
├── ModuleSubscription
│   ├── Depends on: CompanySubscription, Module
│
├── AccessPath
│   ├── Depends on: Module
│   ├── Used by: SubscriptionAccessMiddleware
│
sales_app
├── Sale
│   ├── Depends on: requisition.Company, stock_inventory.Branch, core.User
│   ├── Referenced by: SalesItem
│
├── SalesItem
│   ├── Depends on: Sale, stock_inventory.Product
│
├── Discount
│   ├── Depends on: requisition.Company
│   ├── Referenced by: SalesItem
│
invoicing
├── Invoice
│   ├── Depends on: requisition.Company
│   ├── Referenced by: InvoiceItem, Payment
│
├── InvoiceItem
│   ├── Depends on: Invoice, stock_inventory.Product
│
├── Payment
│   ├── Depends on: Invoice
│
cart_management
├── Cart
│   ├── Depends on: core.User, requisition.Company
│   ├── Referenced by: CartItem
│
├── CartItem
│   ├── Depends on: Cart, stock_inventory.Product
│
├── Order
│   ├── Depends on: core.User, requisition.Company
│
instant_web
├── InstantWeb
│   ├── Depends on: requisition.Company
│   ├── Referenced by: Category, Item
│
├── Category
│   ├── Depends on: requisition.Company, InstantWeb
│   ├── Referenced by: Item
│
├── Item
│   ├── Depends on: requisition.Company, InstantWeb, Category, stock_inventory.Product
│
linkshortener
├── ShortLink
│   ├── Depends on: requisition.Company, core.User
│   ├── Referenced by: ClickAnalytics
│
├── ClickAnalytics
│   ├── Depends on: ShortLink
│
performance_management
├── PerformanceReview
│   ├── Depends on: requisition.Company, core.User
│   ├── Referenced by: Feedback
│
├── Goal
│   ├── Depends on: requisition.Company, core.User
│
├── Feedback
│   ├── Depends on: requisition.Company, core.User, PerformanceReview
│
dashboard
├── DashboardConfiguration
│   ├── Depends on: core.User, requisition.Company
│   ├── Referenced by: Widget
│
├── Widget
│   ├── Depends on: DashboardConfiguration
│
├── Report
│   ├── Depends on: core.User, requisition.Company
```

## Key Dependency Chains

1. **User → Company → Everything Else**

   - Most critical dependency chain in the system
   - Almost all models depend on either User or Company or both

2. **Company → Branch → Team → TeamMember**

   - Organizational structure dependency chain
   - Many models depend on this chain for context

3. **Company → Employee → Clock/Leave Records**

   - Employee management dependency chain
   - HR-related functionality depends on this chain

4. **Company → Product → Stock → Sales**

   - Inventory and sales dependency chain
   - E-commerce functionality depends on this chain

5. **User → AccountSystem → Wallet → Transactions**
   - Financial dependency chain
   - Payment and financial operations depend on this chain

## Cross-Cutting Dependencies

1. **BaseModel**

   - Abstract base class used by most models
   - Provides common fields like UUID, created_at, updated_at

2. **Subscription System**

   - Controls access to features across all apps
   - Creates implicit dependencies through middleware

3. **Company Context**
   - Almost all models have a company field
   - Multi-tenant isolation depends on this

## Potential Dependency Issues

1. **Circular Dependencies**

   - Team depends on Branch, Branch depends on Company, Company is related to Team
   - These circular references are managed through Django's string references

2. **Deep Dependency Chains**

   - Some models have very deep dependency chains (5+ levels)
   - Changes to fundamental models can have wide-ranging impacts

3. **Cross-App Dependencies**
   - Many models depend on models from other apps
   - Requires careful management of app loading order and migrations

## Model Creation Order

For proper system initialization, models should generally be created in this order:

1. User (core)
2. Company (requisition)
3. Branch (stock_inventory)
4. Team (requisition)
5. TeamMember (requisition)
6. AccountSystem (account)
7. Wallet (account)
8. CompanyEmployeeList (payroll_app)
9. Product-related models (stock_inventory)
10. Sales and financial models

This order ensures that dependencies are satisfied before dependent models are created.
