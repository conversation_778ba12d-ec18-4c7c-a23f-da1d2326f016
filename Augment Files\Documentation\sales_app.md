# Sales App Documentation

## Overview

The Sales App manages the sales process, customer relationships, and order management for businesses on the Paybox360 platform. It provides tools for tracking sales opportunities, processing orders, and analyzing sales performance.

## Core Features

### 1. Customer Management

- Customer profile creation and management
- Contact information tracking
- Purchase history
- Customer segmentation

### 2. Sales Pipeline

- Lead management
- Opportunity tracking
- Sales stage progression
- Conversion analytics

### 3. Order Processing

- Order creation and management
- Product selection and pricing
- Discount application
- Order fulfillment tracking

### 4. Quotation Management

- Quote creation and sending
- Quote approval workflow
- Quote to order conversion
- Quote expiration handling

### 5. Sales Analytics

- Sales performance metrics
- Revenue tracking
- Conversion rate analysis
- Sales forecasting

## How It Works

1. **Customer Setup**: Sales teams create and manage customer profiles
2. **Lead Generation**: New sales opportunities are recorded
3. **Quotation Process**: Quotes are created and sent to prospects
4. **Order Creation**: Accepted quotes convert to orders
5. **Fulfillment**: Orders are processed and fulfilled
6. **Analysis**: Sales data is analyzed for performance insights

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `Customer` | Customer information | `name`, `contact_info`, `segment` | Company |
| `Lead` | Sales opportunities | `source`, `status`, `potential_value` | Customer, User |
| `SalesOrder` | Order records | `order_date`, `status`, `total_amount` | Customer, Company |
| `OrderItem` | Order line items | `product`, `quantity`, `price` | SalesOrder |
| `Quotation` | Price quotes | `valid_until`, `status`, `total_amount` | Customer, User |
| `SalesTarget` | Performance goals | `target_amount`, `period`, `achievement` | User, Company |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `calculate_order_total` | Computes order value | `models.py` (SalesOrder) | Called during order processing |
| `convert_quote_to_order` | Creates order from quote | `services.py` | Used in quote acceptance |
| `update_sales_pipeline` | Updates opportunity stages | `services.py` | Used in lead management |
| `generate_sales_report` | Creates performance reports | `reports.py` | Used in analytics views |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/sales_app/customers/` | Manage customers | POST/GET | `CustomerViewSet` |
| `/sales_app/leads/` | Track opportunities | POST/GET | `LeadViewSet` |
| `/sales_app/orders/` | Process orders | POST/GET | `SalesOrderViewSet` |
| `/sales_app/quotations/` | Manage quotes | POST/GET | `QuotationViewSet` |
| `/sales_app/analytics/` | View performance | GET | `SalesAnalyticsView` |

### Integration Points

- **Stock Inventory App**: For product availability
- **Invoicing App**: For invoice generation
- **Account App**: For payment processing
- **Performance Dashboard**: For sales metrics

## Key Considerations

### 1. Sales Pipeline Management

- **Responsible App**: Sales App
- **Key Functions**:
  - Lead stage progression tracking
  - Conversion rate calculation
  - Pipeline visualization tools

### 2. Order Processing Workflow

- **Responsible App**: Sales App
- **Key Functions**:
  - Order status management in SalesOrder model
  - Inventory availability checking
  - Order fulfillment tracking

### 3. Pricing and Discounts

- **Responsible App**: Sales App
- **Key Functions**:
  - Discount rule application
  - Price calculation in `calculate_order_total`
  - Special pricing management

### 4