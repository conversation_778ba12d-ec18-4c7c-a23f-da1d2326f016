from django.db.models import TextChoices


# Create your enumeration type(s) here.
class NotificationTypes(TextChoices):
    HR_MANAGEMENT = "HR_MANAGEMENT", "HR_MANAGEMENT"
    SALES = "SALES", "SALES"
    SPEND_MANAGEMENT = "SPEND_MANAGEMENT", "SPEND_MANAGEMENT"
    STOCK = "STOCK", "STOCK"


class SecretQuestionChoices(TextChoices):
    Q1 = "What is your mother's maiden name?", "What is your mother's maiden name?"


class Employee_Headcount(TextChoices):
    ZERO_TO_FIFTY = "0-50", "0-50"
    FIFTY_TO_ONE_HUNDRED = "50-100", "50-100"
    ONE_HUNDRED_TO_FIVE_HUNDRED = "100-500", "100-500"
    FIVE_HUNDRED_TO_ONE_THOUSAND = "500-1000", "500-1000"
    ONE_THOUSAND_TO_FIVE_THOUSAND = "1000-5000", "1000-5000"
    FIVE_THOUSAND_TO_TEN_THOUSAND = "5000-10000", "5000-10000"


class SupportedAppChoices(TextChoices):
    ANDROID = "ANDROID", "ANDROID"
    MACOS = "MACOS", "MACOS"
    WINDOWS = "WINDOWS", "WINDOWS"
