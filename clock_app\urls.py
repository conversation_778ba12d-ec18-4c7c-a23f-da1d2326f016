from django.urls import path

from . import views


urlpatterns = [
    path('company-settings/', views.CompanySettings.as_view(), 
         name='company-settings'),
    path('attendance-sheet/', views.AttendanceSheetList.as_view(), 
          name='attendance-list'), 
    path('attendance-sheet-detail/', views.AttendanceSheetDetail.as_view(), 
          name='attendance-list'), 
    path('shift/', views.ShiftList.as_view(), 
          name='shift'),
    path('shift-detail/', views.ShiftDetail.as_view(), 
          name='shift'),
    path('record/', views.RecordList.as_view(), 
          name='record'),
    path('record-detail/', views.RecordDetail.as_view(), 
          name='record'),
    path('clock-in/', views.ClockInList.as_view(), 
          name='clock-in'),
    path('clock-in-detail/', views.ClockInDetail.as_view(), 
          name='clock-in'),
    path('start-break/', views.StartBreakList.as_view(), 
          name='start-break'),
    path('start-break-detail/', views.StartBreakDetail.as_view(), 
          name='start-break'),
    path('end-break/', views.EndBreakList.as_view(), 
          name='end_break'),
    path('end-break-detail/', views.EndBreakDetail.as_view(), 
          name='end-break'),
    path('start-overtime/', views.StartOverTimeList.as_view(), 
          name='overtime'),
    path('start-overtime-detail/', views.StartOverTimeDetail.as_view(), 
          name='overtime'),
    path('end-overtime/', views.EndOverTimeList.as_view(), 
          name='end-overtime'),
    path('end-overtime-detail/', views.EndOverTimeDetail.as_view(),
         name='end-overtime-detail'),
    path('clock-out/', views.ClockOutList.as_view(), 
          name='clock-out'),
    path('clock-out-detail/', views.ClockOutDetail.as_view(), 
          name='clock-out'),
    path('submit-task/', views.TaskList.as_view(), 
          name='submit-task'),
    path('submit-task-detail/', views.TaskDetail.as_view(), 
          name='submit-task'),
    path('screenshot/', views.ScreenshotList.as_view(), 
          name='screenshot'),
    path('screenshot-detail/', views.ScreenshotDetail.as_view(), 
          name='screenshot'),
    path('onsite-location/', views.LocationList.as_view(), 
          name='location'),
    path('onsite-location-detail/', views.LocationDetail.as_view(), 
          name='location'),
    path('remote-location/', views.RemoteLocationList.as_view(),
         name='remote-location'),
    path('remote-location-detail/', views.RemoteLocationDetail.as_view(), 
          name='remote-location'),
    path('activities-dashboard-metrics/', 
          views.ActivitiesDashboardMetrics.as_view()),
    path('activities-dashboard-stats/', 
          views.ActivitiesDashboardStats.as_view()),
    path('activities-dashboard-trends/',
         views.ActivitiesDashboardTrends.as_view()),
    path('activities-dashboard-attendance/',
         views.ActivitiesDashboardAttendance.as_view()),
    path('employee-record-history/', 
          views.EmployeesRecordHistory.as_view()),
    path('employee-record-list/', 
          views.EmployeesRecordList.as_view()),
    path('attendance-record-metrics/', 
          views.AttendanceRecordMetrics.as_view()),
    path('attendance-record-list/', 
          views.AttendanceRecordsList.as_view()),
    path('task-record-metrics/', 
          views.TaskRecordMetrics.as_view()),
    path('task-record-list/', 
          views.TaskRecordList.as_view()),
    path('task-record-detail/', 
          views.TaskRecordDetail.as_view()),
    path('employee-attendance-dashboard-metrics/',
         views.EmployeeAttendanceDashboardMetrics.as_view()),
    path('employee-attendance-dashboard-tasks/',
         views.EmployeeAttendanceDashboardTasks.as_view()),
    path('employee-assigned-task-metrics/',
         views.EmployeeAssignedTaskMetrics.as_view()),
    path('employee-assigned-task-list/',
         views.EmployeeAssignedTaskList.as_view()),
    path('employee-attendance-record-metrics/',
         views.EmployeeAttendanceRecordMetrics.as_view()),
    path('employee-attendance-record-list/',
         views.EmployeeAttendanceRecordList.as_view()),
    path('employee-status/', 
          views.EmployeeStatusList.as_view()),
    path('employee-attendance-dashboard-detail/',
         views.EmployeeAttendanceDashboardDetail.as_view()),
    path('task-record-detail/',
         views.TaskRecordDetail.as_view()),
    path('employee-assigned-task-detail/',
         views.EmployeeAssignedTaskDetail.as_view()),
    path('attendance-record-user-detail/',
         views.AttendanceRecordUserDetail.as_view()),
    path('attendance-sheet-popup/',
         views.AttendanceSheetPopupDetail.as_view()),
    path('attendance-sheet-employees/',
         views.AttendanceSheetEmployeeList.as_view()),
    path('invite-company-branch/',
         views.InviteCompanyBranchAPIView.as_view()),
    path('onboard-company-branch/',
         views.OnboardingCompanyBranchAPIView.as_view()),
    path('clock-in-invite/', views.ClockInInvite.as_view()),
    path('update-employee-work/', views.UpdateEmployeeWorkType.as_view()),
    path('add_employee_to_branch/', views.AddEmployeeBranchAPIView.as_view()),
    path('map_employee_to_branch/', views.MapEmployeeBranchAPIView.as_view()),
    path('get_all_branch/', views.GetAllBranchAPIView.as_view()),
    path('get_all_branch_employees/', views.GetAllBranchEmployeeAPIView.as_view()),
    path('biometric_signin/', views.BiometricSignInAPIView.as_view()),
    path('biometric_signout/', views.BiometricSignOutAPIView.as_view()),

]