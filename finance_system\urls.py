from django.urls import path

from finance_system import views

dashboard = [
    path("manual_bank_detail/", views.CreateBankDetailAPIView.as_view()),
    path("global_wallet_balance/", views.GlobalWalletBalanceAPIView.as_view()),
    path("all_wallet_details/", views.GetAllWalletDetailsAPIView.as_view()),
]

journal = [
    path("journal/", views.JournalAPIView.as_view()),
]

chart_of_account = [
        path("account_type_module/", views.AccountTypeModuleAPIView.as_view()),
        path("chart_of_account/", views.ChartOfAccountAPIView.as_view()),
]

urlpatterns = [
    *dashboard,
    *journal,
    *chart_of_account,
    
]
