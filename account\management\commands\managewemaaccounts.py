from datetime import datetime
import pytz

from django.conf import settings
from django.core.management.base import BaseCommand

from account.enums import AccountType
from account.models import AccountSystem
from account.tasks import create_wema_sales_account
from stock_inventory.models import Branch


class Command(BaseCommand):
    help = "MANAGE FAILED SALES ACCOUNT CREATION."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        branches = Branch.objects.all()
        if not branches.exists():
            self.stdout.write(self.style.SUCCESS(f"THERE ARE NO AVAILABLE BRANCHES."))
        else:
            count = 0
            for branch in branches:
                account = AccountSystem.objects.filter(
                    branch=branch,
                    account_type=AccountType.SALES,
                )
                if not account.exists():
                    create_wema_sales_account.delay(branch.id)
                    count += 1
            END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
            self.stdout.write(
                self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
            )
            self.stdout.write(
                self.style.SUCCESS(f"COUNT:  {count}")
            )
            self.stdout.write(
                self.style.SUCCESS(
                    "SUCCESSFULLY CREATED VIRTUAL ACCOUNTS FOR ALL BRANCHES."
                )
            )
