# Requisition App API Documentation

## Overview

This document provides detailed technical documentation of the API endpoints within the requisition app, including their request/response formats, authentication requirements, and cross-references.

## Authentication

All API endpoints require authentication using the `CustomUserAuthentication` class, which validates JWT tokens. Most endpoints also require the `IsAuthenticated` permission class.

## API Endpoint Groups

### 1. Requisition Management Endpoints

| Endpoint | HTTP Method | View | Purpose | Permission Classes |
|----------|-------------|------|---------|-------------------|
| `/requisition/create_requisition/` | POST | `CreateRequisition` | Creates fund requests | `IsAuthenticated` |
| `/requisition/requisitions/` | GET | `ListFilterRequisition` | Lists and filters requisitions | `IsAuthenticated` |
| `/requisition/disbursement/` | POST | `Disbursement` | Processes fund disbursements | `IsAuthenticated`, `CanDisburse`, `CanInitiateTransfer` |
| `/requisition/bulk-disbursement/` | POST | `BulkDisbursementAPIView` | Processes multiple disbursements | `IsAuthenticated`, `CanInitiateTransfer` |
| `/requisition/decline_requisition/` | POST | `DeclineRequsition` | Declines fund requests | `IsAuthenticated` |
| `/requisition/req_dashboard/` | GET | `GetRequisitionDashboard` | Provides dashboard data | `IsAuthenticated` |
| `/requisition/sms_disbursement/` | POST | `SmsDisbursement` | Processes disbursements with SMS notifications | `IsAuthenticated`, `CanDisburse` |

### 2. Expense Management Endpoints

| Endpoint | HTTP Method | View | Purpose | Permission Classes |
|----------|-------------|------|---------|-------------------|
| `/requisition/expense_dashboard/` | GET | `ExpenseDashboard` | Provides expense statistics | `IsAuthenticated` |
| `/requisition/expense_list/` | GET | `ExpenseList` | Lists expense records | `IsAuthenticated` |
| `/requisition/team_expense_list/` | GET | `TeamExpenseList` | Lists team expenses | `IsAuthenticated` |
| `/requisition/record_expense/` | POST | `RecordExpense` | Creates expense records | `IsAuthenticated` |
| `/requisition/receipt_reader/` | POST | `ReadExpenseReceipt` | Extracts data from receipts | `IsAuthenticated` |

### 3. Budget Management Endpoints

| Endpoint | HTTP Method | View | Purpose | Permission Classes |
|----------|-------------|------|---------|-------------------|
| `/requisition/create_budget/` | POST | `CreateBudget` | Creates budget records | `IsAuthenticated` |
| `/requisition/budgets/` | GET | `ListBudget` | Lists budget records | `IsAuthenticated` |
| `/requisition/edit_budget/` | PUT | `EditBudget` | Updates budget records | `IsAuthenticated` |
| `/requisition/create_budget_categories/` | POST | `CreateBudgetCategories` | Creates budget categories | `IsAuthenticated` |
| `/requisition/budget_categories/` | GET | `ListBudgetCategories` | Lists budget categories | `IsAuthenticated` |

### 4. Team Management Endpoints

| Endpoint | HTTP Method | View | Purpose | Permission Classes |
|----------|-------------|------|---------|-------------------|
| `/requisition/create_team/` | POST | `CreateDelTeam` | Creates team records | `IsAuthenticated` |
| `/requisition/teams/` | GET | `ListTeam` | Lists team records | `IsAuthenticated` |
| `/requisition/edit_team/` | PUT | `EditTeam` | Updates team records | `IsAuthenticated` |
| `/requisition/invite_members/` | POST | `InviteMembers` | Invites team members | `IsAuthenticated` |
| `/requisition/team_members/` | GET | `ListTeamMembers` | Lists team members | `IsAuthenticated` |

### 5. Procurement Management Endpoints

| Endpoint | HTTP Method | View | Purpose | Permission Classes |
|----------|-------------|------|---------|-------------------|
| `/requisition/procurement/` | POST | `ProcurementRequisitionAPIView` | Creates procurement requisitions | `IsAuthenticated` |
| `/requisition/procurement/<str:pk>` | GET | `ProcurementRequisitionAPIView` | Gets procurement details | `IsAuthenticated` |
| `/requisition/approve-procurement` | POST | `ApprovePurchaseIndentAPIView` | Approves procurement requests | `IsAuthenticated` |
| `/requisition/procurement/purchase-order/<str:pk>` | GET | `PurchaseOrderAPIView` | Gets purchase order details | `IsAuthenticated` |
| `/requisition/procurement/bulk-requisition` | POST | `BulkProcurementAPIView` | Creates bulk procurement requests | `IsAuthenticated` |

## Detailed API Specifications

### 1. Create Requisition

**Endpoint:** `/requisition/create_requisition/`  
**Method:** POST  
**Authentication:** Required  
**Permissions:** `IsAuthenticated`

**Request Body:**
```json
{
  "company": "uuid",
  "team": "uuid",
  "request_amount": "decimal",
  "request_reason": "string",
  "requisition_category": "string",
  "account_name": "string",
  "account_no": "string",
  "bank_name": "string",
  "bank_code": "string"
}
```

**Response:**
```json
{
  "status": "Success",
  "message": "Requisition created successfully",
  "data": {
    "id": "uuid",
    "company": "uuid",
    "team": "uuid",
    "request_amount": "decimal",
    "request_reason": "string",
    "requisition_category": "string",
    "status": "PENDING",
    "created_at": "datetime"
  }
}
```

**Cross-References:**
- Uses `RequisitionSerializer` for validation
- May call `upload_file_aws_s3_bucket` for invoice uploads
- Sends notifications via signals

### 2. Disbursement

**Endpoint:** `/requisition/disbursement/`  
**Method:** POST  
**Authentication:** Required  
**Permissions:** `IsAuthenticated`, `CanDisburse`, `CanInitiateTransfer`

**Request Body:**
```json
{
  "requisition": "uuid",
  "company": "uuid",
  "wallet_type": "string"
}
```

**Response:**
```json
{
  "status": "Success",
  "message": "Disbursement successful",
  "data": {
    "transaction_ref": "string",
    "amount": "decimal",
    "status": "SUCCESSFUL"
  }
}
```

**Cross-References:**
- Uses `DisbursementSerializer` for validation
- Calls `Requisition.requisition_disbursement` for processing
- Interacts with `AccountSystem` model

### 3. Read Expense Receipt

**Endpoint:** `/requisition/receipt_reader/`  
**Method:** POST  
**Authentication:** Required  
**Permissions:** `IsAuthenticated`

**Request Body:**
```json
{
  "receipt": "file"
}
```

**Response:**
```json
{
  "status": "Success",
  "data": {
    "Date": "string",
    "Merchant": "string",
    "Amount": "string",
    "Invoice Number": "string",
    "Description": "string"
  }
}
```

**Cross-References:**
- Uses `ReceiptDataSerializer` for validation
- Calls `process_invoice_file_2` for OCR processing
- Formats extracted data for client consumption

## API Authentication Flow

1. Client obtains JWT token via login endpoint
2. Token is included in Authorization header for all API requests
3. `CustomUserAuthentication` validates token and identifies user
4. Permission classes check if user has required permissions

## Error Handling

All API endpoints use standard HTTP status codes:
- 200: Success
- 400: Bad Request (validation errors)
- 401: Unauthorized (authentication issues)
- 403: Forbidden (permission issues)
- 404: Not Found
- 500: Server Error

Error responses follow this format:
```json
{
  "status": "error",
  "message": "Error description",
  "errors": {
    "field_name": ["Error details"]
  }
}
```

## Pagination

List endpoints use `CustomPagination` class with these response format:
```json
{
  "count": "integer",
  "next": "url",
  "previous": "url",
  "results": []
}
```

## Filtering and Searching

List endpoints support filtering and searching:
- Filter using query parameters (e.g., `?status=PENDING`)
- Search using the `search` parameter (e.g., `?search=keyword`)

## API Rate Limiting

API rate limiting is implemented to prevent abuse:
- Anonymous users: 100 requests per hour
- Authenticated users: 1000 requests per hour

## API Versioning

The API does not currently use explicit versioning but follows these practices:
- Backward compatibility is maintained
- New fields are added without removing existing ones
- Major changes are communicated to clients
