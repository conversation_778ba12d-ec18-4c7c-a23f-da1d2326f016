# Accounting App Documentation

## Overview

The Accounting app provides financial accounting functionality for the Paybox360 platform. It handles financial record-keeping, transaction categorization, financial reporting, and accounting operations for businesses.

## Core Features

### 1. Chart of Accounts

- Account structure management
- Account categorization
- Account hierarchy

### 2. Transaction Categorization

- Automatic transaction classification
- Manual transaction assignment
- Category management

### 3. Financial Reporting

- Balance sheet generation
- Income statement reporting
- Cash flow analysis
- Financial metrics calculation

### 4. Journal Entries

- Automated journal entry creation
- Manual journal entries
- Entry verification and approval

### 5. Reconciliation

- Bank statement reconciliation
- Transaction matching
- Discrepancy identification

## How It Works

1. **Account Setup**: Companies configure their chart of accounts
2. **Transaction Flow**: System transactions are categorized into appropriate accounts
3. **Journal Processing**: Transactions generate journal entries
4. **Reporting**: Financial reports are generated from accounting data
5. **Period Closing**: Accounting periods are closed and balanced

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `Account` | Chart of accounts | `account_type`, `account_code` | Company |
| `JournalEntry` | Accounting entries | `amount`, `entry_type` | Account, Transaction |
| `AccountingPeriod` | Financial periods | `start_date`, `end_date`, `is_closed` | Company |
| `TransactionCategory` | Transaction classification | `category_name`, `category_type` | - |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `categorize_transaction` | Classifies transactions | `utils.py` | Referenced in transaction processing |
| `generate_journal_entry` | Creates accounting entries | `models.py` | Called after transactions |
| `generate_balance_sheet` | Creates financial reports | `reports.py` | Used in reporting views |
| `reconcile_accounts` | Matches transactions | `reconciliation.py` | Used in reconciliation process |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/accounting/accounts/` | Manage accounts | GET/POST | `AccountViewSet` |
| `/accounting/journal-entries/` | View journal entries | GET | `JournalEntryViewSet` |
| `/accounting/reports/balance-sheet/` | Generate balance sheet | GET | `BalanceSheetView` |
| `/accounting/reconciliation/` | Reconcile accounts | POST | `ReconciliationView` |

### Integration Points

- **Account App**: For transaction data
- **Payroll App**: For salary expense entries
- **Requisition App**: For expense categorization
- **Reporting Services**: For financial report generation

## Key Considerations

### 1. Data Integrity

- **Responsible App**: Accounting app
- **Key Functions**:
  - Double-entry accounting enforcement
  - Transaction validation before entry creation
  - Period closing controls

### 2. Financial Reporting Accuracy

- **Responsible App**: Accounting app
- **Key Functions**:
  - Report generation with proper account classification
  - Balance verification
  - Historical data preservation

### 3. Categorization Logic

- **Responsible App**: Accounting app
- **Key Functions**:
  - `categorize_transaction` applies rules for automatic classification
  - Machine learning models for transaction prediction
  - Manual override capabilities

### 4. Compliance Requirements

- **Responsible App**: Accounting app
- **Key Functions**:
  - Audit trail maintenance
  - Regulatory report generation
  - Data retention policies