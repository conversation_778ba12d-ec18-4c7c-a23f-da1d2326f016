import json
import requests
import random
import string
from django.conf import settings


# Create your utility manager for Third party service(s) here.
class Paystack:
    """
    Manage Paystack APIs seamlessly.
    NOTE: The value for amount is treated in subunits; 
        - multiply the base amount by 100 for outgoing requests
        - divide the base amount by 100 for incoming requests
    For example; #100 equals to 10000 (100 * 100).
    """
    base_url = "https://api.paystack.co"
    headers = {
        "Authorization": f"Bearer {settings.PAYSTACK_SECRET_KEY}",
        "Content-Type": "application/json"
    }

    def get_authorization_url(
        self,
        email: str,
        amount: int,
        reference: str
    ):
        """
        NOTE: A successful request will return a json object containing;
            - authorization url (str): The authorization url for the payment.
            - access code (str): The access code associated with the payment authorization.
            - reference (str): The unique reference associated with the payment authorization.
        """
        absolute_url = f"{self.base_url}/transaction/initialize"
        payload = json.dumps(
            {
                "email": email,
                "amount": float(amount * 100),
                "reference": reference
            }
        )
        response = requests.post(
            url=absolute_url, headers=self.headers, data=payload
        )
        return response.json()

    def verify_payment(self, reference: str):
        """
        """
        absolute_url = f"{self.base_url}/transaction/verify/{reference}"
        response = requests.get(url=absolute_url, headers=self.headers)
        return response.json()

    def fetch_bank_list(self):
        url = f"{self.base_url}/bank"
        response = requests.get(url=url, headers=self.headers)
        return response.json()

    def name_enquiry(self, bank_code, account_no):
        url = f"{self.base_url}/bank/resolve?account_number={account_no}&bank_code={bank_code}"
        response = requests.get(url=url, headers=self.headers)
        return response.json()


class WhisperSms:
    base_url = "https://whispersms.xyz"
    PAYBOX_WHISPER_SMS_TOKEN = settings.PAYBOX_WHISPER_SMS_TOKEN
    PAYBOX_WHISPER_SMS_API_KEY = settings.PAYBOX_WHISPER_SMS_API_KEY
    sender_id_headers = {
        "Authorization": f"Token {settings.PAYBOX_WHISPER_SMS_TOKEN}",
        "Content-Type": "application/json"
    }
    send_campaign_headers = {
        "Authorization": f"Api_key {settings.PAYBOX_WHISPER_SMS_API_KEY}",
        "Content-Type": "application/json"
    }
    sender_id_status_code = [201, 202, 400]
    @classmethod
    def create_sender_id(
        cls,
        company_name,
        sender_id,
        sample_message
    ):
        """
       
        """
        absolute_url = f"{cls.base_url}/whisper/sender/"
        payload = json.dumps(
            {
                "company_name": company_name,
                "sender": sender_id,
                "sample_message": sample_message,
            }
        )
        response = requests.post(
            url=absolute_url, headers=cls.sender_id_headers, data=payload
        )
        if response.status_code in cls.sender_id_status_code:
            whisper_response =  response.json()
            
            if response.status_code == 201 or response.status_code == 202:
                data = {
                    "error": False,
                    "message": f"{sender_id} created successfully",
                    "data": whisper_response.get("data")
                }
            else:
                data = {
                    "error": True,
                    "message": f"{sender_id} was not created",
                    "data": whisper_response.get("data")
                }    
        else:
            data = {
                "error": True,
                "message": "An error occurred, try again!",
                "data": None
            }
        
        return data
    
    @classmethod
    def send_campaign_sms(
        cls,
        campaign_name,
        contacts,
        flash_route,
        message,
        priority_route,
        send_date,
        send_later,
        sender_id,
        campaign_batch
    ):
        from core.helpers.func import DateTimeEncoder
        """
       
        """
        absolute_url = f"{cls.base_url}/api/send_message/"
        payload = {
                    "campaign_name": campaign_name,
                    "contacts": contacts,
                    "flash_route": flash_route,
                    "message": message,
                    "priority_route": priority_route,
                    "send_date": send_date,
                    "send_later": send_later,
                    "sender_id": sender_id,
                }
        
        response = requests.post(
            url=absolute_url, headers=cls.send_campaign_headers, json=payload
        )
        if response.status_code in cls.sender_id_status_code:
            whisper_response = response.json()
            if response.status_code == 200:
                data = {
                    "error": False,
                    "message": f"successful",
                    "data": whisper_response.get("data")
                }
            else:
                data = {
                    "error": True,
                    "message": f"failed",
                    "data": whisper_response.get("data")
                }    
        else:
            data = {
                "error": True,
                "message": "An error occurred, try again!",
                "data": None
            }
        campaign_batch.request = payload
        campaign_batch.response = response.text
        campaign_batch.save()
        return data
    



    @classmethod
    def send_generic_sms(cls, phone_number, message):

        url = f"{cls.base_url}/transactional/send"

        payload = json.dumps(
            {
                "receiver": f"{phone_number}",
                "template": "4e08c548-54ba-453e-8cad-4876713a3f05",
                "place_holders": {"message": message},
            }
        )
        
        try:

            response = requests.request("POST", url, headers=cls.send_campaign_headers, data=payload)
        except requests.ConnectionError:
            return None

        try:
            return response.json()
        except:
            return response.text


class SalesUser:

    @classmethod
    def generate_passcode(cls,):
        verification_code = ''.join(random.choices(string.digits + string.digits, k=6))
        return verification_code
