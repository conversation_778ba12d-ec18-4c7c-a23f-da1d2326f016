import os
import pandas as pd
from pathlib import Path
from django.conf import settings
from datetime import datetime
from core.models import ConstantTable
from core.tasks import send_email_with_attachment, send_email_with_multiple_attachments, upload_file_aws_s3_bucket_using_file_path
from django.core.files import File
import io
from payroll_app.apis.func import round_amount
from django.db.models.functions import Lower
import pandas as pd
from fpdf import FPDF


def pension_data(company_id, payroll_date, company_details_id, all_payable_id):
    from core.models import User
    from payroll_app.models import PayrollTable, PensionFundAdminSettings, CompanyPayrollSettings

    payroll_settings_ins = CompanyPayrollSettings.objects.filter(company__id=company_id).first()
    if payroll_settings_ins:
        hr_email = payroll_settings_ins.hr_email
        employer_pension_code = payroll_settings_ins.employer_pension_code
        if payroll_settings_ins.company_name:
            this_company_name = payroll_settings_ins.company_name
        else:
            this_company_name = None
    else:
        hr_email = ""
        employer_pension_code = ""
        this_company_name = None


    file_path = os.path.join(settings.BASE_DIR, 'payroll_app/') 

    base_qs = PayrollTable.objects.filter(id__in=all_payable_id, company__id=company_id, payroll_deleted=False, employee__pension_fund_admin__isnull=False)
    unique_pfa = base_qs.values('employee__pension_fund_admin').distinct()

    for pfa in unique_pfa:
        pfa_id = pfa['employee__pension_fund_admin']
        
        get_pfa = PensionFundAdminSettings.objects.filter(id=pfa_id).first()
        
        # all_employees = base_qs.filter(employee__pension_fund_admin=get_pfa, employee__pension_pin__isnull=False, pension_amount__gt=0).order_by(Lower("last_name"))
        all_employees = base_qs.filter(
            employee__pension_fund_admin=get_pfa, 
            employee__pension_pin__isnull=False
        ).exclude(employee__pension_pin="").filter(pension_amount__gt=0).order_by(Lower("last_name"))
        this_employee_count = all_employees.count() or 0
        if this_employee_count > 0:
            if this_company_name:
                company_name = this_company_name
            else:
                company_name = all_employees.last().company.company_name

            company_email = all_employees.last().company.user.email
            last_file = os.path.join(f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx") 
            my_file = Path(last_file) 
            if my_file.is_file(): 
                os.remove(last_file)
            try:
                os.mkdir(file_path)
            except Exception:
                pass 

            this_pfa = pd.read_excel(f"{file_path}pension_schedule_template.xlsx", header=None)
            this_pfa.iloc[3,2] = f"{company_name}"
            this_pfa.iloc[5,2] = f"{payroll_date}"
            this_pfa.iloc[7,2] = f"{employer_pension_code}"
            this_pfa.iloc[5,5] = f"{get_pfa.pfa_name}"
            this_pfa.iloc[6,5] = f"{get_pfa.pfa_account_name}"
            this_pfa.iloc[7,5] = f"{get_pfa.pfa_account_number}"
            this_pfa.iloc[8,5] = f"{get_pfa.pfa_sort_code}"
            
            total_contribution_index = 0
            sum_total_amount = 0
            for i in range(0, this_employee_count):
                employee = all_employees[i]
                # Ensure the DataFrame has enough rows
                required_row = 11 + i
                if len(this_pfa) <= required_row:
                    # Add extra rows if necessary
                    extra_rows = required_row - len(this_pfa) + 1
                    new_rows = pd.DataFrame([[None] * len(this_pfa.columns)] * extra_rows, columns=this_pfa.columns)
                    this_pfa = pd.concat([this_pfa, new_rows], ignore_index=True)

                this_pfa.iloc[11+i, 0] = i+1
                this_pfa.iloc[11+i, 1] = f"{employee.last_name} {employee.first_name}"
                this_pfa.iloc[11+i, 2] = f"{employee.employee.pension_pin}"
                # this_pfa.iloc[11+i, 3] = round_amount(employee.employer_contribution_pension_amount)
                this_pfa.iloc[11+i, 3] = 0
                this_pfa.iloc[11+i, 4] = round_amount(employee.pension_amount)
                this_pfa.iloc[11+i, 5] = ""
                this_pfa.iloc[11+i, 6] =round_amount(this_pfa.iloc[11+i, 3]) + round_amount(this_pfa.iloc[11+i, 4])
                total_contribution_index = 11 + i
                sum_total_amount += float(this_pfa.iloc[11+i, 3]) + float(this_pfa.iloc[11+i, 4])
            
            total_row = total_contribution_index + 2
            # Check if the DataFrame has enough rows, if not, extend it
            if len(this_pfa) <= total_row:
                # Add extra rows if necessary
                extra_rows = total_row - len(this_pfa) + 1
                new_rows = pd.DataFrame([[None] * len(this_pfa.columns)] * extra_rows, columns=this_pfa.columns)
                this_pfa = pd.concat([this_pfa, new_rows], ignore_index=True)

            # Now assign the value safely
            this_pfa.iloc[total_row, 6] = sum_total_amount
            
            # this_pfa.iloc[total_contribution_index+2, 6] = sum_total_amount

            this_pfa.to_excel(f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx", index=False, header=False)
            with open(last_file, "rb") as excel_file:
                pension_schedule = excel_file.read()
                excel_file.close()
            
            send_email_with_attachment(
                file = pension_schedule,
                template_dir="pension_schedule.html",
                file_name = f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx",
                subject=f"{company_name} Pension Schedule {payroll_date}",
                recipient=f"{get_pfa.pfa_email}",
                company_name=company_name,
                company_email=hr_email if hr_email else company_email,
                pension_date=payroll_date,
                pension_name=f"{get_pfa.pfa_name}",
                sender_email=company_email,
                cc_email=hr_email
            )
            file_name = f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx"
            
            uploaded_file = upload_file_aws_s3_bucket_using_file_path(
                    model_instance_id=company_details_id, file_path=file_name, model_name="PensionSchedule"
                    )
            try:
                os.remove(f"{file_name}") ## Delete file when done
            except PermissionError:
                pass
        else:
            pass

    return unique_pfa

def manual_pension_data(payroll_date, company_name,  pension_list, user_id, account_id, employer_code, company_email, hr_email):
    from core.models import User
    from payroll_app.models import PayrollTable, PensionFundAdminSettings
    from account.models import AccountSystem, Transaction, Wallet

    file_path = os.path.join(settings.BASE_DIR, 'payroll_app/') 

    unique_pfa = list({item["pfa"] for item in pension_list})

    user_ins = User.objects.get(id=user_id)
    account_inst = AccountSystem.objects.get(id=account_id)
    
    for pfa in unique_pfa:
        pfa_id = pfa

        all_employees = [item for item in pension_list if item['pfa'] == pfa_id]

        total_pension_amount = sum(item['pension_amount'] for item in pension_list if item['pfa'] == pfa_id)
        
        get_pfa = PensionFundAdminSettings.objects.filter(id=pfa_id).first()
        if not get_pfa:
            continue
        
        this_employee_count = len(all_employees) or 0
        if this_employee_count > 0:
            last_file = os.path.join(f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx") 
            my_file = Path(last_file) 
            if my_file.is_file(): 
                os.remove(last_file)
            try:
                os.mkdir(file_path)
            except Exception:
                pass 

            this_pfa = pd.read_excel(f"{file_path}pension_schedule_template.xlsx", header=None)
            this_pfa.iloc[3,2] = f"{company_name}"
            this_pfa.iloc[5,2] = f"{payroll_date}"
            this_pfa.iloc[7,2] = f"{employer_code}"
            this_pfa.iloc[5,5] = f"{get_pfa.pfa_name}"
            this_pfa.iloc[6,5] = f"{get_pfa.pfa_account_name}"
            this_pfa.iloc[7,5] = f"{get_pfa.pfa_account_number}"
            this_pfa.iloc[8,5] = f"{get_pfa.pfa_sort_code}"
            
            total_contribution_index = 0
            sum_total_amount = 0
            for i in range(0, this_employee_count):
                employee = all_employees[i]
                # Ensure the DataFrame has enough rows
                required_row = 11 + i
                if len(this_pfa) <= required_row:
                    # Add extra rows if necessary
                    extra_rows = required_row - len(this_pfa) + 1
                    new_rows = pd.DataFrame([[None] * len(this_pfa.columns)] * extra_rows, columns=this_pfa.columns)
                    this_pfa = pd.concat([this_pfa, new_rows], ignore_index=True)

                last_name = employee.get("last_name")
                first_name = employee.get("first_name")
                pension_amount = employee.get("pension_amount")
                pension_pin = employee.get("pension_pin")
                this_pfa.iloc[11+i, 0] = i+1
                this_pfa.iloc[11+i, 1] = f"{last_name} {first_name}"
                this_pfa.iloc[11+i, 2] = f"{pension_pin}"
                # this_pfa.iloc[11+i, 3] = round_amount(employee.employer_contribution_pension_amount)
                this_pfa.iloc[11+i, 3] = 0
                this_pfa.iloc[11+i, 4] = round_amount(pension_amount)
                this_pfa.iloc[11+i, 5] = ""
                this_pfa.iloc[11+i, 6] =round_amount(this_pfa.iloc[11+i, 3]) + round_amount(this_pfa.iloc[11+i, 4])
                total_contribution_index = 11 + i
                sum_total_amount += float(this_pfa.iloc[11+i, 3]) + float(this_pfa.iloc[11+i, 4])
            
            total_row = total_contribution_index + 2
            # Check if the DataFrame has enough rows, if not, extend it
            if len(this_pfa) <= total_row:
                # Add extra rows if necessary
                extra_rows = total_row - len(this_pfa) + 1
                new_rows = pd.DataFrame([[None] * len(this_pfa.columns)] * extra_rows, columns=this_pfa.columns)
                this_pfa = pd.concat([this_pfa, new_rows], ignore_index=True)

            # Now assign the value safely
            this_pfa.iloc[total_row, 6] = sum_total_amount
            
            # this_pfa.iloc[total_contribution_index+2, 6] = sum_total_amount

            this_pfa.to_excel(f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx", index=False, header=False)
            with open(last_file, "rb") as excel_file:
                pension_schedule = excel_file.read()
                excel_file.close()
            
            send_email_with_attachment(
                file = pension_schedule,
                template_dir="pension_schedule.html",
                file_name = f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx",
                subject=f"{company_name} Pension Schedule {payroll_date}",
                recipient=f"{get_pfa.pfa_email}",
                company_name=company_name,
                company_email=hr_email if hr_email else company_email,
                pension_date=payroll_date,
                pension_name=f"{get_pfa.pfa_name}",
                sender_email=company_email,
                cc_email=hr_email
            )
            file_name = f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx"


            
            try:
                os.remove(f"{file_name}") ## Delete file when done
            except PermissionError:
                pass

            send_money = Transaction.vfd_funds_transfer(
                bank_code=get_pfa.pfa_bank_code,
                bank_name=get_pfa.pfa_bank_name,
                account_name=get_pfa.pfa_account_name,
                account_number=get_pfa.pfa_account_number,
                narration=f"{company_name} Pension Schedule",
                amount=total_pension_amount,
                user=user_ins,
                account=account_inst,
            )
        else:
            pass

    return unique_pfa

def update_single_pfa_pension_data(payroll_date,
                                    company_name,  
                                    pension_list, 
                                    pfa_id, 
                                    employer_code, 
                                    company_email,
                                    hr_email,
                                    beneficiary_account_number,
                                    transaction_type,
                                    payment_method,
                                    transaction_date,
                                    amount,
                                    beneficiary_account_name,
                                    transaction_ref,
                                    pension_data_id,
                                    session_id,
                                    company_id
                                    ):
    from payroll_app.models import PensionFundAdminSettings
    CONST = ConstantTable.get_constant_instance()
    if CONST.liberty_custom_pension_ids:
        all_custom_companies = CONST.liberty_custom_pension_ids.split(",")
    else:
        all_custom_companies = []
        
    file_path = os.path.join(settings.BASE_DIR, 'payroll_app/') 

    all_attachments = []

    receipt = os.path.join(f"{file_path}{pension_data_id}_receipt.pdf") 
    my_file = Path(receipt) 
    if my_file.is_file(): 
        os.remove(receipt)
    try:
        os.mkdir(file_path)
    except Exception:
        pass 
    
    generate_payment_pdf(
        beneficiary_account_number,
        transaction_type,
        payment_method,
        transaction_date,
        amount,
        beneficiary_account_name,
        transaction_ref,
        pension_data_id,
        session_id
    )
    receipt = os.path.join(f"{file_path}{pension_data_id}_receipt.pdf")

    all_attachments.append((receipt, open(receipt, "rb")))
    
    get_pfa = PensionFundAdminSettings.objects.filter(id=pfa_id).first()
    
    this_employee_count = len(pension_list) or 0
    if this_employee_count > 0:
        last_file = os.path.join(f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx") 
        my_file = Path(last_file) 
        if my_file.is_file(): 
            os.remove(last_file)
        try:
            os.mkdir(file_path)
        except Exception:
            pass 

        this_pfa = pd.read_excel(f"{file_path}pension_schedule_template.xlsx", header=None)
        this_pfa.iloc[3,2] = f"{company_name}"
        this_pfa.iloc[5,2] = f"{payroll_date}"
        this_pfa.iloc[7,2] = f"{employer_code}"
        this_pfa.iloc[5,5] = f"{get_pfa.pfa_name}"
        this_pfa.iloc[6,5] = f"{get_pfa.pfa_account_name}"
        this_pfa.iloc[7,5] = f"{get_pfa.pfa_account_number}"
        this_pfa.iloc[8,5] = f"{get_pfa.pfa_sort_code}"
        
        total_contribution_index = 0
        sum_total_amount = 0
        for i in range(0, this_employee_count):
            employee = pension_list[i]
            # Ensure the DataFrame has enough rows
            required_row = 11 + i
            if len(this_pfa) <= required_row:
                # Add extra rows if necessary
                extra_rows = required_row - len(this_pfa) + 1
                new_rows = pd.DataFrame([[None] * len(this_pfa.columns)] * extra_rows, columns=this_pfa.columns)
                this_pfa = pd.concat([this_pfa, new_rows], ignore_index=True)

            last_name = employee.get("last_name")
            first_name = employee.get("first_name")
            pension_amount = employee.get("pension_amount")
            pension_pin = employee.get("pension_pin")
            this_pfa.iloc[11+i, 0] = i+1
            this_pfa.iloc[11+i, 1] = f"{last_name} {first_name}"
            this_pfa.iloc[11+i, 2] = f"{pension_pin}"
            # this_pfa.iloc[11+i, 3] = round_amount(employee.employer_contribution_pension_amount)
            this_pfa.iloc[11+i, 3] = 0
            this_pfa.iloc[11+i, 4] = round_amount(pension_amount)
            this_pfa.iloc[11+i, 5] = ""
            this_pfa.iloc[11+i, 6] =round_amount(this_pfa.iloc[11+i, 3]) + round_amount(this_pfa.iloc[11+i, 4])
            total_contribution_index = 11 + i
            sum_total_amount += float(this_pfa.iloc[11+i, 3]) + float(this_pfa.iloc[11+i, 4])
        
        total_row = total_contribution_index + 2
        # Check if the DataFrame has enough rows, if not, extend it
        if len(this_pfa) <= total_row:
            # Add extra rows if necessary
            extra_rows = total_row - len(this_pfa) + 1
            new_rows = pd.DataFrame([[None] * len(this_pfa.columns)] * extra_rows, columns=this_pfa.columns)
            this_pfa = pd.concat([this_pfa, new_rows], ignore_index=True)

        # Now assign the value safely
        this_pfa.iloc[total_row, 6] = sum_total_amount
    
        this_pfa.to_excel(f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx", index=False, header=False)
        with open(last_file, "rb") as excel_file:
            pension_schedule = excel_file.read()
            excel_file.close()

        file_name = f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx"
        all_attachments.append((file_name, open(file_name, "rb")))
        
        if str(company_id) in all_custom_companies and pfa_id in [1,6]:
            send_email_with_multiple_attachments(
                template_dir="pension_schedule.html",
                file_data = all_attachments,
                subject=f"{company_name} Pension Schedule {payroll_date}",
                recipient=f"{get_pfa.pfa_email}",
                company_name=company_name,
                company_email=hr_email if hr_email else company_email,
                pension_date=payroll_date,
                pension_name=f"{get_pfa.pfa_name}",
                sender_email=company_email,
                cc_email=f"{hr_email}, <EMAIL>"
            )
        else:
            send_email_with_multiple_attachments(
                template_dir="pension_schedule.html",
                file_data = all_attachments,
                subject=f"{company_name} Pension Schedule {payroll_date}",
                recipient=f"{get_pfa.pfa_email}",
                company_name=company_name,
                company_email=hr_email if hr_email else company_email,
                pension_date=payroll_date,
                pension_name=f"{get_pfa.pfa_name}",
                sender_email=company_email,
                cc_email=f"{hr_email}"
            )
        
        try:
            os.remove(f"{file_name}") ## Delete file when done
        except PermissionError:
            pass
        try:
            os.remove(f"{receipt}") ## Delete file when done
        except PermissionError:
            pass
    else:
        pass

    return True

def format_naira(amount):
    """
    Formats a given amount as a Naira string.
    :param amount: The numeric value to format.
    :return: A string with the Naira symbol.
    """
    return f"N{amount:,.2f}"
class PDF(FPDF):
    def header(self):
        # Add a logo (replace 'logo.png' with your image file)
        # self.image('logo.png', 10, 8, 33)  # x, y, width
        self.set_font("Helvetica", "B", 16)
        self.cell(0, 10, "Transaction Receipt", border=False, ln=1, align="C")
        self.ln(10)

    def footer(self):
        self.set_y(-30)
        self.set_font("Helvetica", "I", 8)
        footer_text = (
            "This is a PayBox360 system-generated receipt. No signature is required. "
        )
        self.multi_cell(0, 5, footer_text, align="C")

def generate_payment_pdf(
    beneficiary_account_number,
    transaction_type,
    payment_method,
    transaction_date,
    amount,
    beneficiary_account_name,
    transaction_ref,
    pension_data_id,
    session_id):

    file_path = os.path.join(settings.BASE_DIR, 'payroll_app/')
    if not os.path.exists(file_path):
        os.makedirs(file_path)

    pdf_path = f"{file_path}{pension_data_id}_receipt.pdf"
    this_amount = format_naira(amount)

    pdf = PDF()
    pdf.add_page()

    # Add amount and status
    pdf.set_font("Helvetica", "B", 24)
    pdf.set_text_color(0, 128, 0)  # Green color for amount
    pdf.cell(0, 10, this_amount, border=False, ln=1, align="C")
    
    pdf.set_font("Helvetica", "", 16)
    pdf.set_text_color(0, 0, 0)  # Black color for status
    pdf.cell(0, 10, "SUCCESS", border=False, ln=1, align="C")
    pdf.cell(0, 10, transaction_date.strftime("%Y-%m-%d %H:%M:%S"), border=False, ln=1, align="C")
    pdf.ln(5)

    # Draw a line
    pdf.set_line_width(0.5)
    pdf.line(10, pdf.get_y(), 200, pdf.get_y())
    pdf.ln(10)

    # Add details
    data = {
        "Field": [
            "Recipient Details", "Transaction Type", "Account Number",
            "Payment Method", "Transaction ID", "Transaction Date", "Session ID"
        ],
        "Value": [
            beneficiary_account_name,
            transaction_type,
            beneficiary_account_number,
            payment_method,
            transaction_ref,
            transaction_date.strftime("%Y-%m-%d %H:%M:%S"),
            session_id
        ]
    }
    df = pd.DataFrame(data)

    for index, row in df.iterrows():
        pdf.set_font("Helvetica", "B", 12)
        pdf.cell(90, 8, row["Field"], ln=0)
        pdf.set_font("Helvetica", "", 12)
        pdf.cell(0, 8, row["Value"], ln=1, align="R")
        pdf.ln(5)
    
    pdf.output(pdf_path)

def manual_pension_data_no_payment(payroll_date, company_name,  pension_list, employer_code, company_email, hr_email):
    from payroll_app.models import PensionFundAdminSettings

    file_path = os.path.join(settings.BASE_DIR, 'payroll_app/') 

    unique_pfa = list({item["pfa"] for item in pension_list})
    
    for pfa in unique_pfa:
        pfa_id = pfa

        all_employees = [item for item in pension_list if item['pfa'] == pfa_id]
        
        get_pfa = PensionFundAdminSettings.objects.filter(id=pfa_id).first()
        if not get_pfa:
            continue
        
        this_employee_count = len(all_employees) or 0
        if this_employee_count > 0:
            last_file = os.path.join(f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx") 
            my_file = Path(last_file) 
            if my_file.is_file(): 
                os.remove(last_file)
            try:
                os.mkdir(file_path)
            except Exception:
                pass 

            this_pfa = pd.read_excel(f"{file_path}pension_schedule_template.xlsx", header=None)
            this_pfa.iloc[3,2] = f"{company_name}"
            this_pfa.iloc[5,2] = f"{payroll_date}"
            this_pfa.iloc[7,2] = f"{employer_code}"
            this_pfa.iloc[5,5] = f"{get_pfa.pfa_name}"
            this_pfa.iloc[6,5] = f"{get_pfa.pfa_account_name}"
            this_pfa.iloc[7,5] = f"{get_pfa.pfa_account_number}"
            this_pfa.iloc[8,5] = f"{get_pfa.pfa_sort_code}"
            
            total_contribution_index = 0
            sum_total_amount = 0
            for i in range(0, this_employee_count):
                employee = all_employees[i]
                # Ensure the DataFrame has enough rows
                required_row = 11 + i
                if len(this_pfa) <= required_row:
                    # Add extra rows if necessary
                    extra_rows = required_row - len(this_pfa) + 1
                    new_rows = pd.DataFrame([[None] * len(this_pfa.columns)] * extra_rows, columns=this_pfa.columns)
                    this_pfa = pd.concat([this_pfa, new_rows], ignore_index=True)

                last_name = employee.get("last_name")
                first_name = employee.get("first_name")
                pension_amount = employee.get("pension_amount")
                pension_pin = employee.get("pension_pin")
                this_pfa.iloc[11+i, 0] = i+1
                this_pfa.iloc[11+i, 1] = f"{last_name} {first_name}"
                this_pfa.iloc[11+i, 2] = f"{pension_pin}"
                # this_pfa.iloc[11+i, 3] = round_amount(employee.employer_contribution_pension_amount)
                this_pfa.iloc[11+i, 3] = 0
                this_pfa.iloc[11+i, 4] = round_amount(pension_amount)
                this_pfa.iloc[11+i, 5] = ""
                this_pfa.iloc[11+i, 6] =round_amount(this_pfa.iloc[11+i, 3]) + round_amount(this_pfa.iloc[11+i, 4])
                total_contribution_index = 11 + i
                sum_total_amount += float(this_pfa.iloc[11+i, 3]) + float(this_pfa.iloc[11+i, 4])
            
            total_row = total_contribution_index + 2
            # Check if the DataFrame has enough rows, if not, extend it
            if len(this_pfa) <= total_row:
                # Add extra rows if necessary
                extra_rows = total_row - len(this_pfa) + 1
                new_rows = pd.DataFrame([[None] * len(this_pfa.columns)] * extra_rows, columns=this_pfa.columns)
                this_pfa = pd.concat([this_pfa, new_rows], ignore_index=True)

            # Now assign the value safely
            this_pfa.iloc[total_row, 6] = sum_total_amount
            
            # this_pfa.iloc[total_contribution_index+2, 6] = sum_total_amount

            this_pfa.to_excel(f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx", index=False, header=False)
            with open(last_file, "rb") as excel_file:
                pension_schedule = excel_file.read()
                excel_file.close()
            
            send_email_with_attachment(
                file = pension_schedule,
                template_dir="pension_schedule.html",
                file_name = f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx",
                subject=f"{company_name} Pension Schedule {payroll_date}",
                recipient=f"{get_pfa.pfa_email}",
                company_name=company_name,
                company_email=hr_email if hr_email else company_email,
                pension_date=payroll_date,
                pension_name=f"{get_pfa.pfa_name}",
                sender_email=company_email,
                cc_email=hr_email
            )
            file_name = f"{file_path}{company_name}_{get_pfa.pfa_name}_Pension_Schedule_{payroll_date}.xlsx"

            try:
                os.remove(f"{file_name}") ## Delete file when done
            except PermissionError:
                pass

        else:
            pass

    return unique_pfa