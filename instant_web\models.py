from django.conf import settings
from django.db import models
from django.urls import reverse
from django.conf import settings
from urllib import parse
from core.models import BaseModel, DeleteHandler
from helpers.enums import (
    AlignmentTypes,
    InterfaceTheme,
)
from linkshortener.models import UrlData
from requisition.models import Company
from stock_inventory.models import Branch, Category
from django.contrib.auth import get_user_model

User = get_user_model()
logger = settings.LOGGER

class InstantWeb(BaseModel, DeleteHandler):
    company = models.ForeignKey(
        Company,
        on_delete=models.PROTECT,
        related_name="instant_web_store",
    )
    branch = models.ForeignKey(
        Branch,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name="Instant_web_store",
    )
    store_brand_color = models.CharField(max_length=225, null=True, blank=True)
    store_description = models.TextField(null=True, blank=True)
    header_description = models.TextField(null=True, blank=True)
    header_images = models.JSONField(null=True, blank=True)
    header_logos = models.JSONField(null=True, blank=True)
    header_logo_text = models.CharField(max_length=255, null=True, blank=True)
    navigation_visible = models.BooleanField(default=False)
    webstore_screenshort = models.JSONField(null=True, blank=True)
    navigation_set_menu = models.ManyToManyField(
        "stock_inventory.Category",
        blank=True,
    )
    navigation_alignment = models.CharField(
        max_length=225, choices=AlignmentTypes.choices, default=AlignmentTypes.TOP
    )
    logo_alignment = models.CharField(
        max_length=225, choices=AlignmentTypes.choices, default=AlignmentTypes.TOP
    )
    header_banner_type = models.CharField(
        max_length=225, blank=True, null=True
    )
    banner_color = models.CharField(
        max_length=225, blank=True, null=True
    )
    contact_visible = models.BooleanField(default=False)
    contact_phone_number = models.CharField(max_length=13, null=True, blank=True)
    whatsapp_phone_number = models.CharField(max_length=13, null=True, blank=True)
    x_link = models.URLField(blank=True, null=True)
    instagram_link = models.URLField(blank=True, null=True)
    facebook_link = models.URLField(blank=True, null=True)
    contact_email = models.EmailField(null=True, blank=True)
    contact_address = models.TextField(null=True, blank=True)
    contact_description = models.TextField(null=True, blank=True)
    redirect_phone_number = models.CharField(max_length=13, null=True, blank=True)
    interface_theme = models.CharField(
        max_length=225, choices=InterfaceTheme.choices, default=InterfaceTheme.SYSTEM
    )
    order_completion_message = models.TextField(null=True, blank=True)
    redirect_after_payment_url = models.URLField(null=True, blank=True)
    success_message = models.TextField(null=True, blank=True)
    store_url = models.URLField(max_length=200, null=True, blank=True)
    notification = models.TextField(null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT)

    def __str__(self) -> str:
        return f"{self.company.company_name}: {self.store_url}"


    @property
    def whatsapp_url(self):
        phone_number = self.whatsapp_phone_number
        text = f"""  """
        encoded_message = parse.quote(text.strip())
        whatsapp_url = f"https://wa.me/{phone_number}?text={encoded_message}"
        return whatsapp_url

    # def format_phone_number(self, phone_number):
    #
    #     """Format the phone number by stripping the first digit and adding '234'."""
    #     # Strip any non-digit characters (e.g., spaces, hyphens)
    #
    #     if not phone_number:
    #         return phone_number
    #
    #     phone_number = ''.join(filter(str.isdigit, phone_number))
    #     # Check if the phone number starts with '0' and replace with '234'
    #     if phone_number.startswith("0"):
    #         phone_number = "234" + phone_number[1:]
    #     return phone_number



class QRCode(BaseModel):
    name = models.CharField(max_length=100)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    categories = models.ManyToManyField(Category, blank=True)
    all_categories = models.BooleanField(default=False)
    scans = models.PositiveIntegerField(default=0, null=True, blank=True)
    code_url = models.URLField(max_length=200, null=True, blank=True)
    link = models.URLField(max_length=200, null=True, blank=True)

    def generate_qr_code(self):
        try:
            instant_web = InstantWeb.objects.get(company=self.company, branch=self.branch)
            store_url = instant_web.store_url
        except InstantWeb.DoesNotExist:
            store_url = None
        
        if store_url:
            if self.all_categories:
                self.link = store_url
            else:
                categories = self.categories.all()
                if categories.exists():
                    # category_names = [category.name for category in categories]
                    # print("category_names---", category_names)
                    # category_params = '&'.join([f"category={name}" for name in category_names])
                    # print("category_params---", category_params)
                    # self.link = f"{store_url}?{category_params}"
                    # print("self.link ---", self.link)
                    category_ids = [category.id for category in categories]
                    # print("category_ids---", category_ids)
                    category_params = '&'.join([f"category={category_id}" for category_id in category_ids])
                    # print("category_params---", category_params)
                    self.link = f"{store_url}?{category_params}"
                    # print("self.link ---", self.link)
                else:
                    self.link = store_url
            self.save()  # Save the model instance after modifying self.link
        
        base_url = settings.BASE_URL
        qr_code_link_view_url = reverse('qr-code-link', kwargs={'pk': self.id})
        full_url = f"{base_url}{qr_code_link_view_url}"
        print("full_url---", full_url)

        short_url = UrlData.slugify_url(url=full_url)
        self.code_url = f"https://pbxl.cc/lk/{short_url}"
        self.save() 



class Table(BaseModel):
    name = models.CharField(max_length=100)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    no_of_seats = models.PositiveIntegerField()
    categories = models.ManyToManyField(Category, blank=True)
    all_categories = models.BooleanField(default=False)
    scans = models.PositiveIntegerField(default=0, null=True, blank=True)
    code_url = models.URLField(max_length=200, null=True, blank=True)
    link = models.URLField(max_length=200, null=True, blank=True)


    def generate_table_qr_code(self):
        try:
            instant_web = InstantWeb.objects.get(company=self.company, branch=self.branch)
            store_url = instant_web.store_url
        except InstantWeb.DoesNotExist:
            store_url = None
        
        if store_url:
            if self.all_categories:
                self.link = store_url
            else:
                categories = self.categories.all()
                if categories.exists():
                    # category_names = [category.name for category in categories]
                    # print("category_names---", category_names)
                    # category_params = '&'.join([f"category={name}" for name in category_names])
                    # print("category_params---", category_params)
                    # self.link = f"{store_url}?{category_params}"
                    # print("self.link ---", self.link)
                    category_ids = [category.id for category in categories]
                    # print("category_ids---", category_ids)
                    category_params = '&'.join([f"category={category_id}" for category_id in category_ids])
                    # print("category_params---", category_params)
                    self.link = f"{store_url}?{category_params}"
                    # print("self.link ---", self.link)
                else:
                    self.link = store_url
            self.save()  # Save the model instance after modifying self.link
        
        base_url = settings.BASE_URL
        qr_code_link_view_url = reverse('qr-code-link', kwargs={'pk': self.id})
        full_url = f"{base_url}{qr_code_link_view_url}"
        print("full_url---", full_url)

        short_url = UrlData.slugify_url(url=full_url)
        self.code_url = f"https://pbxl.cc/lk/{short_url}"
        self.save() 






































