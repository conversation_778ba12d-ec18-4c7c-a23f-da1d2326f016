from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from accounting.models import Account, AccountType, JournalEntry, JournalLine
from accounting.resources import AccountResource, AccountTypeResource, JournalEntryResource, JournalLineResource

# Register your models here.

class AccountTypeResourceAdmin(ImportExportModelAdmin):
    resource_class = AccountTypeResource
    search_fields = [
        "name",
    ]
    list_filter = ["standard_balance",]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class AccountResourceAdmin(ImportExportModelAdmin):
    resource_class = AccountResource
    search_fields = [
        "name", "account_code", "company__company_name"
    ]
    list_filter = ["statement_type", "statement_category", "paybox_default", "is_active"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class JournalEntryResourceAdmin(ImportExportModelAdmin):
    resource_class = JournalEntryResource
    search_fields = [
        "journal_number", "company__company_name", "branch__name"
    ]
    list_filter = ["journal_type",]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class JournalLineResourceAdmin(ImportExportModelAdmin):
    resource_class = JournalLineResource
    search_fields = [
        "journal_entry__journal_number",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]



admin.site.register(AccountType, AccountTypeResourceAdmin)
admin.site.register(Account, AccountResourceAdmin)
admin.site.register(JournalEntry, JournalEntryResourceAdmin)
admin.site.register(JournalLine, JournalLineResourceAdmin)