from django.db.models import TextChoices


# Create your enumeration type(s) here.
class CashBookChoices(TextChoices):
    CASH_IN_BANK = "CASH_IN_BANK", "CASH_IN_BANK"
    CASH_IN_HAND = "CASH_IN_HAND", "CASH_IN_HAND"


class CustomerStatus(TextChoices):
    ACTIVE = "ACTIVE", "ACTIVE"
    INACTIVE = "INACTIVE", "INACTIVE"


class MeansOfPaymentChoices(TextChoices):
    CARD = "CARD", "CARD"
    CASH = "CASH", "CASH"
    DELIVERY = "DELIVERY", "DELIVERY"
    OTHER_TRANSFER = "OTHER_TRANSFER", "OTHER_TRANSFER"
    OTHERS = "OTHERS", "OTHERS"
    # PAYMENT_LINK = "PAYMENT_LINK", "PAYMENT_LINK"     # Deprecated.
    SPLIT = "SPLIT", "SPLIT"
    TRANSFER = "TRANSFER", "TRANSFER"
    USSD = "USSD", "USSD"


class RestockChoices(TextChoices):
    NO = "NO", "NO"
    YES = "YES", "YES"


class ReturnRefundTypes(TextChoices):
    REFUND = "REFUND", "REFUND"
    SWAP = "SWAP", "SWAP"


class ReturnChoices(TextChoices):
    COMPATIBILITY = "ITEM IS NOT COMPATIBLE", "ITEM IS NOT COMPATIBLE"
    DAMAGE = "DAMAGED OR DEFECTIVE ITEM", "DAMAGED OR DEFECTIVE ITEM"
    DELIVERY = "DELAYED DELIVERY", "DELAYED DELIVERY"
    EXPIRED = "EXPIRED ITEM", "EXPIRED ITEM"
    HAZARD = "HEALTH & SAFETY", "HEALTH & SAFETY"
    MISSING = "MISSING PARTS", "MISSING PARTS"
    QUALITY = "POOR QUALITY OR CRAFTSMANSHIP", "POOR QUALITY OR CRAFTSMANSHIP"
    OTHERS = "OTHERS", "OTHERS"


class SendMessageTypes(TextChoices):
    EMAIL = "EMAIL", "EMAIL"
    SMS = "SMS", "SMS"


class TransactionStatusChoices(TextChoices):
    APPROVED = "APPROVED", "APPROVED"
    CANCELLED = "CANCELLED", "CANCELLED"
    COMPLETED = "COMPLETED", "COMPLETED"
    DENIED = "DENIED", "DENIED"
    FAILED = "FAILED", "FAILED"
    IN_PROGRESS = "IN_PROGRESS", "IN_PROGRESS"
    NOT_COMPLETED = "NOT_COMPLETED", "NOT_COMPLETED"
    PENDING = "PENDING", "PENDING"
    SAVED = "SAVED", "SAVED"
    SUCCESSFUL = "SUCCESSFUL", "SUCCESSFUL"
