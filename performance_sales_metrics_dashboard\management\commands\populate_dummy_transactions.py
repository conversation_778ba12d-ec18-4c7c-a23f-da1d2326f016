from django.utils import timezone
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
import random
from datetime import datetime, timedelta
import pytz
import uuid
from account import enums as enums
from requisition import models as req
from account import models as acc

User = get_user_model()
logger = settings.LOGGER



class Command(BaseCommand):
    help = 'Populate the database with dummy transactions for testing.'

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        # Get all companies and users from the database
        companies = list(req.Company.objects.all())
        users = list(User.objects.all())

        if not companies or not users:
            self.stdout.write(self.style.ERROR(
                "No companies or users found in the database. Please add some companies and users."))
            return

        payout_types = [
            enums.AccountType.SPEND_MGMT,
            enums.AccountType.SALES,
            enums.AccountType.PAYROLL,
        ]
        transaction_statuses = [
            enums.TransactionStatus.PENDING,
            enums.TransactionStatus.SUCCESSFUL,
            enums.TransactionStatus.FAILED,
        ]
        transfer_stages = [
            enums.TransferStage.FLOAT_TO_INTERNAL,
            enums.TransferStage.FLOAT_TO_EXTERNAL,
            enums.TransferStage.INTERNAL_TO_OUTWARDS,
        ]

        for company in companies:
            for _ in range(15):
                user = random.choice(users)
                payout_type = random.choice(payout_types)
                status = random.choice(transaction_statuses)
                stage = random.choice(transfer_stages)

                amount = round(random.uniform(100.0, 10000.0), 2)
                commission = round(amount * 0.02, 2)
                balance_before = round(random.uniform(1000.0, 20000.0), 2)
                balance_after = balance_before - amount

                transaction = acc.Transaction.objects.create(
                    user=user,
                    company_name=company.company_name,
                    user_full_name=f"{user.first_name} {user.last_name}",
                    user_email=user.email,
                    amount=amount,
                    commission=commission,
                    balance_before=balance_before,
                    balance_after=balance_after,
                    status=status,
                    transaction_type=enums.TransactionType.BANK_TRANSFER,
                    debit_amount=amount,
                    total_amount_received=amount - commission,
                    transfer_provider=enums.AcctProvider.VFD,
                    payout_type=payout_type,
                    transfer_stage=stage,
                    transaction_ref=str(uuid.uuid4()),  # Generating a valid UUID
                    provider_fee=round(amount * 0.01, 2),
                    beneficiary_account_number=str(random.randint(**********, **********)),
                    beneficiary_account_name=f"Beneficiary {random.randint(1, 100)}",
                    bank_name="Random Bank",
                    bank_code="123",
                    source_account_name=f"Source {random.randint(1, 100)}",
                    source_account_number=str(random.randint(**********, **********)),
                    narration="Dummy transaction",
                    date_credited=timezone.now() - timedelta(days=random.randint(0, 30)),
                )
                transaction.save()

        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"Dummy transactions populated successfully in {(END_TIME - START_TIME).total_seconds()} seconds"))
