from decouple import config
import paho.mqtt.client as mqtt


broker = config("MQTT_BROKER", cast=str)
password = config("MQTT_PASS", cast=str)
port = config("MQTT_PORT", cast=int)
username = config("MQTT_USER", cast=str)


# MQTT messaging platform.
class EMQXHandler:
    def __init__(self, keepalive=60):
        self.client_id = username
        self.broker = broker
        self.port = port
        self.keepalive = keepalive
        self.username = username
        self.password = password

        # Initialize the client with callback API version
        self.client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2, self.username)

        # Set authentication if provided
        if self.username and self.password:
            self.client.username_pw_set(self.username, self.password)

        # Set callback functions
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message

    def on_connect(self, client, userdata, flags, rc, properties=None):
        """Callback for when the client receives a CONNACK response from the broker."""
        if rc == 0:
            print("Connected successfully to the broker")
        else:
            print(f"Connection failed with code {rc}")

    def on_message(self, client, userdata, message):
        """Callback for when a PUBLISH message is received from the broker."""
        print(f"Received message: {message.payload.decode()} on topic {message.topic}")

    def connect(self):
        """Connect to the MQTT broker."""
        try:
            self.client.connect(self.broker, self.port, self.keepalive)
            print(f"Successfully connected to client")
        except Exception as e:
            print(f"Could not connect to the broker: {e}")
            exit(1)

    def subscribe(self, topic):
        """Subscribe to a topic."""
        self.client.subscribe(topic)
        print(f"Subscribed to topic: {topic}")

    def publish(self, topic, message):
        """Publish a message to a topic."""
        self.client.publish(topic, message)
        print(f"Published message: '{message}' to topic: '{topic}'")

    def start(self):
        """Start the MQTT client loop."""
        self.client.loop_start()

    def stop(self):
        """Stop the MQTT client loop."""
        self.client.loop_stop()
