# Cart Management App Documentation

## Overview

The Cart Management app handles shopping cart functionality, checkout processes, and order creation for e-commerce operations on the Paybox360 platform. It provides tools for managing product selections, calculating totals, and converting carts to orders.

## Core Features

### 1. Shopping Cart Management

- Cart creation and persistence
- Product addition and removal
- Quantity adjustments
- Cart expiration handling

### 2. Checkout Process

- Shipping information collection
- Billing details management
- Payment method selection
- Order summary generation

### 3. Price Calculation

- Product pricing application
- Discount and promotion handling
- Tax calculation
- Shipping cost determination

### 4. Cart Recovery

- Abandoned cart tracking
- Recovery email generation
- Cart restoration
- Conversion analytics

### 5. Guest Checkout

- Anonymous cart creation
- Guest user tracking
- Account creation option
- Order tracking for guests

## How It Works

1. **Cart Creation**: User adds products to their shopping cart
2. **Cart Management**: User reviews and modifies cart contents
3. **Checkout Initiation**: User proceeds to checkout process
4. **Information Collection**: Shipping and billing details are collected
5. **Order Creation**: Cart is converted to an order
6. **Payment Processing**: Payment is collected for the order

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `Cart` | Shopping cart | `created_at`, `updated_at`, `status` | User, WebStore |
| `CartItem` | Cart line items | `product`, `quantity`, `price` | Cart, StoreProduct |
| `Checkout` | Checkout process | `shipping_address`, `billing_address`, `status` | Cart |
| `ShippingMethod` | Delivery options | `name`, `price`, `estimated_days` | WebStore |
| `CartDiscount` | Applied discounts | `code`, `amount`, `type` | Cart |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `add_to_cart` | Adds products to cart | `services.py` | Called during product selection |
| `calculate_cart_totals` | Computes cart value | `models.py` (Cart) | Used during cart updates |
| `process_checkout` | Handles checkout flow | `services.py` | Called during order creation |
| `apply_cart_discount` | Applies promotions | `services.py` | Used for discount handling |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/cart-management/carts/` | Manage shopping carts | POST/GET | `CartViewSet` |
| `/cart-management/items/` | Manage cart items | POST/GET/DELETE | `CartItemViewSet` |
| `/cart-management/checkout/` | Process checkout | POST | `CheckoutView` |
| `/cart-management/shipping-methods/` | Get shipping options | GET | `ShippingMethodViewSet` |
| `/cart-management/discounts/` | Apply discounts | POST | `CartDiscountView` |

### Integration Points

- **Instant Web App**: For storefront integration
- **Stock Inventory App**: For product availability
- **Sales App**: For order creation
- **Account App**: For payment processing

## Key Considerations

### 1. Cart Persistence

- **Responsible App**: Cart Management app
- **Key Functions**:
  - Session-based cart storage
  - User account cart association
  - Cart merging on login

### 2. Inventory Validation

- **Responsible App**: Cart Management app and Stock Inventory app
- **Key Functions**:
  - Stock availability checking
  - Quantity validation
  - Temporary inventory reservation

### 3. Price Consistency

- **Responsible App**: Cart Management app
- **Key Functions**:
  - Price locking during checkout
  - Price change notifications
  - Discount validation

### 4. Checkout Security

- **Responsible App**: Cart Management app
- **Key Functions**:
  - Secure data transmission
  - Address validation
  - Fraud prevention measures

### 5. Cart Abandonment Handling

- **Responsible App**: Cart Management app
- **Key Functions**:
  - Abandonment detection
  - Recovery workflow
  - Conversion tracking