from import_export import resources

from performance_sales_metrics_dashboard.models import (
    Activity,
    Category,
    Lead,
    Merchants,
    Notes,
    Pipeline,
    ProductVerticals,
    RevenueLines,
    SalesLead,
    SalesOfficer,
    Stage,
    # SystemUsageMetrics,
    Task,
    Emails,
    Attachment,
    BookADemo, NewsLetterSubscription,
)


class SalesLeadResource(resources.ModelResource):
    class Meta:
        model = SalesLead


class ProductVerticalsResource(resources.ModelResource):
    class Meta:
        model = ProductVerticals


class RevenueLinesResource(resources.ModelResource):
    class Meta:
        model = RevenueLines


class SalesOfficerResource(resources.ModelResource):
    class Meta:
        model = SalesOfficer


class ProductVerticalResource(resources.ModelResource):
    class Meta:
        model = ProductVerticals


class RevenueLineResource(resources.ModelResource):
    class Meta:
        model = RevenueLines


class PipelineResource(resources.ModelResource):
    class Meta:
        model = Pipeline


class StageResource(resources.ModelResource):
    class Meta:
        model = Stage


class LeadResource(resources.ModelResource):
    class Meta:
        model = Lead


class ActivityResource(resources.ModelResource):
    class Meta:
        model = Activity


class TaskResource(resources.ModelResource):
    class Meta:
        model = Task


class NotesResource(resources.ModelResource):
    class Meta:
        model = Notes


class MerchantResource(resources.ModelResource):
    class Meta:
        model = Merchants


class CategoryResource:
    class Meta:
        model = Category


# class SystemUsageMetricssResource(resources.ModelResource):
#     class Meta:
#         model = SystemUsageMetrics


class EmailResource:
    class Meta:
        model = Emails


class AttachmentResource:
    class Meta:
        model = Attachment


class DemoResource:
    class Meta:
        model = BookADemo


class NewsLetterSubscriptionResource:
    class Meta:
        model = NewsLetterSubscription
