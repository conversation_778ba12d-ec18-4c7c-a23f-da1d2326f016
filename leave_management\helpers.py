from datetime import datetime, timedelta
from django.utils import timezone


def filter_dates(datetime):
    inception = datetime(2023, 1, 1)

    today = timezone.now()
    previous_day = timezone.now() - timedelta(days=1)

    current_month_start = datetime(today.year, today.month, 1)
    current_date = timezone.now().date()
    month_start = datetime(current_date.year, current_date.month, 1)
    if today.month > 1:
        previous_month_start = datetime(today.year, today.month-1, 1)
    else:
        previous_month_start = datetime(today.year, 12, 1)
    previous_month_end = current_month_start + timedelta(days=-1)

    week_start = today - timedelta(days=current_date.weekday())
    previous_week_end = week_start - timedelta(days=1)
    previous_week_start = previous_week_end - timedelta(days=previous_week_end.weekday())

    year_start = datetime(current_date.year, 1, 1)
    previous_year_start = datetime(today.year-1, 1, 1)
    previous_year_end = datetime(today.year-1, 12, 31)

    data = {
        "inception": inception,
        "today": today + timedelta(days=1),
        "previous_day": previous_day,
        "week_start": week_start.date(),
        "month_start": month_start,
        "year_start": year_start,
        "previous_week_start": previous_week_start.date(),
        "previous_week_end": previous_week_end.date(),
        "previous_month_end": previous_month_end,
        "previous_month_start": previous_month_start,
        "previous_year_start": previous_year_start,
        "previous_year_end": previous_year_end
    }

    return data