# Stock Inventory App Documentation

## Overview

The Stock Inventory app manages product inventory, stock levels, and inventory operations for businesses on the Paybox360 platform. It provides tools for tracking products, managing stock movements, and maintaining accurate inventory records across multiple locations.

## Core Features

### 1. Product Management

- Product creation and categorization
- Product attributes and variants
- Pricing information
- Product lifecycle tracking

### 2. Inventory Tracking

- Stock level monitoring
- Inventory valuation
- Low stock alerts
- Inventory counts and adjustments

### 3. Stock Movement

- Stock receipts and issues
- Transfers between locations
- Movement history
- Batch and serial number tracking

### 4. Multi-location Support

- Branch and warehouse management
- Location-specific inventory
- Transfer operations
- Consolidated reporting

### 5. Supplier Management

- Supplier information
- Purchase order tracking
- Supplier performance metrics
- Reorder management

## How It Works

1. **Product Setup**: Companies define products and their attributes
2. **Location Configuration**: Inventory locations are established
3. **Initial Stocking**: Products are added to inventory with quantities
4. **Movement Tracking**: System records all inventory movements
5. **Stock Monitoring**: Inventory levels are tracked and reported
6. **Replenishment**: Low stock triggers reordering processes

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `Product` | Product information | `name`, `sku`, `category` | Company |
| `Inventory` | Stock records | `quantity`, `reorder_level` | Product, Location |
| `StockMovement` | Inventory transactions | `quantity`, `movement_type`, `date` | Product, Location |
| `Location` | Storage locations | `name`, `type`, `address` | Company |
| `Supplier` | Vendor information | `name`, `contact_info`, `rating` | Company |
| `PurchaseOrder` | Procurement records | `order_date`, `status`, `total_amount` | Supplier |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `update_inventory_level` | Adjusts stock quantities | `models.py` (Inventory) | Called after stock movements |
| `calculate_inventory_value` | Computes stock worth | `services.py` | Used in financial reporting |
| `check_reorder_levels` | Identifies low stock | `tasks.py` | Scheduled task for alerts |
| `process_stock_transfer` | Handles location transfers | `services.py` | Used in transfer operations |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/stock-inventory/products/` | Manage products | POST/GET | `ProductViewSet` |
| `/stock-inventory/inventory/` | View stock levels | GET | `InventoryViewSet` |
| `/stock-inventory/movements/` | Record stock changes | POST/GET | `StockMovementViewSet` |
| `/stock-inventory/locations/` | Manage locations | POST/GET | `LocationViewSet` |
| `/stock-inventory/suppliers/` | Manage suppliers | POST/GET | `SupplierViewSet` |

### Integration Points

- **Procurement Module**: For purchase order processing
- **Sales App**: For order fulfillment
- **Accounting App**: For inventory valuation
- **Notification System**: For stock alerts

## Key Considerations

### 1. Inventory Accuracy

- **Responsible App**: Stock Inventory app
- **Key Functions**:
  - Regular reconciliation processes
  - Inventory count workflows
  - Movement validation in StockMovement model

### 2. Stock Valuation Methods

- **Responsible App**: Stock Inventory app
- **Key Functions**:
  - FIFO/LIFO/Average cost calculations
  - Valuation adjustments
  - Cost tracking for financial reporting

### 3. Multi-location Management

- **Responsible App**: Stock Inventory app
- **Key Functions**:
  - Location-specific inventory in Location model
  - Transfer operations between locations
  - Consolidated view across all locations

### 4. Batch and Expiry Tracking

- **Responsible App**: Stock Inventory app
- **Key Functions**:
  - Batch number assignment and tracking
  - Expiry date monitoring
  - FEFO (First Expired, First Out) management

### 5. Integration with Procurement

- **Responsible App**: Stock Inventory app and Requisition app
- **Key Functions**:
  - Purchase order to inventory receipt workflow
  - Supplier performance tracking
  - Reorder automation based on inventory levels