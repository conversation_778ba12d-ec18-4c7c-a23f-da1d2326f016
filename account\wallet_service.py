import random
from account.abstract_provider import VfdAccountProvider
from account.enums import AcctProvider, TransactionStatus
from helpers.reusable_functions import convert_date_format
from stock_inventory.models import Branch
from .models import (
    AccountSystem,
    Wallet,
    KycDetails,
    User,
    Company,
    BlackListEntry,
    AccountCreationFailure,
)

from django.db import transaction
from django.utils import timezone

import logging

logger = logging.getLogger(__name__)

PROVIDER_MAP = {
    "vfd": VfdAccountProvider,
    # Add other providers here
}


def get_account_provider(slug):
    provider_class = PROVIDER_MAP.get(slug.lower())
    if not provider_class:
        raise ValueError("Unsupported account provider")
    return provider_class()


def get_valid_kyc_or_none(user_id):
    try:
        return KycDetails.objects.get(
            user_id=user_id,
            bvn_birthdate__isnull=False,
            bvn_number__isnull=False,
        )
    except KycDetails.DoesNotExist:
        return None


def get_company_or_none(company_id, branch_id=None):

    try:
        # Fetch the company instance with company wallet type "CORPORATE"
        company_instance = Company.objects.get(
            id=company_id, company_wallet_type="CORPORATE"
        )
        company_owner = company_instance.user
        user_id = company_owner.id
        branch = Branch.retrieve_company_branch(id=branch_id, company=company_instance)
        return company_instance
    except Company.DoesNotExist:
        return None


def find_previous_account_by_bvn(bvn, corporate=False):
    for kyc in KycDetails.objects.filter(bvn_number=bvn):
        account = AccountSystem.objects.filter(
            user=kyc.user,
            company__isnull=not corporate,
            account_provider=AcctProvider.VFD,
            is_active=True,
        ).first()
        if account:
            return account.account_number
    return None


@transaction.atomic
def get_or_create_account(user_id, account_type, provider, company_id=None):
    company = Company.objects.filter(id=company_id).first() if company_id else None
    existing = AccountSystem.objects.filter(
        user_id=user_id,
        company=company,
        account_type=account_type,
        account_provider=provider,
    )
    if existing.exists():
        return existing.first()

    account_number = timezone.now().strftime("%Y%m%d%H%M%S%f")
    # account_number = "".join(str(random.randint(0, 9)) for _ in range(11))
    user = User.objects.get(id=user_id)
    return AccountSystem.objects.create(
        user_id=user_id,
        account_provider=provider,
        company=company,
        account_number=account_number[6:],
        account_name=user.full_name.title(),
        account_type=account_type,
        request_status=TransactionStatus.FAILED,
        is_active=False,
        payload={},
    )


@transaction.atomic
def get_or_create_wallet(user_id, account, wallet_type):
    wallet, _ = Wallet.objects.get_or_create(
        user_id=user_id,
        account=account,
        wallet_type=wallet_type,
        defaults={"is_active": False},
    )
    return wallet


def generate_unique_bvn_and_rc_number_for_corporate_wallet(
    user_id, bvn, cac_num, provider=AcctProvider.VFD
):

    existing_account_count = AccountSystem.objects.filter(
        user_id=user_id,
        company__isnull=True,
        account_provider=provider,
        is_active=True,
    ).count()

    if existing_account_count > 0:
        bvn = f"{bvn}-{existing_account_count + 1}"
        rc_number = f"{cac_num}-{existing_account_count + 1}"
    else:
        rc_number = cac_num

    return bvn, rc_number


def get_wallet_and_account_instance(user_id, account_type, provider, company_id=None):

    account = get_or_create_account(user_id, account_type, provider, company_id)
    wallet = get_or_create_wallet(user_id, account, account_type)

    return {"account": account, "wallet": wallet}


def handle_account_success(account, wallet, result, corporate_account=False):

    response_data = result.get("response", {}).get("data", {})
    account_number = response_data.get("accountNo")
    account.account_number = account_number
    if not corporate_account:
        account_name = (
            f"{response_data.get('firstname')} {response_data.get('lastname')}"
        )
        account.account_name = account_name
    else:
        account_name = response_data.get("accountName")
        account.account_name = account_name

    account.bank_name = "VFD Microfinance Bank"
    account.bank_code = "999999"
    account.is_active = True
    account.request_status = TransactionStatus.SUCCESSFUL
    account.payload = result
    account.save(
        update_fields=[
            "account_number",
            "account_name",
            "bank_name",
            "bank_code",
            "is_active",
            "request_status",
            "payload",
        ]
    )
    wallet.is_active = True
    wallet.save(update_fields=["is_active"])
    email = account.user.email
    BlackListEntry.remove_user_eamil_from_blacklist(user_email=email)
    BlackListEntry.remove_user_eamil_from_blacklist(
        user_email=email, blacklist_type="ACCOUNT_CREATION"
    )
    return {
        "account_number": account_number,
        "account_name": account_name,
    }


def handle_account_failure(user, account_type, provider, error):
    user_id = user.id
    AccountCreationFailure.objects.create(
        user=user,
        account_type=account_type,
        account_provider=provider,
        payload=str(error),
    )
    BlackListEntry.blacklist_user_email(
        user_email=user.email, blacklist_type="ACCOUNT_CREATION"
    )
    logger.error(f"Account creation failed for user {user_id}: {error}")


def create_personal_wallet(
    user_id,
    account_type,
    provider: AcctProvider,
    company_id=None,
):
    user = User.objects.get(id=user_id)
    kyc = get_valid_kyc_or_none(user_id)
    wallet_account_details = get_wallet_and_account_instance(
        user_id, account_type, provider, company_id
    )

    wallet = wallet_account_details.get("wallet")
    account = wallet_account_details.get("account")

    if account.is_active:
        return {"message": "Account already exists and is active"}

    if not kyc:

        return handle_account_failure(
            user=user,
            account_type=account_type,
            provider=provider,
            error="KYC DoesNotExist",
        )

    provider_class = get_account_provider(provider)
    previous_account_number = find_previous_account_by_bvn(kyc.bvn_number)

    try:
        formatted_date = convert_date_format(date_str=kyc.bvn_birthdate)
        result = provider_class.create_account(
            kyc=kyc,
            formatted_date=formatted_date,
            previous_account_number=previous_account_number,
        )
        if (
            result.get("status") == "success"
            and result.get("response", {}).get("status") == "00"
        ):
            return handle_account_success(account, wallet, result)
        else:
            return handle_account_failure(user, account_type, provider, result)
    except Exception as e:
        return handle_account_failure(user, account_type, provider, str(e))



def create_corporate_wallet(
    account_type,
    provider: AcctProvider,
    company_id,
    branch_id=None,
):

    company = get_company_or_none(company_id=company_id, branch_id=branch_id)

    if not company:
        return handle_account_failure(
            account=account,
            account_type=account_type,
            provider=provider,
            error=f"Invalid company id {company_id}",
        )
    user = company.user
    user_id = user.id

    kyc = get_valid_kyc_or_none(user_id)

    wallet_account_details = get_wallet_and_account_instance(
        user_id, account_type, provider, company_id
    )

    if not kyc:

        return handle_account_failure(
            user=user,
            account_type=account_type,
            provider=provider,
            error="KYC DoesNotExist",
        )
    wallet = wallet_account_details.get("wallet")
    account: AccountSystem = wallet_account_details.get("account")

    if account.is_active:
        return {"message": "Account already exists and is active"}

    provider_class = get_account_provider(provider)
    bvn, cac_number = generate_unique_bvn_and_rc_number_for_corporate_wallet(
        user_id=user_id, bvn=kyc.bvn_number, cac_num=company.cac_num
    )

    try:
        incorporation_date = convert_date_format(
            date_str=company.incorporation_date, with_spaces=True
        )
        result = provider_class.create_account(
            kyc=kyc,
            formatted_date=incorporation_date,
            cac_num=cac_number,
            account_type=account_type,
            company_name=company.company_name,
            bvn=bvn,
        )
        if (
            result.get("status") == "success"
            and result.get("response", {}).get("status") == "00"
        ):
            return handle_account_success(account, wallet, result, True)
        else:
            return handle_account_failure(user, account_type, provider, result)
    except Exception as e:
        return handle_account_failure(user, account_type, provider, str(e))


def vfd_create_update_paybox_user_default_wallet(user_id):
    main_user_walletsystem = [
        "PERSONAL_PAYROLL",
        "INSTANT_WAGE",
        "PERSONAL_SPEND_MGMT",
    ]
    result = []
    for user_wallet in main_user_walletsystem:

        create_wallet_handler = create_personal_wallet(
            user_id=user_id,
            account_type=user_wallet,
            provider="VFD",
        )

        result.append(create_wallet_handler)
    return result


def vfd_create_corporate_wallet(company_id, branch_id, account_type=None):
    corporate_wallet = [
        account_type,
        "PAYROLL",
        "SPEND_MGMT",
        "PENSION",
    ]
    result = []
    for wallet_type in corporate_wallet:
        if not wallet_type:
            continue
        create_corporate_wallet_handler = create_corporate_wallet(
            account_type=wallet_type,
            branch_id=branch_id,
            company_id=company_id,
            provider="VFD",
        )

        result.append(create_corporate_wallet_handler)
    return result
