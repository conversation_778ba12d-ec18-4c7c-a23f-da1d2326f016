import logging
import random
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand, CommandError

from subscription_and_invoicing.models import (
    Module,
    CompanySubscription,
    ModuleSubscription,
    SubscriptionAudit,
)

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = "Create free trial and free merchant subscriptions for the user"

    def handle(self, *args, **kwargs):
        email = "<EMAIL>"
        try:
            # Get the user by email
            user = User.objects.get(email=email)

            # Ensure the user has a default company
            default_company = user.default_company
            if not default_company:
                raise CommandError(f"User {email} has no default company set.")

            self.stdout.write(self.style.SUCCESS(f"Processing free subscriptions for: {default_company.company_name}"))

            # Fetch all modules from the database
            modules = Module.objects.all()

            # Create FREE TRIAL subscription
            free_trial_subscription = CompanySubscription.objects.create(
                company=default_company,
                access_type="trial",
                status="trial",
                created_by=user,
            )

            for module in modules:
                trial_days = random.randint(5, 12)  # Randomly assign between 5 and 12 days
                start_date = timezone.now()
                end_date = start_date + timezone.timedelta(days=trial_days)

                ModuleSubscription.objects.create(
                    company_subscription=free_trial_subscription,
                    module=module,
                    is_active=True,
                    start_date=start_date,
                    end_date=end_date,
                    status="active",
                )

            # Create FREE MERCHANT subscription
            free_merchant_subscription = CompanySubscription.objects.create(
                company=default_company,
                access_type="bypass",
                status="active",
                created_by=user,
            )

            for module in modules:
                ModuleSubscription.objects.create(
                    company_subscription=free_merchant_subscription,
                    module=module,
                    is_active=True,
                    start_date=timezone.now(),
                    end_date=timezone.now() + timezone.timedelta(days=365),  # Set a default period (e.g., 1 year)
                    status="active",
                )

            # Log the subscription audit
            SubscriptionAudit.objects.create(
                company=default_company,
                subscription=free_trial_subscription,
                action="Free Trial Subscription created",
                details={
                    "status": "trial",
                    "modules": [module.name for module in modules],
                },
            )

            SubscriptionAudit.objects.create(
                company=default_company,
                subscription=free_merchant_subscription,
                action="Free Merchant Subscription created",
                details={
                    "status": "active",
                    "modules": [module.name for module in modules],
                },
            )

            logger.info(
                f"Successfully processed free subscriptions for {default_company.company_name} under user {email}")
            self.stdout.write(self.style.SUCCESS(
                f"Free subscriptions created successfully for {email} under {default_company.company_name}"))

        except User.DoesNotExist:
            raise CommandError(f"User with email {email} does not exist.")
        except Exception as e:
            logger.error(f"Error creating subscriptions for {email}: {e}")
            raise CommandError(f"Failed to create subscriptions for {email}: {e}")
