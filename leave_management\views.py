from django.shortcuts import get_object_or_404, render
from core.tasks import upload_file_aws_s3_bucket
from django_filters.rest_framework import DjangoFilterBackend
from payroll_app.models import CompanyEmployeeList
from payroll_app.views import CustomPagination
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics, filters
from leave_management.services import LeaveDashboard, LeaveRecords, NewLeaveRequests

from payroll_app.permissions import AdminPermission, CanApproveLeave, CanCreateLeavePolicy, CanCreateLeaveType, CanDeleteLeavePolicy, CanDeleteLeaveType, CanEditLeavePolicy, CanEditLeaveType, EmployeeIsActive, UserIsActive
from .models import Company, Employee, Department, PayGroup, GradeLevel, \
    LeavePolicy, LeaveType, LeaveRequest, LeaveRecord 
from .serializers import ApproveLeaveRequestSerializer, CreateLeavePolicySerializer, CreateLeaveRequestSerializer, CreateLeaveTypeSerializer, LeavePolicySerializer, LeaveRecordDetailSerializer, LeaveRequestApprovalSerializer, LeaveTypeSerializer, \
    LeaveRequestSerializer, LeaveRecordSerializer, ListLeavePolicy, ListLeaveRecordSerializer, ListLeaveType, RejectLeaveRequestSerializer, UpdateLeavePolicySerializer, UpdateLeaveTypeSerializer
from rest_framework.permissions import AllowAny, IsAuthenticated
from core.auth.custom_auth import CustomUserAuthentication
from django.db.models import F, Value, CharField
from django.utils import timezone
from datetime import timedelta

class FetchLeaveTypeList(generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListLeaveType

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = ("frequency",)
    search_fields = ("title",)

    def get_queryset(self):
        request_user = self.request.user
        company_id = self.request.query_params.get("company_uuid")

        all_leave_types = LeaveType.objects.filter(company__id=company_id, is_deleted=False)
        return all_leave_types

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class UpdateLeaveType(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, CanEditLeaveType]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_id = self.request.query_params.get("company_uuid")      
        leave_type_id = request.query_params.get("leave_type_id")

        leave_instance = LeaveType.objects.filter(id=leave_type_id,company__id=company_id, is_deleted=False).first()
        if not leave_instance:
            return Response({"message": "leave type not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = UpdateLeaveTypeSerializer(leave_instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({"message": "leave type updated successfully"}, status=status.HTTP_200_OK)
    
class DeleteLeaveType(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, CanDeleteLeaveType]
    authentication_classes = [CustomUserAuthentication]

    def delete(self, request):
        company_id = self.request.query_params.get("company_uuid")      
        leave_type_id = request.query_params.get("leave_type_id")

        leave_instance = LeaveType.objects.filter(id=leave_type_id,company__id=company_id, is_deleted=False).first()
        if not leave_instance:
            return Response({"message": "leave type not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        leave_instance.is_deleted = True
        leave_instance.save()
        return Response({"message": "leave type deleted successfully"}, status=status.HTTP_200_OK)
    
class LeaveTypeDetails(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")      
        leave_type_id = request.query_params.get("leave_type_id")

        leave_instance = LeaveType.objects.filter(id=leave_type_id,company__id=company_id, is_deleted=False).first()
        if not leave_instance:
            return Response({"message": "leave type not found"}, status=status.HTTP_400_BAD_REQUEST)
    
        serializer = UpdateLeaveTypeSerializer(leave_instance)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
class CreateLeaveType(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, CanCreateLeaveType]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = self.request.query_params.get("company_uuid")

        company = Company.objects.get(id=company_id)

        serializer = CreateLeaveTypeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        title = serializer.validated_data.get("title")

        leave_instance = LeaveType.objects.create(
            company=company,
            title=title,
            created_by=request.user
        )
        return Response({"message": "leave type created"}, status=status.HTTP_200_OK)
    
class CreateLeavePolicy(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, CanCreateLeavePolicy]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = self.request.query_params.get("company_uuid")

        company = Company.objects.get(id=company_id)

        serializer = CreateLeavePolicySerializer(data=request.data, context={"company_id": company_id})
        serializer.is_valid(raise_exception=True)
        
        leave_type = serializer.validated_data.get("leave_type")
        grade_level = serializer.validated_data.get("grade_level")
        monthly_allocation = serializer.validated_data.get("monthly_allocation")
        yearly_allocation = serializer.validated_data.get("yearly_allocation")

        leave_policy = LeavePolicy.objects.create(
            company=company,
            leave_type=leave_type,
            grade_level=grade_level,
            monthly_allocation=monthly_allocation,
            yearly_allocation=yearly_allocation,
            created_by=request.user
        )
        return Response({"message": "leave policy created"}, status=status.HTTP_200_OK)

class FetchLeavePolicyList(generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListLeavePolicy

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("grade_level__pay_grade_name",)
    search_fields = ("leave_type__title", "grade_level__pay_grade_name")

    def get_queryset(self):
        request_user = self.request.user
        company_id = self.request.query_params.get("company_uuid")

        all_leave_types = LeavePolicy.objects.filter(company__id=company_id, is_deleted=False)
        return all_leave_types

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)

class LeavePolicyDetails(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")      
        leave_policy_id = request.query_params.get("leave_policy_id")

        leave_policy_instance = LeavePolicy.objects.filter(id=leave_policy_id, company__id=company_id, is_deleted=False).first()
        if not leave_policy_instance:
            return Response({"message": "leave policy not found"}, status=status.HTTP_400_BAD_REQUEST)
    
        serializer = UpdateLeavePolicySerializer(leave_policy_instance)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
class UpdateLeavePolicy(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, CanEditLeavePolicy]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_id = self.request.query_params.get("company_uuid")      
        leave_policy_id = request.query_params.get("leave_policy_id")

        leave_policy_instance = LeavePolicy.objects.filter(id=leave_policy_id,company__id=company_id, is_deleted=False).first()
        if not leave_policy_instance:
            return Response({"message": "leave policy not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = UpdateLeavePolicySerializer(leave_policy_instance, data=request.data, partial=True, context={"company_id": company_id})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({"message": "leave type updated successfully"}, status=status.HTTP_200_OK)

class DeleteLeavePolicy(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, CanDeleteLeavePolicy]
    authentication_classes = [CustomUserAuthentication]

    def delete(self, request):
        company_id = self.request.query_params.get("company_uuid")      
        leave_policy_id = request.query_params.get("leave_policy_id")

        leave_policy_instance = LeavePolicy.objects.filter(id=leave_policy_id,company__id=company_id, is_deleted=False).first()
        if not leave_policy_instance:
            return Response({"message": "leave policy not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        leave_policy_instance.is_deleted = True
        leave_policy_instance.save()
        return Response({"message": "leave policy deleted successfully"}, status=status.HTTP_200_OK)

class CreateLeaveRequest(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = self.request.query_params.get("company_uuid")

        company = Company.objects.get(id=company_id)

        serializer = CreateLeaveRequestSerializer(data=request.data, context={"company_id": company_id, "user":request.user})
        serializer.is_valid(raise_exception=True)
        
        leave_type = serializer.validated_data.get("leave_type")
        reason = serializer.validated_data.get("reason")
        start_date = serializer.validated_data.get("start_date")
        end_date = serializer.validated_data.get("end_date")
        priority = serializer.validated_data.get("priority")
        leave_days = serializer.validated_data.get("leave_days")
        employee = serializer.validated_data.get("employee")
        leave_document = serializer.validated_data.get("leave_document")

        leave_request = LeaveRequest.objects.create(
            employee = employee,
            company = company,
            leave_type = leave_type,
            reason = reason,
            start_date = start_date,
            end_date = end_date,
            leave_priority = priority,
            leave_days = leave_days
        )
        if leave_document:
            uploaded_file = upload_file_aws_s3_bucket(
                model_instance_id=leave_request.id, file=leave_document, model_name="LeaveRequest"
            )
        
        return Response({"message": "leave has been requested"}, status=status.HTTP_200_OK)
    
class WithDrawLeaveRequest(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = self.request.query_params.get("company_uuid")
        leave_request_id = self.request.query_params.get("leave_id")

        leave_request_ins = LeaveRequest.objects.filter(
            id=leave_request_id, 
            employee__employee=request.user, 
            company__id=company_id, 
            is_deleted=False).first()
        if not leave_request_ins:
            return Response({"message": "leave request not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        if leave_request_ins.status != "REQUESTED":
            return Response({"message": "you cannot withdraw this leave request"}, status=status.HTTP_400_BAD_REQUEST)

        leave_request_ins.status = "WITHDRAWN"
        leave_request_ins.save()
        return Response({"message": "leave request has been withdrawn"}, status=status.HTTP_200_OK)
    
class ApproveLeaveRequest(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, CanApproveLeave]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = self.request.query_params.get("company_uuid")
        leave_request_id = self.request.query_params.get("leave_id")

        serializer = ApproveLeaveRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        start_date = serializer.validated_data.get("start_date")
        end_date = serializer.validated_data.get("end_date")
        other_information = serializer.validated_data.get("other_information")
        approved_leave_days = serializer.validated_data.get("approved_leave_days")

        employee = CompanyEmployeeList.objects.filter(employee=request.user, company__id=company_id).first()

        leave_request_ins = LeaveRequest.objects.filter(
            id=leave_request_id, 
            company__id=company_id, 
            is_deleted=False).first()
        if not leave_request_ins:
            return Response({"message": "leave request not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        if leave_request_ins.status != "REQUESTED":
            return Response({"message": "you cannot approve this request"}, status=status.HTTP_400_BAD_REQUEST)

        leave_request_ins.status = "APPROVED"
        leave_request_ins.approved_leave_days = approved_leave_days
        leave_request_ins.approved_start_date = start_date
        leave_request_ins.approved_end_date = end_date
        leave_request_ins.other_information = other_information
        leave_request_ins.signed_by = employee
        leave_request_ins.save()
        return Response({"message": "leave request approved"}, status=status.HTTP_200_OK)
    
class RejectLeaveRequest(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, CanApproveLeave]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = self.request.query_params.get("company_uuid")
        leave_request_id = self.request.query_params.get("leave_id")

        serializer = RejectLeaveRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        reason = serializer.validated_data.get("reason")

        employee = CompanyEmployeeList.objects.filter(employee=request.user, company__id=company_id).first()

        leave_request_ins = LeaveRequest.objects.filter(
            id=leave_request_id, 
            company__id=company_id, 
            is_deleted=False).first()
        if not leave_request_ins:
            return Response({"message": "leave request not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        if leave_request_ins.status != "REQUESTED":
            return Response({"message": "you cannot approve this request"}, status=status.HTTP_400_BAD_REQUEST)

        leave_request_ins.status = "DISAPPROVED"
        leave_request_ins.rejected_by = employee
        leave_request_ins.rejection_reason = reason
        leave_request_ins.save()
        return Response({"message": "leave request rejected"}, status=status.HTTP_200_OK)

class FetchLeaveRecordList(generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListLeaveRecordSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("status",)
    search_fields = ("leave_type__title", "full_name")

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")

        all_leave_records = LeaveRequest.objects.filter(company__id=company_id, is_deleted=False).annotate(
            full_name=Value(F('employee__full_name'), output_field=CharField())
        )
        return all_leave_records

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)

class LeaveRecordDetails(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")      
        leave_id = request.query_params.get("leave_id")

        leave_policy_instance = LeaveRequest.objects.filter(id=leave_id, company__id=company_id, is_deleted=False).first()
        if not leave_policy_instance:
            return Response({"message": "leave record not found"}, status=status.HTTP_400_BAD_REQUEST)
    
        serializer = LeaveRecordDetailSerializer(leave_policy_instance)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
class LeaveRecordDashboard(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")
        leave_policy_instance = LeaveRequest.objects.filter(company__id=company_id, is_deleted=False)
        active_leave = leave_policy_instance.filter(status = "ACTIVE").count() or 0
        total_leave = leave_policy_instance.count() or 0
        approved_leave = leave_policy_instance.filter(status = "APPROVED").count() or 0
        rejected_leave = leave_policy_instance.filter(status = "DISAPPROVED").count() or 0

        response = {
            "total_leave": total_leave,
            "active_leave": active_leave,
            "approved_leave": approved_leave,
            "rejected_leave": rejected_leave
        }
        return Response(response, status=status.HTTP_200_OK)
    
class LeaveManagementDashboard(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")

        today = timezone.now()
        seven_days_after = today + timedelta(days=6)

        leave_policy_instance = LeaveRequest.objects.filter(company__id=company_id, is_deleted=False)
        active_leave = leave_policy_instance.filter(status = "ACTIVE").count() or 0
        total_leave = leave_policy_instance.count() or 0
        approved_leave = leave_policy_instance.filter(status = "APPROVED").count() or 0
        rejected_leave = leave_policy_instance.filter(status = "DISAPPROVED").count() or 0
        pending_leave = leave_policy_instance.filter(status = "REQUESTED").count() or 0
        upcoming_leave = leave_policy_instance.filter(status = "APPROVED", approved_start_date__gte = seven_days_after.date()).count() or 0

        response = {
            "total_leave": total_leave,
            "active_leave": active_leave,
            "approved_leave": approved_leave,
            "rejected_leave": rejected_leave,
            "pending_leave": pending_leave,
            "upcoming_leave": upcoming_leave
        }
        return Response(response, status=status.HTTP_200_OK)

class FetchNewLeaveRequestList(generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListLeaveRecordSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("status",)
    search_fields = ("leave_type__title", "full_name")

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")

        all_leave_records = LeaveRequest.objects.filter(company__id=company_id, status="REQUESTED", is_deleted=False).annotate(
            full_name=Value(F('employee__full_name'), output_field=CharField())
        )
        return all_leave_records

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class LeaveTypeList(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        queryset = LeaveType.objects.filter(is_deleted=False)
        serializer = LeaveTypeSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
    def post(self, request):
        serializer = LeaveTypeSerializer(data=request.data, context = {'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.create(serializer.validated_data)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
class LeaveTypeDetail(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        pk=request.query_params.get("leave_type_id")
        item = get_object_or_404(LeaveType, pk=pk)
        serializer = LeaveTypeSerializer(item)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def put(self, request):
        pk=request.query_params.get("leave_type_id")
        instance = get_object_or_404(LeaveType, pk=pk)
        serializer = LeaveTypeSerializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.update(instance, serializer.validated_data)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    def delete(self, request):
        pk=request.query_params.get("leave_type_id")
        item = get_object_or_404(LeaveType, pk=pk)
        item.is_deleted = True
        item.save()
        return Response(status=status.HTTP_204_NO_CONTENT)
    
class LeavePolicyList(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        queryset = LeavePolicy.objects.all()
        serializer = LeavePolicySerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def post(self, request):
        serializer = LeavePolicySerializer(data=request.data, context = {'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.create(serializer.validated_data)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
class LeavePolicyDetail(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        pk=request.query_params.get("leave_policy_id")
        item = get_object_or_404(LeavePolicy, pk=pk)
        serializer = LeavePolicySerializer(item)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def put(self, request):
        pk=request.query_params.get("leave_policy_id")
        instance = get_object_or_404(LeavePolicy, pk=pk)
        serializer = LeavePolicySerializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.update(instance, serializer.validated_data)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    def delete(self, request):
        pk=request.query_params.get("leave_policy_id")
        item = get_object_or_404(LeavePolicy, pk=pk)
        item.is_deleted = True
        item.save()
        return Response(status=status.HTTP_204_NO_CONTENT)
    
class LeaveRequestList(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        queryset=LeaveRequest.objects.all()
        serializer=LeaveRequestSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def post(self, request):
        serializer=LeaveRequestSerializer(data=request.data, context = {"request": request})
        serializer.is_valid(raise_exception=True)
        serializer.create(serializer.validated_data)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
class LeaveRequestDetail(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        pk = request.query_params.get('leave_request_id')
        item = get_object_or_404(LeaveRequest, pk=pk)
        serializer = LeaveRequestSerializer(item)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def patch(self, request):
        pk = request.query_params.get('leave_request_id')
        instance = get_object_or_404(LeaveRequest, pk=pk)
        serializer = LeaveRequestSerializer(instance, data=request.data, context = {"request": request, "employee": instance.employee})
        serializer.is_valid(raise_exception=True)
        serializer.update(instance, serializer.validated_data)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    def delete(self, request):
        pk = request.query_params.get('leave_request_id')
        item = get_object_or_404(LeaveRequest, pk=pk)
        item.approval_status = "W"
        item.is_deleted = True
        item.save()
        return Response(status=status.HTTP_204_NO_CONTENT)
    
class LeaveRequestApprovalList(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        queryset=LeaveRequest.objects.all()
        serializer=LeaveRequestApprovalSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def post(self, request):
        serializer=LeaveRequestApprovalSerializer(data=request.data, context = {"request": request})
        serializer.is_valid(raise_exception=True)
        serializer.create(serializer.validated_data)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
class LeaveRequestApprovalDetail(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        pk = request.query_params.get('leave_request_id')
        item = get_object_or_404(LeaveRequest, pk=pk)
        serializer = LeaveRequestApprovalSerializer(item)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def patch(self, request):
        pk = request.query_params.get('leave_request_id')
        instance = get_object_or_404(LeaveRequest, pk=pk)
        serializer = LeaveRequestApprovalSerializer(instance, data=request.data, context = {"request": request, "employee": instance.employee})
        serializer.is_valid(raise_exception=True)
        serializer.update(instance, serializer.validated_data)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    def delete(self, request):
        pk = request.query_params.get('leave_request_id')
        item = get_object_or_404(LeaveRequest, pk=pk)
        # item.approval_status = "W"
        item.is_deleted = True
        item.save()
        return Response(status=status.HTTP_204_NO_CONTENT)
    
class LeaveRecordList(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        queryset=LeaveRecord.objects.all()
        serializer=LeaveRecordSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def post(self, request):
        serializer=LeaveRecordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
class LeaveRecordDetail(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request, pk):
        item = get_object_or_404(LeaveRecord, pk=pk)
        serializer = LeaveRecordSerializer(item)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def post(self, request, pk):
        item = get_object_or_404(LeaveRecord, pk=pk)
        serializer = LeaveRecordSerializer(item, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    def delete(self, request, pk):
        item = get_object_or_404(LeaveRecord, pk=pk)
        item.is_deleted = True
        item.save()
        return Response(status=status.HTTP_204_NO_CONTENT)
    
class LeaveDashboardMetrics(APIView):
    permission_classes=[IsAuthenticated] #AdminPermission, UserIsActive
    authentication_classes=[CustomUserAuthentication]
    def get(self, request):
        try:
            response = LeaveDashboard(request=request).get_metrics()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)},status=status.HTTP_400_BAD_REQUEST)
        
class LeaveDashboardRequest(APIView):
    permission_classes=[IsAuthenticated] #AdminPermission, UserIsActive
    authentication_classes=[CustomUserAuthentication]
    def get(self, request):
        try:
            response = LeaveDashboard(request=request).get_new_leave_request()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
class LeaveDashboardRecord(APIView):
    permission_classes=[IsAuthenticated] #AdminPermission, UserIsActive
    authentication_classes=[CustomUserAuthentication]
    def get(self, request):
        try:
            response = LeaveDashboard(request=request).get_leave_record()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
class LeaveRecordMetrics(APIView):
    permission_classes=[IsAuthenticated] #AdminPermission, UserIsActive
    authentication_classes=[CustomUserAuthentication]
    def get(self, request):
        try:
            response = LeaveRecords(request=request).get_metrics()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
class LeaveRecordRecords(APIView):
    permission_classes=[IsAuthenticated] #AdminPermission, UserIsActive
    authentication_classes=[CustomUserAuthentication]
    def get(self, request):
        try:
            response = LeaveRecords(request=request).get_leave_records()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
class NewLeaveRequest(APIView):
    permission_classes=[IsAuthenticated] #AdminPermission, UserIsActive
    authentication_classes=[CustomUserAuthentication]
    def get(self, request):
        try:
            response = NewLeaveRequests(request=request).get_new_leave_requests()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)