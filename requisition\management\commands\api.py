# from django.core.management.base import BaseCommand
# from requisition.models import Budget, Expense, Requisition


# class Command(BaseCommand):
#     help = 'This is my custom management command'

#     def handle(self, *args, **options):

#         requisition = Requisition.objects.get(id=80)
#         _budget = Budget.objects.filter(team=requisition.team).last()
#         print(_budget)
#         print(requisition)
#         print(requisition.team)
#         create_expense = Expense.objects.create(
#                                   user=requisition.user,
#                                    requisition=requisition,
#                                    budget=_budget,
#                                    company=requisition.member.team.company,
#                                    expense_category=requisition.requisition_category,
#                                    expense_amount=requisition.request_amount,
#                                    team = requisition.team,
#                                    receipt=requisition.invoice,
#                                    disbursement_wallet="MAIN",
#                                    ref="888888888888888888888",
#                                    transaction_ref="unique_ref",
#                                    payload=""
#                                    )
        
#         print(create_expense)

         
# #        file_path="receipt_1.png"
# #        data_list = process_invoice_file(file_path=file_path)
       
# #     #    print(data_list)
# # #        extracted_data = {}
# # #        item_description = []
# # #        for line in data_list:
# # #             line = line.strip()  # Remove leading/trailing whitespaces
# # #             if line.startswith('1. Date') and line:
# # #                 extracted_data['Date'] = line.split(': ', 1)[1]
# # #             # elif line.startswith('2. Amount'):
# # #             #     extracted_data['Amount'] = line.split(': ', 1)[1]
# # #             # elif line.startswith('3. Mode of Payment'):
# # #             #     extracted_data['Mode of Payment'] = line.split(': ', 1)[1]
# # #             # elif line.startswith('4. Vendor'):
# # #             #     extracted_data['Vendor'] = line.split(': ', 1)[1]
# # #             # elif line.startswith('5. Invoice Number'):
# # #             #     extracted_data['Invoice Number'] = line.split(': ', 1)[1]
# # #             # elif line.startswith('6. Location'):
# # #             #     extracted_data['Location'] = line.split(': ', 1)[1]
# # #             # elif line.startswith('7. Tax Information'):
# # #             #     extracted_data['Tax Information'] = line.split(': ', 1)[1]
# # #             # elif line.startswith('8. Item description'):
# # #             #     item_description_started = True
# # #             # elif item_description_started and line.startswith('-'):
# # #             #     item_description.append(line.lstrip('- ').strip())
# # #             # else:
# # #             #     item_description_started = False

# # # # # Store item description in the extracted data
# # #             print(extracted_data, "yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy")
# # #             # extracted_data['Item description'] = item_description
# # #             # print(data, "pppppppppppppppppppppppppppp")

# # #             # Printing extracted data
# # #             # for key, value in extracted_data.items():
# # #             #     print(key + ":", value)

     