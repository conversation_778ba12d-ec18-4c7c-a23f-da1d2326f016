from rest_framework import serializers

from helpers.reusable_functions import is_valid_uuid
from instant_web.models import QRCode, Table
from instant_web.models import InstantWeb
from requisition.models import Company
from stock_inventory.models import (
    Category,
    Product,
    StockDetail,
    Branch,
)
from stock_inventory.helper.reusable import validate_subdomain
from stock_inventory.serializers import CategorySerializer
from rest_framework.response import Response
from django.core.exceptions import ObjectDoesNotExist


# Create your serializer(s) here.



class CompanyGetSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = "__all__"

class BranchGetSerializer(serializers.ModelSerializer):
    class Meta:
        model = Branch
        fields = "__all__"


    # ['id', 'company_name']

class UUIDPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    def to_internal_value(self, data):
        if not is_valid_uuid(data):
            raise serializers.ValidationError(f"Invalid UUID was supplied: {data}")
        return super().to_internal_value(data)

class InstantWebSerializer(serializers.Serializer):
    company_id = UUIDPrimaryKeyRelatedField(
        queryset=Company.objects.all(),
        source="company",
        write_only=True,
    )
    branch_id = UUIDPrimaryKeyRelatedField(
        queryset=Branch.objects.all(),
        source="branch",
        required=False,
    )
    store_description = serializers.CharField(required=True)
    store_url = serializers.CharField(required=True)

    company_name = serializers.CharField(source='company.company_name', read_only=True)
    company_ids = serializers.UUIDField(source='company.id', read_only=True)
    branch_name = serializers.CharField(source='branch.branch_name', read_only=True, required=False)

    class Meta:
        model = InstantWeb
        fields = ['company_id', 'branch_id', 'store_description', 'store_url', 'company_name', 'company_ids', 'branch_name']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        company_representation = CompanyGetSerializer(instance.company).data
        representation['company_name'] = company_representation['company_name']
        representation['company_ids'] = company_representation['id']
        if instance.branch:
            representation['branch_name'] = instance.branch.name
        return representation



    # def validate(self, attrs):
    #     store_url = attrs.get("store_url").lower()
    #     valid_subdomain = validate_subdomain(store_url)
    #     if not valid_subdomain:
    #         raise serializers.ValidationError({"message": f"{store_url} is not valid"})
    #     return super().validate(attrs)


class ViewInstantWebStoreSerializer(serializers.ModelSerializer):
    navigation_set_menu = CategorySerializer(many=True)
    whatsapp_url = serializers.SerializerMethodField()

    class Meta:
        model = InstantWeb
        fields = [
            "id",
            "branch",
            "contact_visible",
            "contact_phone_number",
            "whatsapp_phone_number",
            "x_link",
            "instagram_link",
            "facebook_link",
            "contact_email",
            "contact_address",
            "webstore_screenshort",
            "whatsapp_phone_number",
            "contact_description",
            "navigation_visible",
            "navigation_set_menu",
            "navigation_alignment",
            "logo_alignment",
            "header_banner_type",
            "banner_color",
            "header_description",
            "header_images",
            "header_logos",
            # "header_image",
            # "header_logo",
            "header_logo_text",
            "store_brand_color",
            "redirect_phone_number",
            "order_completion_message",
            "redirect_after_payment_url",
            "success_message",
            "notification",
            "store_description",
            "store_url",
            "whatsapp_url",
        ]
    def get_whatsapp_url(self, obj):
        return obj.whatsapp_url


    # def validate(self, attrs):
    #     # web_store_id = attrs.get("id")
    #     # instant_web = InstantWeb.object.get(id=web_store_id)
    #     # whatsapp_url = instant_web.whatsapp_url
    #     attrs["whatsapp_url"] = self.instance.whatsapp_url if self.instance else ""
    #
    #     return attrs






class EditInstantWebStoreSerializer(serializers.ModelSerializer):
    company = CompanyGetSerializer(read_only=True)
    branch = BranchGetSerializer(read_only=True)
    class Meta:
        model = InstantWeb
        fields = [
            "contact_visible",
            "contact_phone_number",
            "whatsapp_phone_number",
            "x_link",
            "instagram_link",
            "facebook_link",
            "contact_email",
            "contact_address",
            "webstore_screenshort",
            "contact_description",
            "navigation_visible",
            "navigation_set_menu",
            "navigation_alignment",
            "logo_alignment",
            "header_banner_type",
            "banner_color",
            "header_description",
            "whatsapp_phone_number",
            "header_logo_text",
            "header_images",
            "header_logos",
            # "header_logo",
            # "header_image",
            "store_brand_color",
            "redirect_phone_number",
            "order_completion_message",
            "redirect_after_payment_url",
            "success_message",
            "notification",
            "company",
            "branch",
            # "success_message_url",
            # "notification_url",
        ]


class CompanyAvailableStockSerializer(serializers.ModelSerializer):
    branch_name = serializers.CharField(source="branch.name")
    item_name = serializers.CharField(source="item.name")
    product_description = serializers.CharField(source="item.product_description")

    class Meta:
        model = StockDetail
        fields = [
            "branch",
            "branch_name",
            "category",
            "item",
            "item_name",
            "product_description",
            "quantity",
            "selling_price",
            "image",
        ]


class InstantWebRetrieveSerializer(serializers.ModelSerializer):
    branch_id = serializers.UUIDField(source='branch.id', read_only=True)
    branch_name = serializers.CharField(source='branch.name', read_only=True)
    company_name = serializers.CharField(source='company.company_name', read_only=True)

    class Meta:
        model = InstantWeb
        fields = '__all__'






class CompanyCategoriesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = [
            "id",
            "name",
        ]


class CompanyProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = [
            "name",
            "product_description",
            "product_tag",
            "selling_price",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["images"] = [
            instance.product_image_1,
            instance.product_image_2,
            instance.product_image_3,
            instance.product_image_4,
            instance.product_image_5,
        ]
        return representation


class QRCodeSerializer(serializers.ModelSerializer):
    categories = CompanyCategoriesSerializer(many=True)
    class Meta:
        model = QRCode
        fields = [
            "id",
            "name",
            "company",
            "branch",
            "categories",
            "all_categories",
            "scans",
            "code_url",
            "link", 
        ]

class CreateQRCodeSerializer(serializers.ModelSerializer):
    categories = serializers.PrimaryKeyRelatedField(queryset=Category.objects.all(), many=True)

    class Meta:
        model = QRCode
        fields = ['name', 'company', 'branch', 'categories', 'all_categories']

    def validate(self, data):
        # Check if a qr code with the same name exists for the given branch
        if QRCode.objects.filter(name=data['name'], branch=data['branch']).exists():
            raise serializers.ValidationError("A QR Code with this name already exists in the branch.")
        return data
    
    def create(self, validated_data):
        categories = validated_data.pop('categories', [])
        qr_code = QRCode.objects.create(**validated_data)
        qr_code.categories.set(categories)
        return qr_code


class ScanQRCodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = QRCode
        fields = ['id', 'link']


class TableSerializer(serializers.ModelSerializer):
    categories = CompanyCategoriesSerializer(many=True)

    class Meta:
        model = Table
        fields = [
            'id', 'name', 'company', 'branch', 'no_of_seats', 'categories',
            'all_categories', 'scans', 'code_url', 'link'
        ]

class CreateTableSerializer(serializers.ModelSerializer):
    categories = serializers.PrimaryKeyRelatedField(queryset=Category.objects.all(), many=True)

    class Meta:
        model = Table
        fields = ['name', 'company', 'branch', 'no_of_seats', 'categories', 'all_categories']

    def validate_company(self, value):
        try:
            Company.objects.get(pk=value.pk)
        except ObjectDoesNotExist:
            raise serializers.ValidationError(f"Invalid company ID")
        return value

    def validate_branch(self, value):
        try:
            Branch.objects.get(pk=value.pk)
        except ObjectDoesNotExist:
            raise serializers.ValidationError(f"Invalid branch ID")
        return value

    def validate_categories(self, value):
        invalid_pks = []
        for category in value:
            try:
                Category.objects.get(pk=category.pk)
            except ObjectDoesNotExist:
                invalid_pks.append(str(category.pk))
        
        if invalid_pks:
            raise serializers.ValidationError(f"One or more invalid category ID ")
        
        return value
    
    def validate(self, data):
        # Check if a table with the same name exists for the given branch
        if Table.objects.filter(name=data['name'], branch=data['branch']).exists():
            raise serializers.ValidationError("A table with this name already exists in the branch.")
        
        return data
    
    def create(self, validated_data):
        categories = validated_data.pop('categories', [])
        table = Table.objects.create(**validated_data)
        table.categories.set(categories)
        return table

class UpdateTableSerializer(serializers.ModelSerializer):
    categories = serializers.PrimaryKeyRelatedField(queryset=Category.objects.all(), many=True, required=False)

    class Meta:
        model = Table
        fields = ['name', 'no_of_seats', 'categories', 'all_categories']


class TransactionAnalysisSerializer(serializers.Serializer):
    total_transactions = serializers.IntegerField()
    total_transaction_volume = serializers.DecimalField(max_digits=13, decimal_places=2)
    total_online = serializers.DecimalField(max_digits=13, decimal_places=2)
    total_offline = serializers.DecimalField(max_digits=13, decimal_places=2)
    transactions_by_month = serializers.DictField(child=serializers.DecimalField(max_digits=13, decimal_places=2))
    transaction_source = serializers.DictField(child=serializers.DecimalField(max_digits=13, decimal_places=2))
    gross_profit = serializers.DictField(child=serializers.DecimalField(max_digits=13, decimal_places=2))
    net_profit = serializers.DictField(child=serializers.DecimalField(max_digits=13, decimal_places=2))
