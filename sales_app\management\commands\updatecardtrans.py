from django.core.management.base import BaseCommand

from sales_app.helper.enums import TransactionStatusChoices
from sales_app.models import CardTransaction, SalesTransaction


class Command(BaseCommand):
    help = "UPDATE CARD TRANSACTIONS."

    def handle(self, *args, **kwargs):
        card_trans = CardTransaction.objects.filter(
            created_at__date__gte="2025-02-10",
        )
        for card_tran in card_trans:
            if card_tran.status:
                sales_trans = SalesTransaction.objects.filter(
                    card_rrn=card_tran.rrn
                ).last()
                sales_trans.status = TransactionStatusChoices.SUCCESSFUL
                sales_trans.save()
        self.stdout.write(
            self.style.SUCCESS("UPDATE SUCCESSFUL.")
        )
