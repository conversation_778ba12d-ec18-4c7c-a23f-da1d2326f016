from django.shortcuts import render
from django_filters.rest_framework import DjangoFilterBackend

from payroll_app.models import CompanyDepartmentSettings, CompanyEmployeeList
from payroll_app.permissions import AdminPermission, EmployeeIsActive, UserIsActive
from payroll_app.services import valid_uuid_check
from payroll_app.views import CustomPagination
from performance_management.func import fetch_all_check_in_dates, period_module
from performance_management.models import ActivityLog, CheckInFrequency, CompanyGoals, CompanyKeyResult, KPICategory, KPICheckIn, Review, Tasks, StatusUpdate
from performance_management.serializers import ActivityLogSerializer, AlignGoalSerializer, AlignKeyResultSerializer, CheckInFrequencySerializer, CheckInSerializer, CompanyGoalDataSerializer, CompanyGoalOverviewSerializer, CompanyGoalSerializer, CompanyKeyResultSerializer, CompanyKeyResultTaskSerializer, CompanyTaskSerializer, EditCompanyKeyResultSerializer, GoalSerializer, KPICategorySerializer, ReviewSerializer, TaskSerializer, StatusUpdateSerializer
from requisition.models import Company
from rest_framework import filters, generics, status, viewsets
from rest_framework.exceptions import ValidationError
from rest_framework.filters import SearchFilter
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from core.auth.custom_auth import CustomUserAuthentication, IsStaff
from django.db.models import (Q)
# Create your views here.


class CreateCompanyGoalAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = CompanyGoalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        serializer.save()
        response = {"message": "company goal created successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class CreateGoalAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = GoalSerializer(data=request.data, context={"company_id":company_uuid})
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        serializer.validated_data["goal_type"] = "COMPANY"
        owner = serializer.validated_data.pop("owner")
        company_goal = serializer.save()
        for entry in owner:
            number_id = entry.get("id")
            type = entry.get("type")
            if type == "department":
                department = CompanyDepartmentSettings.objects.filter(id=number_id, company__id=company_uuid, is_deleted=False).first()
                if department:
                    company_goal.department.add(department)
            elif type == "employee":
                employee = CompanyEmployeeList.objects.filter(id=number_id, company__id=company_uuid, is_deleted=False).first()
                if employee:
                    company_goal.employees.add(employee)
        response = {"message": "success"}
        return Response(response, status=status.HTTP_200_OK)
    
class CreateIndividualGoalAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")

        employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company__id=company_uuid, is_deleted=False
        ).first()
        serializer = GoalSerializer(data=request.data, context={"company_id":company_uuid})
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        serializer.validated_data["goal_type"] = "INDIVIDUAL"
        serializer.validated_data["created_by"] = employee
        owner = serializer.validated_data.pop("owner")
        serializer.save()

        response = {"message": "success"}
        return Response(response, status=status.HTTP_200_OK)
    
class EditCompanyGoalAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_goal_id = request.query_params.get("goal_id")
        if not valid_uuid_check(company_goal_id):
            raise ValidationError({"message": "invalid Goal ID"})
        try:
            company_goal = CompanyGoals.objects.get(id=company_goal_id, goal_type="COMPANY", company__id=company_uuid, is_deleted=False)
        except CompanyGoals.DoesNotExist:
            return Response({"message": "company goal does not exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = CompanyGoalSerializer(company_goal, data=request.data, partial=True, context={"company_id":company_uuid})
        serializer.is_valid(raise_exception=True)
        owner = serializer.validated_data.pop("owner", [])
        company_goal = serializer.save()

        all_employees = []
        all_departments = []
        for entry in owner:
            number_id = entry.get("id")
            type = entry.get("type")
            if type == "department":
                all_departments.append(number_id)
            elif type == "employee":
                all_employees.append(number_id)

        if all_employees:
            company_goal.employees.set(all_employees)
        if not all_employees:
            company_goal.employees.clear()
        if all_departments:
            company_goal.department.set(all_departments)
        if not all_departments:
            company_goal.department.clear()
        response = {"message": "company goal edited successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class EditIndividualGoalAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_goal_id = request.query_params.get("goal_id")
        if not valid_uuid_check(company_goal_id):
            raise ValidationError({"message": "invalid Goal ID"})
        
        employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company__id=company_uuid, is_deleted=False
        ).first()
        try:
            company_goal = CompanyGoals.objects.get(id=company_goal_id, company__id=company_uuid, goal_type="INDIVIDUAL", created_by=employee,  is_deleted=False)
        except CompanyGoals.DoesNotExist:
            return Response({"message": "Goal does not exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = CompanyGoalSerializer(company_goal, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        response = {"message": "Goal edited successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class CompanyGoalsAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyGoalSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", is_deleted=False
        )
        return company_goals

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "COMPANY_GOALS",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class IndividualGoalsAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyGoalSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="INDIVIDUAL", is_deleted=False
        )
        return company_goals

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "INDIVIDUAL_GOALS",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class CreateCompanyKeyResultAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = CompanyKeyResultSerializer(data=request.data, context={"company_id":company_uuid, "key_result_type": "COMPANY"})
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        owner = serializer.validated_data.pop("owner")
        check_in_frequency = serializer.validated_data.pop('check_in_frequency', [])
        key_result = serializer.save()

        # serializer.validated_data["created_by"] = employee
        if check_in_frequency:
            key_result.check_in_frequency.set(list(check_in_frequency))

        for entry in owner:
            number_id = entry.get("id")
            type = entry.get("type")
            if type == "department":
                department = CompanyDepartmentSettings.objects.filter(id=number_id, company__id=company_uuid, is_deleted=False).first()
                if department:
                    key_result.department.add(department)
            elif type == "employee":
                employee = CompanyEmployeeList.objects.filter(id=number_id, company__id=company_uuid, is_deleted=False).first()
                if employee:
                    key_result.employees.add(employee)
        response = {"message": "key feature created successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class CreateIndividualKeyResultAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = CompanyKeyResultSerializer(data=request.data, context={"company_id":company_uuid, "key_result_type": "INDIVIDUAL"})
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        owner = serializer.validated_data.pop("owner")
        check_in_frequency = serializer.validated_data.pop('check_in_frequency', [])
        key_result = serializer.save()

        # serializer.validated_data["created_by"] = employee
        if check_in_frequency:
            key_result.check_in_frequency.set(list(check_in_frequency))

        # for entry in owner:
        #     number_id = entry.get("id")
        #     type = entry.get("type")
        #     if type == "department":
        #         department = CompanyDepartmentSettings.objects.filter(id=number_id, company__id=company_uuid, is_deleted=False).first()
        #         if department:
        #             key_result.department.add(department)
        #     elif type == "employee":
        #         employee = CompanyEmployeeList.objects.filter(id=number_id, company__id=company_uuid, is_deleted=False).first()
        #         if employee:
        #             key_result.employees.add(employee)
        response = {"message": "key feature created successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class EditCompanyKeyResultAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_key_feature_id = request.query_params.get("key_feature_id")
        if not valid_uuid_check(company_key_feature_id):
            raise ValidationError({"message": "invalid Key Feature ID"})
        try:
            company_key_feature = CompanyKeyResult.objects.get(id=company_key_feature_id, company_goal__goal_type="COMPANY", company__id=company_uuid, is_deleted=False)
        except CompanyKeyResult.DoesNotExist:
            return Response({"message": "company key feature does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = EditCompanyKeyResultSerializer(company_key_feature, data=request.data, partial=True, context={"company_id":company_uuid, "company_goal": company_key_feature.company_goal})
        serializer.is_valid(raise_exception=True)
        owner = serializer.validated_data.pop("owner", [])
        key_result = serializer.save()

        all_employees = []
        all_departments = []
        for entry in owner:
            number_id = entry.get("id")
            type = entry.get("type")
            if type == "department":
                all_departments.append(number_id)
            elif type == "employee":
                all_employees.append(number_id)

        if all_employees:
            key_result.employees.set(all_employees)
        if not all_employees:
            key_result.employees.clear()
        if all_departments:
            key_result.department.set(all_departments)
        if not all_departments:
            key_result.department.clear()
        response = {"message": "company key feature edited successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class EditIndividualKeyResultAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_key_feature_id = request.query_params.get("key_feature_id")
        if not valid_uuid_check(company_key_feature_id):
            raise ValidationError({"message": "invalid Key Feature ID"})
        try:
            company_key_feature = CompanyKeyResult.objects.get(id=company_key_feature_id, company_goal__goal_type="INDIVIDUAL", company__id=company_uuid, is_deleted=False)
        except CompanyKeyResult.DoesNotExist:
            return Response({"message": "company key feature does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = EditCompanyKeyResultSerializer(company_key_feature, data=request.data, partial=True, context={"company_id":company_uuid, "company_goal": company_key_feature.company_goal})
        serializer.is_valid(raise_exception=True)
        owner = serializer.validated_data.pop("owner", [])
        key_result = serializer.save()

        # all_employees = []
        # all_departments = []
        # for entry in owner:
        #     number_id = entry.get("id")
        #     type = entry.get("type")
        #     if type == "department":
        #         all_departments.append(number_id)
        #     elif type == "employee":
        #         all_employees.append(number_id)

        # if all_employees:
        #     key_result.employees.set(all_employees)
        # if all_departments:
        #     key_result.department.set(all_departments)
        response = {"message": "company key feature edited successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class CompanyKeyResultsAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyKeyResultSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
    )
    search_fields = (
        "name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        company_key_features = CompanyKeyResult.objects.filter(
            company__id=company_id, company_goal__goal_type="COMPANY", is_deleted=False
        )
        return company_key_features

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "COMPANY_KEY_FEATURES",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class IndividualKeyResultsAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyKeyResultSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
    )
    search_fields = (
        "name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        company_key_features = CompanyKeyResult.objects.filter(
            company__id=company_id, company_goal__goal_type="INDIVIDUAL", is_deleted=False
        )
        return company_key_features

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "COMPANY_KEY_FEATURES",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class CompanyGoalDataAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyGoalDataSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", is_deleted=False
        )
        return company_goals

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "COMPANY_GOALS",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class IndividualGoalDataAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyGoalDataSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="INDIVIDUAL", is_deleted=False
        )
        return company_goals

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "INDIVIDUAL_GOALS",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class PeriodModule(APIView):
    def get(self, request):
        get_periods  = period_module()
        return Response(get_periods)
    
class CreateCheckInAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")

        employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company__id=company_uuid, is_deleted=False
        ).first()
        serializer = CheckInSerializer(data=request.data, context={"company_id":company_uuid, "employee":employee})
        serializer.is_valid(raise_exception=True)
        this_status = serializer.validated_data.get("status")
        key_result = serializer.validated_data.get("key_result")
        achieved_value = serializer.validated_data.get("achieved_value")
        # serializer.validated_data.pop("status")
        serializer.save()
        # key_result.status=this_status
        # if achieved_value:
        #     key_result.achieved_value=achieved_value
        # key_result.save()
        response = {"message": "check in successful"}
        return Response(response, status=status.HTTP_200_OK)
    

class CreateKeyResultTaskAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = CompanyKeyResultTaskSerializer(data=request.data, context={"company_id":company_uuid, "task_type": "COMPANY"})
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        employee = CompanyEmployeeList.objects.filter(
                company__id=company_uuid, employee=request.user, is_active=True
            ).first()
        assign_to_data = serializer.validated_data.pop('assign_to', [])
        serializer.validated_data["created_by"] = employee
        serializer.validated_data["task_type"] = "COMPANY"
        task_instance = serializer.save()
        if assign_to_data:
            task_instance.assign_to.set(list(assign_to_data))
        response = {"message": "task created successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class CreateIndividualKeyResultTaskAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = CompanyKeyResultTaskSerializer(data=request.data, context={"company_id":company_uuid, "task_type": "INDIVIDUAL"})
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        employee = CompanyEmployeeList.objects.filter(
                company__id=company_uuid, employee=request.user, is_active=True
            ).first()
        assign_to_data = serializer.validated_data.pop('assign_to', [])
        serializer.validated_data["created_by"] = employee
        serializer.validated_data["task_type"] = "INDIVIDUAL"
        task_instance = serializer.save()
        # if assign_to_data:
        #     task_instance.assign_to.set(list(assign_to_data))
        response = {"message": "task created successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class KeyResultTask(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = TaskSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "priority", "status"
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        key_result_id = self.request.query_params.get("key_result_id")
        if not valid_uuid_check(key_result_id):
            raise ValidationError({"message": "invalid key_result_id param"})
        key_result_tasks = Tasks.objects.filter(
            company__id=company_id, key_result__id=key_result_id, is_deleted=False
        )
        return key_result_tasks

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "KEY_RESULT_TASKS",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class TaskList(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = TaskSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "priority", "status"
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")

        tasks = Tasks.objects.filter(
            company__id=company_id, task_type="COMPANY", is_deleted=False
        )
        return tasks

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "ALL_TASKS",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class KeyResultCheckInHistory(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CheckInSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = (
        
    # )
    # search_fields = (
    
    # )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        key_result_id = self.request.query_params.get("key_result_id")
        if not valid_uuid_check(key_result_id):
            raise ValidationError({"message": "invalid key_result_id param"})
        key_result_check_in = KPICheckIn.objects.filter(
          key_result__id=key_result_id,
        )
        all_check_in_results = key_result_check_in.filter(key_result__company__id=company_id)

        return all_check_in_results

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "KEY_RESULT_CHECK_IN_HISTORY",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)

class CheckInFrequencyList(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CheckInFrequencySerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = (
    # )
    search_fields = (
        "name",
    )

    def get_queryset(self):
        kpi_category = CheckInFrequency.objects.exclude(is_deleted=True)
        return kpi_category

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "CHECK_IN_FREQUENCY_LIST",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class KPICategoryList(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = KPICategorySerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "type",
    )
    search_fields = (
        "name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        kpi_category = KPICategory.objects.exclude(is_deleted=True).filter(Q(company__isnull=True) | Q(company__id=company_id))
        return kpi_category

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "KPI_CATEGORY_TYPE_LIST",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class KPICategoryAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = KPICategorySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        serializer.save()
        response = {"message": "success"}
        return Response(response, status=status.HTTP_200_OK)
    
class EditKPICategoryAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        kpi_category_id = request.query_params.get("kpi_category_id")
        if not valid_uuid_check(kpi_category_id):
            raise ValidationError({"message": "invalid KPI Category ID param"})
        try:
            kpi_category = KPICategory.objects.get(id=kpi_category_id, company__id=company_uuid, is_deleted=False)
        except KPICategory.DoesNotExist:
            return Response({"message": "KPI category does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = KPICategorySerializer(kpi_category, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        response = {"message": "KPI Category edited successfully"}
        return Response(response, status=status.HTTP_200_OK)
    

class CheckInDateAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        key_result_id = request.query_params.get("key_result_id")
        if not valid_uuid_check(key_result_id):
            return Response({"message": "invalid Key Result id param"}, status=status.HTTP_400_BAD_REQUEST)
        
        check_in_dates = []
        company_key_features = CompanyKeyResult.objects.filter(
            id=key_result_id, company__id=company_uuid, is_deleted=False
        ).first()
        
        if not company_key_features:
            return Response({"message": "Key Result does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        
        all_check_in_dates = company_key_features.check_in_frequency.all()

        check_in_dates = fetch_all_check_in_dates(all_check_in_dates, company_key_features.period_start_date, company_key_features.period_end_date)
        return Response(check_in_dates, status=status.HTTP_200_OK)
    
class AlignGoalAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    
    ]
    authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = AlignGoalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # serializer.save()
        response = {"message": "goal aligned successfully"}
        return Response(response, status=status.HTTP_200_OK) 
    
class AlignKeyResultAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    
    ]
    authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = AlignKeyResultSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # serializer.save()
        response = {"message": "key result aligned successfully"}
        return Response(response, status=status.HTTP_200_OK) 
    

class CompanyGoalOverviewAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyGoalOverviewSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", is_deleted=False
        )
        return company_goals

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "COMPANY_GOALS_OVERVIEW",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class IndividualDashboardOverviewAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="INDIVIDUAL", is_deleted=False
        )
        total_goals = company_goals.count() or 0
        not_started = len([goal for goal in company_goals if goal.percentage <= 0])
        at_risk = len([goal for goal in company_goals if goal.percentage < 50 and goal.percentage > 0])
        on_track = len([goal for goal in company_goals if goal.percentage >= 50 and goal.percentage < 100 ])
        completed = len([goal for goal in company_goals if goal.percentage >= 100])
        response_data = {
            "total_goals": total_goals,
            "not_started": not_started,
            "at_risk": at_risk,
            "on_track": on_track,
            "completed": completed,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class CompanyDashboardOverviewAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", is_deleted=False
        )
        total_goals = company_goals.count() or 0
        not_started = len([goal for goal in company_goals if goal.percentage <= 0])
        at_risk = len([goal for goal in company_goals if goal.percentage < 50 and goal.percentage > 0])
        on_track = len([goal for goal in company_goals if goal.percentage >= 50 and goal.percentage < 100])
        completed = len([goal for goal in company_goals if goal.percentage >= 100])
        response_data = {
            "total_goals": total_goals,
            "not_started": not_started,
            "at_risk": at_risk,
            "on_track": on_track,
            "completed": completed,
        }
        return Response(response_data, status=status.HTTP_200_OK)

class CreateCompanyReview(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_goal_id = request.query_params.get("goal_id")
        if not valid_uuid_check(company_goal_id):
            raise ValidationError({"message": "invalid Goal ID"})
        try:
            company_goal = CompanyGoals.objects.get(id=company_goal_id, company__id=company_uuid, is_deleted=False)
        except CompanyGoals.DoesNotExist:
            return Response({"message": "company goal does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        if company_goal.goal_type != "COMPANY":
            return Response({"message": "invalid company goal"}, status=status.HTTP_400_BAD_REQUEST)
        
        employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company__id=company_uuid, is_deleted=False
        ).first()

        serializer = ReviewSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.validated_data["company_goal"] = company_goal
        serializer.validated_data["created_by"] = employee
        serializer.save()
        response = {"message": "review created successfully"}
        return Response(response, status=status.HTTP_200_OK)    

class CreateIndividualReview(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_goal_id = request.query_params.get("goal_id")
        if not valid_uuid_check(company_goal_id):
            raise ValidationError({"message": "invalid Goal ID"})
        try:
            company_goal = CompanyGoals.objects.get(id=company_goal_id, company__id=company_uuid, is_deleted=False)
        except CompanyGoals.DoesNotExist:
            return Response({"message": "company goal does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        if company_goal.goal_type != "INDIVIDUAL":
            return Response({"message": "invalid individual goal"}, status=status.HTTP_400_BAD_REQUEST)
        
        employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company__id=company_uuid, is_deleted=False
        ).first()

        serializer = ReviewSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.validated_data["company_goal"] = company_goal
        serializer.validated_data["created_by"] = employee
        review = serializer.save()
        # review.company_goal = 
        response = {"message": "review created successfully"}
        return Response(response, status=status.HTTP_200_OK)    
    

class CompanyReviewList(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ReviewSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = (
    #     "is_active",
    # )
    # search_fields = (
    #     "name",
    #     "description",
    # )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        goal_id = self.request.query_params.get("goal_id")
        all_reviews = Review.objects.filter(
            company_goal__id=goal_id, is_deleted=False
        )
        this_review = all_reviews.filter(company_goal__company__id=company_id)
        all_review = this_review.filter(company_goal__goal_type="COMPANY")
        return all_review

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "COMPANY_REVIEW",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class IndividualReviewList(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ReviewSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = (
    #     "is_active",
    # )
    # search_fields = (
    #     "name",
    #     "description",
    # )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        goal_id = self.request.query_params.get("goal_id")
        all_reviews = Review.objects.filter(
            company_goal__id=goal_id, is_deleted=False
        )
        this_review = all_reviews.filter(company_goal__company__id=company_id)
        all_review = this_review.filter(company_goal__goal_type="INDIVIDUAL")
        return all_review

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "COMPANY_REVIEW",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class EditCompanyReviewAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        review_id = request.query_params.get("review_id")
        if not valid_uuid_check(review_id):
            raise ValidationError({"message": "invalid review ID"})
        try:
            company_review = Review.objects.get(id=review_id, company_goal__company__id=company_uuid, is_deleted=False)
        except Review.DoesNotExist:
            return Response({"message": "review does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        if company_review.company_goal.goal_type != "COMPANY":
            return Response({"message": "invalid review"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = ReviewSerializer(company_review, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        if serializer.validated_data.get("created_by"):
            serializer.validated_data.pop("created_by")
        serializer.save()
        response = {"message": "review updated successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class EditIndividualReviewAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        review_id = request.query_params.get("review_id")
        if not valid_uuid_check(review_id):
            raise ValidationError({"message": "invalid review ID"})
        try:
            company_review = Review.objects.get(id=review_id, company_goal__company__id=company_uuid, is_deleted=False)
        except Review.DoesNotExist:
            return Response({"message": "review does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        if company_review.company_goal.goal_type != "INDIVIDUAL":
            return Response({"message": "invalid review"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = ReviewSerializer(company_review, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        if serializer.validated_data.get("created_by"):
            serializer.validated_data.pop("created_by")
        serializer.save()
        response = {"message": "review updated successfully"}
        return Response(response, status=status.HTTP_200_OK)

class SingleCompanyGoalOverviewAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyGoalOverviewSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        goal_id = self.request.query_params.get("goal_id")
        company_goals = CompanyGoals.objects.filter(
            id=goal_id, company__id=company_id, is_deleted=False
        )
        if not company_goals:
            raise ValidationError({"message": "invalid goal_id param"})
        return company_goals

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "SINGLE_GOALS_OVERVIEW",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)

class SingleGoalStatusUpdateHistory(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = StatusUpdateSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = (
        
    # )
    # search_fields = (
    
    # )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        goal_id = self.request.query_params.get("goal_id")
        if not valid_uuid_check(goal_id):
            raise ValidationError({"message": "invalid goal_id param"})
        key_result_check_in = StatusUpdate.objects.filter(
          key_result__company_goal__id=goal_id,
        )
        all_check_in_results = key_result_check_in.filter(key_result__company__id=company_id)

        return all_check_in_results

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "GOAL_STATUS_UPDATE",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)

class OverviewDashboard(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", is_deleted=False
        )
        key_result_qs = CompanyKeyResult.objects.filter(
            company__id=company_id, is_deleted=False
        )
        company_key_result= key_result_qs.filter(company_goal__goal_type="COMPANY")
        company_departments = CompanyDepartmentSettings.objects.filter(company__id=company_id, is_deleted=False)
        company_employees = CompanyEmployeeList.objects.filter(company__id=company_id, is_active=True, is_deleted=False)

        total_goals = company_goals.count() or 0
        total_key_results = company_key_result.count() or 0
        total_departments = company_departments.count() or 0
        total_employees = company_employees.count() or 0

        response_data = {
            "total_goals": total_goals,
            "total_key_results": total_key_results,
            "total_departments": total_departments,
            "total_employees": total_employees
        }
        return Response(response_data, status=status.HTTP_200_OK)

class OverallGoalProgressAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", is_deleted=False
        )
        total_goals = company_goals.count() or 0
        not_started = len([goal for goal in company_goals if goal.percentage <= 0])
        at_risk = len([goal for goal in company_goals if goal.percentage < 50 and goal.percentage > 0])
        on_track = len([goal for goal in company_goals if goal.percentage >= 50 and goal.percentage < 100])
        completed = len([goal for goal in company_goals if goal.percentage >= 100])

        try:
            not_started_percentage = (not_started / total_goals) * 100
        except ZeroDivisionError:
            not_started_percentage = 0

        try:
            at_risk_percentage = (at_risk / total_goals) * 100
        except ZeroDivisionError:
            at_risk_percentage = 0

        try:
            on_track_percentage = (on_track / total_goals) * 100
        except ZeroDivisionError:
            on_track_percentage = 0

        try:
            completed_percentage = (completed / total_goals) * 100
        except ZeroDivisionError:
            completed_percentage = 0

        response_data = {
            "not_started_percentage": not_started_percentage,
            "at_risk_percentage": at_risk_percentage,
            "on_track_percentage": on_track_percentage,
            "completed_percentage": completed_percentage,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class OverallGoalAchievedRemainingAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", is_deleted=False
        )
        total_goals = company_goals.count() or 0
        not_started = len([goal for goal in company_goals if goal.percentage <= 0])
        at_risk = len([goal for goal in company_goals if goal.percentage < 50 and goal.percentage > 0])
        on_track = len([goal for goal in company_goals if goal.percentage >= 50 and goal.percentage < 100])
        completed = len([goal for goal in company_goals if goal.percentage >= 100])

        achieved = completed
        remaining = on_track + at_risk + not_started

        try:
            achieved_percentage = (achieved / total_goals) * 100
        except ZeroDivisionError:
            achieved_percentage = 0

        try:
            remaining_percentage = (remaining / total_goals) * 100
        except ZeroDivisionError:
            remaining_percentage = 0

        response_data = {
            "achieved_percentage": achieved_percentage,
            "remaining_percentage": remaining_percentage
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class OverallGoalByDepartmentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")
        department_data = []
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", is_deleted=False
        )
        company_departments = CompanyDepartmentSettings.objects.filter(company__id=company_id, is_deleted=False)
        for department in company_departments:
            department_goals = company_goals.filter(department__id=department.id)
            total_goals = department_goals.count() or 0
            not_started = len([goal for goal in department_goals if goal.percentage <= 0])
            at_risk = len([goal for goal in department_goals if goal.percentage < 50 and goal.percentage > 0])
            on_track = len([goal for goal in department_goals if goal.percentage >= 50 and goal.percentage < 100])
            completed = len([goal for goal in department_goals if goal.percentage >= 100])

            try:
                not_started_percentage = (not_started / total_goals) * 100
            except ZeroDivisionError:
                not_started_percentage = 0

            try:
                at_risk_percentage = (at_risk / total_goals) * 100
            except ZeroDivisionError:
                at_risk_percentage = 0

            try:
                on_track_percentage = (on_track / total_goals) * 100
            except ZeroDivisionError:
                on_track_percentage = 0

            try:
                completed_percentage = (completed / total_goals) * 100
            except ZeroDivisionError:
                completed_percentage = 0

            department_data.append(
                {
                    "department": department.department_name,
                    "not_started_percentage": not_started_percentage,
                    "at_risk_percentage": at_risk_percentage,
                    "on_track_percentage": on_track_percentage,
                    "completed_percentage": completed_percentage
                }
            )
        response_data = {
            "department_data": department_data
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class OverallAchievedGoalRemainingGoalAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")
        department_data = []
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", is_deleted=False
        )
        company_departments = CompanyDepartmentSettings.objects.filter(company__id=company_id, is_deleted=False)
        for department in company_departments:
            department_goals = company_goals.filter(department__id=department.id)
            total_goals = department_goals.count() or 0
            not_started = len([goal for goal in department_goals if goal.percentage <= 0])
            at_risk = len([goal for goal in department_goals if goal.percentage < 50 and goal.percentage > 0])
            on_track = len([goal for goal in department_goals if goal.percentage >= 50 and goal.percentage < 100])
            completed = len([goal for goal in department_goals if goal.percentage >= 100])

            achieved = completed
            remaining = on_track + at_risk + not_started

            try:
                achieved_percentage = (achieved / total_goals) * 100
            except ZeroDivisionError:
                achieved_percentage = 0

            try:
                remaining_percentage = (remaining / total_goals) * 100
            except ZeroDivisionError:
                remaining_percentage = 0

            department_data.append(
                {
                    "department": department.department_name,
                    "achieved_percentage": achieved_percentage,
                    "remaining_percentage": remaining_percentage
                }
            )
        response_data = {
            "department_data": department_data
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class DepartmentGoalAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyGoalOverviewSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        department_id = self.request.query_params.get("department_id")
        company_goals = CompanyGoals.objects.filter(
            company__id=company_id, goal_type="COMPANY", department__id=department_id, is_deleted=False
        )
        return company_goals

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "DEPARTMENT_GOALS_OVERVIEW",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class CreateTaskAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = CompanyTaskSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        employee = CompanyEmployeeList.objects.filter(
                company__id=company_uuid, employee=request.user, is_active=True
            ).first()
        assign_to_data = serializer.validated_data.pop('assign_to', [])
        serializer.validated_data["created_by"] = employee
        serializer.validated_data["task_type"] = "COMPANY"
        task_instance = serializer.save()
        if assign_to_data:
            task_instance.assign_to.set(list(assign_to_data))
        response = {"message": "task created successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class EditTaskAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        task_id = request.query_params.get("task_id")
        if not valid_uuid_check(task_id):
            raise ValidationError({"message": "invalid Task ID"})
        try:
            company_task = Tasks.objects.get(id=task_id, task_type="COMPANY", company__id=company_uuid, is_deleted=False)
        except Tasks.DoesNotExist:
            return Response({"message": "Task does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = CompanyTaskSerializer(company_task, data=request.data, partial=True, )
        serializer.is_valid(raise_exception=True)

        assign_to_data = serializer.validated_data.pop('assign_to', [])
        task_instance = serializer.save()
        if assign_to_data:
            task_instance.assign_to.set(list(assign_to_data))
        else:
            task_instance.assign_to.clear()
        response = {"message": "Task edited successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class EditIndividualTaskAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        task_id = request.query_params.get("task_id")
        if not valid_uuid_check(task_id):
            raise ValidationError({"message": "invalid Task ID"})
        try:
            company_task = Tasks.objects.get(id=task_id, task_type="INDIVIDUAL", company__id=company_uuid, is_deleted=False)
        except Tasks.DoesNotExist:
            return Response({"message": "Task does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = CompanyTaskSerializer(company_task, data=request.data, partial=True )
        serializer.is_valid(raise_exception=True)

        assign_to_data = serializer.validated_data.pop('assign_to', [])
        task_instance = serializer.save()
        if assign_to_data:
            task_instance.assign_to.set(list(assign_to_data))
        else:
            task_instance.assign_to.clear()
        response = {"message": "Task edited successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class CreateIndividualTaskAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = CompanyTaskSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company_ins = Company.objects.get(id=company_uuid)
        serializer.validated_data["company"] = company_ins
        employee = CompanyEmployeeList.objects.filter(
                company__id=company_uuid, employee=request.user, is_active=True
            ).first()
        assign_to_data = serializer.validated_data.pop('assign_to', [])
        serializer.validated_data["created_by"] = employee
        serializer.validated_data["task_type"] = "INDIVIDUAL"
        task_instance = serializer.save()
        if assign_to_data:
            task_instance.assign_to.set(list(assign_to_data))
        response = {"message": "task created successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class IndividualTaskList(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = TaskSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "priority", "status"
    )
    search_fields = (
        "name",
        "description",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")

        employee = CompanyEmployeeList.objects.filter(
                company__id=company_id, employee=self.request.user, is_active=True
            ).first()

        tasks = Tasks.objects.filter(
            company__id=company_id, task_type="INDIVIDUAL", created_by=employee, is_deleted=False
        )
        return tasks

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "ALL_TASKS",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class CloneCompanyGoalAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_goal_id = request.query_params.get("goal_id")
        if not valid_uuid_check(company_goal_id):
            raise ValidationError({"message": "invalid Goal ID"})
        
        employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company__id=company_uuid, is_deleted=False
        ).first()
        
        try:
            company_goal = CompanyGoals.objects.get(id=company_goal_id, goal_type="COMPANY", company__id=company_uuid, is_deleted=False)
        except CompanyGoals.DoesNotExist:
            return Response({"message": "company goal does not exist"}, status=status.HTTP_400_BAD_REQUEST)
    
        clone_goal = CompanyGoals.clone_goal(company_goal, employee) 
        if not clone_goal:
            return Response({"message": "company goal could not be cloned"}, status=status.HTTP_400_BAD_REQUEST)
        response = {"message": "company goal cloned successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class CloneIndividualGoalAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_goal_id = request.query_params.get("goal_id")
        if not valid_uuid_check(company_goal_id):
            raise ValidationError({"message": "invalid Goal ID"})
        
        employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company__id=company_uuid, is_deleted=False
        ).first()
        try:
            company_goal = CompanyGoals.objects.get(id=company_goal_id, company__id=company_uuid, goal_type="INDIVIDUAL", created_by=employee, is_deleted=False)
        except CompanyGoals.DoesNotExist:
            return Response({"message": "Goal does not exist"}, status=status.HTTP_400_BAD_REQUEST)
    
        clone_goal = CompanyGoals.clone_goal(company_goal, employee) 
        if not clone_goal:
            return Response({"message": "goal could not be cloned"}, status=status.HTTP_400_BAD_REQUEST)
        response = {"message": "goal cloned successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class ActivityLogHistory(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ActivityLogSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = (
        
    # )
    # search_fields = (
    
    # )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        check_in_id = self.request.query_params.get("check_in_id")
        if not valid_uuid_check(check_in_id):
            raise ValidationError({"message": "invalid check_in_id param"})
        check_in_ins = KPICheckIn.objects.filter(
          id=check_in_id,
        ).first()
        if not check_in_ins:
            return []
        all_activity_history = ActivityLog.objects.filter(Q(status_update__key_result=check_in_ins.key_result) & Q(status_update__check_in_date=check_in_ins.check_in_date))
        return all_activity_history

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "ACTIVITY_LOG_HISTORY",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)