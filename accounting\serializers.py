from rest_framework import serializers
from django.db import transaction

from requisition.models import Company
from stock_inventory.models import Branch
from .models import AccountType, Account, JournalEntry, JournalLine


class AccountTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountType
        fields = "__all__"


class AccountSerializer(serializers.ModelSerializer):
    account_type_data = serializers.SerializerMethodField()

    class Meta:
        model = Account
        fields = "__all__"

    def get_account_type_data(self, obj):
        return AccountTypeSerializer(obj.account_type).data


class JournalLineSerializer(serializers.ModelSerializer):
    class Meta:
        model = JournalLine
        fields = "__all__"

    def validate(self, attrs):

        # if attrs.get("debit_amount", 0) == 0:
        #     raise serializers.ValidationError(
        #         detail={"error": "debit_amount must be non-zero."}
        #     )
        # if attrs.get("credit_amount", 0) == 0:
        #     raise serializers.ValidationError(
        #         detail={"error": "credit_amount must be non-zero."}
        #     )
        
        if attrs.get("debit_amount", 0) > 0 and attrs.get("credit_amount", 0) > 0:
            raise serializers.ValidationError(
                detail={"error": "You can have either a debit amount or a credit amount, not both."}
            )
        if attrs.get("debit_amount", 0) == 0 and attrs.get("credit_amount", 0) == 0:
            raise serializers.ValidationError(
                detail={"error": "Either debit amount or credit amount must be non-zero."}
            )

        return attrs


class JournalEntrySerializer(serializers.ModelSerializer):
    lines = JournalLineSerializer(many=True)

    class Meta:
        model = JournalEntry
        fields = [
            "id",
            "date",
            "journal_number",
            "references",
            "notes",
            "journal_type",
            "transaction_type",
            "lines",
            "company",
        ]
        extra_kwargs = {"journal_number": {"read_only": True}}

    def create(self, validated_data):
        lines_data = validated_data.pop("lines")

        # Use atomic transaction to ensure all-or-nothing behavior
        with transaction.atomic():
            journal_entry = JournalEntry.objects.create(**validated_data)
            # total_debit = 0
            # total_credit = 0

            for line_data in lines_data:
                #     total_debit += line_data.get("debit_amount", 0)
                #     total_credit += line_data.get("credit_amount", 0)
                JournalLine.objects.create(journal_entry=journal_entry, **line_data)

            # Validate that total debits equal total credits
            # if total_debit != total_credit:
            #     raise serializers.ValidationError(
            #         "The sum of debit amounts must equal the sum of credit amounts."
            #     )

        return journal_entry


class JournalEntryUploadSerializer(serializers.Serializer):
    file = serializers.FileField()
    company = serializers.CharField(max_length=255)
    branch = serializers.CharField(max_length=255, required=False)

    def validate_company(self, value):
        try:
            return Company.objects.get(id=value)
        except Company.DoesNotExist:
            raise serializers.ValidationError("invalid company ID")

    def validate_branch(self, value):
        try:
            return Branch.objects.get(id=value)
        except Branch.DoesNotExist:
            raise serializers.ValidationError("invalid branch ID")

    def validate(self, attrs):
        branch = attrs.get("branch")
        company = attrs["company"]

        if branch:
            if branch.company != company:
                raise serializers.ValidationError(
                    "the branch provided must belong to the company provided"
                )
        return super().validate(attrs)
