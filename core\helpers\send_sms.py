from core.services import WhisperSms 
from itertools import combinations

def vfd_sms(phone_number, product_vertical:list):
    pairs = list(combinations(product_vertical, 2))
    for vertical_one in product_vertical:
        if vertical_one == "Spend_Management":
            message = """Thank you for your interest in our spend management solution.\nTake a moment to fill out this scorecard to get personalised insights\nhttps://oti-jeazipiu.scoreapp.com
                    """
            try:
                WhisperSms.send_generic_sms(
                    phone_number=phone_number,
                    message=message,
                )
                return "SPEND_MANAGEMENT SMS SENT SUCCESSFULLY"
            except Exception as e:
                return f"AN ERROR OCCURRED WHILE ATTEMPTING TO SEND SMS {str(e)}"
        elif vertical_one == "Stock_and_Inventory":
            message = """Thanks for exploring our stocks and inventory options.\nTake a moment to fill out this scorecard for tailored recommendations\nhttps://oti-tr1fgp2a.scoreapp.com
                """
            try:
                WhisperSms.send_generic_sms(
                    phone_number=phone_number,
                    message=message,
                )
                return "STOCKS_AND_INVENTORY SMS SENT SUCCESSFULLY"
            except Exception as e:
                return f"AN ERROR OCCURRED WHILE ATTEMPTING TO SEND SMS {str(e)}"
        elif vertical_one == "Sales_Management":
            message = """Thanks for exploring our point of sale options.\nTake a moment to fill out this scorecard for tailored recommendations\nhttps://oti-tr1fgp2a.scoreapp.com
                """
            try:
                WhisperSms.send_generic_sms(
                    phone_number=phone_number,
                    message=message,
                )
                return "POINT_OF_SALES SMS SENT SUCCESSFULLY"
            except Exception as e:
                return f"AN ERROR OCCURRED WHILE ATTEMPTING TO SEND SMS {str(e)}"
        elif vertical_one == "HR_Solution":
            message = """We are excited about your interest in our HR solution!\nComplete this quick scorecard for tailored recommendations\nhttps://oti-a93axb5b.scoreapp.com
                """
            try:
                WhisperSms.send_generic_sms(
                    phone_number=phone_number,
                    message=message,
                )
                return "HR_MANAGEMENT SMS SENT SUCCESSFULLY"
            except Exception as e:
                return f"AN ERROR OCCURRED WHILE ATTEMPTING TO SEND SMS {str(e)}"
        elif vertical_one == "Payroll":
            message = """We are excited about your interest in our payroll solution!\nComplete this quick scorecard for tailored recommendations\nhttps://oti-a93axb5b.scoreapp.com
            """
            try:
                WhisperSms.send_generic_sms(
                    phone_number=phone_number,
                    message=message,
                )
                return "PAYROLL SMS SENT SUCCESSFULLY"
            except Exception as e:
                return f"AN ERROR OCCURRED WHILE ATTEMPTING TO SEND SMS {str(e)}"
    for vertical_one, vertical_two in pairs:
        if vertical_one == "Stock_and_Inventory" and vertical_two == "Sales_Management":
            message = """Thanks for exploring our options.\nTake a moment to fill out this scorecard for tailored recommendations\nhttps://oti-tr1fgp2a.scoreapp.com
                """
            try:
                WhisperSms.send_generic_sms(
                    phone_number=phone_number,
                    message=message,
                )
                return "STOCKS_AND_INVENTORY & POINT_OF_SALES SMS SENT SUCCESSFULLY"
            except Exception as e:
                return f"AN ERROR OCCURRED WHILE ATTEMPTING TO SEND SMS {str(e)}"
        if vertical_one == "HR_Solution" and vertical_two == "Payroll":
            message = """We are excited about your interest in our solution!\nComplete this quick scorecard for tailored recommendations\nhttps://oti-a93axb5b.scoreapp.com
                """
            try:
                WhisperSms.send_generic_sms(
                    phone_number=phone_number,
                    message=message,
                )
                return "HR_MANAGEMENT & PAYROLL SMS SENT SUCCESSFULLY"
            except Exception as e:
                return f"AN ERROR OCCURRED WHILE ATTEMPTING TO SEND SMS {str(e)}"