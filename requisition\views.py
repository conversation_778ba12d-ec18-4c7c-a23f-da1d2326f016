import json
import uuid
from datetime import datetime, timedelta
from secrets import compare_digest

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password
from django.db import transaction
from django.core.cache import cache
from django.db.models import (
    Char<PERSON><PERSON>,
    Count,
    F,
    Prefetch,
    ProtectedError,
    Q,
    Sum,
    Value,
)
from django.db.models.functions import Cast, Concat
from django.http import Http404
from django.utils import timezone
from django.utils.crypto import get_random_string
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, generics, status
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import AccessToken

from account.models import AccountSystem, Transaction, Wallet
from clock_app.views import CustomRecordPagination
from core.auth.custom_auth import CustomUserAuthentication
from core.exceptions import raise_serializer_error_msg
from core.helpers.func import encrypt_string
from core.models import OTP, CategoryList, ConstantTable
from core.pagenator import CustomPagination
from core.permissions import IsUSerBlackListed
from core.serializers import VerifyOtpSerializer
from core.tasks import send_email, upload_file_aws_s3_bucket
from helpers.custom_permissions import (
    CanDisburse,
    CanEditBudget,
    CanEditTeam,
    CanInitiateTransfer,
    IsCompanyOwner,
)
from helpers.disbursment_helper import LibertyPay
from helpers.reusable_functions import is_valid_uuid, split_string_by_colon
from payroll_app.models import CompanyEmployeeList
from requisition.helpers.enums import UserRole
from requisition.helpers.send_sms import MonoApis, sms_notifications
from stock_inventory.models import Supplier
from django.contrib.auth.hashers import check_password

from .helpers.func import get_company_verification_count
from .helpers.request_cls import verify_company
from .models import (
    AdGeneneratedUsers,
    Budget,
    BudgetAllocation,
    Category,
    Company,
    CompanyIndustry,
    CompanyVerificationInfo,
    CompanyVerificationMetaData,
    Expense,
    JotFormDataSync,
    MonoTopUpRecord,
    Requisition,
    Team,
    TeamMember,
    TeamMemberInvite,
    UserLinkedBanks,
    PurchaseIndent,
    ProcurementPurchaseInvoice,
    UserLinkedBanks,
    PurchaseIndent,
    ProcurementPurchaseOrder,
    PurchaseOrderComment,
    ProcurementPurchaseInvoiceUploadedFIles,
    ProcurementCreditNote,
    Notes,
    Flags, Asset, AssetExpense,
)
from .serializers import (
    AnnulAllocationToCompanySerializer,
    BudgetAllocationSerializer,
    BudgetSerializer,
    CommentSerializer,
    CreateBudgetCategoriesSerializer,
    CreateCompanySerializer,
    CreateRequisitionTransactionPinSerializer,
    CreateUpdateTeamSerializer,
    DeclineReqSerializer,
    DisbursementSerializer,
    EditBudgetCategorySerializer,
    EditBudgetSerializer,
    EditTeamMemberPermissionAndOthersSerializer,
    ExpenseDashboardSerializer,
    ExpenseDashboardSerializerList,
    FlagsSerializer,
    FundWalletSerializer,
    GetMonokeyExchangCodeSerializer,
    IndustrySerializer,
    LinkTeamToBudgetSerializer,
    ListBudgetSerializer,
    ListOfCompanySerializer,
    ListPaidBudgetCategoriesSerializer,
    ListReqSerializer,
    ListRequisitionSerializer,
    ListTeamSerializer,
    MembersInviteSerializer,
    MonoPaymentHookSerializer,
    PaginatedTeamMembersSerializer,
    ProcurementPurchaseInvoiceSerializerModify,
    ProcurementPurchaseInvoiceUploadedFilesSerializer,
    ReceiptDataSerializer,
    RequisitionExpenseSerializer,
    RequisitionSerializer,
    SetDefaultCompanySerializer,
    TeamInviteApprovalSerializer,
    TeamMemberInviteSerializer,
    TeamMemberSerializer,
    TrackBudgetSerializer,
    UpdateAgencyTransactionPinSerializer,
    VerifyCorporateCompanyInfoSerializer,
    VerifyCorporateCompanyRegistrationNumberSerializer,
    WallwtWithdrawalSerializer,
    BulkDisbursementSerializerIn,
    PurchaseIndentSerializerIn,
    PurchaseIndentSerializerOut,
    ProcurementPurchaseInvoiceSerializerIn,
    ProcurementPurchaseInvoiceSerializerOut,
    PurchaseIndentApproveSerializerIn,
    PurchaseIndentBulkUploadSerializerIn,
    BulkProcurementSerailizerIn,
    ProcurementPurchaseOrderSerializerOut,
    AddSupplierSerializerIn,
    SupplierSerializerOut,
    SupplierCompleteOnboardingSerializerIn,
    ApproveSupplierSerializerIn,
    SupplierLoginSerializerIn,
    SupplierApproveDeclinePurchaseOrderSerializerIn,
    PurchaseOrderCommentSerializerIn,
    PurchaseOrderCommentSerializerOut,
    ConfirmDeliverySerializerIn,
    NameEnquirySerializerIn,
    AcceptDeclineReturnSerializerIn,
    ProcurementCreditNoteSerializerOut,
    UpdateSupplierProfileSerializerIn,
    NotesSerializer,
    ConfirmProcurementPurchaseAmountPaidSerializer,
    CompanyPaymentWalletSeriaizer,
    EnterAmountToPaySerialzer,
    DisbursementPinSerializer,
    RequestProcurementOTPSerializerIn,
    OrderFulfilmentSerializerIn, AssetSerializerOut, AssetSerializerIn, AssetImageSerializerIn, SupplierChangePasswordSerializerIn,
    SupplierResetPasswordSerializerIn, AssetExpenseSerializerIn,
)
from account.models import AccountSystem

##### parsing documents import
from rest_framework.pagination import PageNumberPagination
from rest_framework.decorators import api_view, permission_classes
from rest_framework.request import Request
from .utils import (
    get_purchase_order_data,
    get_previous_next_item,
    get_supplier_dashboard_data,
    is_supplier_check, get_asset_dashboard, get_spend_overview,
)
from rest_framework.parsers import MultiPartParser, FormParser, FileUploadParser

import re
import spacy
from spacy.pipeline import EntityRuler

nlp = spacy.load("en_core_web_sm")

User = get_user_model()

logger = settings.LOGGER


# list request


class ListFilterCompanies(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListOfCompanySerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "id",
        "company_name",
    )
    search_fields = ("company_name",)

    def get_queryset(self):
        request_user = self.request.user

        companies = (
            Company.objects.filter(
                Q(user=request_user) & Q(is_active=True, is_deleted=False)
                | Q(teams__members__member=request_user)
                & Q(is_active=True, is_deleted=False)
            )
            .distinct()
            .prefetch_related("teams__members__member")
        )

        return companies

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        request_user = request.user
        requisitions = Requisition.objects.filter(company__user=request_user)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "total_number_of_teams": Team.objects.filter(
                company__user=request_user, is_deleted=False
            ).aggregate(count=Count("id"))["count"],
            "total_number_of_members": TeamMember.objects.filter(
                team__company__user=request_user, is_deleted=False
            )
            .values("email")
            .distinct()
            .count(),
            "requisition_count": requisitions.aggregate(count=Count("id"))["count"],
            "approved_requisition_count": requisitions.filter(
                status="APPROVED"
            ).aggregate(count=Count("id"))["count"],
            "pending_requisition_count": requisitions.filter(
                status="PENDING"
            ).aggregate(count=Count("id"))["count"],
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetIndustries(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = IndustrySerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    search_fields = ("industry",)

    def get_queryset(self):
        return CompanyIndustry.objects.all()


class GetCategories(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get_queryset(self):
        """
        Get the queryset for the view based on query parameters.

        This method fetches and filters data based on company_id, team_id, and search parameters.

        :return: A list of budgeted and un-budgeted categories.
        :rtype: list[dict]
        """
        company_id = self.request.query_params.get("company_id", None)
        team_id = self.request.query_params.get("team_id", None)
        search_param = self.request.query_params.get("search", None)
        company_instance = (
            Company.objects.filter(id=company_id).first() if company_id else None
        )
        team_instance = Team.objects.filter(id=team_id).first() if team_id else None

        # if (company_id and company_instance) and not (team_id and team_instance): # If company_id is provided and
        # team_id is not, fetch company-related categories. print("company instance") budgeted_categories =
        # Category.objects.filter(budget__company=company_instance).order_by("title").distinct( "title")
        #
        #     budgeted_categories_title = list(
        #         budgeted_categories.values_list("title", flat=True))
        #
        #     un_budgeted_categories = CategoryList.objects.exclude(title__in=budgeted_categories_title).order_by(
        #         "title").values("title")
        #
        #     initial_budgeted_categories = budgeted_categories.values("title")
        #
        #     updated_budgeted_categories = [
        #         {"title": category["title"], "budgeted": True} for category in initial_budgeted_categories]
        #
        #     updated_un_budgeted_categories = [
        #         {"title": category["title"], "budgeted": False} for category in un_budgeted_categories]
        #
        #     all_categories = updated_budgeted_categories + updated_un_budgeted_categories

        if team_id and team_instance:
            # If team_id is provided and company_id is not, fetch team-related categories.
            # print("Team instance")
            budgeted_categories = (
                Category.objects.filter(budget__team=team_instance)
                .order_by("title")
                .distinct("title")
            )

            budgeted_categories_title = list(
                budgeted_categories.values_list("title", flat=True)
            )

            un_budgeted_categories = (
                CategoryList.objects.exclude(title__in=budgeted_categories_title)
                .order_by("title")
                .values("title")
            )

            initial_budgeted_categories = budgeted_categories.values("title")

            updated_budgeted_categories = [
                {"title": category["title"], "budgeted": True}
                for category in initial_budgeted_categories
            ]

            updated_un_budgeted_categories = [
                {"title": category["title"], "budgeted": False}
                for category in un_budgeted_categories
            ]

            all_categories = (
                updated_budgeted_categories + updated_un_budgeted_categories
            )

        else:
            # If neither company_id nor team_id is provided, fetch all categories.

            all_categories = (
                CategoryList.objects.all().order_by("title").values("title", "budgeted")
            )

        if search_param is None:

            return all_categories

        else:
            # Filter categories based on search_param.
            filtered_data = []
            for item in all_categories:
                first_value = next(iter(item.values()))
                if search_param.upper() in first_value.upper():
                    filtered_data.append(item)

            return filtered_data

    def list(self, request, *args, **kwargs):
        """
        List view for the API endpoint, paginating and responding with the data.

        :param request: The HTTP request object.
        :type request: rest_framework.request.Request
        :param args: Additional positional arguments.
        :param kwargs: Additional keyword arguments.
        :return: A paginated response containing the filtered data.
        :rtype: rest_framework.response.Response
        """

        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            return self.get_paginated_response(page)

        return Response(queryset)


class ListFilterTeam(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListTeamSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    filterset_fields = ("id", "team_name", "company", "branch")
    search_fields = (
        "team_name",
        "members__member__first_name",
        "members__member__last_name",
        "members__email",
    )

    def get_queryset(self):
        user = self.request.user
        # teams = Team.objects.filter(Q(user=user) | Q(
        #     members__member=user), is_deleted=False).distinct()
        # return teams

        # queryset = Team.objects.filter(Q(user=user) | Q(
        #     members__member=user), is_deleted=False)
        # queryset = queryset.prefetch_related(
        #     Prefetch('members', queryset=TeamMember.objects.select_related('member'))).distinct()

        queryset = Team.objects.filter(
            Q(user=user) | Q(members__member=user), is_deleted=False
        )

        # Filter the team members based on the search query
        search_query = self.request.query_params.get("search", None)
        if search_query:
            queryset = queryset.prefetch_related(
                Prefetch(
                    "members",
                    queryset=TeamMember.objects.select_related("member").filter(
                        Q(member__email__icontains=search_query)
                        | Q(member__first_name__icontains=search_query)
                        | Q(member__last_name__icontains=search_query)
                    ),
                )
            ).distinct()
        else:
            queryset = queryset.prefetch_related(
                Prefetch(
                    "members", queryset=TeamMember.objects.select_related("member")
                )
            ).distinct()

        return queryset
        # return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        if queryset.exists():
            response_data = {
                "count": paginator.page.paginator.count,
                "next": paginator.get_next_link(),
                "previous": paginator.get_previous_link(),
                "company_name": queryset.first().company.company_name,
                "results": serializer.data,
            }
            return Response(response_data, status=status.HTTP_200_OK)

        else:
            response_data = {
                "count": 0,
                "next": None,
                "previous": None,
                "company_name": "",
                "results": [],
            }
            return Response(response_data, status=status.HTTP_200_OK)


class ListFilterTeamMembers(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = TeamMemberSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )
    filterset_fields = ("id", "role", "team__branch")
    search_fields = ("role", "member__first_name", "member__last_name", "email")

    def get_queryset(self):
        team_id = self.request.query_params.get("team_id")
        if not team_id:
            raise NotFound({"error": "Team ID not provided"})

        if not is_valid_uuid(team_id):
            raise NotFound({"error": "Invalid team ID"})

        try:
            team = Team.objects.get(id=team_id)
        except Team.DoesNotExist:
            raise NotFound({"error": "Invalid team ID provided"})

        team_members = TeamMember.objects.filter(
            Q(team__user_id=self.request.user.id) | Q(member_id=self.request.user.id),
            is_deleted=False,
        )

        if team_id:
            team_members = team_members.filter(team_id=team_id)

        # Implement partial search for team member's first name, last name, and email
        search_query = self.request.query_params.get("search")
        if search_query:
            team_members = team_members.filter(
                Q(role__icontains=search_query)
                | Q(member__first_name__icontains=search_query)
                | Q(member__last_name__icontains=search_query)
                | Q(email__icontains=search_query)
            )

        return team_members


class ListFilterRequisition(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListReqSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    filterset_fields = ("id", "requisition_category", "team", "company")
    search_fields = ("requisition_category",)

    def get_queryset(self):
        user = self.request.user

        team_id = self.request.GET.get("team")
        # team = Team.objects.get(id=team_id)
        logger.debug(f"LIST REQUISITION VIEW")
        logger.debug(f"LIST REQUISITION VIEW")
        logger.debug(f"TEAM ID: {team_id}")

        user_role = (
            TeamMember.objects.filter(member=user, team_id=team_id)
            .values_list("role", flat=True)
            .last()
        )
        logger.info(f"GET REQUISITION >>> {team_id}")

        logger.info(f"{user.id}")
        logger.info(f"{team_id}")

        roles = [
            "ADMIN",
            "DISBURSER",
            "OWNER",
            "SUB_ADMIN",
        ]

        if user_role in roles:

            team_ids = TeamMember.objects.filter(member=user).values_list(
                "team_id", flat=True
            )

            logger.info(f"CHECK ONE >>>> {team_ids}")

            requisition = Requisition.objects.filter(
                Q(user=user) | Q(team__in=team_ids)
            ).distinct()

            logger.info(f"REQUISITIONS >>>> {requisition.count()}")
        else:
            requisition = Requisition.objects.filter(user=user)

        return requisition

    def list(self, request, *args, **kwargs):
        user = self.request.user
        team_id = request.GET.get("team")
        if not team_id:
            err_message = {
                # "message": f"Invalid Team ID: {team_id}"
                "message": f"Team ID is required"
            }
            return Response(err_message, status=status.HTTP_400_BAD_REQUEST)
        else:
            user_role = (
                TeamMember.objects.filter(member=user, team_id=team_id)
                .values_list("role", flat=True)
                .last()
            )
            queryset = self.filter_queryset(self.get_queryset())
            paginator = self.pagination_class()
            page = paginator.paginate_queryset(queryset, request)
            serializer = self.get_serializer(page, many=True)

            response_data = {
                "count": paginator.page.paginator.count,
                "next": paginator.get_next_link(),
                "previous": paginator.get_previous_link(),
                "role": user_role,
                "results": serializer.data,
            }
            return Response(response_data, status=status.HTTP_200_OK)


class ListRequisition(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListReqSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    filterset_fields = ("id", "requisition_category", "company")
    search_fields = ("requisition_category",)

    def get_queryset(self):
        user = self.request.user
        company_id = self.request.GET.get("company")
        company = Company.objects.get(id=company_id)
        expenses = Requisition.objects.filter(company=company, company__user=user)

        if expenses is None:
            return None
        else:
            return expenses

    def list(self, request, *args, **kwargs):
        user = self.request.user
        queryset = self.filter_queryset(self.get_queryset())
        company_id = request.GET.get("company")
        company = Company.objects.filter(id=company_id).last()
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        total_count = queryset.aggregate(count=Count("id"))["count"]
        pending_count = queryset.filter(status="PENDING").aggregate(count=Count("id"))[
            "count"
        ]
        approved_count = queryset.filter(status="APPROVED").aggregate(
            count=Count("id")
        )["count"]

        response = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "company_name": company.company_name,
            "requisition": total_count,
            "pending_count": pending_count,
            "approved_count": approved_count,
            "results": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)


class ListFilterBudget(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListBudgetSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    filterset_fields = ("id", "company", "company__company_name", "team__team_name")
    search_fields = ("budget_name", "company__company_name", "team__team_name")

    def get_queryset(self):
        user = self.request.user

        budgets = Budget.objects.filter(
            Q(user=user) | Q(team__members__member=user),
            is_active=True,
            is_deleted=False,
        ).distinct()
        return budgets


class ListFilterAllCompanyRequisitions(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListReqSerializer

    def get_queryset(self):
        user = self.request.user
        comapnies = Company.objects.filter(user=user)
        for comp in comapnies:
            requisitions = Requisition.objects.filter(company=comp, is_deleted=False)
        return requisitions


class ListFilterPaidBudget(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListPaidBudgetCategoriesSerializer
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    filterset_fields = ("budget_id", "budget")

    def get_queryset(self):
        user = self.request.user
        categories = (
            Category.objects.filter(
                Q(budget__company__user=user) | Q(requisition__user=user),
                paid_budget=True,
            )
            .exclude(requisition__isnull=True)
            .distinct()
        )

        return categories


# =========================================== START COMPANY ===========================================


class CreateListAndDelCompany(APIView):
    permission_classes = [IsAuthenticated, IsUSerBlackListed]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):  # create a new company
        serializer = CreateCompanySerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        transaction_pin = validated_data.get("transaction_pin")

        company = Company.create_company(
            user=request.user,
            validated_data=validated_data,
            transaction_pin=transaction_pin,
        )

        res_data = {
            "status": "Success",
            "data": {
                "id": company.id,
                "company_name": validated_data.get("company_name"),
                "industry": validated_data.get("industry"),
                "size": validated_data.get("size"),
            },
        }

        return Response(res_data, status=status.HTTP_200_OK)

    @staticmethod
    def delete(request):
        company_uuid = request.query_params.get("id")

        try:
            company = Company.objects.get(pk=company_uuid)

            if company.user.email != request.user.email:
                return Response(
                    {
                        "status": "Error",
                        "message": "You do not have the permission to perform this action.",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

        except Company.DoesNotExist:
            return Response(
                {"status": "Error", "message": "Company not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        company.is_deleted = True
        company.is_active = False
        company.save()
        return Response(
            {"status": "Success", "message": "Successfully deleted"},
            status=status.HTTP_200_OK,
        )


class VerifyCorporateCompanyRegistrationNumber(APIView):
    permission_classes = [IsAuthenticated, IsUSerBlackListed]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = VerifyCorporateCompanyRegistrationNumberSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        cac_num = validated_data.get("cac_num")

        company = Company.objects.filter(cac_num=cac_num, is_active=True)

        if company.exists():
            return Response(
                {"message": f"Company with registered number {cac_num} already exists"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        existing_company_info = CompanyVerificationInfo.objects.filter(
            registration_no=cac_num
        ).first()
        if existing_company_info:
            response_Data = {
                "status": "success",
                "active": True,
                "data": {
                    "cac_num": cac_num,
                    "verification_id": existing_company_info.id,
                    "requires_tin_verification": (
                        True
                        if existing_company_info.tax_identification_number is not None
                        else False
                    ),
                },
            }
            return Response(response_Data, status=status.HTTP_200_OK)

        else:
            initial_verification_res = CompanyVerificationMetaData.objects.filter(
                registration_no=cac_num
            ).first()

            if initial_verification_res:
                if initial_verification_res.is_active:
                    verification_info = CompanyVerificationInfo.objects.filter(
                        registration_no=cac_num
                    ).first()

                    response_Data = {
                        "status": "Success",
                        "active": True,
                        "data": {
                            "cac_num": cac_num,
                            "verification_id": verification_info.id,
                            "requires_tin_verification": (
                                True
                                if verification_info.tax_identification_number
                                is not None
                                else False
                            ),
                        },
                    }
                    return Response(response_Data, status=status.HTTP_200_OK)

                else:
                    verification_response = (
                        initial_verification_res.verification_response
                    )
                    verification_message = split_string_by_colon(
                        verification_response.get("message", "")
                    )

                    response_Data = {
                        "status": "Failed",
                        "active": False,
                        "message": (
                            "Company is currently not active"
                            if verification_message == "success"
                            else verification_message
                        ),
                        "data": None,
                    }

                    return Response(response_Data, status=status.HTTP_400_BAD_REQUEST)

            verification_trial_object = get_company_verification_count(
                user_id=request.user.email
            )
            const = ConstantTable.get_constant_instance()
            if (
                verification_trial_object.get("count")
                >= const.company_verification_number
            ):
                retry_date = verification_trial_object.get("retry_datetime")[:20]
                verification_message = (
                    f"We're sorry, but it seems you have made too many verification attempts for "
                    f"this company number. Please try again on: {retry_date}"
                )

                response_Data = {
                    "status": "Failed",
                    "message": verification_message,
                    "active": False,
                    "data": None,
                }
                return Response(data=response_Data, status=status.HTTP_400_BAD_REQUEST)

            verification_response = verify_company(registration_no=cac_num)

            verification_metadata = CompanyVerificationMetaData.objects.create(
                user=request.user,
                registration_no=cac_num,
                verification_response=verification_response,
            )

            verification_status_code = verification_response.get("statusCode")
            # print(verification_status_code, "\n\n")
            verification_message = split_string_by_colon(
                verification_response.get("message", "")
            )
            # print(verification_message, "\n\n")

            if verification_status_code == 200:

                data = verification_response.get("data")
                tin = data.get("tin", None)

                if (
                    data.get("status") == "found"
                    or data.get("companyStatus") == "ACTIVE"
                ):
                    verification_info = CompanyVerificationInfo.objects.create(
                        registration_no=cac_num,
                        tax_identification_number=tin,
                        is_active=True,
                        verification_response=verification_response,
                    )

                    verification_metadata.is_active = True
                    verification_metadata.save()

                    response_Data = {
                        "status": "Success",
                        "active": True,
                        "data": {
                            "cac_num": cac_num,
                            "verification_id": verification_info.id,
                            "requires_tin_verification": (
                                True if tin is not None else False
                            ),
                        },
                    }
                    return Response(response_Data, status=status.HTTP_200_OK)
                else:
                    response_Data = {
                        "status": "Failed",
                        "message": "Company is currently not active",
                        "active": False,
                        "data": None,
                    }
                    return Response(response_Data, status=status.HTTP_400_BAD_REQUEST)
            else:
                response_Data = {
                    "status": "Failed",
                    "message": verification_message,
                    "active": False,
                    "data": None,
                }
                return Response(data=response_Data, status=status.HTTP_400_BAD_REQUEST)


class VerifyCorporateCompanyInfo(APIView):
    permission_classes = [IsAuthenticated, IsUSerBlackListed]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        # create a new company
        serializer = VerifyCorporateCompanyInfoSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        verification_instance = validated_data.get("verification_id")
        directors = validated_data.get("directors")
        tin = validated_data.get("tin")
        director_lastname = validated_data.get("director_lastname").title()
        director_firstname = validated_data.get("director_firstname").title()

        director_full_name = f"{director_lastname} {director_firstname}".title()

        director_properties = directors.get(director_full_name)

        request_doc_type = director_properties.get("documentType")
        paid_shared_capital = director_properties.get("paidShareCapital")
        id_number = director_properties.get("documentNumber")
        director_date_of_birth = director_properties.get("dateOfBirth")

        verification_instance.document_type = request_doc_type
        verification_instance.document_number = director_properties.get(
            "documentNumber"
        )
        verification_instance.registration_date = director_properties.get(
            "registrationDate"
        )
        verification_instance.registration_submission_date = director_properties.get(
            "registrationSubmissionDate"
        )
        verification_instance.paid_share_capital = director_properties.get(
            "paidShareCapital"
        )
        if director_date_of_birth is not None:
            verification_instance.director_date_of_birth = director_date_of_birth

        verification_instance.user = request.user
        verification_instance.save()

        data = {
            "status": "success",
            "data": {
                "director_lastname": director_lastname,
                "director_firstname": director_firstname,
                "document_type": request_doc_type,
                "tin": tin,
                "reg_number": verification_instance.registration_no,
                "fields": {
                    "id_number": False if not id_number else True,
                    "paid_shared_capital": False if not paid_shared_capital else True,
                    "director_date_of_birth": (
                        False if not director_date_of_birth else True
                    ),
                },
            },
        }

        return Response(data, status=status.HTTP_200_OK)


# =========================================== END COMPANY ===========================================


class CreateRequisition(APIView):
    """
    THIS VIEW ALLOWS TEAM MEMBERS WITHIN A PARTICULAR TEAM
    USER A COMPANY TO CREATE A REQUISITION ONCE LOGGED IN
    """

    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    # @staticmethod
    def post(self, request):
        user = request.user
        serializer = RequisitionSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        is_vendor = data.get("is_vendor")
        vendor_name = data.get("vendor_name")
        vendor_no = data.get("vendor_no")
        account_name = data.get("account_name")
        account_no = data.get("account_no")
        bank_name = data.get("bank_name")
        team = data.get("team")
        request_amount = data.get("request_amount")
        requisition_category = data.get("requisition_category")

        team_instance = Team.objects.filter(team_name=team).last()
        company = team_instance.company

        if is_vendor is True:
            chec_supplier = Supplier.objects.filter(
                phone_number=vendor_no, name=vendor_name, bank_account_number=account_no
            ).exists()
            if chec_supplier:
                pass
            else:
                Supplier.objects.create(
                    name=vendor_name,
                    phone_number=vendor_no,
                    email=user.email,
                    bank_account_name=account_name,
                    bank_account_number=account_no,
                    bank_name=bank_name,
                    company=company,
                    created_by=user,
                )
        validated_data = serializer.validated_data
        invoice = validated_data.get("invoice")
        user = request.user

        active_budget = Budget.objects.filter(team=team, is_active=True).first()

        category = Category.objects.filter(
            budget=active_budget,
            title=requisition_category.upper() if requisition_category else None,
        ).first()

        with transaction.atomic():

            budget_result = Budget.get_current_allocated_amount(
                budget_instance=active_budget, team_instance=team_instance
            )
            purse_balance = budget_result.get("purse_balance")
            total_request_amount_made_so_far = budget_result.get(
                "total_request_amount_made_so_far"
            )
            budget_instance = budget_result.get("budget_instance")

            # Calculate the budget balance after the expense
            budget_balance_before = float(budget_instance.budget_amount) - float(
                total_request_amount_made_so_far
            )
            budget_balance_after = budget_balance_before - float(request_amount)
            purse_value_after = float(purse_balance) - float(request_amount)

            # create expense
            expense_instance = Expense.objects.create(
                user=user,
                budget=active_budget,
                company=company,
                expense_category=requisition_category,
                expense_amount=request_amount,
                team=team,
                in_budget=True if active_budget else False,
                budget_balance_before=budget_balance_before,
                budget_balance_after=budget_balance_after,
                purse_value_before=purse_balance,
                purse_value_after=purse_value_after,
            )
            requisition_instance = serializer.save(
                user=user, out_of_budget=True if category is None else False
            )
            expense_instance.requisition = requisition_instance
            expense_instance.save()

        data = serializer.data
        model_instance_id = data.get("id")
        if invoice:
            upload_file_aws_s3_bucket(
                model_instance_id=model_instance_id,
                file=invoice,
                model_name="Requisition",
            )
        response_data = {
            "status": "Success",
            "message": "Requisition created successfully",
            "data": data,
        }

        return Response(response_data, status=status.HTTP_200_OK)


class CreateDelTeam(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = CreateUpdateTeamSerializer(
            data=request.data,
            context={"user": request.user, "request": request, "create_team": True},
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        create_team_result = Team.create_team(user=request.user, **data)
        # handle response data
        if create_team_result.get("status") == False:
            response_data = {
                "status": "Failed",
                "message": create_team_result.get("message"),
            }
            return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
        data["team_id"] = create_team_result.get("team").id
        response_data = {"status": "Success", "data": data}
        return Response(response_data, status=status.HTTP_200_OK)

    def delete(self, request):
        pk = request.query_params.get("id")
        try:
            team = Team.objects.get(user=request.user, pk=pk)
        except Team.DoesNotExist:
            return Response(
                {"status": "Error", "message": "Team not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        team.soft_delete()
        return Response(
            {"status": "Success", "message": "Team deleted successfully"},
            status=status.HTTP_200_OK,
        )


class EditTeam(APIView):
    permission_classes = [IsAuthenticated, CanEditTeam]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = CreateUpdateTeamSerializer(
            data=request.data,
            context={"user": request.user, "request": request, "create_team": False},
        )
        serializer.is_valid(raise_exception=True)
        Team.update_team(validated_data=serializer.validated_data)
        response_data = {"status": "success", "data": serializer.data}
        return Response(response_data, status=status.HTTP_200_OK)

    def delete(self, request):
        params = request.query_params
        team_id = params.get("team_id")
        member_id = params.get("member_id")

        try:
            team = Team.objects.get(pk=team_id)
        except Team.DoesNotExist:
            return Response(
                {"status": "Error", "message": "Team not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        try:
            member = TeamMember.objects.get(pk=member_id, team_id=team_id)
        except TeamMember.DoesNotExist:
            return Response(
                {"status": "Error", "message": "Member not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        if member in team.members.all():
            team.members.remove(member)
            member.is_deleted = True
            member.save()
            return Response(
                {"status": "Success", "message": "Team member removed successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "Error", "message": "Member not found on team"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class EditTeamMemberPermissionAndOthers(APIView):
    permission_classes = [IsAuthenticated, CanEditTeam]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = EditTeamMemberPermissionAndOthersSerializer(
            data=request.data, context={"user": request.user}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        # user = request.user
        team_id = data.get("team_id")
        team_member_id = data.get("team_member_id")
        role = data.get("role")
        # admin_password = data.get('admin_password')

        try:
            team_mem = TeamMember.objects.get(pk=team_member_id, team_id=team_id)

        except TeamMember.DoesNotExist:
            return Response(
                {"message": "Invalid team member selection"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        team_mem.role = role
        team_mem.save()

        response_data = {"status": "Success", "data": data}
        return Response(response_data, status=status.HTTP_200_OK)


class CreateComment(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = CommentSerializer(data=request.data, context={"request": request})
        serializer.is_valid(raise_exception=True)
        user = request.user
        # requisition=data.get("requisition")

        serializer.save(commenter=user)
        response_data = {
            "status": "success",
            "message": "Comment created successfully",
            "data": serializer.data,
        }

        return Response(response_data, status=status.HTTP_200_OK)


# Start Expense====================================================================================


class ExpenseList(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    serializer_class = ExpenseDashboardSerializerList
    pagination_class = CustomPagination
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("id", "expense_category", "team", "company", "budget")

    def get_queryset(self):
        user = self.request.user
        expenses = Expense.objects.filter(
            Q(user=user) | Q(company__user=user), status="SUCCESSFUL"
        )
        return expenses

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "total_expense": queryset.aggregate(
                total_expenses=Sum("expense_amount")
            ).get("total_expenses"),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class TeamExpenseList(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    serializer_class = ExpenseDashboardSerializerList
    pagination_class = CustomPagination
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("id", "expense_category", "team")

    def get_queryset(self):
        user = self.request.user
        team_id = self.request.GET.get("team")
        team = Team.objects.get(id=team_id)
        expenses = Expense.objects.filter(
            Q(user=user) | Q(company__user=user), team=team, status="SUCCESSFUL"
        )
        return expenses

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "total_expense": queryset.aggregate(
                total_expenses=Sum("expense_amount")
            ).get("total_expenses"),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class ExpenseDashboard(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    serializer_class = ExpenseDashboardSerializer
    pagination_class = CustomPagination
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    filterset_fields = (
        "id",
        "status",
        "company__id",
        "company__company_name",
        "expense_category",
        "team",
        "budget",
        "company",
    )
    search_fields = ("expense_category",)

    def post(self, request):
        serializer = RequisitionExpenseSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)

        user = request.user
        serializer.save(user=user)
        response_data = {
            "status": "Success",
            "message": "Expense created successfully",
            "data": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)

    def get_queryset(self):
        user = self.request.user
        expenses = Expense.objects.filter(
            Q(user=user) | Q(company__user=user), status="SUCCESSFUL"
        )
        return expenses

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        paginator.paginate_queryset(queryset, request)

        if queryset.exists():
            category_counts = queryset.values("expense_category").annotate(
                category_count=Count("id")
            )
            total_amount = queryset.aggregate(total_amount=Sum("expense_amount")).get(
                "total_amount", 0
            )
            last_updated = (
                queryset.latest("updated_at").updated_at if queryset.exists() else None
            )

            all_time_expense = queryset.aggregate(
                total_expenses=Sum("expense_amount")
            ).get("total_expenses")
            current_year_expense = queryset.filter(created_at__year=timezone.now().year)
            this_year_expense = current_year_expense.aggregate(
                total_expenses=Sum("expense_amount")
            ).get("total_expenses")

            current_month_expense = queryset.filter(
                created_at__month=timezone.now().month
            )

            this_month_expense = current_month_expense.aggregate(
                total_expenses=Sum("expense_amount")
            ).get("total_expenses")

            most_expensed_category = (
                queryset.values("expense_category")
                .annotate(total_expenses=Sum("expense_amount"))
                .order_by("-total_expenses")
                .first()
                .get("expense_category")
            )

            response = {
                "count": paginator.page.paginator.count,
                "next": paginator.get_next_link(),
                "previous": paginator.get_previous_link(),
                "company_id": queryset.last().company.id,
                "company_name": queryset.last().company.company_name,
                "all_time_expense": all_time_expense,
                "this_year": this_year_expense,
                "this_month": this_month_expense,
                "most_expensed_category": most_expensed_category,
                "categories": [],
                "category_amount_monthly": [],
            }

            for category in category_counts:
                category_total = (
                    queryset.filter(expense_category=category["expense_category"])
                    .aggregate(total_amount=Sum("expense_amount"))
                    .get("total_amount", 0)
                )
                response["categories"].append(
                    {
                        "category": category["expense_category"],
                        "budgeted_amount": 0,
                        "total_amount_by_category": category_total,
                        "category_count": category["category_count"],
                        "last_updated": last_updated,
                    }
                )
            current_year = datetime.now().year
            for i in range(1, 13):
                month_name = datetime(current_year, i, 1).strftime("%B")
                # month_name = DateFormat(
                #     timezone.now().replace(month=i)).format('F')

                monthly_expense = queryset.filter(
                    created_at__year=timezone.now().year, created_at__month=i
                )
                total_expense_month = monthly_expense.aggregate(
                    total_expenses=Sum("expense_amount")
                ).get("total_expenses")
                year = monthly_expense.values("created_at__year").first()
                category_expenses = []
                for category in category_counts:
                    category_month_expense = (
                        monthly_expense.filter(
                            expense_category=category["expense_category"]
                        )
                        .aggregate(total_expenses=Sum("expense_amount"))
                        .get("total_expenses")
                    )
                    category_expenses.append(
                        {
                            "category": category["expense_category"],
                            "total_expenses": category_month_expense,
                        }
                    )
                if monthly_expense.exists():
                    year = year.get("created_at__year")
                    print(year)
                else:
                    year = None
                response["category_amount_monthly"].append(
                    {
                        "year": year,
                        "month": month_name,
                        "category_expenses": category_expenses,
                        "total_expenses_month": total_expense_month,
                    }
                )

            return Response(response)

        else:
            response = {
                "count": paginator.page.paginator.count,
                "next": paginator.get_next_link(),
                "previous": paginator.get_previous_link(),
                "company_id": None,
                "company_name": None,
                "all_time_expense": "",
                "this_year": "",
                "this_month": "",
                "most_expensed_category": "",
                "categories": [],
                "category_amount_monthly": [],
            }
            return Response(response, status=status.HTTP_200_OK)


# ================================================= START BUDGET =================================================


class CreateBudget(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        user = request.user
        serializer = BudgetSerializer(
            data=request.data, context={"request": request, "edit": False}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        # increment end date by one
        end_date = validated_data.get("end_date") + timedelta(days=1)

        serializer.save(user=user, end_date=end_date)
        response = {
            "status": "Success",
            "message": "Budget created successfully",
            "data": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)

    def put(self, request):
        serializer = EditBudgetSerializer(
            data=request.data, context={"request": request, "edit": True}
        )
        serializer.is_valid(raise_exception=True)
        Budget.update_budget(data=serializer.data)
        response = {
            "status": "Success",
            "message": "Budget updated successfully",
            "data": serializer.data,
        }
        return Response(response)

    def delete(self, request):
        pk = request.query_params.get("id")
        try:
            budget = Budget.objects.get(user=request.user, pk=pk)
        except Budget.DoesNotExist:
            return Response(
                {"status": "Error", "message": "Budget not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        budget.is_deleted = True
        budget.is_active = False
        budget.save()
        return Response(
            {"status": "Success", "message": "Budget deleted successfully"},
            status=status.HTTP_200_OK,
        )


class CreateBudgetCategories(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = CreateBudgetCategoriesSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        Budget.create_budget_categories(data=data)

        response = {
            "status": "Success",
            "message": "Budget categories created successfully",
            "data": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)


class EditBudgetCategory(APIView):
    permission_classes = [IsAuthenticated, CanEditBudget]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = EditBudgetCategorySerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        budget_categories = data.get("categories")
        budget = data.get("budget")

        Budget.update_budget_categories(
            budget=budget, budget_categories=budget_categories
        )

        response = {
            "status": "Success",
            "message": "Updated successfully",
            "data": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)

    @staticmethod
    def delete(request):
        category_id = request.query_params.get("id")
        try:
            category = Category.objects.get(pk=category_id)
        except Category.DoesNotExist:
            return Response(
                {"message": "Category not found"}, status=status.HTTP_404_NOT_FOUND
            )

        try:
            category.is_active = False
            category.is_deleted = True
            category.save()

        except ProtectedError:
            return Response(
                {
                    "message": "Category cannot be deleted.There are expenses associated with this category"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            {"status": "Success", "message": "Successfully deleted"},
            status=status.HTTP_200_OK,
        )


class TrackBudget(APIView):
    permission_classes = [IsAuthenticated, IsCompanyOwner]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        user = request.user
        serializer = TrackBudgetSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        Expense.track_budget_by_creating_expense_object(
            user=user, validated_data=serializer.validated_data
        )

        response = {
            "status": "Success",
            "message": "Created successfully",
            "data": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)


class LinkTeamToBudget(APIView):
    permission_classes = [IsAuthenticated, IsCompanyOwner]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        user = request.user
        serializer = LinkTeamToBudgetSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        budget = validated_data.get("budget")
        team = validated_data.get("team")

        if budget.user != user:
            return Response(
                {"message": "Budget can only be updated by company owner"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        budget.team = team
        budget.save()

        response_data = {
            "status": "Success",
            "message": "Updated successfully",
            "data": serializer.data,
        }

        return Response(response_data, status=status.HTTP_200_OK)


class AllocateBudget(APIView):
    permission_classes = [IsAuthenticated, IsCompanyOwner]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = BudgetAllocationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        budget = validated_data.get("budget")
        team = validated_data.get("team")
        amount = validated_data.get("amount")

        budget_allocation = BudgetAllocation.objects.create(
            user=request.user, budget=budget, team=team, amount=amount
        )
        response = {
            "status": "Success",
            "message": "Created successfully",
            "data": {
                "id": budget_allocation.id,
                "amount": amount,
                "budget": budget.id,
                "team": team.id,
            },
        }

        return Response(response, status=status.HTTP_200_OK)


# ================================================= END BUDGET =================================================


class GetRequisitionDashboard(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListRequisitionSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    filterset_fields = ("id", "requisition_category", "team", "company")
    search_fields = ("requisition_category",)

    def get_queryset(self):
        user = self.request.user

        team_ids = TeamMember.objects.filter(member=user).values_list(
            "team_id", flat=True
        )
        requisition = Requisition.objects.filter(
            Q(user=user) | Q(team__in=team_ids)
        ).distinct()

        return requisition

    def list(self, request, *args, **kwargs):
        user = self.request.user
        queryset = self.filter_queryset(self.get_queryset())
        team_id = request.GET.get("team")
        user_role = (
            TeamMember.objects.filter(member=user, team_id=team_id)
            .values_list("role", flat=True)
            .last()
        )
        total_count = queryset.aggregate(count=Count("id"))["count"]
        pending_count = queryset.filter(status="PENDING").aggregate(count=Count("id"))[
            "count"
        ]
        approved_count = queryset.filter(status="APPROVED").aggregate(
            count=Count("id")
        )["count"]

        if queryset.exists():
            # Attempt to access company and team names from the last item in the queryset
            company_name = queryset.last().company.company_name
            team_name = queryset.last().team.team_name
        else:
            # Handle the exception when the queryset is empty
            company_name = None
            team_name = None

        response = {
            "company_name": company_name,
            "team_name": team_name,
            "role": user_role,
            "requisition": total_count,
            "pending_count": pending_count,
            "approved_count": approved_count,
        }
        return Response(response, status=status.HTTP_200_OK)


class DeclineRequsition(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        serializer = DeclineReqSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        requisition_id = serializer.validated_data.get("requisition_id")
        declined_reason = serializer.validated_data.get("declined_reason")

        user = request.user
        request_user_email = user.email
        logger.info(f"DECLINE REQUISITION...........")
        logger.info(f"DECLINE REQUISITION...........")
        logger.info(f"REQUEST USER EAMIL --->{request_user_email}")
        logger.info(f"REQUEST USER EAMIL --->{request_user_email}")

        try:
            requisition = Requisition.objects.get(id=requisition_id)
        except Requisition.DoesNotExist:

            return Response(
                {"message": "invalid requisition"}, status=status.HTTP_400_BAD_REQUEST
            )
        logger.info(f"REQUISITION ID --->{requisition.id}")
        logger.info(f"REQUISITION ID --->{requisition.id}")

        team = requisition.member.team
        team_members = team.members.all()
        logger.info(f"TEAM NAME --->{team.team_name}")
        logger.info(f"TEAM NAME --->{team.team_name}")

        # logger.info(f"MEMBERS --->{team_members}")
        # logger.info(f"MEMBERS --->{team_members}")

        roles = ["SUB_ADMIN", "DISBURSER", "OWNER", "ADMIN"]
        logger.info(f"ROLES --->{roles}")
        member_exist_as_sub_or_disburser_on_team = team_members.filter(
            role__in=roles, email=request_user_email
        ).last()

        logger.info(
            f"EXIST AS SUB ADMIN OR SUPER ROLE --->{member_exist_as_sub_or_disburser_on_team}"
        )
        logger.info(
            f"EXIST AS SUB ADMIN OR SUPER ROLE --->{member_exist_as_sub_or_disburser_on_team}"
        )
        if member_exist_as_sub_or_disburser_on_team:
            if requisition.status == "PENDING":
                requisition.status = "DECLINED"
                requisition.declined_reason = declined_reason
                requisition.save()

                # SEND EMAIL NOTIFICATION TO REQUESTER ABOUT THE DECLINE

                response = {
                    "status": status.HTTP_200_OK,
                    "message": "Decline Successful",
                }
                return Response(response)
            else:
                return Response(
                    {"message": "Approved Requisition cannot be Declined"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"message": "You do not have permissions"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AgencyTransactionPinExist(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        user = request.user
        if user.agency_banking_transaction_pin:
            response = {
                "status": True,
                "pin": True,
                "message": "Bank transaction pin exists",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "status": False,
                "pin": False,
                "message": "Bank transaction pin does not exists",
            }
            return Response(response, status=status.HTTP_200_OK)


class OnDisbursementVerifyReqPin(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        user = request.user
        if user.requisition_transaction_pin:
            response = {
                "status": True,
                "pin": True,
                "message": "Requisition transaction pin exists",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "status": False,
                "pin": False,
                "message": "Requisition transaction pin does not exists",
            }
            return Response(response, status=status.HTTP_200_OK)


class CreateRequisitionTransactionPin(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        user = request.user
        serializer = CreateRequisitionTransactionPinSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        pin1 = serializer.validated_data.get("pin1")
        pin_hash = make_password(pin1)
        user.requisition_transaction_pin = pin_hash
        user.save()
        return Response(
            {"status": True, "message": "Created successfully"},
            status=status.HTTP_200_OK,
        )


class UpdateAgencyTransactionPin(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        user = request.user
        serializer = UpdateAgencyTransactionPinSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        transaction_pin = serializer.validated_data.get("transaction_pin")

        pin = encrypt_string(pass_code=transaction_pin)
        user.agency_banking_transaction_pin = pin
        user.save()
        return Response(
            {"status": True, "message": "Updated successfully"},
            status=status.HTTP_200_OK,
        )


class BulkDisbursementAPIView(APIView):
    permission_classes = [IsAuthenticated, CanInitiateTransfer]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = BulkDisbursementSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class Disbursement(APIView):
    permission_classes = [IsAuthenticated, CanDisburse, CanInitiateTransfer]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        serializer = DisbursementSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        requisition = validated_data.get("requisition")
        company = validated_data.get("company")
        wallet_type = validated_data.get("wallet_type")

        user = request.user
        logger.debug(f"COMPANY: {company}")
        logger.debug(f"WALLET TYPE: {wallet_type}")

        account_instance = AccountSystem.objects.filter(
            company=company,
            account_type=(
                "SPEND_MGMT" if wallet_type == "CORPORATE" else "NON_CORP_SPEND_MGMT"
            ),
        ).first()

        requisition_payout = Requisition.requisition_disbursement(
            requisition=requisition,
            user=user,
            account=account_instance,
            wallet_type=wallet_type,
        )

        if isinstance(requisition_payout, dict):
            response = {
                "status": "error",
                "message": "Insufficient funds. Contact management.",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        else:
            response_data = {
                "status": "success",
                "message": "Requisition completed successfully",
            }
            return Response(response_data, status=status.HTTP_200_OK)


class RecordExpense(APIView):
    """
    THIS VIEW ALLOWS TEAM MEMBERS WITHIN A PARTICULAR TEAM
    USER A COMPANY TO CREATE AN EXPENSE ONCE LOGGED IN
    """

    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    # @staticmethod
    def post(self, request):
        serializer = RequisitionExpenseSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        receipt = validated_data.get("receipt")
        user = request.user
        serializer.save(user=user)

        data = serializer.data
        model_instance_id = data.get("id")
        upload_file_aws_s3_bucket(
            model_instance_id=model_instance_id, file=receipt, model_name="Expense"
        )
        response_data = {
            "status": "Success",
            "message": "Expense created successfully",
            "data": data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class ReadExpenseReceipt(APIView):
    """THIS ENDPOINT REQUEST FOR RECEIPT IMAGES AND EXTRACT VALUABLE DATA"""

    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = ReceiptDataSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        receipt = validated_data.get("receipt")
        from requisition.helpers.receipt_extract import process_invoice_file_2

        data = process_invoice_file_2(file=receipt)

        new_data = data[0].replace(",", " ").replace("\n", '"},')
        new_data = new_data.replace("1. Date: ", '{"Date": "')

        new_data = new_data.replace("2. Amount: ", '{"Amount": "')
        new_data = new_data.replace("3. Mode of Payment: ", '{"Mode of Payment": "')
        new_data = new_data.replace("4. Vendor: ", '{"Vendor": "')
        new_data = new_data.replace("5. Invoice Number: ", '{"Invoice Number": "')
        new_data = new_data.replace("6. Location: ", '{"Location": "')
        new_data = new_data.replace("7. Tax Information: ", '{"Tax Information": "')
        new_data = new_data.replace("8. Item description: ", '{"Item description": "')

        list_data = new_data.split(",")
        last_item = list_data[len(list_data) - 1]

        del list_data[-1]

        last_item = last_item + '"}'
        result_dict = {}
        list_data.append(last_item)

        for item in list_data:

            try:
                item_dict = json.loads(item)
                result_dict.update(item_dict)
            except json.JSONDecodeError:
                pass

        return Response(result_dict, status=status.HTTP_200_OK)


class FundWalletDetails(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        user = request.user
        company = Company.objects.filter(user=user).last()
        print(company)
        user_main_balance = LibertyPay().get_agent_balance(
            user_ids=[user.liberty_pay_id]
        )
        # print(user.liberty_pay_id)

        for item in user_main_balance:
            for wallet in item["wallets"]:
                if wallet["wallet_type"] == "COLLECTION":
                    avail_balance = wallet["available_balance"]

        account_details = AccountSystem.objects.filter(
            Q(user=company.user)
            & (Q(account_type="SPEND_MGMT") | Q(account_type="NON_CORP_SPEND_MGMT"))
        ).last()
        if account_details:
            response = {
                "account_name": account_details.account_name,
                "account_number": account_details.account_number,
                "bank_name": account_details.bank_name,
                "available_balance": avail_balance,
            }

            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "account_name": "",
                "account_number": "",
                "bank_name": "",
                "available_balance": "",
            }
            return Response(response, status=status.HTTP_302_FOUND)

    def post(self, request):

        token = request.headers.get("Authorization").split(" ")[1]
        serializer = FundWalletSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        amount = serializer.validated_data.get("amount")
        transaction_pin = serializer.validated_data.get("transaction_pin")
        company_id = serializer.validated_data.get("company")
        user = request.user

        company = Company.objects.get(id=company_id, user=user)
        print(company)
        company_account = AccountSystem.objects.filter(
            Q(company=company) & Q(account_type="SPEND_MGMT")
            | Q(account_type="NON_CORP_SPEND_MGMT")
        ).last()
        print(company_account)
        fund_wallet = Wallet().fund_Wallet_from_LibertyPay(
            amount=amount,
            transaction_pin=transaction_pin,
            company_user=company_account,
            auth_token=token,
        )

        if fund_wallet.get("status_code") == "00":
            response = fund_wallet

            return Response(response, status=status.HTTP_200_OK)

        elif fund_wallet.get("status_code") == "01":
            response = fund_wallet

            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        elif fund_wallet.get("status_code") == "02":

            response = fund_wallet

            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        elif fund_wallet.get("status_code") == "03":

            response = fund_wallet

            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        else:
            response = {
                "succeded": False,
                "status_code": "02",
                "message": "Service is currently not available.",
                "results": None,
            }
        return Response({}, status=status.HTTP_400_BAD_REQUEST)


class WithdrawWallet(APIView):
    permission_classes = [IsAuthenticated, CanInitiateTransfer]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        serializer = FundWalletSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company_id = self.request.GET.get("company_id")

        if not company_id:
            response = {"message": "company id is required"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        amount = serializer.validated_data.get("amount")
        transaction_pin = serializer.validated_data.get("transaction_pin")
        # user = request.user
        company = Company.objects.filter(id=company_id).last()

        company_account = AccountSystem.objects.filter(
            (
                Q(company=company)
                & (Q(account_type="SPEND_MGMT") | Q(account_type="NON_CORP_SPEND_MGMT"))
            )
        ).first()
        bank_code = company_account.user.bank_code
        # new send money
        payout = Transaction.vfd_funds_transfer(
            bank_code=bank_code,
            bank_name=company_account.user.bank,
            account_name=company_account.user.full_name,
            account_number=company_account.user.account_no,
            narration="Paybox Withdrawal",
            amount=float(amount),
            user=request.user,
            account=company_account,
        )
        if isinstance(payout, str):
            response = {
                "succeded": True,
                "status_code": "00",
                "message": "pending",
                "results": {
                    "status": "PENDING",
                },
            }
            return Response(response, status=status.HTTP_200_OK)
        else:

            response = {
                "succeded": False,
                "status_code": "03",
                "message": "insufficient funds",
                "results": None,
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class WithdrawToExternal(APIView):
    permission_classes = [IsAuthenticated, IsCompanyOwner, CanInitiateTransfer]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        serializer = WallwtWithdrawalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company_id = self.request.GET.get("company_id")
        if not company_id:
            response = {"message": "company id is required"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        amount = serializer.validated_data.get("amount")
        # transaction_pin = serializer.validated_data.get("transaction_pin")
        bank_code = serializer.validated_data.get("bank_code")
        account_no = serializer.validated_data.get("account_no")
        account_name = serializer.validated_data.get("account_name")
        bank_name = serializer.validated_data.get("bank_name")
        user = request.user

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"message": "Selected company not found"}, status=status.HTTP_404_NOT_FOUND)

        company_account = AccountSystem.objects.filter(
            (
                Q(company=company)
                & (Q(account_type="SPEND_MGMT") | Q(account_type="NON_CORP_SPEND_MGMT"))
            )
        ).first()

        payout = Transaction.vfd_funds_transfer(
            bank_code=bank_code,
            bank_name=bank_name,
            account_name=account_name,
            account_number=account_no,
            narration="Paybox Withdrawal",
            amount=float(amount),
            user=request.user,
            account=company_account,
        )
        if isinstance(payout, str):
            response = {
                "succeded": True,
                "status_code": "00",
                "message": "pending",
                "results": {
                    "status": "PENDING",
                },
            }
            return Response(response, status=status.HTTP_200_OK)
        else:

            response = {
                "succeded": False,
                "status_code": "03",
                "message": "insufficient funds",
                "results": None,
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class AllVendorDetails(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):

        company_id = self.request.GET.get("company")
        if not company_id:
            response = {
                "message": "company id is required",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            comp_id = uuid.UUID(company_id)
            company = Company.objects.get(id=comp_id)
        except (ValueError, Company.DoesNotExist):
            return Response(
                {"message": "Invalid company"}, status=status.HTTP_400_BAD_REQUEST
            )

        vendors = Supplier.objects.filter(company=company)

        vendor_list = []
        for vendor in vendors:
            vendor_details = {
                "id": vendor.id,
                "name": vendor.name,
                "phone": vendor.phone_number,
                "bank_name": vendor.bank_name,
                "account_name": vendor.bank_account_name,
                "bank_number": vendor.bank_account_number,
            }
            vendor_list.append(vendor_details)

        return Response(vendor_list, status=status.HTTP_200_OK)


class VendorDetails(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = self.request.GET.get("company")
        vendor_id = self.request.GET.get("vendor_id")
        if not company_id:
            response = {
                "message": "company id is required",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        if not vendor_id:
            response = {
                "message": "Vendor id is required",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            comp_id = uuid.UUID(company_id)
            company = Company.objects.get(id=comp_id)
        except (ValueError, Company.DoesNotExist):
            return Response(
                {"message": "Invalid company"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            vend_id = uuid.UUID(vendor_id)
            vendor = Supplier.objects.get(id=vend_id, company=company)
        except (ValueError, Supplier.DoesNotExist):
            return Response(
                {"message": "Vendor not found"}, status=status.HTTP_400_BAD_REQUEST
            )

        vendor_details = {
            "id": vendor.id,
            "name": vendor.name,
            "phone": vendor.phone_number,
            "bank_name": vendor.bank_name,
            "account_name": vendor.bank_account_name,
            "bank_number": vendor.bank_account_number,
        }

        return Response(vendor_details, status=status.HTTP_200_OK)


class ReqReminder(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    """
    This view sends SMS reminder for a requisition that is yet to be treated (approved/declined) 

    """

    def post(self, request):

        user = request.user
        req_id = self.request.GET.get("id")
        if not req_id:
            return Response(
                {"message": "requisition id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            req = Requisition.objects.get(id=req_id)
        except Requisition.DoesNotExist:
            return Response(
                {"message": "Requisition not found"}, status=status.HTTP_400_BAD_REQUEST
            )

        if req.status == "APPROVED" or req.status == "DECLINED":
            return Response(
                {
                    "message": "Cannot send reminder for an approved or declined requisition"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if user == req.user:
            team = req.team
            members = team.members.filter(
                Q(role="SUB_ADMIN")
                | Q(role="REVIEWER")
                | Q(role="DISBURSER")
                | Q(role="ADMIN")
            )
            for member in members:
                send_email.delay(
                    recipient=member.email,
                    subject="Requisition",
                    template_dir="",
                    use_template=False,
                    body=f"This a Reminder notification of requisition created by {req.user.email}, Reason --> {req.request_reason}",
                )
                sms_notifications().requsition_created_sms(
                    num=req.company.user.phone_no,
                    name=req.company.user.first_name,
                    link="http://www.home.paybox360.com",
                    amount=req.request_amount,
                    requester=req.user.first_name,
                    reason=req.request_reason,
                )
            response = {
                "message": "Reminder sent successfully",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "message": "You do not have permission to perform this reminder action"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class GetRequisitionInfo(APIView):

    def get(self, request):

        webh_token = self.request.GET.get("Secret-Web-Token")
        if not compare_digest(webh_token, settings.SECRET_WEB_TOKEN):
            response = {"message": "incorrect Secret-Web-Token"}
            return Response(response, status=status.HTTP_401_UNAUTHORIZED)

        req_id = self.request.GET.get("id")
        try:
            req = Requisition.objects.get(id=req_id)
        except Requisition.DoesNotExist:
            response = {"message": "error: requisition not found", "data": {}}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        if req.created_at < timezone.now() - timedelta(hours=1):
            response = {"message": "this link has expired", "data": {}}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        response = {
            "message": "success",
            "data": {
                "id": req.id,
                "team_name": req.team.team_name,
                "user_name": req.user.first_name,
                "request_amount": req.request_amount,
                "user_email": req.user.email,
                "requisition_category": req.requisition_category,
                "request_time": req.created_at,
            },
        }
        return Response(response, status=status.HTTP_200_OK)


class GetUnUtilizedAllocations(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        # Get the 'company_id' parameter from the GET request
        company_id = request.GET.get("company_id")

        # Check if 'company_id' is missing or invalid
        if not company_id:
            data = {"message": "Kindly provide a valid company ID"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # Retrieve the Company instance based on the 'company_id'
        company_instance = Company.objects.filter(id=company_id).first()

        # Query the budgets for the provided company that are inactive, have a positive running balance,
        # and annul_balance is False
        in_active_budget = Budget.objects.filter(
            company=company_instance,
            is_active=False,
            running_balance__gt=0,
            annul_balance=False,
        )

        # Calculate the total un_utilized budget by summing the 'running_balance' of the inactive budgets
        total_un_utilized = (
            in_active_budget.aggregate(sum_amount=Sum("running_balance"))["sum_amount"]
            or 0
        )

        # Retrieve specific fields from the inactive budgets using 'values'
        val = in_active_budget.values(
            "team_id", "team__team_name", "running_balance", "end_date"
        )

        # Prepare the response data
        data = {
            "count": 1,
            "next": None,
            "previous": None,
            "total_un_utilized": total_un_utilized,
            "company": {
                "id": company_id,
                "company_name": (
                    company_instance.company_name if company_instance else ""
                ),
                "total_un_utilized": total_un_utilized,
            },
            "teams": val,
        }

        # Return the response with a 200 OK status
        return Response(data=data, status=status.HTTP_200_OK)


class AnnulRunningBalanceToCompanyWallet(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = AnnulAllocationToCompanySerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validate_data = serializer.validated_data
        # print(validate_data, "\n")

        company_instance = validate_data.get("company_instance")
        total_un_utilized = validate_data.get("total_un_utilized")
        in_active_budget = validate_data.get("in_active_budget")

        print(total_un_utilized, in_active_budget)
        data = {}
        return Response(data=data, status=status.HTTP_200_OK)


class MonoPaymentHook(APIView):
    serializer_class = MonoPaymentHookSerializer

    def post(self, request):

        headers = request.headers.get("mono-webhook-secret")
        if not headers:
            return Response({"message": "mono-webhook-secret is required"})

        if not compare_digest(headers, settings.MONO_WEBHOOK_SECRET):
            return Response(
                {"message": "Incorrect token in mono-webhook-secret."},
                content_type="text/plain",
            )
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        event = serializer.validated_data.get("event")
        payload = serializer.validated_data.get("data")

        account_id = payload.get("account").get("_id")
        institution = payload.get("account").get("institution")
        account_no = payload.get("account").get("accountNumber")
        acct_type = payload.get("account").get("type")
        currency = payload.get("account").get("currency")
        name = payload.get("account").get("name")
        balance = payload.get("account").get("balance")
        created_at = payload.get("account").get("created_at")
        updated_at = payload.get("account").get("updated_at")
        try:
            account = UserLinkedBanks.objects.get(account_id=account_id)
        except UserLinkedBanks.DoesNotExist:
            return Response(
                {"message": "account with this id does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        MonoTopUpRecord.objects.create(
            event=event,
            user=account.user,
            institution=institution,
            account_id=account,
            user_name=name,
            account_no=account_no,
            acct_type=acct_type,
            currency=currency,
            balance=balance,
            created_at=created_at,
            updated_at=updated_at,
            payload=payload,
        )

        response = {"status": "received"}
        return Response(response, status=status.HTTP_200_OK)


class GetMonokeyExchangCode(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = GetMonokeyExchangCodeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = request.user
        code = serializer.validated_data.get("code")
        exchange = MonoApis.mono_key_exchange(code=code)
        account_id = exchange.get("id")
        if UserLinkedBanks.objects.filter(account_id=account_id).exists():
            return Response(
                {"message": "account with this ID already exist"},
                status=status.HTTP_208_ALREADY_REPORTED,
            )
        if account_id:
            account = UserLinkedBanks.objects.create(user=user, account_id=account_id)
            get_details = MonoApis.get_bank_details(id=account_id)

            if get_details.get("status") == "successful":
                data = get_details.get("data")
                account.name = data.get("name")
                account.account_type = data.get("type")
                account.account_number = data.get("account_number")
                account.balance = data.get("balance")
                account.bank_name = data.get("institution").get("name")
                account.bank_code = data.get("institution").get("bank_code")
                account.auth_method = data.get("institution").get("auth_method")
                account.has_details = True
                account.save()
            response = {"message": "mono key exchange successful"}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "Unexpected error"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class ResetTransactionPinView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = VerifyOtpSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serialized_data = serializer.data
        otp = serialized_data.get("otp")
        is_valid = OTP.verify_otp(recipient=request.user.email, otp=otp)
        # print(is_valid)
        if is_valid.get("status") is True:

            return Response(data=is_valid, status=200)
        else:
            return Response(data=is_valid, status=400)

    def get(self, request):
        user_email = request.user.email

        otp = OTP.get_otp(type="EMAIL", recipient=user_email, length=6, expiry_time=3)
        # print(otp, "\n\n")
        send_email.delay(
            recipient=user_email,
            subject="Reset Transaction Pin",
            template_dir="txn_otp.html",
            otp=otp,
        )
        return Response(data={"status": "Success", "data": "Otp Sent"}, status=200)


class SmsDisbursement(APIView):

    def put(self, request):
        webh_token = self.request.GET.get("Secret-Web-Token")
        print(webh_token, "\n")
        if not compare_digest(webh_token, settings.SECRET_WEB_TOKEN):
            response = {"message": "incorrect Secret-Web-Token"}
            return Response(response, status=status.HTTP_401_UNAUTHORIZED)

        serializer = DisbursementSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        requisition = validated_data.get("requisition")
        company = validated_data.get("company")
        wallet_type = validated_data.get("wallet_type")

        user = serializer.validated_data.get("user")

        account_instance = AccountSystem.objects.filter(
            company=company,
            account_type=(
                "SPEND_MGMT" if wallet_type == "CORPORATE" else "NON_CORP_SPEND_MGMT"
            ),
        ).first()

        requisition_payout = Requisition.requisition_disbursement(
            requisition=requisition,
            user=user,
            account=account_instance,
            wallet_type=wallet_type,
        )
        if isinstance(requisition_payout, dict):
            response = {"status": "error", "message": "Insufficient funds"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        else:
            response_data = {
                "status": "success",
                "message": "Requisition completed successfully",
            }
            return Response(response_data, status=status.HTTP_200_OK)


class SmsDeclineRequsition(APIView):
    def put(self, request):

        webh_token = self.request.GET.get("Secret-Web-Token")
        if not compare_digest(webh_token, settings.SECRET_WEB_TOKEN):
            response = {"message": "incorrect Secret-Web-Token"}
            return Response(response, status=status.HTTP_401_UNAUTHORIZED)

        serializer = DeclineReqSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        declined_reason = serializer.validated_data.get("declined_reason")

        user = serializer.validated_data.get("user")
        # print(user, "\n")
        requisition = serializer.validated_data.get("requisition")

        request_user_email = user.email
        team = requisition.member.team
        team_members = team.members.all()

        roles = ["SUB_ADMIN", "DISBURSER", "OWNER", "ADMIN"]
        member_exist_as_sub_or_disburser_on_team = team_members.filter(
            role__in=roles, email=request_user_email
        ).last()
        if member_exist_as_sub_or_disburser_on_team:
            if requisition.status == "PENDING":
                requisition.status = "DECLINED"
                requisition.declined_reason = declined_reason
                requisition.save()

                # SEND EMAIL NOTIFICATION TO REQUESTER ABOUT THE DECLINE

                response = {
                    "status": status.HTTP_200_OK,
                    "message": "Decline Successful",
                }
                return Response(response)
            else:
                return Response(
                    {"message": "Approved Requisition cannot be Declined"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"message": "You do not have permissions"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class TeamMemberRoleAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, *args, **kwargs):
        roles = [role[0] for role in UserRole.choices]
        return Response(data={"roles": roles}, status=status.HTTP_200_OK)


class IndustryCreateAPIView(APIView):

    def post(self, request, *args, **kwargs):
        serializer = IndustrySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        CompanyIndustry.add(**serializer.validated_data)
        return Response(data={"message": "success"}, status=status.HTTP_200_OK)


class CompanyMembersAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, *args, **kwargs):
        company_id = request.GET.get("company")
        search = request.GET.get("search")

        if not company_id:
            return Response(
                data={"message": "provide a valid company ID to view members."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(company_id):
            return Response(
                data={"message": "invalid ID type provided for company."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if Company.retrieve_company(id=company_id) is None:
            return Response(
                data={"message": "invalid company ID."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        members = (
            TeamMember.objects.filter(team__company=company_id)
            .order_by("member__id")
            .distinct("member")
        )
        if search:
            members = members.filter(email__icontains=search)
        serializer = TeamMemberSerializer(instance=members, many=True)
        return Response(data=serializer.data, status=status.HTTP_200_OK)


class GetTeamMembersListFilterTeam(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, *args, **kwargs):
        company_id = request.GET.get("company")
        search = request.GET.get("search")

        # Validate company_id
        if not company_id:
            return Response(
                {"message": "Provide a valid company ID to view members and invites."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(company_id):
            return Response(
                {"message": "Invalid ID type provided for company."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if the company exists
        try:
            company = Company.objects.get(id=company_id, is_deleted=False)
        except Company.DoesNotExist:
            return Response(
                {"message": "Invalid company ID."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Query team members
        members = (
            TeamMember.objects.filter(team__company=company)
            .select_related("team__company", "team__branch", "member")
            .annotate(
                member_email=F("email"),
                member_first_name=F("member__first_name"),
                member_last_name=F("member__last_name"),
                company_name=F("team__company__company_name"),
                role_status=Value(
                    "ACTIVE", output_field=CharField()
                ),  # Renamed annotation
                type=Value("MEMBER", output_field=CharField()),
            )
            .values(
                "member_first_name",
                "member_email",
                "member_last_name",
                "company_name",
                "role_status",
                "type",
            )
        )

        # Query team member invites
        invites = (
            TeamMemberInvite.objects.filter(company=company)
            .annotate(
                member_email=F("email"),  # Rename field for consistency
                member_first_name=F("first_name"),
                member_last_name=F("last_name"),
                company_name=F("company__company_name"),
                role_status=Value(
                    "INVITED", output_field=CharField()
                ),  # Renamed annotation
                type=Value("INVITE", output_field=CharField()),
            )
            .values(
                "member_first_name",
                "member_email",
                "member_last_name",
                "company_name",
                "role_status",
                "type",
            )
        )
        unique_emails = {}

        # List to hold the final result
        distinct_result = []

        # Iterate over members and add unique entries to distinct_result
        for member in members:
            email = member["member_email"]
            if email not in unique_emails:
                unique_emails[email] = True
                distinct_result.append(member)

        # Iterate over invites and add unique entries to distinct_result if not already present
        for invite in invites:
            email = invite["member_email"]
            if email not in unique_emails:
                unique_emails[email] = True
                distinct_result.append(invite)

        # Sort distinct_result by role_status: "ACTIVE" first, "INVITED" last
        distinct_result.sort(key=lambda x: x["role_status"] == "INVITED")

        # Apply search filter if provided
        if search:
            search_result = [
                entry
                for entry in distinct_result
                if entry["member_email"]
                and search.lower() in entry["member_email"].lower()
            ]
        else:
            search_result = distinct_result

        return Response(search_result, status=status.HTTP_200_OK)


class GetTeamMembersListFilterTeamPaginated(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    serializer_class = PaginatedTeamMembersSerializer
    pagination_class = CustomRecordPagination

    def get_queryset(self):
        company_id = self.request.query_params.get("company")
        search = self.request.query_params.get("search")

        # Validate company_id
        if not company_id:
            raise ValidationError({"message": "Provide a valid company ID"})

        if not is_valid_uuid(company_id):
            raise ValidationError({"message": "Invalid ID type provided for company."})

        # Check if the company exists
        try:
            company = Company.objects.get(id=company_id, is_deleted=False)
        except Company.DoesNotExist:
            raise ValidationError({"message": "Invalid company ID."})

        # Query team members
        members = (
            TeamMember.objects.filter(team__company=company)
            .select_related("team__company", "team__branch", "member")
            .annotate(
                member_email=F("email"),
                member_first_name=F("member__first_name"),
                member_last_name=F("member__last_name"),
                company_name=F("team__company__company_name"),
                role_status=Value(
                    "ACTIVE", output_field=CharField()
                ),  # Renamed annotation
                type=Value("MEMBER", output_field=CharField()),
            )
            .values(
                "member_first_name",
                "member_email",
                "member_last_name",
                "company_name",
                "role_status",
                "type",
            )
        )

        # Query team member invites
        invites = (
            TeamMemberInvite.objects.filter(company=company)
            .annotate(
                member_email=F("email"),  # Rename field for consistency
                member_first_name=F("first_name"),
                member_last_name=F("last_name"),
                company_name=F("company__company_name"),
                role_status=Value(
                    "INVITED", output_field=CharField()
                ),  # Renamed annotation
                type=Value("INVITE", output_field=CharField()),
            )
            .values(
                "member_first_name",
                "member_email",
                "member_last_name",
                "company_name",
                "role_status",
                "type",
            )
        )
        unique_emails = {}

        # List to hold the final result
        distinct_result = []

        # Iterate over members and add unique entries to distinct_result
        for member in members:
            email = member["member_email"]
            if email not in unique_emails:
                unique_emails[email] = True
                distinct_result.append(member)

        # Iterate over invites and add unique entries to distinct_result if not already present
        for invite in invites:
            email = invite["member_email"]
            if email not in unique_emails:
                unique_emails[email] = True
                distinct_result.append(invite)

        # Sort distinct_result by role_status: "ACTIVE" first, "INVITED" last
        distinct_result.sort(key=lambda x: x["role_status"] == "INVITED")

        # Apply search filter if provided
        if search:
            search_result = [
                entry
                for entry in distinct_result
                if entry["member_email"]
                and search.lower() in entry["member_email"].lower()
            ]
        else:
            search_result = distinct_result

        return search_result

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        if queryset:
            response_data = {
                "count": paginator.page.paginator.count,
                "next": paginator.get_next_link(),
                "previous": paginator.get_previous_link(),
                "company_name": queryset[0].get("company"),
                "results": serializer.data,
            }
            return Response(response_data, status=status.HTTP_200_OK)

        else:
            response_data = {
                "count": 0,
                "next": None,
                "previous": None,
                "company_name": "",
                "results": [],
            }
            return Response(response_data, status=status.HTTP_200_OK)

    # def get(self, request, *args, **kwargs):
    #     company_id = request.GET.get("company")
    #     search = request.GET.get("search")

    #     # Validate company_id
    #     if not company_id:
    #         return Response(
    #             {"message": "Provide a valid company ID to view members and invites."},
    #             status=status.HTTP_400_BAD_REQUEST,
    #         )
    #     if not is_valid_uuid(company_id):
    #         return Response(
    #             {"message": "Invalid ID type provided for company."},
    #             status=status.HTTP_400_BAD_REQUEST,
    #         )

    #     # Check if the company exists
    #     try:
    #         company = Company.objects.get(id=company_id, is_deleted=False)
    #     except Company.DoesNotExist:
    #         return Response(
    #             {"message": "Invalid company ID."},
    #             status=status.HTTP_400_BAD_REQUEST,
    #         )

    #     # Query team members
    #     members = TeamMember.objects.filter(
    #         team__company=company
    #     ).select_related('team__company', 'team__branch', 'member').annotate(
    #         member_email=F('email'),
    #         member_first_name=F('member__first_name'),
    #         member_last_name=F('member__last_name'),
    #         company_name=F('team__company__company_name'),
    #         role_status=Value('ACTIVE', output_field=CharField()),  # Renamed annotation
    #         type=Value('MEMBER', output_field=CharField())
    #     ).values(
    #         'member_first_name', 'member_email', 'member_last_name', 'company_name', 'role_status', 'type'
    #     )

    #     # Query team member invites
    #     invites = TeamMemberInvite.objects.filter(company=company).annotate(
    #         member_email=F('email'),  # Rename field for consistency
    #         member_first_name=F('first_name'),
    #         member_last_name=F('last_name'),
    #         company_name=F('company__company_name'),
    #         role_status=Value('INVITED', output_field=CharField()),  # Renamed annotation
    #         type=Value('INVITE', output_field=CharField()),
    #     ).values(
    #         'member_first_name', 'member_email', 'member_last_name', 'company_name', 'role_status', 'type'
    #     )
    #     unique_emails = {}

    #     # List to hold the final result
    #     distinct_result = []

    #     # Iterate over members and add unique entries to distinct_result
    #     for member in members:
    #         email = member['member_email']
    #         if email not in unique_emails:
    #             unique_emails[email] = True
    #             distinct_result.append(member)

    #     # Iterate over invites and add unique entries to distinct_result if not already present
    #     for invite in invites:
    #         email = invite['member_email']
    #         if email not in unique_emails:
    #             unique_emails[email] = True
    #             distinct_result.append(invite)

    #     # Sort distinct_result by role_status: "ACTIVE" first, "INVITED" last
    #     distinct_result.sort(key=lambda x: x['role_status'] == 'INVITED')

    #     # Apply search filter if provided
    #     if search:
    #         search_result = [
    #             entry for entry in distinct_result if entry['member_email'] and search.lower() in entry['member_email'].lower()
    #         ]
    #     else:
    #         search_result = distinct_result

    #     return Response(search_result, status=status.HTTP_200_OK)


class AddTeamMembersTeam(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        """Add Team Members"""
        company_uuid = request.query_params.get("company_id")
        if not company_uuid:
            return Response(
                {"message": "provide a valid company ID to send invites."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(company_uuid):
            return Response(
                {"message": "invalid ID type provided for company."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        company_ins = Company.objects.filter(id=company_uuid).first()
        if not company_ins:
            return Response(
                {"message": "invalid company"}, status=status.HTTP_400_BAD_REQUEST
            )
        serializer = MembersInviteSerializer(
            data=request.data, context={"company": company_ins}
        )
        serializer.is_valid(raise_exception=True)
        members = serializer.validated_data.get("members")
        team_data = Team.add_team_members(company_ins, members)

        return Response(
            {"message": "members invited successfully"}, status=status.HTTP_200_OK
        )


class TeamInviteApproval(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        """Add Team Members"""
        company_uuid = request.query_params.get("company_id")
        team_invite_id = request.query_params.get("team_invite_id")
        if not company_uuid:
            return Response(
                {"message": "provide a valid company ID"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not team_invite_id:
            return Response(
                {"message": "provide a valid team_invite_id."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(company_uuid):
            return Response(
                {"message": "invalid ID type provided for company."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not team_invite_id.isnumeric():
            return Response(
                {"message": "invalid ID type provided for team invite."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = TeamInviteApprovalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        toggle_type = serializer.validated_data.get("toggle_type")
        team_invite = TeamMemberInvite.objects.filter(
            id=team_invite_id,
            email=request.user.email,
            company__id=company_uuid,
            status="PENDING",
        ).first()
        if not team_invite:
            return Response(
                {"message": "team invite not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        if toggle_type == "APPROVE":
            team_invite.status = "APPROVED"
            team_invite.first_name = request.user.first_name
            team_invite.last_name = request.user.last_name
            team_invite.save()

        elif toggle_type == "REJECT":
            team_invite.status = "REJECTED"
            team_invite.delete()

        if toggle_type == "APPROVE":
            status_toggle = "approved"
        elif toggle_type == "REJECT":
            status_toggle = "rejected"

        return Response(
            {"message": f"team invite {status_toggle}"}, status=status.HTTP_200_OK
        )


class GetAllMembersInvite(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = TeamMemberInviteSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = ("id", "company_name",)
    # search_fields = ("company_name",)

    def get_queryset(self):
        request_user = self.request.user
        all_invites = TeamMemberInvite.objects.filter(
            email=request_user.email, status="PENDING"
        )
        return all_invites

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "total_invites": queryset.count() or 0,
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetAllFilterCompanies(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListOfCompanySerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "id",
        "company_name",
    )
    search_fields = ("company_name",)

    def get_queryset(self):
        request_user = self.request.user

        # Construct a combined Q object for filtering companies
        combined_query = (
            Q(user=request_user, is_active=True, is_deleted=False)
            | Q(teams__members__member=request_user, is_active=True, is_deleted=False)
            | Q(
                id__in=CompanyEmployeeList.objects.filter(
                    employee=request_user, is_deleted=False
                ).values_list("company__id", flat=True)
            )
        )

        # Get the distinct companies based on the combined query
        all_company = (
            Company.objects.filter(combined_query)
            .distinct()
            .prefetch_related("teams__members__member")
        )
        return all_company

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        request_user = request.user
        requisitions = Requisition.objects.filter(company__user=request_user)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "total_number_of_teams": Team.objects.filter(
                company__user=request_user, is_deleted=False
            ).aggregate(count=Count("id"))["count"],
            "total_number_of_members": TeamMember.objects.filter(
                team__company__user=request_user, is_deleted=False
            )
            .values("email")
            .distinct()
            .count(),
            "requisition_count": requisitions.aggregate(count=Count("id"))["count"],
            "approved_requisition_count": requisitions.filter(
                status="APPROVED"
            ).aggregate(count=Count("id"))["count"],
            "pending_requisition_count": requisitions.filter(
                status="PENDING"
            ).aggregate(count=Count("id"))["count"],
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class SetDefaultCompanyAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        serializer = SetDefaultCompanySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company = serializer.validated_data.get("company")
        request.user.default_company = company
        request.user.save()
        return Response(
            {"message": f"{company.company_name} set as default"},
            status=status.HTTP_200_OK,
        )


# START PROCUREMENT ENDPOINTS
class ProcurementRequisitionAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, pk=None):
        query = Q(team__members__member__in=[request.user]) | Q(
            team__company__user=request.user
        )

        if pk:
            pi = PurchaseIndent.objects.filter(query, id=pk)
            queryset = pi.last() if pi else None
            if not queryset:
                return Response(
                    {"message": "Procurement requisition not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            serializer = PurchaseIndentSerializerOut(
                queryset, context={"request": request}
            ).data
            return Response(
                {
                    "message": "Procurement requisition retried successfully",
                    "data": serializer,
                }
            )

        search = request.GET.get(
            "search"
        )  # Search with indent_no, supplier name, delivery_address
        approval_status = request.GET.get("status")
        supplier_id = request.GET.get("vendor_id")
        payment_type = request.GET.get("payment_condition")
        total_from = request.GET.get("price_from")
        total_to = request.GET.get("price_to")
        delivery_from = request.GET.get("delivery_date_from")
        delivery_to = request.GET.get("delivery_date_to")
        created_from = request.GET.get("date_from")
        created_to = request.GET.get("date_to")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")

        pi_req = PurchaseIndent.objects.filter(query)
        pending_requests = pi_req.filter(approval_status="PENDING_APPROVAL").count()
        approved_requests = pi_req.filter(
            approval_status__in=[
                "APPROVE_ONLY",
                "APPROVE_WITH_PO",
                "APPROVE_WITH_INVOICE",
            ]
        ).count()

        if company_id:
            query &= Q(team__company_id=company_id)
        if team_id:
            query &= Q(team_id=team_id)
        if search:
            query &= (
                Q(indent_no__iexact=search)
                | Q(delivery_address__icontains=search)
                | Q(supplier__name__icontains=search)
            )
        if approval_status:
            query &= Q(approval_status=approval_status)
        if supplier_id:
            query &= Q(supplier_id=supplier_id)
        if payment_type:
            query &= Q(payment_term=payment_type)
        if total_from and total_to:
            query &= Q(estimated_cost__range=[total_from, total_to])
        if delivery_from and delivery_to:
            query &= Q(expected_date_of_delivery__range=[delivery_from, delivery_to])
        if created_to and created_from:
            query &= Q(created_at__range=[created_from, created_to])

        queryset = self.paginate_queryset(
            PurchaseIndent.objects.filter(query).order_by("-created_at").distinct(),
            request,
        )
        serializer = PurchaseIndentSerializerOut(
            queryset, many=True, context={"request": request}
        ).data
        response = self.get_paginated_response(serializer).data
        return Response(
            {
                "message": "Success",
                "data": response,
                "total_pending": pending_requests,
                "total_approved": approved_requests,
            }
        )

    def post(self, request):
        serializer = PurchaseIndentSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(
            {
                "message": "Procurement requisition(s) created successfully",
                "data": response,
            },
            status=status.HTTP_201_CREATED,
        )

    def put(self, request, pk):
        query = Q(team__members__member__in=[request.user]) | Q(
            team__company__user=request.user
        )
        pi = PurchaseIndent.objects.filter(query, id=pk)
        instance = pi.last() if pi else None
        if not instance:
            return Response(
                {"message": "Procurement requisition not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        serializer = PurchaseIndentSerializerIn(
            instance=instance, data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "Procurement requisition edited", "data": response})

    def delete(self, request, pk):
        query = Q(team__members__member__in=[request.user]) | Q(
            team__company__user=request.user
        )
        pi = PurchaseIndent.objects.filter(query, id=pk)
        instance = pi.last() if pi else None
        if not instance:
            return Response(
                {"message": "Procurement requisition not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        if instance.approval_status != "PENDING_APPROVAL":
            return Response(
                {
                    "message": "Cannot edit this procurement requisition, as it has passed it review stage"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        instance.delete()
        return Response({"message": "Procurement requisition deleted"})


class ApprovePurchaseIndentAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = PurchaseIndentApproveSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": response})


class PurchaseIndentBulkUploadAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = PurchaseIndentBulkUploadSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": response})


class CustomPageNumberPagination(PageNumberPagination):
    page_size = 8
    page_size_query_param = "page_size"


class PurchaseInvoiceAPIList(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    serializer_class = ProcurementPurchaseInvoiceSerializerOut
    pagination_class = CustomPageNumberPagination

    def get(self, request, *args, **kwargs):
        user = self.request.user
        po_invoice_number = self.request.query_params.get("po_invoice_number")
        vendor_id = self.request.query_params.get("vendor")
        amount_from = self.request.query_params.get("amount_from")
        amount_to = self.request.query_params.get("amount_to")
        date_from = self.request.query_params.get("date_from")
        date_to = self.request.query_params.get("date_to")
        search = self.request.query_params.get("search")

        document_date = self.request.query_params.get("document_date")
        due_date = self.request.query_params.get("due_date")
        status_param = self.request.query_params.get("status")
        procurement_type = self.request.query_params.get("procurement_type")
        accepted = self.request.query_params.get("accepted")
        paid = self.request.query_params.get("paid")
        received_date = self.request.query_params.get("received_date")
        payment_status = self.request.query_params.get("payment_status")
        company_id = request.GET.get("company_id")
        branch_id = request.GET.get("branch_id")

        query = (
            Q(purchase_order__indent__team__members__member__in=[user])
            | Q(purchase_order__indent__team__company__user=user)
            | Q(purchase_order__indent__supplier__email__iexact=user.email)
        )

        if po_invoice_number:
            query &= Q(po_invoice_number__icontains=po_invoice_number)
        if vendor_id:
            query &= Q(vendor_id=vendor_id)
        if status_param:
            query &= Q(status=status_param)
        if date_from and date_to:
            query &= Q(created_at__range=[date_from, date_to])
        if amount_from and amount_to:
            query &= Q(total__range=[amount_from, amount_to])
        if document_date:
            year, month = map(int, document_date.split("-"))
            query &= Q(document_date__year=year, document_date__month=month)
        if due_date:
            year, month = map(int, due_date.split("-"))
            query &= Q(Due_date__year=year, Due_date__month=month)
        if procurement_type:
            query &= Q(procurement_type=procurement_type)
        if accepted is not None:
            query &= Q(accepted=accepted.lower() == "true")
        if paid is not None:
            query &= Q(paid=paid.lower() == "true")
        if received_date:
            year, month = map(int, received_date.split("-"))
            query &= Q(received_date__year=year, received_date__month=month)
        if payment_status:
            query &= Q(payment_status=payment_status)
        if company_id:
            query &= Q(procured_for__company_id=company_id)
        if branch_id:
            query &= Q(procured_for__branch__id=branch_id)

        if search:
            query &= (
                Q(po_invoice_number__iexact=search)
                | Q(vendor__name__iexact=search)
                | Q(purchase_order__order_no__iexact=search)
            )

        purchase_invoices = ProcurementPurchaseInvoice.objects.filter(query).order_by(
            "-document_date"
        )

        paginator = self.pagination_class()
        page = paginator.paginate_queryset(purchase_invoices, request)
        serializer = self.serializer_class(page, many=True)

        total_amount_paid = (
            ProcurementPurchaseInvoice.objects.filter(query).aggregate(
                total=Sum("amount_paid")
            )["total"]
            or 0.0
        )

        pending_total_amount = (
            ProcurementPurchaseInvoice.objects.filter(
                query, status="PENDING"
            ).aggregate(total=Sum("total"))["total"]
            or 0.0
        )
        pending_total_count = ProcurementPurchaseInvoice.objects.filter(
            query, status="PENDING"
        ).count()

        in_progress_total_amount = (
            ProcurementPurchaseInvoice.objects.filter(
                query, status="IN_PROGRESS"
            ).aggregate(total=Sum("total"))["total"]
            or 0.0
        )
        in_progress_total_count = ProcurementPurchaseInvoice.objects.filter(
            query, status="IN_PROGRESS"
        ).count()

        fullfiled_total_amount = (
            ProcurementPurchaseInvoice.objects.filter(
                query, status="FULLFILLED"
            ).aggregate(total=Sum("total"))["total"]
            or 0.0
        )
        fullfiled_total_count = ProcurementPurchaseInvoice.objects.filter(
            query, status="FULLFILLED"
        ).count()

        total_paid_amount = (
            ProcurementPurchaseInvoice.objects.filter(query, paid=True).aggregate(
                total=Sum("total")
            )["total"]
            or 0.0
        )
        total_paid_count = ProcurementPurchaseInvoice.objects.filter(
            query, paid=True
        ).count()

        total_not_paid_amount = (
            ProcurementPurchaseInvoice.objects.filter(query, paid=False).aggregate(
                total=Sum("total")
            )["total"]
            or 0.0
        )
        total_not_paid_count = ProcurementPurchaseInvoice.objects.filter(
            query, paid=False
        ).count()

        total_payment_pending_amount = (
            ProcurementPurchaseInvoice.objects.filter(
                query, payment_status="PENDING"
            ).aggregate(total=Sum("total"))["total"]
            or 0.0
        )
        total_payment_pending_count = ProcurementPurchaseInvoice.objects.filter(
            query, payment_status="PENDING"
        ).count()

        total_payment_paid_amount = (
            ProcurementPurchaseInvoice.objects.filter(
                query, payment_status="PAID"
            ).aggregate(total=Sum("total"))["total"]
            or 0.0
        )
        total_payment_paid_count = ProcurementPurchaseInvoice.objects.filter(
            query, payment_status="PAID"
        ).count()

        total_payment_fulfilled_amount = (
            ProcurementPurchaseInvoice.objects.filter(
                query, payment_status="FULLFILLED"
            ).aggregate(total=Sum("total"))["total"]
            or 0.0
        )
        total_payment_fulfilled_count = ProcurementPurchaseInvoice.objects.filter(
            query, payment_status="FULLFILLED"
        ).count()

        total_accepted_amount = (
            ProcurementPurchaseInvoice.objects.filter(query, accepted=True).aggregate(
                total=Sum("total")
            )["total"]
            or 0.0
        )
        total_not_accepted_count = ProcurementPurchaseInvoice.objects.filter(
            query, accepted=False
        ).count()

        total_amount = (
            ProcurementPurchaseInvoice.objects.filter(query).aggregate(
                total=Sum("total")
            )["total"]
            or 0.0
        )
        total_count = ProcurementPurchaseInvoice.objects.filter(query).count() or 0.0

        return paginator.get_paginated_response(
            {
                "message": "Data retrieved",
                "data": serializer.data,
                "pending_total_amount": pending_total_amount,
                "pending_total_count": pending_total_count,
                "in_progress_total_amount": in_progress_total_amount,
                "in_progress_total_count": in_progress_total_count,
                "fullfiled_total_amount": fullfiled_total_amount,
                "fullfiled_total_count": fullfiled_total_count,
                "total_paid_amount": total_paid_amount,
                "total_amount_paid": total_amount_paid,
                "total_paid_count": total_paid_count,
                "total_not_paid_amount": total_not_paid_amount,
                "total_not_paid_count": total_not_paid_count,
                "total_payment_pending_amount": total_payment_pending_amount,
                "total_payment_pending_count": total_payment_pending_count,
                "total_payment_paid_amount": total_payment_paid_amount,
                "total_payment_paid_count": total_payment_paid_count,
                "total_payment_fulfilled_amount": total_payment_fulfilled_amount,
                "total_payment_fulfilled_count": total_payment_fulfilled_count,
                "total_accepted_amount": total_accepted_amount,
                "total_not_accepted_count": total_not_accepted_count,
                "total_amount": total_amount,
                "total_count": total_count,
            }
        )

    def post(self, request, *args, **kwargs):
        serializer = ProcurementPurchaseInvoiceSerializerIn(data=request.data)
        if serializer.is_valid():
            instance = serializer.save()
            serializer_out = ProcurementPurchaseInvoiceSerializerOut(instance)
            return Response(
                {
                    "message": "Purchase invoice created",
                    "data": serializer_out.data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


import base64


class ProcurementPurchaseInoviceImagePreview(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request, id):
        print(id)
        user = request.user
        try:
            purchase_invoice = ProcurementPurchaseInvoice.objects.get(pk=id)
            serializer = ProcurementPurchaseInvoiceSerializerModify(
                purchase_invoice, data=request.data
            )

            if serializer.is_valid():
                if "hard_copy_upload" in request.FILES:
                    file = request.FILES["hard_copy_upload"]
                    file_content = file.read()
                    encoded_file = base64.b64encode(file_content).decode("utf-8")
                    file_name = file.name
                    file_size = file.size / 1024

                    try:
                        structured_data = serializer.extract_text_and_structure(file)

                        total = float(structured_data["invoice_details"].get("total", 0))

                        items_count = len(structured_data["invoice_details"].get("items", []))

                        items = structured_data["invoice_details"].get("items", [])

                        flags: dict = {}

                        if purchase_invoice.no_of_items != items_count:
                            flags["no_of_items"] = (
                                "Number of items in invoice does not match."
                            )
                        if purchase_invoice.total != total:
                            flags["total"] = "Total amount in invoice does not match."


                        # serializer.save_extracted_data(purchase_invoice, structured_data)

                        serializer_out = ProcurementPurchaseInvoiceSerializerOut(
                            purchase_invoice
                        ).data
                        item_names = serializer_out.get("items", [])

                        item_name_list = sorted(
                            [item.get("name") for item in item_names]  #### For items in the DB
                        )

                        struct_item_desc = sorted(
                            [
                                item.get("item_description") for item in items #### for the OCR
                            ]
                        )

                        matched_items = set(item_name_list) & set(struct_item_desc)
                        unmatched_items = set(item_name_list) ^ set(struct_item_desc)

                        if unmatched_items:
                            return Response(
                                {
                                    "message": "Mismatch found in items",
                                    "matched_items": list(matched_items),
                                    "unmatched_items": list(unmatched_items),
                                    "error": "Some items in the invoice do not match the extracted data.",
                                },
                                status=status.HTTP_404_NOT_FOUND,
                            )

                        return Response(
                            {
                                "message": "Image previewed",
                                "data": serializer_out,
                                "structured_data": structured_data["invoice_details"],
                                "file_name": file_name,
                                "file_size": file_size,
                                "file_content_base64": encoded_file,
                                "matched_items": list(matched_items),
                            },
                            status=status.HTTP_202_ACCEPTED,
                        )

                    except Exception as e:
                        return Response(
                            {
                                "message": "Error processing uploaded file",
                                "error": str(e),
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                return Response(
                    {
                        "message": "Image not found in the request",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except ProcurementPurchaseInvoice.DoesNotExist:
            return Response(
                {"message": "Purchase invoice not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            return Response(
                {"message": "An unexpected error occurred", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PurchaseInvoiceAPIDetail(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    parser_classes = [MultiPartParser, FormParser, FileUploadParser]

    def get(self, request, id):

        user = self.request.user

        query = (
            Q(purchase_order__indent__team__members__member__in=[user])
            | Q(purchase_order__indent__team__company__user=user)
            | Q(purchase_order__indent__supplier__email__iexact=user.email)
        )
        try:
            purchase_invoice = ProcurementPurchaseInvoice.objects.get(pk=id) or None
            serializer = ProcurementPurchaseInvoiceSerializerOut(purchase_invoice)
            invoice_files = ProcurementPurchaseInvoiceUploadedFIles.objects.filter(
                invoice_no=purchase_invoice.po_invoice_number
            )
            notes = Notes.objects.filter(invoice_no=purchase_invoice.po_invoice_number)
            invoice_files_serializer = (
                ProcurementPurchaseInvoiceUploadedFilesSerializer(
                    invoice_files, many=True
                ).data
                or None
            )
            notes_serializer = NotesSerializer(notes, many=True).data or None
            flags = (
                Flags.objects.filter(
                    invoice_no=purchase_invoice.po_invoice_number
                ).last()
                or None
            )
            flags_serializer = FlagsSerializer(flags).data
            return Response(
                {
                    "message": "Retrieved",
                    "data": serializer.data,
                    "invoice_files": invoice_files_serializer,
                    "notes": notes_serializer,
                    "flags": flags_serializer,
                },
                status=status.HTTP_200_OK,
            )
        except ProcurementPurchaseInvoice.DoesNotExist:
            return Response({"error": "No invoice found"})

    def put(self, request, id):
        user = self.request.user
        try:
            purchase_invoice = ProcurementPurchaseInvoice.objects.get(pk=id)
            serializer = ProcurementPurchaseInvoiceSerializerModify(
                purchase_invoice, data=request.data
            )

            if serializer.is_valid():
                note = serializer.validated_data.get("note", None)
                if note:
                    created_note = Notes.objects.create(
                        invoice_no=purchase_invoice.po_invoice_number,
                        notes=note,
                        created_by=user,
                    )
                    note_serializer = NotesSerializer(created_note).data
                if "hard_copy_upload" in request.FILES:
                    file = serializer.validated_data.get("hard_copy_upload")
                    file_content = file.read()
                    encoded_file = base64.b64encode(file_content).decode("utf-8")
                    file_name = file.name
                    file_size = file.size / 1024

                    try:
                        structured_data = serializer.extract_text_and_structure(file)
                        
                        total = float(structured_data["invoice_details"].get("total", 0))

                        items_count = len(structured_data["invoice_details"].get("items", []))

                        items = structured_data["invoice_details"].get("items", [])

                        flags: dict = {}

                        if purchase_invoice.no_of_items != items_count:
                            flags["no_of_items"] = (
                                "Number of items in invoice does not match."
                            )
                        if purchase_invoice.total != total:
                            flags["total"] = "Total amount in invoice does not match."


                        # serializer.save_extracted_data(purchase_invoice, structured_data)

                        serializer_out = ProcurementPurchaseInvoiceSerializerOut(
                            purchase_invoice
                        ).data
                        item_names = serializer_out.get("items", [])

                        item_name_list = sorted(
                            [item.get("name") for item in item_names]  #### For items in the DB
                        )

                        struct_item_desc = sorted(
                            [
                                item.get("item_description") for item in items #### for the OCR
                            ]
                        )

                        matched_items = set(item_name_list) & set(struct_item_desc)
                        unmatched_items = set(item_name_list) ^ set(struct_item_desc)

                        if unmatched_items:
                            return Response(
                                {
                                    "message": "Mismatch found in items",
                                    "matched_items": list(matched_items),
                                    "unmatched_items": list(unmatched_items),
                                    "error": "Some items in the invoice do not match the extracted data.",
                                },
                                status=status.HTTP_404_NOT_FOUND,
                            )
                        invoice_files = (
                            ProcurementPurchaseInvoiceUploadedFIles.objects.create(
                                invoice_no=purchase_invoice.po_invoice_number, file=file
                            )
                        )

                        stored_flags = Flags.objects.create(
                            invoice_no=purchase_invoice.po_invoice_number,
                            no_of_items_flag=flags.get("no_of_items", ""),
                            total_flag=flags.get("total", ""),
                        )

                        invoice_files_serializer = (
                            ProcurementPurchaseInvoiceUploadedFilesSerializer(
                                invoice_files
                            ).data
                        )

                        return Response(
                            {
                                "message": "Invoice successfully processed and modified",
                                "data": serializer_out,
                                "structured_data": structured_data["invoice_details"],
                                "file_name": file_name,
                                "file_size": file_size,
                                "flags": flags,
                                "matched_items": list(matched_items),
                            },
                            status=status.HTTP_202_ACCEPTED,
                        )

                    except Exception as e:
                        return Response(
                            {
                                "message": "Error processing uploaded file",
                                "error": str(e),
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                serializer.save()
                return Response(
                    {
                        "message": "Invoice successfully modified",
                        "data": serializer.data,
                    },
                    status=status.HTTP_202_ACCEPTED,
                )

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except ProcurementPurchaseInvoice.DoesNotExist:
            return Response(
                {"message": "Purchase invoice not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def delete(self, request, id):
        user = self.request.user
        try:
            purchase_invoice = ProcurementPurchaseInvoice.objects.get(pk=id).first()

            if purchase_invoice:
                purchase_invoice.delete()
                return Response(status=status.HTTP_204_NO_CONTENT)
            return Response(
                {"message": "Purchase invoice not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except ProcurementPurchaseInvoice.DoesNotExist:
            return Response({"message": "file is unavailable"})


class ProcurementInvoiceConfirmAmountPaidAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request, id):
        user = self.request.user

        serializer = ConfirmProcurementPurchaseAmountPaidSerializer(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            amount_paid = serializer.validated_data.get("amount_paid", 0.0)

            try:
                invoice = ProcurementPurchaseInvoice.objects.get(pk=id)
                if invoice.total > amount_paid > 0:
                    invoice.payment_status = (
                        ProcurementPurchaseInvoice.PaymentTermsStatus.PARTIAL_UPFRONT
                    )
                    invoice.amount_paid += amount_paid
                    invoice.save()
                elif invoice.total == amount_paid:
                    invoice.payment_status = (
                        ProcurementPurchaseInvoice.PaymentTermsStatus.FULL_UPFRONT
                    )
                    invoice.paid = True
                    invoice.amount_paid += amount_paid
                    invoice.save()
                elif invoice.total == 0.0:
                    invoice.payment_status = (
                        ProcurementPurchaseInvoice.PaymentTermsStatus.AFTER_DELIVERY
                    )
                    invoice.amount_paid += amount_paid
                    invoice.save()

                invoice_serializer = ProcurementPurchaseInvoiceSerializerOut(
                    invoice
                ).data

                return Response(
                    {
                        "message": "Status updated",
                        "data": serializer.data,
                        "invoice": invoice_serializer,
                    },
                    status=status.HTTP_200_OK,
                )
            except ProcurementPurchaseInvoice.DoesNotExist:
                return Response(
                    {"message": serializer.errors}, status=status.HTTP_404_NOT_FOUND
                )


class CompanyPaymentWalletAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, invoice_no):
        try:
            invoice_instance = (
                ProcurementPurchaseInvoice.objects.filter(
                    po_invoice_number=invoice_no
                ).last()
                or None
            )
            company = (
                Company.objects.filter(user=invoice_instance.pi_requester).last()
                or None
            )
            account = AccountSystem.objects.filter(company=company) or None

            serializer = CompanyPaymentWalletSeriaizer(account, many=True)
            return Response(
                {"message": "Acccout retreived", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except (AccountSystem.DoesNotExist, Company.DoesNotExist):
            return Response(
                {"data": "Objects does not exist"}, status=status.HTTP_404_NOT_FOUND
            )


class EnterAmountAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request, wallet_id, invoice_no):
        user = self.request.user
        serializer = EnterAmountToPaySerialzer(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            entered_amount = serializer.validated_data.get("enter_amount", 0.0)
            request.session["entered_amount"] = entered_amount

        try:
            account = AccountSystem.objects.get(pk=wallet_id)
            invoice = (
                ProcurementPurchaseInvoice.objects.filter(
                    po_invoice_number=invoice_no
                ).last()
                or None
            )
            vendor_company = (
                invoice.vendor.company.company_name if invoice.vendor else None
            )
            return Response(
                {
                    "pay_from": account.account_name,
                    "transfer_from": vendor_company,
                    "entered_amount": entered_amount,
                },
                status=status.HTTP_200_OK,
            )
        except (AccountSystem.DoesNotExist, ProcurementPurchaseInvoice.DoesNotExist):
            account = None
            vendor_company = None

        return Response(
            {"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST
        )


class MakepaymentAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request, wallet_id, invoice_no):
        transfer_funds = Transaction
        user = self.request.user
        # amount = request.session.get("entered_amount", False)
        # print(amount)
        # if not amount:
        #     return Response({"message": "Amount not found in session"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = DisbursementPinSerializer(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            pin = serializer.validated_data.get("pin")
            amount = serializer.validated_data.get("enter_amount")

            if not user.requisition_transaction_pin:
                return Response(
                    {
                        "message": "Transaction PIN is not set. Please set your PIN and try again."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not check_password(pin, user.requisition_transaction_pin):
                return Response(
                    {"message": "Incorrect pin"}, status=status.HTTP_400_BAD_REQUEST
                )

            try:
                account = AccountSystem.objects.get(pk=wallet_id)
                invoice = ProcurementPurchaseInvoice.objects.get(
                    po_invoice_number=invoice_no
                )
                vendor_company = (
                    invoice.vendor.company.company_name if invoice.vendor else None
                )

                escrow_balance = (
                    invoice.allocation_balance.balance
                    if invoice.allocation_balance
                    else None
                )
                if escrow_balance is None or escrow_balance <= amount:
                    return Response(
                        {"message": "Insufficient balance. Add funds and try again"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                vendor = invoice.vendor
                if not vendor:
                    return Response(
                        {"message": "Vendor details are missing"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                transfer_funds = transfer_funds.vfd_funds_transfer(
                    bank_code=vendor.bank_code,
                    bank_name=vendor.bank_account_name,
                    account_name=vendor.bank_account_name,
                    account_number=vendor.bank_account_number,
                    amount=amount,
                    account=account,
                    user=user,
                    narration=f"Invoice: {invoice_no} payment",
                )
                print(transfer_funds)

                if isinstance(transfer_funds, str):
                    invoice.allocation_balance.balance -= amount
                    invoice.allocation_balance.save()

                if invoice.total > invoice.amount_paid > 0:
                    invoice.payment_terms = (
                        ProcurementPurchaseInvoice.PaymentTermsStatus.PARTIAL_UPFRONT
                    )
                elif invoice.total == invoice.amount_paid:
                    invoice.payment_terms = (
                        ProcurementPurchaseInvoice.PaymentTermsStatus.FULL_UPFRONT
                    )
                elif invoice.total == 0.0:
                    invoice.payment_terms = (
                        ProcurementPurchaseInvoice.PaymentTermsStatus.AFTER_DELIVERY
                    )

                invoice.accepted = True
                invoice.paid = True
                invoice.save()

                return Response({"message": "Payment successful"})

            except AccountSystem.DoesNotExist:
                return Response(
                    {"message": "Wallet not found"}, status=status.HTTP_404_NOT_FOUND
                )
            except ProcurementPurchaseInvoice.DoesNotExist:
                return Response(
                    {"message": "Invoice not found"}, status=status.HTTP_404_NOT_FOUND
                )
        else:
            return Response(serializer.errors, status=status.HTTP_404_NOT_FOUND)


class BulkProcurementAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = BulkProcurementSerailizerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": response}, status=status.HTTP_201_CREATED)


class PurchaseOrderAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, pk=None):
        query = (
            Q(indent__team__members__member__in=[request.user])
            | Q(indent__team__company__user=request.user)
            | Q(indent__supplier__email__iexact=request.user.email)
        )

        if pk:
            po = ProcurementPurchaseOrder.objects.filter(query, id=pk)
            queryset = po.last() if po else None
            if not queryset:
                return Response(
                    {"message": "Purchase Order not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            # Getting the previous and last item
            prev_id, next_id = get_previous_next_item(
                model_to_check=ProcurementPurchaseOrder,
                identifier=queryset.id,
                query_item=query,
            )
            serializer = ProcurementPurchaseOrderSerializerOut(queryset).data
            return Response(
                {
                    "message": "Purchase Order retried successfully",
                    "data": serializer,
                    "previous": prev_id,
                    "next": next_id,
                }
            )

        order_status = request.GET.get("status")
        search = request.GET.get("search")
        supplier_id = request.GET.get("supplier_id")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        analysis = request.GET.get("analysis")
        user_id = str(request.user.id)

        # Dashboard Analysis
        analysis_result = dict()
        if (
            analysis == "yes"
            and not Supplier.objects.filter(email__iexact=request.user.email).exists()
        ):
            # Fetch from cache
            if cache.get(f"purchase_order_data_{user_id}"):
                analysis_result = cache.get(f"purchase_order_data_{user_id}")
            else:
                analysis_result = get_purchase_order_data(query, user_id)

        if company_id:
            query &= Q(indent__team__company_id=company_id)
        if team_id:
            query &= Q(indent__team_id=team_id)

        if supplier_id:
            query &= Q(indent__supplier_id=supplier_id)

        if order_status:
            query &= (
                Q(status=order_status)
                | Q(indent__approval_status=order_status)
                | Q(indent__payment_term=order_status)
                | Q(delivery_status=order_status)
            )
        if search:
            query &= Q(indent__supplier__name__icontains=search)
        if date_from and date_to:
            query &= Q(created_at__range=[date_from, date_to])

        queryset = self.paginate_queryset(
            ProcurementPurchaseOrder.objects.filter(query)
            .order_by("-created_at")
            .distinct(),
            request,
        )
        serializer = ProcurementPurchaseOrderSerializerOut(queryset, many=True).data
        response = self.get_paginated_response(serializer).data
        return Response(
            {
                "message": "Success",
                "data": response,
                "dashboard_analysis": analysis_result,
            }
        )

    def put(self, request, pk):
        # Cancel a particular PO
        query = Q(indent__team__members__member__in=[request.user]) | Q(
            indent__team__company__user=request.user
        )
        po = ProcurementPurchaseOrder.objects.filter(
            query, id=pk, status="UNDER_REVIEW"
        )
        instance = po.last() if po else None
        if not instance:
            return Response(
                {"message": "Procurement purchase order not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        instance.status = "CANCELLED"
        instance.save()
        serializer = ProcurementPurchaseOrderSerializerOut(instance).data
        return Response(
            {"message": "Procurement purchase order cancelled", "data": serializer}
        )


class CompanySupplierAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = AddSupplierSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class CompanySupplierListAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, company_id, pk=None):
        search = request.GET.get("search")
        active_filter = request.GET.get("active")
        approved_filter = request.GET.get("approved")
        category_name = request.GET.get("categories")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")

        expecting_delivery = dict()
        pending_payment = dict()

        if not (
            TeamMember.objects.filter(
                member=request.user, team__company_id=company_id
            ).exists()
            or Company.objects.filter(user=request.user).exists()
        ):
            return Response(
                {"message": "You are not permitted to perform this action"},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        query = Q(company_id=company_id)
        invoice_filter = Q(purchase_order__indent__team__company_id=company_id)
        po_filter = Q(
            indent__team__company_id=company_id,
            delivery_status="NOT_DELIVERED",
            status="ACCEPTED",
        )

        if pk:
            suppliers = Supplier.objects.filter(query, id=pk)
            queryset = suppliers.last() if suppliers else None
            if not queryset:
                return Response(
                    {"message": "Supplier detail not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            invoice = ProcurementPurchaseInvoice.objects.filter(
                invoice_filter, purchase_order__indent__supplier=queryset
            )
            po = ProcurementPurchaseOrder.objects.filter(
                po_filter, indent__supplier=queryset
            )
            expecting_delivery["count"] = po.count()
            expecting_delivery["amount"] = (
                po.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
            )
            pending_payment["count"] = invoice.count()
            pending_payment["amount"] = (
                invoice.aggregate(Sum("total"))["total__sum"] or 0
            )
            prev_id, next_id = get_previous_next_item(
                model_to_check=Supplier, identifier=queryset.id, query_item=query
            )
            serializer = SupplierSerializerOut(
                queryset, context={"request": request}
            ).data
            return Response(
                {
                    "message": "Supplier retried successfully",
                    "data": serializer,
                    "total_expected_delivery": expecting_delivery,
                    "total_pending_payment": pending_payment,
                    "previous": prev_id,
                    "next": next_id,
                }
            )

        if search:
            query &= (
                Q(name__icontains=search)
                | Q(email__icontains=search)
                | Q(phone_number__icontains=search)
                | Q(registration_no__icontains=search)
            )
        if active_filter == "true":
            query &= Q(is_active=True)
        if approved_filter == "true":
            query &= Q(is_approved=True)
        if active_filter == "false":
            query &= Q(is_active=False)
        if approved_filter == "false":
            query &= Q(is_approved=False)
        if date_from and date_to:
            query &= Q(created_at__range=[date_from, date_to])
        if category_name:
            query &= Q(category__name__iexact=category_name)

        invoice = ProcurementPurchaseInvoice.objects.filter(invoice_filter)
        po = ProcurementPurchaseOrder.objects.filter(po_filter)
        expecting_delivery["count"] = po.count()
        expecting_delivery["amount"] = (
            po.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
        )
        pending_payment["count"] = invoice.count()
        pending_payment["amount"] = invoice.aggregate(Sum("total"))["total__sum"] or 0

        queryset = self.paginate_queryset(
            Supplier.objects.filter(query).order_by("-created_at").distinct(), request
        )
        serializer = SupplierSerializerOut(
            queryset, many=True, context={"request": request}
        ).data
        response = self.get_paginated_response(serializer).data
        return Response(
            {
                "message": "Success",
                "data": response,
                "total_expected_delivery": expecting_delivery,
                "total_pending_payment": pending_payment,
            }
        )


class SupplierOnboardingAPIView(APIView):
    permission_classes = []

    def post(self, request):
        serializer = SupplierCompleteOnboardingSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class SupplierApprovalAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = ApproveSupplierSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "Supplier approved", "data": response})


class SupplierLoginAPIView(APIView):
    permission_classes = []

    def post(self, request):
        serializer = SupplierLoginSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        user = serializer.save()

        supplier = is_supplier_check(user.email)
        supplier_id = str(supplier.id)

        if cache.get(f"supplier_dashboard_data_{supplier_id}"):
            supplier_dashboard_data = cache.get(
                f"supplier_dashboard_data_{supplier_id}"
            )
        else:
            supplier_dashboard_data = get_supplier_dashboard_data(supplier_id)
        return Response(
            {
                "message": "Login successful",
                "data": SupplierSerializerOut(
                    supplier, context={"request": request}
                ).data,
                "access_token": f"{AccessToken.for_user(user)}",
                "dashboard_data": supplier_dashboard_data,
                "wallet_balance": 0,
            }
        )


class SupplierApproveDeclinePurchaseOrderAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = SupplierApproveDeclinePurchaseOrderSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class PurchaseOrderCommentAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = PurchaseOrderCommentSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "Comment added successfully", "data": response})

    def get(self, request):
        purchase_order_id = request.GET.get("purchase_order_id")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        if not purchase_order_id:
            return Response(
                {"message": "Purchase Order ID is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        query = (
            Q(indent__team__members__member__in=[request.user])
            | Q(indent__team__company__user=request.user)
            | Q(indent__supplier__email__iexact=request.user.email)
        )
        if company_id:
            query &= Q(indent__team__company_id=company_id)
        if team_id:
            query &= Q(indent__team_id=team_id)
        purchase_order = ProcurementPurchaseOrder.objects.filter(
            query, id=purchase_order_id
        )
        if not purchase_order.exists():
            return Response(
                {"message": "Purchase Order not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        po = purchase_order.last()

        queryset = self.paginate_queryset(
            PurchaseOrderComment.objects.filter(
                purchase_order=po, parent_comment__isnull=True
            )
            .order_by("-created_at")
            .distinct(),
            request,
        )
        serializer = PurchaseOrderCommentSerializerOut(
            queryset, many=True, context={"request": request}
        ).data
        response = self.get_paginated_response(serializer).data
        return Response({"message": "Success", "data": response})

    def delete(self, request, pk):
        try:
            instance = PurchaseOrderComment.objects.get(sender=request.user, id=pk)
        except PurchaseOrderComment.DoesNotExist:
            return Response(
                {
                    "message": "Unable to delete message. Please ensure you are the sender"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Delete Children Comments
        child_comments = PurchaseOrderComment.objects.filter(parent_comment=instance)
        if child_comments.exists():
            child_comments.delete()
        instance.delete()
        return Response({"message": "Comment deleted successfully"})

    def put(self, request, pk):
        try:
            instance = PurchaseOrderComment.objects.get(sender=request.user, id=pk)
        except PurchaseOrderComment.DoesNotExist:
            return Response(
                {
                    "message": "Only the sender of this message is permitted to perform edit"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = PurchaseOrderCommentSerializerIn(
            instance=instance, data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "Purchase Order Comment edited", "data": response})


class CompanyOnboardingViaJotFormWebhookApiView(APIView):
    permission_classes = []
    authentication_classes = []

    def post(self, request):
        JotFormDataSync.process_datasync(request.data)
        return Response(data={"message": "data received"}, status=status.HTTP_200_OK)


class ConfirmDeliveryAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = ConfirmDeliverySerializerIn(
            data=request.data, context={"request": request}
        )
        # serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        if serializer.is_valid():
            response = serializer.save()
            api_status = status.HTTP_200_OK
        else:
            response = serializer.errors
            api_status = status.HTTP_400_BAD_REQUEST
        return Response(response, status=api_status)


class BankNameResolveAPIView(APIView):
    permission_classes = []

    def get(self, request):
        cache_key = "banks"
        if cache.get(cache_key):
            response = cache.get(cache_key)
        else:
            from core.services import Paystack

            paystack_manager = Paystack()
            response = paystack_manager.fetch_bank_list()
            cache.set(key=cache_key, value=response, timeout=86400)
        return Response(response)

    def post(self, request):
        serializer = NameEnquirySerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class SupplierApproveDeclineReturnsAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = AcceptDeclineReturnSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class PayboxActivityLogsViaUssdApiview(APIView):
    permission_classes = []
    authentication_classes = []

    def post(self, request):

        AdGeneneratedUsers.create_record(**request.data)
        return Response(data={"message": "received successful"})

    def patch(self, request):
        AdGeneneratedUsers.update_record(**request.data)
        return Response(data={"message": "received successful"})


class JotFormForPaybox360UssdAdApiView(APIView):
    permission_classes = []
    authentication_classes = []

    def post(self, request):
        JotFormDataSync.process_datasync_for_ussd_campaign(request.data)
        return Response(data={"message": "data received"}, status=status.HTTP_200_OK)


class ProcurementCreditNoteAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, pk=None):
        query = (
            Q(
                procurement_return__purchase_order__indent__team__members__member__in=[
                    request.user
                ]
            )
            | Q(
                procurement_return__purchase_order__indent__team__company__user=request.user
            )
            | Q(
                procurement_return__purchase_order__indent__supplier__email__iexact=request.user.email
            )
        )

        if pk:
            credit_note = ProcurementCreditNote.objects.filter(query, id=pk)
            queryset = credit_note.last() if credit_note else None
            if not queryset:
                return Response(
                    {"message": "Credit note not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            # Getting the previous and last item
            prev_id, next_id = get_previous_next_item(
                model_to_check=ProcurementCreditNote,
                identifier=queryset.id,
                query_item=query,
            )
            serializer = ProcurementCreditNoteSerializerOut(queryset).data
            return Response(
                {
                    "message": "Credit note retried successfully",
                    "data": serializer,
                    "previous": prev_id,
                    "next": next_id,
                }
            )

        search = request.GET.get("search")
        supplier_id = request.GET.get("supplier_id")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")

        if company_id:
            query &= Q(
                procurement_return__purchase_order__indent__team__company_id=company_id
            )
        if team_id:
            query &= Q(procurement_return__purchase_order__indent__team_id=team_id)
        if supplier_id:
            query &= Q(
                procurement_return__purchase_order__indent__supplier_id=supplier_id
            )
        if search:
            query &= Q(
                procurement_return__purchase_order__indent__supplier__name__icontains=search
            )
        if date_from and date_to:
            query &= Q(created_at__range=[date_from, date_to])

        queryset = self.paginate_queryset(
            ProcurementCreditNote.objects.filter(query)
            .order_by("-created_at")
            .distinct(),
            request,
        )
        serializer = ProcurementCreditNoteSerializerOut(queryset, many=True).data
        response = self.get_paginated_response(serializer).data
        return Response({"message": "Success", "data": response})


class UpdateSupplierProfileAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        supplier = is_supplier_check(request.user.email)
        serializer = UpdateSupplierProfileSerializerIn(
            instance=supplier, data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class RequestProcurementOTPAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = RequestProcurementOTPSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class SupplierOrderFulfilmentAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = OrderFulfilmentSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "Order fulfilled successfully", "data": response})


class UpdateUserWhatsappConversationApiView(APIView):
    permission_classes = []
    authentication_classes = []

    def post(self, request):
        AdGeneneratedUsers.update_whatsapp_conversation(**request.data)
        return Response(data={"message": "received successful"})


class AssetAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = AssetSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "New Asset added successfully", "data": response})

    def get(self, request, pk=None):
        query = Q(team__members__member__in=[request.user]) | Q(team__company__user=request.user)

        if pk:
            asset = Asset.objects.filter(query, id=pk)
            queryset = asset.last() if asset else None
            if not queryset:
                return Response({"message": "Asset not found"}, status=status.HTTP_404_NOT_FOUND)
            return Response(AssetSerializerOut(queryset, context={"monthly": True, "yearly": True, "request": request}).data)

        asset_no = request.GET.get("asset_no")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        supplier_id = request.GET.get("supplier_id")
        purchase_cost_from = request.GET.get("purchase_cost_from")
        purchase_cost_to = request.GET.get("purchase_cost_to")
        purchase_date_from = request.GET.get("purchase_date_from")
        purchase_date_to = request.GET.get("purchase_date_to")
        depreciation_method = request.GET.get("depreciation_method")
        residual_value_from = request.GET.get("residual_value_from")
        residual_value_to = request.GET.get("residual_value_to")
        asset_category = request.GET.get("asset_category")
        search = request.GET.get("search")
        date_created_to = request.GET.get("date_created_to")
        date_created_from = request.GET.get("date_created_from")

        if date_created_from and date_created_to:
            query &= Q(created_at__range=[date_created_from, date_created_to])
        if asset_category:
            query &= Q(asset_category=asset_category)
        if search:
            query &= Q(supplier__name__icontains=search) | Q(product__name__icontains=search)
        if company_id:
            query &= Q(team__company_id=company_id)
        if team_id:
            query &= Q(team_id=team_id)
        if asset_no:
            query &= Q(asset_no__icontains=asset_no)
        if supplier_id:
            query &= Q(supplier_id=supplier_id)
        if purchase_cost_from and purchase_cost_to:
            query &= Q(purchase_cost__range=[purchase_cost_from, purchase_cost_to])
        if purchase_date_from and purchase_date_to:
            query &= Q(purchase_date__range=[purchase_date_from, purchase_date_to])
        if depreciation_method:
            query &= Q(depreciation_method=depreciation_method)
        if residual_value_from and residual_value_to:
            query &= Q(residual_value__range=[residual_value_from, residual_value_to])

        queryset = self.paginate_queryset(Asset.objects.filter(query).order_by("-created_at").distinct(), request)
        serializer = AssetSerializerOut(queryset, many=True, context={"request": request}).data
        response = self.get_paginated_response(serializer).data
        return Response({"message": "Success", "data": response})

    def put(self, request, pk):
        query = Q(team__members__member__in=[request.user]) | Q(team__company__user=request.user)
        asset = Asset.objects.filter(query, id=pk)
        instance = asset.last() if asset else None
        if not instance:
            return Response({"message": "Asset not found"}, status=status.HTTP_404_NOT_FOUND)
        serializer = AssetSerializerIn(instance=instance, data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "Asset updated successfully", "data": response})


class AssetImageUploadAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = AssetImageSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "Asset image uploaded", "data": response})


class SupplierChangePasswordAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = SupplierChangePasswordSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class SupplierResetPasswordAPIView(APIView):
    permission_classes = []

    def get(self, request):
        email = request.GET.get("email")

        if not email:
            return Response({"message": "Email is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Generate random OTP
        otp = get_random_string(length=6, allowed_chars="0123456789")
        hashed_token = make_password(otp)
        data = dict()

        try:
            User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({"message": "User with this email is not found"}, status=status.HTTP_404_NOT_FOUND)

        supplier = is_supplier_check(email)
        supplier.otp = hashed_token
        supplier.code_expiry = timezone.now() + timezone.timedelta(minutes=5)
        supplier.save()

        # Send OTP to phone email
        send_email.delay(
            recipient=email, subject="Password Reset OTP", supplier_name=supplier.name, template_dir="supplier_reset_password_otp.html", otp=otp
        )
        data["message"] = "OTP sent to provided email"
        if str(settings.ENVIRONMENT) in ["development", "dev", "staging"]:
            data["otp"] = otp

        return Response(data)

    def post(self, request):
        serializer = SupplierResetPasswordSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class TeamDestroyAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def delete(self, request, team_id):
        user = request.user
        allowed_roles = {UserRole.ADMIN, UserRole.DISBURSER, UserRole.OWNER, UserRole.SUB_ADMIN}

        try:
            team = Team.objects.get(id=team_id)
        except Team.DoesNotExist:
            return Response({"message": "Team not found"}, status=status.HTTP_404_NOT_FOUND)

        team_member = team.members.filter(member=user).first()
        if not (team_member and team_member.role in allowed_roles) and team.company.user != user:
            return Response({"message": "You do not have permission to perform this action"}, status=status.HTTP_401_UNAUTHORIZED)

        # Check if team does not have a budget
        blocking_conditions = [
            (Budget.objects.filter(team=team).exists(), "Team with existing budget cannot be deleted"),
            (Requisition.objects.filter(team=team).exists(), "Team with existing requisition cannot be deleted"),
            (Asset.objects.filter(team=team).exists(), "Team with existing Asset cannot be deleted"),
            (ProcurementPurchaseOrder.objects.filter(indent__team=team).exists(), "Team with existing procurement cannot be deleted"),
        ]

        for condition, message in blocking_conditions:
            if condition:
                return Response({"message": message}, status=status.HTTP_400_BAD_REQUEST)

        team.delete()

        return Response({"message": "Team deleted"})


class AssetExpenseAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = AssetExpenseSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "Asset Expense add successful", "data": response})

    def put(self, request, pk):
        query = Q(asset__team__members__member__in=[request.user]) | Q(asset__team__company__user=request.user)
        asset = AssetExpense.objects.filter(query, id=pk)
        instance = asset.last() if asset else None
        if not instance:
            return Response({"message": "Asset Expense not found"}, status=status.HTTP_404_NOT_FOUND)
        serializer = AssetExpenseSerializerIn(instance=instance, data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"message": "Asset Expense updated successfully", "data": response})


class AssetDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        team_id = request.GET.get("team_id")
        query = Q(members__member__in=[request.user]) | Q(company__user=request.user)
        if not team_id:
            return Response({"message": "Team ID is required"}, status=status.HTTP_400_BAD_REQUEST)
        if not Team.objects.filter(query).exists():
            return Response({"message": "Please ensure you're a member of selected team to perform this action"}, status=status.HTTP_403_FORBIDDEN)

        if cache.get(f"asset_dashboard_{team_id}"):
            data = cache.get(f"asset_dashboard_{team_id}")
        else:
            data = get_asset_dashboard(team_id)

        return Response({"message": "Success", "data": data})


class ProcurementDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        team_id = request.GET.get("team_id")
        query = Q(members__member__in=[request.user]) | Q(company__user=request.user)
        if not team_id:
            return Response({"message": "Team ID is required"}, status=status.HTTP_400_BAD_REQUEST)
        if not Team.objects.filter(query).exists():
            return Response({"message": "Please ensure you're a member of selected team to perform this action"}, status=status.HTTP_403_FORBIDDEN)

        if cache.get(f"procurement_dashboard_{team_id}"):
            data = cache.get(f"procurement_dashboard_{team_id}")
        else:
            data = get_spend_overview(team_id)

        return Response({"message": "Success", "data": data})

