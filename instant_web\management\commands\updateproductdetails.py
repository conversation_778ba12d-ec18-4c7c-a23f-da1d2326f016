from django.core.management.base import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>arser,
)

from requisition.models import Company
from stock_inventory.models import Product, StockDetail


class Command(BaseCommand):
    help = "UPDATE COMPANY PRODUCT DETAILS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("company_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        company_id = kwargs["company_id"]
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            raise CommandError("INVALID COMPANY ID PROVIDED!")
        stock_details = StockDetail.objects.filter(company=company)
        if not stock_details.exists():
            self.stdout.write(
                self.style.ERROR_OUTPUT("COMPANY HAS NO AVAILABLE STOCK DETAILS.")
            )
        for stock in stock_details:
            try:
                product = Product.objects.get(id=stock.item.id)
                product.selling_price = stock.selling_price
                product.product_description = "This cutting-edge item combines sleek design with powerful performance to keep you connected and productive on the go."
                product.product_image_1 = stock.image
                product.product_image_2 = stock.image
                product.product_image_3 = stock.image
                product.product_image_4 = stock.image
                product.product_image_5 = stock.image
                product.save()
            except Product.DoesNotExist:
                pass
        self.stdout.write(
            self.style.SUCCESS(
                "COMPANY PRODUCT DETAILS HAS BEEN UPDATED SUCCESSFULLY."
            )
        )
