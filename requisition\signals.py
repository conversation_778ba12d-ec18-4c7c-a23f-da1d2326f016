from urllib.parse import urlencode

from django.db.models import Q
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver

from account.tasks import (
    create_corporate_account_wallet_handler,
    create_non_corporate_company_wallet,
    create_wema_sms_account,
)
from config import settings
from core.models import CategoryList
from core.tasks import send_email
from helpers.reusable_functions import match_acct_number
from payroll_app.models import CompanyPayrollSettings
from requisition.helpers.brevo_crm import BrevoMarketingApis
from requisition.helpers.enums import TeamChoices, UserRole
from requisition.helpers.request_cls import drop_a_notification_on_paybox360_calendly_slack_channel
from requisition.helpers.send_sms import sms_notifications
from requisition.models import (
    AdGeneneratedUserActivity,
    AdGeneneratedUsers,
    Budget,
    BudgetAllocation,
    Category,
    Company,
    Requisition,
    Team,
    TeamMember,
)
from requisition.tasks import notify_coumpany_owner_on_create_requisition


@receiver(post_save, sender=Requisition)
def update_team_member_requisition_count(sender, instance, created, **kwargs):
    if created:
        # update requisition count on create
        member_instance = instance.member
        member_instance.req_no += 1
        member_instance.save()
        team = member_instance.team
        team.requisitions += 1
        team.save()

        user = instance.user
        account_no = instance.account_no
        check = match_acct_number(acct_number=account_no)

        if check is False:
            instance.bank_code = user.bank_code
            instance.account_name = user.account_name
            instance.account_no = user.account_no
            instance.bank_name = user.bank
            instance.save()
        else:
            pass

        instance.company = team.company
        instance.save()
        api_key = f"{settings.BITLY_ACCESS_TOKEN}"
        notify_coumpany_owner_on_create_requisition.delay(
            requisition_id=instance.id,
            amount=instance.request_amount,
            name=instance.company.user.first_name,
            company_phone=instance.company.user.phone_no,
            first_name=instance.user.first_name,
            reason=instance.request_reason,
            api_key=api_key,
        )

        # update requisition company
        # notify members with super admin permission
        members = team.members.filter(
            Q(role="SUB_ADMIN")
            | Q(role="REVIEWER")
            | Q(role="DISBURSER")
            | Q(role="OWNER")
        )

        for member in members:
            send_email.delay(
                recipient=member.email,
                subject="Requisition",
                template_dir="",
                use_template=False,
                body=f"Requisition Created successfully by {instance.user.email}, Reason --> {instance.request_reason}",
            )

        # send notification to the company owner
        send_email.delay(
            recipient=team.company.user.email,
            subject="Requisition",
            template_dir="",
            use_template=False,
            body=f"Requisition Created successfully by {instance.user.email}, Reason --> {instance.request_reason}",
        )


@receiver(post_save, sender=TeamMember)
def team_member_invite_notification(sender, instance, created, **kwargs):
    if created:
        if instance.team.team_type == TeamChoices.PAYROLL:
            application_module = f"https://www.home.paybox360.com/login?callbackUrl=/spend-management/teams/{instance.team.id}/{instance.team.team_name}"
        if instance.team.team_type == TeamChoices.SALES:
            application_module = f"https://www.home.paybox360.com/login?callbackUrl=/spend-management/teams/{instance.team.id}/{instance.team.team_name}"
        if instance.team.team_type == TeamChoices.SPEND_MGMT:
            application_module = f"https://www.home.paybox360.com/login?callbackUrl=/spend-management/teams/{instance.team.id}/{instance.team.team_name}"
        if instance.team.team_type == TeamChoices.STOCK:
            application_module = f"https://www.home.paybox360.com/login?callbackUrl=/stock/team-members?team={instance.team.id}&company={instance.team.company.id}&companyName={instance.team.company.company_name}&teamName={instance.team.team_name}"

        send_email.delay(
            recipient=instance.email,
            subject="Team Invite",
            template_dir="team_member_invite.html",
            application_url=application_module,
            company_name=instance.team.company.company_name,
            team_name=instance.team.team_name,
            team_id=instance.team.id,
        )


@receiver(post_save, sender=Team)
def to_run_team_signals(sender, instance, created, **kwargs):
    if created:
        # create team member as soon as the team is created
        # print("Creating team member")

        user = instance.user
        team_member = TeamMember.member_exists(email=user.email, team_ins=instance)
        if team_member is None:
            member = TeamMember.objects.create(
                email=user.email,
                phone_no=user.phone_no,
                member=user,
                team=instance,
                is_registered=True,
                status="ACTIVE",
                role=UserRole.OWNER,
                channel=instance.channel,
            )
            instance.members.add(member)


@receiver(post_save, sender=Company)
def on_board_corporate_user(sender, instance, created, **kwargs):
    if created:
        if instance.company_wallet_type == "CORPORATE":
            create_corporate_account_wallet_handler.delay(company_id=instance.id)
            CompanyPayrollSettings.create_company_payroll_settings(company=instance)

        elif instance.company_wallet_type == "MAIN":
            if instance.user:  # Check if user is not None
                user_id = instance.user.id
                company_id = instance.id

                create_non_corporate_company_wallet.delay(
                    user_id=user_id,
                    account_type="NON_CORP_PAYROLL",
                    company_id=company_id,
                )
                create_non_corporate_company_wallet.delay(
                    user_id=user_id,
                    account_type="NON_CORP_SPEND_MGMT",
                    company_id=company_id,
                )

                CompanyPayrollSettings.create_company_payroll_settings(company=instance)
            else:
                # Handle the case where user is None, maybe log a warning or raise an exception
                pass
    else:
        if instance.is_deleted:
            Team.objects.filter(company=instance).update(
                is_active=False, is_deleted=True
            )
            Requisition.objects.filter(company=instance).update(is_deleted=True)
            TeamMember.objects.filter(team__company=instance).update(
                is_active=False, is_deleted=True
            )


@receiver(post_save, sender=Category)
def signal_category_model(sender, instance, created, **kwargs):
    if created:
        title = instance.title.upper()
        other_category = instance.other_category.upper()

        if title == "OTHERS" or title == "OTHER":

            # create category
            category_list = CategoryList.objects.filter(title__icontains=other_category)

            if category_list.exists():

                instance.title = category_list.last().title.upper()
                instance.other_category = ""
                instance.save()
            else:
                category_list = CategoryList.objects.create(title=other_category)
                instance.title = category_list.title.upper()
                instance.other_category = ""
                instance.save()

        else:
            # create category
            category_list = CategoryList.objects.filter(title__icontains=title)
            if not category_list.exists():
                category_list = CategoryList.objects.create(title=title)
                instance.title = category_list.title.upper()
                instance.other_category = ""
                instance.save()


@receiver(post_save, sender=BudgetAllocation)
def signal_budget_allocation_model(sender, instance, created, **kwargs):
    if created:
        team = Team.objects.get(id=instance.team.id)
        team.total_allocation += instance.amount
        team.save()


# @receiver(post_save, sender=Expense)
def update_budget_running_balance(sender, instance, created, **kwargs):
    """
    Update the running balance for a budget after an expense has been made.

    Args:
        sender: The model class.
        instance: The instance of the model being saved.
        created (bool): Indicates whether the instance is being created.
        **kwargs: Additional keyword arguments.

    Returns:
        None: If the instance is not created.

    """
    if not created:
        # No need to update if the instance is not being created
        return

    # team_instance = instance.team
    # budget_instance = Budget.objects.filter(team=team_instance, is_active=True).first()
    #
    # if budget_instance:
    #     start_date = budget_instance.start_date
    #     end_date = budget_instance.end_date
    #
    #     # Calculate the total amount made so far through requisitions
    #     requisitions = Requisition.objects.filter(
    #         Q(status__in=["PENDING", "PROCESSING", "APPROVED", "SUCCESSFUL"]),
    #         team=team_instance,
    #         created_at__range=[start_date, end_date]
    #     )
    #     total_request_amount_made_so_far = requisitions.aggregate(sum_amount=Sum('request_amount'))["sum_amount"] or 0
    #
    #     # Calculate the total amount allocated to the team
    #     allocations = BudgetAllocation.objects.filter(budget=budget_instance, is_active=True)
    #     allocated_amount = allocations.aggregate(sum_amount=Sum('amount'))["sum_amount"] or 0
    #
    #     # Calculate the purse balance
    #     purse_balance = float(allocated_amount) - float(total_request_amount_made_so_far)
    #
    #     # Extract the expensed amount from the current instance
    #     expensed_amount = float(instance.expense_amount)
    #
    #     # Calculate the budget balance after the expense
    #     budget_balance_after = budget_instance.budget_amount - total_request_amount_made_so_far
    #
    #     # Update the instance attributes
    #     # instance.budget_balance_before = float(budget_balance_after) + expensed_amount
    #     # instance.budget_balance_after = budget_balance_after
    #     # instance.purse_value_before = purse_balance + expensed_amount
    #     # instance.purse_value_after = purse_balance
    #     #
    #     # # Update budget instance
    #     # instance.save()
    #
    #     expenses = Expense.objects.filter(id=instance.id)
    #     expenses.update(
    #         budget_balance_before=F("budget_balance_before") + float(budget_balance_after) + expensed_amount,
    #         budget_balance_after=F("budget_balance_after") + budget_balance_after,
    #         purse_value_before=F("purse_value_before") + purse_balance + expensed_amount,
    #         purse_value_after=F("purse_value_after") + purse_balance,
    #
    #     )


@receiver(post_save, sender=Budget)
def create_transferred_balance_allocation(sender, instance, created, **kwargs):
    if created:
        budget = Budget.objects.filter(
            team=instance.team,
            running_balance__gt=0,
            annul_balance=False,
            is_active=False,
            is_deleted=False,
        ).first()

        if budget:
            # create a new allocation for the team and tie to the budget instance that will be created
            BudgetAllocation.objects.create(
                user=instance.team.company.user,
                budget=instance,
                team=instance.team,
                amount=budget.running_balance,
                transferred_balance=True,
            )

            budget.annul_balance = True
            budget.save()


@receiver(post_save, sender=Company)
def create_company_sms_account(sender, instance, created, **kwargs):
    if created:
        create_wema_sms_account.delay(company_id=instance.id)



@receiver(post_save, sender=AdGeneneratedUsers)
def add_user_to_email_sequence_contact_list(sender, instance, created, **kwargs):
    if instance.added_to_brevo_contact_list == False:
        if instance.email:
            instance.added_to_brevo_contact_list = True
            instance.save()

            # create contact in brevo
            brevo_crm = BrevoMarketingApis()

            try:
                brevo_crm.create_contact(
                    first_name=instance.first_name,
                    last_name=instance.last_name,
                    phone_number=instance.phone_number,
                    email=instance.email,
                )
            except:
                pass



@receiver(post_save, sender=AdGeneneratedUserActivity)
def update_slack_and_crm_on_user_activities(sender, instance, created, **kwargs):

    if created:

        activity_messages = {
            "REQUEST_A_FREE_DEMO": "requested for a free demo.",
            "SPEAK_WITH_AN_ADVISOR": "requested to speak with an advisor/agent.",
            "SPEAK_WITH_AN_ADVISOR": "requested to speak with an advisor/agent.",
            "START_A_FREE_TRIAL": "started a free trial.",
            "INTERESTED_IN_HR_MODULE": "expressed interest in the HR module.",
            "INTERESTED_IN_SPEND_MANAGEMENT_MODULE": "expressed interest in the Spend Management module.",
            "INTERESTED_IN_SALES_MODULE": "expressed interest in the Sales module."
        }

        # Check if the activity is in the defined messages
        if instance.activity in activity_messages:
            activity_message = activity_messages[instance.activity]
            
            slack_msg = f"Hello Team,\n User with this phone number {instance.phone_number} {activity_message} Kindly reach out to the user."

            if instance.email:
                slack_msg = f"""Hello Team,\n

                User with this details
                'Phone number': {instance.phone_number}, 
                'Email': {instance.email} 
                
                {activity_message} Kindly reach out to the user."""

            if instance.first_name:
                if instance.email:
                    slack_msg = f"Hello Team,\n User with this details \n 'First Name': {instance.first_name}, 'Email': {instance.email}\n 'Phone number': {instance.phone_number} \n {activity_message} Kindly reach out to the user."            
                    
                else:
                    slack_msg = f"Hello Team, \n User with this details \n 'First Name': {instance.first_name}, \n 'Phone number': {instance.phone_number} \n {activity_message} \n Kindly reach out to the user."

                try:
                    drop_a_notification_on_paybox360_calendly_slack_channel(slack_msg)
                except Exception as e:
                    # Optionally log the exception
                    print(f"Error sending notification: {e}")
        
