from django.conf import settings
from django.utils import timezone
import requests


class AgencyBankingAPI:
    base_url = f"{settings.LIBERTY_PAY_PLUS_BASE_URL}"
    # base_url = f"http://127.0.0.1:8090"

    @classmethod
    def get_user_transaction_metrics(cls, token):
        url = f"{cls.base_url}/api/agents/paybox-transactions-metrics"
        headers = {
            "Authorization": f"Bearer {token}",
        }

        try:
            response = requests.get(url, headers=headers)

            if response.status_code != 200:
                return {"status": False, "data": response.json()}
            else:
                message = {
                    "status": True,
                    "data": response.json()
                }
                return message
        except requests.exceptions.RequestException as e:
            return {"status": False, "data": str(e)}


def get_filter_dates():
    from django.utils import timezone
    from datetime import timedelta

    today = timezone.now().date()
    yesterday = today - timedelta(days=1)

    # Calculate week dates
    today_weekday = today.weekday()
    this_week_start = today - timedelta(days=today_weekday)
    last_week_start = this_week_start - timedelta(days=7)
    last_week_end = this_week_start - timedelta(days=1)

    # Calculate month dates
    this_month_start = today.replace(day=1)
    last_month_end = this_month_start - timedelta(days=1)
    last_month_start = last_month_end.replace(day=1)

    # Calculate year dates
    this_year_start = today.replace(month=1, day=1)
    last_year_end = this_year_start - timedelta(days=1)
    last_year_start = last_year_end.replace(month=1, day=1)

    return {
        "today": today,
        "yesterday": yesterday,
        "this_week_start": this_week_start,
        "last_week_start": last_week_start,
        "last_week_end": last_week_end,
        "this_month_start": this_month_start,
        "last_month_start": last_month_start,
        "last_month_end": last_month_end,
        "this_year_start": this_year_start,
        "last_year_start": last_year_start,
        "last_year_end": last_year_end,
    }

def get_filter_date_values(query_filter):
    """
    Function to return date filter values.
    ARGS:
    filter_value:
        - 'today': filter for today.
        - 'yesterday': filter for previous day.
        - 'this_week': filter for the current week.
        - 'this_month''
        - 'this_year',
        - 'last_year',
    """

    filter_dates = get_filter_dates()
    today = filter_dates.get("today")
    yesterday = filter_dates.get("yesterday")
    # today_weekday = filter_dates.get("today_weekday")
    this_week_start = filter_dates.get("this_week_start")
    last_week_start = filter_dates.get("last_week_start")
    last_week_end = filter_dates.get("last_week_end")
    this_month_start = filter_dates.get("this_month_start")
    last_month_end = filter_dates.get("last_month_end")
    last_month_start = filter_dates.get("last_month_start")
    this_year_start = filter_dates.get("this_year_start")
    last_year_end = filter_dates.get("last_year_end")
    last_year_start = filter_dates.get("last_year_start")

    if query_filter == "today":
        filter_start_date = today
        filter_end_date = today
    elif query_filter == "yesterday":
        filter_start_date = yesterday
        filter_end_date = yesterday
    elif query_filter == "this_week":
        filter_start_date = this_week_start
        filter_end_date = today
    elif query_filter == "last_week":
        filter_start_date = last_week_start
        filter_end_date = last_week_end
    elif query_filter == "this_month":
        filter_start_date = this_month_start
        filter_end_date = today
    elif query_filter == "last_month":
        filter_start_date = last_month_start
        filter_end_date = last_month_end
    elif query_filter == "this_year":
        filter_start_date = this_year_start
        filter_end_date = today
    elif query_filter == "last_year":
        filter_start_date = last_year_start
        filter_end_date = last_year_end
    else:
        filter_start_date = timezone.now().replace(year=2021).date()
        filter_end_date = timezone.now().date()

    return filter_start_date, filter_end_date