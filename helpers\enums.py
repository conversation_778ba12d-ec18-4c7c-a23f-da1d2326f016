from django.db.models import TextChoices


# Create your enumeration type(s) here.
class Currency(TextChoices):
    NAIRA = "NGN", "NAIRA"
    USD = "USD", "USD"


class FilterTypes(TextChoices):
    TODAY = "TODAY", "TODAY"
    CUSTOM = "CUSTOM", "CUSTOM"
    LAST_WEEK = "LAST_WEEK", "LAST_WEEK"
    CURRENT_WEEK = "CURRENT_WEEK", "CURRENT_WEEK"
    LAST_MONTH = "LAST_MONTH", "LAST_MONTH"
    THIS_MONTH = "THIS_MONTH", "THIS_MONTH"
    LAST_QUARTER = "LAST_QUARTER", "LAST_QUARTER"
    THIS_QUARTER = "THIS_QUARTER", "THIS_QUARTER"
    LAST_YEAR = "LAST_YEAR", "LAST_YEAR"
    THIS_YEAR = "THIS_YEAR", "THIS_YEAR"
    YESTERDAY = "YESTERDAY", "YESTERDAY"


class ChannelTypes(TextChoices):
    INSTANT_WEB = "INSTANT_WEB", "INSTANT_WEB"
    MOBILE = "MOBILE", "MOBILE"
    POS = "POS", "POS"
    USSD = "USSD", "USSD"
    WEB = "WEB", "WEB"
    WEB_POS = "WEB_POS", "WEB_POS"


class AlignmentTypes(TextChoices):
    LEFT = "LEFT", "LEFT"
    RIGHT = "RIGHT", "RIGHT"
    CENTER = "CENTER", "CENTER"
    TOP = "TOP", "TOP"
    DOWN = "DOWN", "DOWN"


class InterfaceTheme(TextChoices):
    SYSTEM = "SYSTEM", "SYSTEM"
    LIGHT = "LIGHT", "LIGHT"
    DARK = "DARK", "DARK"


class ProductInventoryType(TextChoices):
    WITH_INVENTORY = "WITH_INVENTORY", "WITH_INVENTORY"
    WITHOUT_INVENTORY = "WITHOUT_INVENTORY", "WITHOUT_INVENTORY"


class Backorders(TextChoices):
    DO_NOT_ALLOW = "DO_NOT_ALLOW", "DO_NOT_ALLOW"
    ALLOW_NOTIFY_CUSTOMERS = "ALLOW_NOTIFY_CUSTOMERS", "ALLOW_NOTIFY_CUSTOMERS"
    ALLOW = "ALLOW", "ALLOW"


class RequestMethodType(TextChoices):
    DELETE = "DELETE", "DELETE"
    GET = "GET", "GET"
    HEAD = "HEAD", "HEAD"
    OPTIONS = "OPTIONS", "OPTIONS"
    POST = "POST", "POST"
    PATCH = "PATCH", "PATCH"
    PUT = "PUT", "PUT"
