from django.contrib import admin

from import_export.admin import ImportExportModelAdmin

from . import resources

from .models import (
    Clock,
    Break,
    Location,
    OverTime,
    Record,
    Task,
    Screenshot,
    Shift,
    AttendanceSheet,
    CompanyBranchInvite,
    CompanyMeeting
)


# Register your model(s) here.
class ClockAdmin(ImportExportModelAdmin):
    resource_class = resources.ClockResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BreakAdmin(ImportExportModelAdmin):
    resource_class = resources.BreakResource
    search_fields = ["record__employee__employee_email", "company__company_name"]
    list_filter = ["is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LocationAdmin(ImportExportModelAdmin):
    resource_class = resources.LocationResource
    search_fields = ["name", "company__company_name", "employee__employee_email"]
    list_filter = ["location_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OvertimeAdmin(ImportExportModelAdmin):
    resource_class = resources.OverTimeResource
    search_fields = ["record__employee__employee_email", "company__company_name"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RecordAdmin(ImportExportModelAdmin):
    resource_class = resources.RecordResource
    search_fields = ["employee__employee_email", "company__company_name", "location__name"]
    list_filter = ["location_type", "week_day"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TaskAdmin(ImportExportModelAdmin):
    resource_class = resources.TaskResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ScreenshotAdmin(ImportExportModelAdmin):
    resource_class = resources.ScreenshotResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ShiftAdmin(ImportExportModelAdmin):
    resource_class = resources.ShiftResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class AttendanceSheetAdmin(ImportExportModelAdmin):
    resource_class = resources.AttendanceSheetResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class CompanyBranchInviteAdmin(ImportExportModelAdmin):
    resource_class = resources.CompanyBranchInviteResource
    search_fields = ["employee__employee_email", "company__company_name", "invite_id"]
    list_filter = ["invite_status", "is_expired", "is_completed"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class CompanyMeetingAdmin(ImportExportModelAdmin):
    resource_class = resources.CompanyMeetingResource
    search_fields = ["company__company_name"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(Clock, ClockAdmin)
admin.site.register(Break, BreakAdmin)
admin.site.register(Location, LocationAdmin)
admin.site.register(OverTime, OvertimeAdmin)
admin.site.register(Record, RecordAdmin)
admin.site.register(Screenshot, ScreenshotAdmin)
admin.site.register(Shift, ShiftAdmin)
admin.site.register(Task, TaskAdmin)
admin.site.register(AttendanceSheet, AttendanceSheetAdmin)
admin.site.register(CompanyBranchInvite, CompanyBranchInviteAdmin)
admin.site.register(CompanyMeeting, CompanyMeetingAdmin)
