from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Sum
from clock_app.helpers import get_distance
from core.tasks import send_email
from rest_framework.response import Response
from rest_framework import serializers, status
from stock_inventory.models import Branch
from .models import Clock, Break, CompanyBranchInvite, CompanyMeeting, Shift, Location, OverTime, Record, Screenshot, Task, AttendanceSheet #LocationOneOffLink
from payroll_app.models import CompanyEmployeeList as Employee, CompanyPayrollSettings as CompanyProfile
from requisition.models import Company

# Define a dictionary to map weekdays to corresponding attributes
weekday_attributes = {
    0: ('hybrid_monday', 'Monday'),
    1: ('hybrid_tuesday', 'Tuesday'),
    2: ('hybrid_wednesday', 'Wednesday'),
    3: ('hybrid_thursday', 'Thursday'),
    4: ('hybrid_friday', 'Friday'),
    5: ('hybrid_saturday', 'Saturday'),
    6: ('hybrid_sunday', 'Sunday')
}

class CompanyProfileSerializer(serializers.ModelSerializer):
    def patch(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"]=company
        return super().save(**kwargs)
    class Meta:
        model = CompanyProfile
        fields = ['id', 'company', 'resumption_time', 'closing_time', 'break_duration', 'has_shifts',
                  'work_duration', 'overtime_duration', 'created_at', 'updated_at',
                  'hybrid_monday', 'hybrid_tuesday', 'hybrid_wednesday', 'hybrid_thursday', 'hybrid_friday',
                  'hybrid_saturday', 'hybrid_sunday']


class ShiftSerializer(serializers.ModelSerializer):
    class Meta:
        model = Shift
        fields = ['id', 'company', 'employee', 'title', 'shift_resumption_time', 
                  'shift_closing_time', 'date_created', 'date_updated']
        
    def to_representation(self, instance):
        serializer_data = super().to_representation(instance)
        all_assignee = serializer_data["employee"]
        all_employee = []
        for employee in all_assignee:
            this_employee = Employee.objects.filter(id=employee).first()
            all_employee.append({
                "employee_email": this_employee.employee_email,
                "employee_first_name": this_employee.employee_first_name
            })
        serializer_data["employee"] = all_employee
        return serializer_data

    def save(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"] = company

        if self.validated_data["employee"]:
            employee_list = self.validated_data.pop("employee")

            if employee_list:
                for employee in employee_list:
                    shifts = employee.shift.filter(company=company)
                    for shift in shifts:
                        shift.employee.remove(employee)

        shift = Shift.objects.create(**self.validated_data)
        company_profile = CompanyProfile.objects.get(company=company)
        company_profile.has_shifts = True
        company_profile.save()
        shift = shift.employee.set(employee_list) 

        self.validated_data["employee"]=employee_list
        self.instance = shift      
        return self.instance


class UpdateShiftSerializer(serializers.ModelSerializer):
    class Meta:
        model = Shift
        fields = ['id', 'company', 'title', 'shift_resumption_time', 'shift_closing_time', 'date_updated']


class RecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = Record
        fields = ['id', 'company', 'employee', 'is_late', 'lateness_duration', 'work_duration', 'break_duration', 
                  'is_break_exceeded', 'break_excess', 'overtime_duration', 'date_created', 'date_updated']     


class ClockInSerializer(serializers.ModelSerializer):
    class Meta:
        model = Clock
        fields = ['id', 'company', 'clock_in', "date_created", "date_updated"]

    def save(self, **kwargs):   
        company = self.context['company']
        self.validated_data["company"] = company 

        employee = self.context['employee_id']
        employee = Employee.objects.get(id=employee)
        if employee is None:
            raise serializers.ValidationError({"message":"employee does not exists"})

        location = self.context['location']
        location = Location.objects.get(id=location)
        if location.company != company:
            raise serializers.ValidationError({"message":f"Clock-in location:{location.company} \
                                               doesn't belong to {company}"})

        if company is not None:
            company_profile = CompanyProfile.objects.get(company=company)
            employee_shift = employee.shift.all().last()
            if company_profile.has_shifts and employee_shift:
                resumption_time = employee_shift.shift_resumption_time
                resumption_time_in_seconds = resumption_time.hour * 3600 + resumption_time.minute * 60 + resumption_time.second
            else:
                try:
                    resumption_time = company_profile.resumption_time
                    resumption_time_in_seconds = resumption_time.hour * 3600 + resumption_time.minute * 60 + resumption_time.second
                except Exception as e: 
                    raise serializers.ValidationError({"message": "Complete Company Profile Settings"})
        else:
            resumption_time = datetime.now().time()
            resumption_time = resumption_time.replace(hour=8, minute=0, second=0)
            resumption_time_in_seconds = resumption_time.hour * 3600 + resumption_time.minute * 60 + resumption_time.second 

        record = Record.objects.filter(date_created__date = datetime.now().date(), 
                                        employee = employee, company = company).last()
        if record is None:
            record = Record.objects.create(employee=employee, company=company, location=location)
            clock, created = Clock.objects.get_or_create(record = record, company = company)
            clock_in_time = datetime.now().time()
            clock_in_time_in_seconds = clock_in_time.hour * 3600 + clock_in_time.minute * 60 + clock_in_time.second
            clock_in_time = clock_in_time.replace(hour=clock_in_time.hour, minute=clock_in_time.minute, second=clock_in_time.second)
        else:
            raise serializers.ValidationError({"message":"Clock-in record exists"})
            
        if clock_in_time_in_seconds > resumption_time_in_seconds:
            is_late = True
            time_difference = clock_in_time_in_seconds - resumption_time_in_seconds
            total_seconds = time_difference
            hours = int(total_seconds // 3600)
            minutes = int((total_seconds % 3600) // 60)
            seconds = int(total_seconds % 60)
            lateness_duration = timedelta(hours=hours, minutes=minutes, seconds=seconds)
            send_email.delay(recipient=employee.employee_email, subject="Late Clock In",
                    employee_name=employee.full_name,
                    template_dir="late_clock_in.html", company_name=company.company_name,
            )
        else:
            is_late=False
            lateness_duration = None 
        record.is_late = is_late
        record.lateness_duration = lateness_duration
        record.employee = employee
        record.company = company
        record.save()
        clock.clock_in = clock_in_time
        clock.save()
        self.instance = clock
        return self.instance

class EmployeeClockInSerializer(serializers.Serializer):
    latitude = serializers.FloatField(required=True)
    longitude = serializers.FloatField(required=True)

    def validate(self, attrs):
        latitude = attrs.get("latitude")
        longitude = attrs.get("longitude")
        company = self.context.get("company")
        employee = self.context.get("employee")
        company_profile = CompanyProfile.objects.filter(company=company, is_deleted=False).first()

        today_date_time = timezone.now()
        today_date = today_date_time.date()
        record = Record.objects.filter(employee=employee, company=company, date_created__date=today_date).first()
        if record:
            raise serializers.ValidationError({"error": "the clock-in record for today already exists."})

        if not employee.work_type:
            raise serializers.ValidationError({"error": "you do not have a work_type"})
        
        work_type = employee.work_type

        if company_profile.clock_in_anywhere:
            all_location = Location.objects.filter(company=company, location_type="ONSITE", is_deleted=False)
            this_location = None
            for location in all_location:
                lon1 = location.longitude
                lat1 = location.latitude
                lon2 = longitude
                lat2 = latitude
                distance = get_distance(lon1, lat1, lon2, lat2)
                if distance <= location.radius:
                    this_location = location
                    break
        else:
            this_location = None
            if employee.employee_branch:
                lon1 = employee.employee_branch.longitude
                lat1 = employee.employee_branch.latitude
                lon2 = longitude
                lat2 = latitude
                distance = get_distance(lon1, lat1, lon2, lat2)
                if distance <= employee.employee_branch.radius:
                    this_location = employee.employee_branch
    

        # Get the current weekday
        current_weekday = today_date_time.weekday()
        this_day_name = ""
        # Check if the current weekday exists in the dictionary
        if current_weekday in weekday_attributes:
            attr_name, day_name = weekday_attributes[current_weekday]

            this_day_name = day_name.upper()

        location_type = ""
        if work_type == "REMOTE":
            location_type = "REMOTE"
        elif work_type == "HYBRID": 
            # Check if the attribute is True in the company profile
            if getattr(company_profile, attr_name):
                if not this_location:
                    raise serializers.ValidationError(
                        {"error": f"Your {day_name} clock-in should be within the office"}
                    )
                else:
                    location_type = "ONSITE"
            else:
                location_type = "REMOTE"
        else:
            if not this_location:
                raise serializers.ValidationError({"error": "clock-in should be within the office"})
            else:
                location_type = "ONSITE"
            
        employee_attendance = None
        attendance = AttendanceSheet.objects.filter(company=company, status="ACTIVE", is_deleted=False)
        for att in attendance:
            if att.employee.filter(pk=employee.pk).exists():
                employee_attendance = att
                break
        
        resumption_time = None
        closing_time = None
        employee_has_shift = False
        employee_shift = None

        if employee_attendance:
            all_shift = Shift.objects.filter(company=company, attendance=employee_attendance, is_deleted=False)
            for shift in all_shift:
                this_attendance = shift.attendance.employee
                if this_attendance.filter(pk=employee.pk).exists():
                    employee_has_shift = True
                    employee_shift = shift
                    resumption_time = shift.shift_resumption_time
                    closing_time = shift.shift_closing_time
                    break

        if not employee_has_shift:
            resumption_time = company_profile.start_resumption_time
            closing_time = company_profile.closing_time

        resumption_time_in_seconds = timedelta(hours=resumption_time.hour, minutes=resumption_time.minute, seconds=0).total_seconds()
        closing_time_in_seconds = timedelta(hours=closing_time.hour, minutes=closing_time.minute, seconds=closing_time.second).total_seconds()

        # current_time = timezone.localtime(timezone.now())
        current_time = timezone.localtime()
        
        clock_in_time_in_seconds = timedelta(hours=current_time.hour, minutes=current_time.minute, seconds=current_time.second).total_seconds()
        
        ### first check if employee clock-in after closing hours
        if clock_in_time_in_seconds > closing_time_in_seconds:
            raise serializers.ValidationError({"error": "you cannot clock-in after closing hours"})

        if clock_in_time_in_seconds > resumption_time_in_seconds:
            is_late = True
            # Calculate the difference between the resumption time and the clock in time
            time_difference = clock_in_time_in_seconds - resumption_time_in_seconds
            # Extract the duration components
            hours, remainder = divmod(time_difference, 3600)
            minutes, seconds = divmod(remainder, 60)
            # Create a timedelta object from the duration components
            late_hours = int(hours)
            late_minutes = int(minutes)
            late_seconds = int(seconds)
            lateness_duration = timedelta(hours=late_hours, minutes=late_minutes, seconds=late_seconds)
        else:
            is_late=False
            lateness_duration = None

        if employee_attendance:
            attendance_choice = employee_attendance.title.upper()
        else:
            attendance_choice = "MAIN"
        
        attrs["is_late"] = is_late
        attrs["lateness_duration"] = lateness_duration
        attrs["clock_in_time"] = current_time
        attrs["work_type"] = "ONSITE" if this_location else "REMOTE"
        #create a clock-in record for employee
        record = Record.objects.create(
            employee=employee, 
            company=company, 
            location=this_location,
            clock_in_longitude=longitude,
            clock_in_latitude=latitude,
            clock_in_time=current_time,
            is_late=is_late,
            attendance=attendance_choice,
            lateness_duration=lateness_duration,
            shift=employee_shift,
            location_type=location_type,
            week_day=this_day_name
        )
        if is_late:
            send_email.delay(recipient=employee.employee_email, subject="Late Clock In",
                        employee_name=employee.full_name,
                        template_dir="late_clock_in.html", company_name=company.company_name,
            )
        attrs["meeting_link"] = None
        attrs["meeting_time"] = None
        this_meeting = CompanyMeeting.objects.filter(company=company, meeting_date=today_date).first()
        if this_meeting:
            attrs["meeting_link"] = this_meeting.meeting_link
            attrs["meeting_time"] = this_meeting.meeting_time
            send_email.delay(recipient=employee.employee_email, subject="Today Meeting",
                        employee_name=employee.full_name, call_back_url=this_meeting.meeting_link,
                        template_dir="meeting_link.html", company_name=company.company_name,
                        meeting_time=this_meeting.meeting_time.strftime("%I:%M %p")
            )
        return attrs


class EmployeeClockOutSerializer(serializers.Serializer):
    latitude = serializers.FloatField(required=True)
    longitude = serializers.FloatField(required=True)

    def validate(self, attrs):
        latitude = attrs.get("latitude")
        longitude = attrs.get("longitude")
        company = self.context.get("company")
        employee = self.context.get("employee")
        company_profile = CompanyProfile.objects.filter(company=company, is_deleted=False).first()
        today_date = timezone.now().date()
        record = Record.objects.filter(employee=employee, company=company, date_created__date=today_date).first()
        if not record:
            raise serializers.ValidationError({"error": "you must clock-in before clock-out"})
        if record:
            if record.clock_out_time:
                raise serializers.ValidationError({"error": "you have already clock-out"})
        
        work_type = record.location_type
        
        if company_profile.clock_in_anywhere:
            all_location = Location.objects.filter(company=company, location_type="ONSITE", is_deleted=False)
            this_location = None
            for location in all_location:
                lon1 = location.longitude
                lat1 = location.latitude
                lon2 = longitude
                lat2 = latitude
                distance = get_distance(lon1, lat1, lon2, lat2)
                if distance <= location.radius:
                    this_location = location
                    break
        else:
            this_location = None
            if employee.employee_branch:
                lon1 = employee.employee_branch.longitude
                lat1 = employee.employee_branch.latitude
                lon2 = longitude
                lat2 = latitude
                distance = get_distance(lon1, lat1, lon2, lat2)
                if distance <= employee.employee_branch.radius:
                    this_location = employee.employee_branch
        
        if work_type == "ONSITE":
            if not this_location:
                raise serializers.ValidationError({"error": "clock-out should be within the office"})

        employee_attendance = None
        attendance = AttendanceSheet.objects.filter(company=company, status="ACTIVE", is_deleted=False)
        for att in attendance:
            if att.employee.filter(pk=employee.pk).exists():
                employee_attendance = att
                break
        
        resumption_time = None
        closing_time = None
        employee_has_shift = False
        employee_shift = None
        
        if employee_attendance:
            all_shift = Shift.objects.filter(company=company, attendance=employee_attendance, is_deleted=False)
            for shift in all_shift:
                this_attendance = shift.attendance.employee
                if this_attendance.filter(pk=employee.pk).exists():
                    employee_has_shift = True
                    employee_shift = shift
                    resumption_time = shift.shift_resumption_time
                    closing_time = shift.shift_closing_time
                    break

        if not employee_has_shift:
            resumption_time = company_profile.resumption_time
            closing_time = company_profile.closing_time

        current_time = timezone.localtime()
        break_instance = Break.objects.filter(date_created__date=today_date, break_end_time__isnull=True,
                                                record=record, company=company).first()
        if break_instance:
            raise serializers.ValidationError({"error": "end your break before you clock-out"})
        
        no_duration = timedelta(hours=0, minutes=0, seconds=0)
        
        if current_time.time() < closing_time:  
            all_breaks = Break.objects.filter(date_created__date=today_date,
                                                record=record, company=company)
            total_break_duration = all_breaks.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration

            clock_in = record.clock_in_time
            clock_in_seconds = clock_in.hour * 3600 + clock_in.minute * 60 + clock_in.second
            today_closing_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
            current_time_seconds = current_time.time().hour * 3600 + current_time.time().minute * 60 + current_time.time().second

            resumption_time_in_seconds = timedelta(hours=resumption_time.hour, minutes=resumption_time.minute, seconds=0).total_seconds()
            this_closing_time_seconds = today_closing_seconds + no_duration.total_seconds()    

            break_duration_seconds = total_break_duration.total_seconds()
            work_duration_seconds = current_time_seconds - clock_in_seconds - break_duration_seconds
            work_duration = timedelta(seconds=work_duration_seconds)

            break_exceeded = False
            exceeded_break_time = no_duration
            if total_break_duration > company_profile.break_duration:
                
                break_exceeded = True
                exceeded_break_time = total_break_duration - company_profile.break_duration
            else:
                break_exceeded = False
                exceeded_break_time = no_duration
    
            if current_time_seconds <= resumption_time_in_seconds:
                record.work_duration = no_duration
                record.break_duration = no_duration
                record.is_break_exceeded = False
                record.break_excess = no_duration
                record.overtime_duration = no_duration
                record.clock_out_longitude = longitude
                record.clock_out_latitude = latitude
                record.clock_out_time = current_time.time()
                record.save()
            else:
                record.work_duration = work_duration
                record.break_duration = total_break_duration
                record.is_break_exceeded = break_exceeded
                record.break_excess = exceeded_break_time
                record.overtime_duration = no_duration
                record.clock_out_longitude = longitude
                record.clock_out_latitude = latitude
                record.clock_out_time = current_time.time()
                record.save()

        if current_time.time() >= closing_time:
            all_breaks = Break.objects.filter(date_created__date=today_date,
                                                record=record, company=company)
            total_break_duration = all_breaks.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration

            clock_in = record.clock_in_time
            clock_in_seconds = clock_in.hour * 3600 + clock_in.minute * 60 + clock_in.second
            today_closing_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
            current_time_seconds = current_time.time().hour * 3600 + current_time.time().minute * 60 + current_time.time().second
            
            if company_profile.overtime_duration > no_duration:
                this_closing_time_seconds = today_closing_seconds + company_profile.overtime_duration.total_seconds()
            else:
                this_closing_time_seconds = today_closing_seconds + no_duration.total_seconds()
            

            break_duration_seconds = total_break_duration.total_seconds()
            work_duration_seconds = this_closing_time_seconds - clock_in_seconds - break_duration_seconds
            work_duration = timedelta(seconds=work_duration_seconds)
            break_exceeded = False
            exceeded_break_time = no_duration
            extra_time_duration = no_duration

            get_extra_time_duration = all_breaks.filter(date_created__time__gt=closing_time)
            if get_extra_time_duration:
                extra_time_duration = get_extra_time_duration.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration
            else:
                extra_time_duration = no_duration

            if current_time_seconds > this_closing_time_seconds:
                over_time_data = this_closing_time_seconds - today_closing_seconds
                # print(over_time_data ,this_closing_time_seconds, today_closing_seconds, ' seconds 1')
                over_time_duration = timedelta(seconds=over_time_data) - extra_time_duration
            else:
                over_time_data = this_closing_time_seconds - current_time_seconds
                # print(over_time_data ,this_closing_time_seconds, current_time_seconds, ' seconds 2')
                over_time_first_duration = timedelta(seconds=over_time_data) - extra_time_duration
                over_time_duration = company_profile.overtime_duration - over_time_first_duration 

            if total_break_duration > company_profile.break_duration:
                break_exceeded = True
                exceeded_break_time = total_break_duration - company_profile.break_duration
            else:
                break_exceeded = False
                exceeded_break_time = no_duration

            record.work_duration = work_duration
            record.break_duration = total_break_duration
            record.is_break_exceeded = break_exceeded
            record.break_excess = exceeded_break_time
            record.overtime_duration = over_time_duration
            record.clock_out_longitude = longitude
            record.clock_out_latitude = latitude
            record.clock_out_time = current_time.time()
            record.save()

        return attrs

class EmployeeStartBreakSerializer(serializers.Serializer):
 
    latitude = serializers.FloatField(required=True)
    longitude = serializers.FloatField(required=True)

    def validate(self, attrs):
        latitude = attrs.get("latitude")
        longitude = attrs.get("longitude")
        company = self.context.get("company")
        employee = self.context.get("employee")
        company_profile = CompanyProfile.objects.filter(company=company, is_deleted=False).first()
        today_date = timezone.localtime().date()
        record = Record.objects.filter(employee=employee, company=company, date_created__date=today_date).first()
        if not record:
            raise serializers.ValidationError({"error": "you cannot start a break before clock-in"})
        if record:
            if record.clock_out_time:
                raise serializers.ValidationError({"error": "you cannot start break after clock-out"})
        work_type = record.location_type

        if company_profile.clock_in_anywhere:
            all_location = Location.objects.filter(company=company, location_type="ONSITE", is_deleted=False)
            this_location = None
            for location in all_location:
                lon1 = location.longitude
                lat1 = location.latitude
                lon2 = longitude
                lat2 = latitude
                distance = get_distance(lon1, lat1, lon2, lat2)
                if distance <= location.radius:
                    this_location = location
                    break
        else:
            this_location = None
            if employee.employee_branch:
                lon1 = employee.employee_branch.longitude
                lat1 = employee.employee_branch.latitude
                lon2 = longitude
                lat2 = latitude
                distance = get_distance(lon1, lat1, lon2, lat2)
                if distance <= employee.employee_branch.radius:
                    this_location = employee.employee_branch
        
        if work_type == "ONSITE":
            if not this_location:
                raise serializers.ValidationError({"error": "start-break should be within the office"})

        employee_attendance = None
        attendance = AttendanceSheet.objects.filter(company=company, status="ACTIVE", is_deleted=False)
        for att in attendance:
            if att.employee.filter(pk=employee.pk).exists():
                employee_attendance = att
                break
        
        closing_time = None
        employee_has_shift = False

        all_shift = Shift.objects.filter(company=company, attendance=employee_attendance, is_deleted=False)
        for shift in all_shift:
            this_attendance = shift.attendance.employee
            if this_attendance.filter(pk=employee.pk).exists():
                employee_has_shift = True
                resumption_time = shift.shift_resumption_time
                closing_time = shift.shift_closing_time
                break

        if not employee_has_shift:
            resumption_time = company_profile.resumption_time
            closing_time = company_profile.closing_time
        
        current_time = timezone.localtime()
        ongoing_break = Break.objects.filter(date_created__date=today_date, break_end_time__isnull=True,
                                                record=record, company=company).first()
        
        if resumption_time >= current_time.time():
            raise serializers.ValidationError({"error": f"breaks cannot be observed before {resumption_time}"}) 
        if ongoing_break:
            raise serializers.ValidationError({"error": "you have an on-going break"})
        if current_time.time() >= closing_time:  
            raise serializers.ValidationError({"error": "breaks cannot be observed after closing"}) 
        break_instance = Break.objects.create(
            record=record, 
            company=company, 
            break_start_time=current_time,
            start_break_longitude=longitude,
            start_break_latitude=latitude
            )        
        return attrs

class EmployeeEndBreakSerializer(serializers.Serializer):
    latitude = serializers.FloatField(required=True)
    longitude = serializers.FloatField(required=True)

    def validate(self, attrs):
        latitude = attrs.get("latitude")
        longitude = attrs.get("longitude")
        company = self.context.get("company")
        employee = self.context.get("employee")
        company_profile = CompanyProfile.objects.filter(company=company, is_deleted=False).first()
        today_date = timezone.now().date()
        record = Record.objects.filter(employee=employee, company=company, date_created__date=today_date).first()
        if not record:
            raise serializers.ValidationError({"error": "you cannot end a break before clock-in"})
        
        work_type = record.location_type
        if company_profile.clock_in_anywhere:
            all_location = Location.objects.filter(company=company, location_type="ONSITE", is_deleted=False)
            this_location = None
            for location in all_location:
                lon1 = location.longitude
                lat1 = location.latitude
                lon2 = longitude
                lat2 = latitude
                distance = get_distance(lon1, lat1, lon2, lat2)
                if distance <= location.radius:
                    this_location = location
                    break
        else:
            this_location = None
            if employee.employee_branch:
                lon1 = employee.employee_branch.longitude
                lat1 = employee.employee_branch.latitude
                lon2 = longitude
                lat2 = latitude
                distance = get_distance(lon1, lat1, lon2, lat2)
                if distance <= employee.employee_branch.radius:
                    this_location = employee.employee_branch
        
        if work_type == "ONSITE":
            if not this_location:
                raise serializers.ValidationError({"error": "start-break should be within the office"})

        employee_attendance = None
        attendance = AttendanceSheet.objects.filter(company=company, status="ACTIVE", is_deleted=False)
        for att in attendance:
            if att.employee.filter(pk=employee.pk).exists():
                employee_attendance = att
                break
        
        closing_time = None
        employee_has_shift = False

        all_shift = Shift.objects.filter(company=company, attendance=employee_attendance, is_deleted=False)
        for shift in all_shift:
            this_attendance = shift.attendance.employee
            if this_attendance.filter(pk=employee.pk).exists():
                employee_has_shift = True
                closing_time = shift.shift_closing_time
                break

        if not employee_has_shift:
            closing_time = company_profile.closing_time
        
        current_time = timezone.localtime()

        break_instance = Break.objects.filter(date_created__date=today_date, break_end_time__isnull=True,
                                                record=record, company=company).first()
        if not break_instance:
            raise serializers.ValidationError({"error": "you are currently not on break"})
        
        break_start = timezone.localtime(break_instance.break_start_time)
        break_start_time = break_start.time()
        
        if current_time.time() < closing_time:  
            start_break_time_in_seconds = timedelta(hours=break_start_time.hour, minutes=break_start_time.minute, seconds=break_start_time.second).total_seconds()
            end_break_time_in_seconds = timedelta(hours=current_time.hour, minutes=current_time.minute, seconds=current_time.second).total_seconds()

            # Calculate the difference between the resumption time and the clock in time
            time_difference = end_break_time_in_seconds - start_break_time_in_seconds
            # Extract the duration components
            hours, remainder = divmod(time_difference, 3600)
            minutes, seconds = divmod(remainder, 60)
            # Create a timedelta object from the duration components
            late_hours = int(hours)
            late_minutes = int(minutes)
            late_seconds = int(seconds)
            break_duration = timedelta(hours=late_hours, minutes=late_minutes, seconds=late_seconds)
            
            break_instance.break_end_time = current_time
            break_instance.break_duration = break_duration
            break_instance.end_break_longitude=longitude
            break_instance.end_break_latitude=latitude
            break_instance.save()
        
        if current_time.time() >= closing_time:
            start_break_time_in_seconds = timedelta(hours=break_start_time.hour, minutes=break_start_time.minute, seconds=break_start_time.second).total_seconds()
            end_break_time_in_seconds = timedelta(hours=closing_time.hour, minutes=closing_time.minute, seconds=closing_time.second).total_seconds()
           
            print(break_start_time, closing_time, current_time.time())
            # Calculate the difference between the resumption time and the clock in time
            time_difference = end_break_time_in_seconds - start_break_time_in_seconds
            # Extract the duration components
            hours, remainder = divmod(time_difference, 3600)
            minutes, seconds = divmod(remainder, 60)
            # Create a timedelta object from the duration components
            end_break_hours = int(hours)
            end_break_minutes = int(minutes)
            end_break_seconds = int(seconds)
            break_duration = timedelta(hours=end_break_hours, minutes=end_break_minutes, seconds=end_break_seconds)
            closing_date = f'{today_date} {closing_time}'
            closing_date_time = datetime.strptime(closing_date, '%Y-%m-%d %H:%M:%S')
            break_instance.break_end_time = closing_date_time
            break_instance.break_duration = break_duration
            break_instance.end_break_longitude=longitude
            break_instance.end_break_latitude=latitude
            break_instance.save()

        return attrs

class ClockOutSerializer(serializers.ModelSerializer):    
    class Meta:
        model = Clock
        fields = ['id', 'company', 'clock_out', 'date_created', 'date_updated']

    def save(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"] = company

        location = self.context['location']
        location = Location.objects.get(id=location.id)

        employee = self.context['employee_id']
        employee = Employee.objects.get(id=employee)

        current_time = datetime.now().time()
        if company is not None:
            company_profile = CompanyProfile.objects.get(company=company)
            employee_shift = employee.shift.all().last()
            if company_profile.has_shifts and employee_shift:
                closing_time = employee_shift.shift_closing_time
                closing_time_in_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
            else:
                allowed_work_duration = company_profile.work_duration
                closing_time = company_profile.closing_time
                closing_time_in_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
        else:
            allowed_work_duration = timedelta(hour=8, minute=0, second=0)
            closing_time = current_time.replace(hour=17, minute=0, second=0)
            closing_time_in_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second

        record = Record.objects.filter(date_created__date = datetime.now().date(),
                                       employee=employee, company=company).last()

        clock = Clock.objects.filter(date_created__date = datetime.now().date(), clock_out__isnull = True, 
                                     record=record, company=company).last()

        if clock:
            # if location == record.location:
            clock_in_time = clock.clock_in
            clock_in_time_in_seconds = clock_in_time.hour * 3600 + clock_in_time.minute * 60 + clock_in_time.second 
            
            clock_out_time = current_time
            clock_out_time_in_seconds = clock_out_time.hour * 3600 + clock_out_time.minute * 60 + clock_out_time.second
            
            closing_time = closing_time
            closing_time_in_seconds =  closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second

            if clock_out_time_in_seconds >= closing_time_in_seconds:
                if clock_in_time_in_seconds >= closing_time_in_seconds:
                    work_duration = record.work_duration

                    overtime_start_time_in_datetime = datetime.now().replace(hour=clock_in_time.hour,
                                                                                minute=clock_in_time.minute,
                                                                                second=clock_in_time.second)

                    overtime = OverTime.objects.create(record = record, overtime_start_time = overtime_start_time_in_datetime,
                                                        overtime_end_time = datetime.now(), company=company)

                    overtime_duration = overtime.overtime_end_time - overtime.overtime_start_time
                    total_seconds = overtime_duration.total_seconds()
                    hours = int(total_seconds // 3600)
                    minutes = int((total_seconds % 3600) // 60)
                    seconds = int(total_seconds % 60)
                    if record.overtime_duration is None:
                        record.overtime_duration = timedelta(hours=0, minutes=0, seconds=0)
                    record.overtime_duration += timedelta(hours=hours, minutes=minutes, seconds=seconds)

                elif clock_in_time_in_seconds <= closing_time_in_seconds:
                    if record.break_duration is not None:                        
                        wasted_time = record.break_duration

                        hours = wasted_time.seconds // 3600
                        minutes = (wasted_time.seconds % 3600) // 60
                        seconds = wasted_time.seconds % 60
                        wasted_time_in_timedelta = timedelta(hours, minutes, seconds)        
                        wasted_time_in_seconds = hours*3600 + minutes*60 + seconds

                        work_duration = (closing_time_in_seconds - clock_in_time_in_seconds) - wasted_time_in_seconds
                    else:
                        work_duration = closing_time_in_seconds - clock_in_time_in_seconds
                    
                    overtime_start_time_in_datetime = datetime.now().replace(hour=closing_time.hour,
                                                                                minute=closing_time.minute,
                                                                                second=closing_time.second)

                    overtime = OverTime.objects.create(record = record, overtime_start_time = overtime_start_time_in_datetime,
                                                        overtime_end_time = datetime.now(), company=company)

                    overtime_duration = overtime.overtime_end_time - overtime.overtime_start_time
                    total_seconds = overtime_duration.total_seconds()
                    hours = int(total_seconds // 3600)
                    minutes = int((total_seconds % 3600) // 60)
                    seconds = int(total_seconds % 60)
                    if record.overtime_duration is None:
                        record.overtime_duration = timedelta(hours=0, minutes=0, seconds=0)
                    record.overtime_duration += timedelta(hours=hours, minutes=minutes, seconds=seconds)

            else:
                if record.break_duration is not None:
                    wasted_time = record.break_duration

                    hours = wasted_time.seconds // 3600
                    minutes = (wasted_time.seconds % 3600) // 60
                    seconds = wasted_time.seconds % 60
                    wasted_time_in_timedelta = timedelta(hours, minutes, seconds)
                    wasted_time_in_seconds = hours * 3600 + minutes * 60 + seconds

                    work_duration_in_seconds = clock_out_time_in_seconds - clock_in_time_in_seconds
                    work_duration = work_duration_in_seconds - wasted_time_in_seconds
                else:
                    work_duration = clock_out_time_in_seconds - clock_in_time_in_seconds

            if work_duration is not None:
                total_seconds = work_duration
                hours = int(total_seconds // 3600)
                minutes = int((total_seconds % 3600) // 60)
                seconds = int(total_seconds % 60)
                work_duration = timedelta(seconds=work_duration)
                record.work_duration = work_duration

            clock_out = clock_out_time
            clock.clock_out = clock_out

            record.save()
            clock.save()

            self.instance = clock
            return self.instance
            # else:
            #     raise serializers.ValidationError({"message":"You have to clocked-out from your clock-in location"})
        else:
            raise serializers.ValidationError({"message":"You haven't clocked-in"})       


class StartBreakSerializer(serializers.ModelSerializer):
    class Meta:
        model = Break
        fields = ['id', 'break_start_time', 'date_created', 'date_updated']

    def save(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"] = company

        location = self.context['location']
        location = Location.objects.get(id=location.id)

        employee = self.context['employee_id']
        employee = Employee.objects.get(id=employee)

        if company is not None:          
            company_profile = CompanyProfile.objects.get(company=company)
            allowed_break_duration = company_profile.break_duration
            closing_time = company_profile.closing_time
        else:
            allowed_break_duration = datetime.now().time()
            allowed_break_duration = allowed_break_duration.replace(hour=1, minute=0, second=0)

        record = Record.objects.filter(date_created__date = datetime.now().date(), employee=employee, company=company).last()
        if record is not None:
            ongoing_break = Break.objects.filter(date_created__date = datetime.now().date(), break_end_time__isnull = True,
                                                 record=record, company=company).last()
            if record.work_duration is None:            
                if location == record.location:
                    if ongoing_break:
                        raise serializers.ValidationError({"message":"You have an on-going break"})
                    elif datetime.now().time() >= closing_time:
                        raise serializers.ValidationError({"message":"You can't take a break during overtime"})
                    elif record.break_duration is None:                
                        break_instance = Break.objects.create(record = record, break_start_time=datetime.now(), company = company)
                        self.instance = break_instance
                        return self.instance
                    elif record.break_duration is not None:
                        if record.break_duration < allowed_break_duration:
                            if ongoing_break:
                                raise serializers.ValidationError({"message":"You have an on-going break"})
                            else:
                                company = self.context['company']
                                self.validated_data["company"] = company
                                break_instance = Break.objects.create(record = record, 
                                                                    break_start_time=datetime.now(), 
                                                                    company=company)
                                self.instance = break_instance
                                return self.instance
                        else:
                            raise serializers.ValidationError({"message":"Daily Break Duration exceeded"})            
                else:
                    raise serializers.ValidationError({"message":"You have to start break from your clock-in location"})
            else:
                raise serializers.ValidationError({"message":"Break can't be observed after closing"})
        else:
            raise serializers.ValidationError({"message":"You haven't clocked in"})


class EndBreakSerializer(serializers.ModelSerializer):
    class Meta:
        model = Break
        fields = ['id', 'break_end_time', 'date_created', 'date_updated']

    def save(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"] = company

        employee = self.context['employee_id']
        employee = Employee.objects.get(id=employee)

        location = self.context['location']
        location = Location.objects.get(id=location.id)

        if company is not None:
            company_profile = CompanyProfile.objects.get(company=company)
            allowed_break_duration = company_profile.break_duration
        else:
            allowed_break_duration = datetime.now().time()
            allowed_break_duration = allowed_break_duration.replace(hour=1, minute=0, second=0)
        record = Record.objects.filter(date_created__date = datetime.now().date(), employee=employee, 
                                       company=company).last()
        if record is not None:
            ongoing_break = Break.objects.filter(date_created__date = datetime.now().date(), 
                                                 break_end_time__isnull = True, record=record,
                                                 company=company).last()
            if ongoing_break:
                if location is not None and location == record.location:
                    break_start_time = ongoing_break.break_start_time
                    break_start_time_in_seconds = break_start_time.hour * 3600 + break_start_time.minute * 60 + break_start_time.second
                    break_end_time = timezone.now()
                    break_end_time_in_seconds = break_end_time.hour * 3600 + break_end_time.minute * 60 + break_end_time.second
                    total_seconds = break_end_time - break_start_time
                    current_break_duration = record.break_duration
                    
                    if current_break_duration is None:                
                        current_break_duration = total_seconds
                    elif current_break_duration is not None:
                        current_break_duration = record.break_duration + total_seconds
                    
                    if current_break_duration > allowed_break_duration:                
                        break_excess = current_break_duration - allowed_break_duration
                        total_seconds = break_excess.total_seconds()
                        hours = int(total_seconds // 3600)
                        minutes = int((total_seconds % 3600) // 60)
                        seconds = int(total_seconds % 60)
                        record.break_duration = current_break_duration
                        record.break_excess = timedelta(hours= hours, minutes=minutes, seconds=seconds)
                        record.is_break_exceeded = True
                        ongoing_break.break_end_time=break_end_time
                    else:
                        record.break_duration = current_break_duration
                        record.is_break_exceeded = False
                        ongoing_break.break_end_time=break_end_time            
                    ongoing_break.save()
                    record.save()
                    self.instance = ongoing_break
                    return self.instance
                else:
                    raise serializers.ValidationError({"message":"You have to end break from your clock-in location"})
            else:
                raise serializers.ValidationError({"message":"You don't have an on-going break"})
        else:
            raise serializers.ValidationError({"message":"You haven't clocked in"})


class StartOverTimeSerializer(serializers.ModelSerializer):
    class Meta:
        model = OverTime
        fields = ['id', 'company', 'overtime_start_time', 'date_created', 'date_updated']  

    def save(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"] = company

        employee = self.context['employee_id']
        employee = Employee.objects.get(id=employee)

        location = self.context['location']
        location = Location.objects.get(id=location.id)

        if company is not None:
            company_profile = CompanyProfile.objects.get(company=company)
            allowed_overtime_duration = company_profile.overtime_duration
        else:
            allowed_overtime_duration = datetime.now().time()
            allowed_overtime_duration = allowed_overtime_duration.replace(hour=4, minute=0, second=0)
        record = Record.objects.filter(date_created__date=datetime.now().date(), employee=employee, 
                                       company=company).last()
        if record is not None:
            ongoing_overtime = OverTime.objects.filter(date_created__date=datetime.now().date(), 
                                                       overtime_end_time__isnull = True,
                                                       record=record, company=company).last()
            current_time = datetime.now().time()
            allowed_overtime_start = company_profile.closing_time
            
            if current_time >= allowed_overtime_start:  
                if location is not None and location == record.location:
                    if ongoing_overtime:
                        raise serializers.ValidationError({"message":"overtime has started before now"})
                    elif record.overtime_duration is None:
                        overtime = OverTime.objects.create(record = record, overtime_start_time = datetime.now(), company=company)
                        self.instance = overtime
                        return self.instance
                    else:
                        raise serializers.ValidationError({"message":"You're entitled to one(1) overtime daily."})
                else:
                    raise serializers.ValidationError({"message":"You have to start overtime from your clock-in location"})
            else:
                raise serializers.ValidationError({"message":"It's not yet closing time"})
        else:
            raise serializers.ValidationError({"message":"You haven't clocked in"})      


class EndOverTimeSerializer(serializers.ModelSerializer):
    class Meta:
        model = OverTime
        fields = ['id', 'company', 'overtime_end_time', 'date_created', 'date_updated']

    def save(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"] = company
        
        employee = self.context['employee_id']
        employee = Employee.objects.get(id=employee)

        location = self.context['location']
        location = Location.objects.get(id=location.id)

        if company is not None:
            company_profile = CompanyProfile.objects.get(company=company)
            allowed_overtime_duration = company_profile.overtime_duration
        else:
            allowed_overtime_duration = datetime.now().time()
            allowed_overtime_duration = allowed_overtime_duration.replace(hour=4, minute=0, second=0)
        record = Record.objects.filter(date_created__date=datetime.now().date(), employee=employee,
                                       company=company).last()
        if record is not None:
            ongoing_overtime = OverTime.objects.filter(date_created__date=datetime.now().date(), 
                                                       overtime_end_time__isnull = True, record=record,
                                                       company=company).last()
            if ongoing_overtime:
                if location is not None and location == record.location:
                    ongoing_overtime.overtime_end_time=timezone.now()
                    overtime_duration = ongoing_overtime.overtime_end_time - ongoing_overtime.overtime_start_time
                    total_seconds = overtime_duration.total_seconds()
                    hours = int(total_seconds // 3600)
                    minutes = int((total_seconds % 3600) // 60)
                    seconds = int(total_seconds % 60)
                    if record.overtime_duration is None:
                        record.overtime_duration = timedelta(hours=0, minutes=0, seconds=0)
                    record.overtime_duration += timedelta(hours=hours, minutes=minutes, seconds=seconds)
                    record.save()
                    ongoing_overtime.save()
                    self.instance = ongoing_overtime
                    return self.instance
                else:
                    raise serializers.ValidationError({"message":"You have to end overtime from your clock-in location"})
            else:
                raise serializers.ValidationError({"message":"You don't have an on-going Overtime"})
        else:
            raise serializers.ValidationError({"message":"You haven't clocked in"})


class TaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ['id', 'company', 'assignee', 'project_name', 'start_date', 'due_date', 'task_description', 
                  'status', 'challenges', 'date_created', 'date_updated']
        
    def to_representation(self, instance):
        serializer_data = super().to_representation(instance)
        all_assignee = serializer_data["assignee"]
        all_employee = []

        for assignee in all_assignee:
            this_employee = Employee.objects.filter(id=assignee).first()
            all_employee.append({
                "employee_email": this_employee.employee_email,
                "employee_first_name": this_employee.employee_first_name
            })
        serializer_data["assignee"] = all_employee
        return  serializer_data
    
    def save(self, **kwargs):
        company = self.context['company']

        if company:
            self.validated_data['company'] = company
        employee = self.context['employee']

        if employee:
            self.validated_data['created_by'] = employee
        record = Record.objects.filter(date_created__date = datetime.now().date()).last()

        if record:
            self.validated_data['record'] = record

        if self.validated_data["assignee"]:
            assignee = self.validated_data.pop("assignee")

        if self.validated_data['due_date'] < self.validated_data['start_date']:
            raise serializers.ValidationError({"message":"due date cannot be earlier than start date"})
        
        if self.validated_data['due_date'] < timezone.now():
            raise serializers.ValidationError({"message":"due date cannot be a date earlier than today"})
        
        if self.validated_data['due_date'] < timezone.now():
            raise serializers.ValidationError({"message":"due date cannot be a date earlier than today"})
        
        task = Task.objects.create(**self.validated_data)
        
        task = task.assignee.set(assignee)
        self.validated_data["assignee"] = assignee

        self.instance = task
        return self.instance


class UpdateTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ['status', 'challenges', "priority", "achievement", "other_information", "date_updated"]

class ScreenshotSerializer(serializers.ModelSerializer):
    class Meta:
        model = Screenshot
        fields = ['id', 'company','image_url', 'timestamp']

    def save(self, **kwargs):
        company = self.context['conpany']
        record = Record.objects.filter(date_created__date = datetime.now().date(), company=company).last()

        if record:
            self.validated_data['record'] = record            
        self.validated_data['company'] = company
        screenshot=Screenshot.objects.create(**self.validated_data)
        self.instance = screenshot
        return self.instance

class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = ['id', 'company', 'branch', 'name', 'latitude', 'longitude', 'location_type', 'date_created', 'date_updated']

    def validate(self, values):
        if "location_type" not in values:
            raise serializers.ValidationError({"message":"location_type is required"})

        if values["location_type"] == "REMOTE":
            raise serializers.ValidationError({"message":"This endpoint is for onsite location"})
        return values

    def save(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"] = company

        latitude = self.validated_data['latitude']
        longitude = self.validated_data['longitude']

        existing_location = Location.objects.filter(latitude = latitude, longitude = longitude, company = company).first()

        if existing_location is not None:
            raise serializers.ValidationError({"message":f"location for {company} with current latitude and longitude exists"})
        if latitude is not None:
            self.validated_data['latitude'] = latitude
        if longitude is not None:
            self.validated_data['longitude'] = longitude
        return super().save(**kwargs)  

class CreateOnsiteLocationSerializer(serializers.Serializer):
    name = serializers.CharField(required=True)
    latitude = serializers.CharField(required=True)
    longitude = serializers.CharField(required=True)
    branch_id = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    def validate(self, attrs):
        employee = self.context.get('employee')
        company = self.context.get('company')
        branch_id = attrs.get('branch_id')
        name = attrs.get('name') 
        latitude = attrs.get('latitude')
        longitude = attrs.get('longitude')
        if branch_id:
            branch_ins = Branch.objects.filter(id=branch_id, is_active=True).first()
            if not branch_ins:
                raise serializers.ValidationError({"message": "branch does not exist"})
            else:
                location_branch = Location.objects.filter(company=company, location_type="ONSITE", branch=branch_ins, is_deleted=False).first()
                if location_branch:
                    raise serializers.ValidationError({"message": "branch already exists"})
        else:
            branch_ins = None

        

        Location.create_company_branch(
                employee=employee,
                company=company,
                location_type="ONSITE",
                name=name,
                latitude=latitude,
                longitude=longitude,
                branch=branch_ins,
            )
        return attrs



class UpdateLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = ['id', 'company', 'name', 'latitude', 'longitude', 'location_type', 'branch', 'date_updated']
        extra_kwargs = {
            'latitude':{'required':False},
            'location_type':{'required':False},
            'longitude':{'required':False},
            'branch':{'required':False},
            }

    def save(self, **validated_data):
        company = self.context['company']
        self.validated_data["company"] = company
        branch = self.context.get('branch')
        if branch:
            self.validated_data["branch"] = branch
        latitude = self.validated_data['latitude']
        longitude = self.validated_data['longitude']

        if latitude is not None:
            self.validated_data['latitude'] = latitude
        if longitude is not None:
            self.validated_data['longitude'] = longitude
        return super().save(**validated_data)   


class RemoteLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = ['id', 'company', 'employee', 'name', 'latitude', 'location_type', 'longitude', 'date_created', 'date_updated']
        
    def to_representation(self, instance):
        serializer_data = super().to_representation(instance)
        employee = serializer_data["employee"]
        this_employee = Employee.objects.filter(id=employee).first()        
        serializer_data["employee"] = this_employee.employee_email
        return  serializer_data

    def validate(self, values):
        if "location_type" not in values:
            raise serializers.ValidationError({"message":"location_type is required"})
        if values["location_type"] == "ONSITE":
            raise serializers.ValidationError({"message":"This endpoint is for remote location"})
        return values

    def save(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"] = company

        latitude = self.validated_data['latitude']
        longitude = self.validated_data['longitude']
        
        employee = self.context['employee']
        self.validated_data["employee"] = employee

        existing_location = Location.objects.filter(latitude = latitude, longitude = longitude, company = company, location_type= 'REMOTE', employee = employee).first()
        if existing_location is not None:
            raise serializers.ValidationError({"message":f"{employee}'s Remote location for {company} with current latitude and longitude exists"})

        if latitude is not None:
            self.validated_data['latitude'] = latitude
        if longitude is not None:
            self.validated_data['longitude'] = longitude
        return super().save(**kwargs)

class AttendanceSheetSerializer(serializers.ModelSerializer):
    class Meta:
        model=AttendanceSheet
        fields=['company', 'title', 'description', 'employee', 'date_created', 'date_updated']
        extra_kwargs = {
            'employee':{'required':False}
        }

    def save(self, **kwargs):
        company = self.context['company']
        self.validated_data["company"] = company

        return super().save(**kwargs)

class BranchLocationInviteSerializer(serializers.Serializer):
    name = serializers.CharField(required=True)
    branch_id = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    def validate(self, attrs):
        branch_id = attrs.get('branch_id')
        if branch_id:
            branch_ins = Branch.objects.filter(id=branch_id, is_active=True).first()
            if not branch_ins:
                raise serializers.ValidationError({"message": "branch does not exist"})
        else:
            branch_ins = None
        attrs["branch"] = branch_ins

        return attrs

class CompanyBranchLocationSerializer(serializers.Serializer):
   latitude = serializers.CharField(required=True)
   longitude = serializers.CharField(required=True)
   image = serializers.FileField(required=True, allow_null=False)

class ClockInInviteSerializer(serializers.Serializer):
    CLOCKS = [
        ("CLOCK_IN","clock_in"),
        ("START_BREAK","start_break"),
        ("END_BREAK","end_break"),
        ("CLOCK_OUT","clock_out")
    ]
    clock_type = serializers.ChoiceField(required=True, choices=CLOCKS)

class AddEmployeeBranchSerializer(serializers.Serializer):
    employees = serializers.JSONField()
    branch_id = serializers.CharField(required=True)

    def validate(self, values):
        if len(values["employees"]) < 1:
            raise serializers.ValidationError(
                {"error_code": "24", "employees": "employees cannot be empty"}
            )
        return values
    
class MapEmployeeBranchSerializer(serializers.Serializer):
    employee_id = serializers.CharField(required=True)
    location_id = serializers.CharField(required=True)

    def validate(self, values):
        employee_id = values.get("employee_id")
        location_id = values.get("location_id")
        company_id = self.context.get("company_id")
        get_employee = Employee.objects.filter(id=employee_id, company__id=company_id, is_active=True).first()
        if not get_employee:
            raise serializers.ValidationError({"error": "employee not found"})
        get_location = Location.objects.filter(id=location_id, company__id=company_id, is_deleted=False).first()
        if not get_location:
            raise serializers.ValidationError({"error": "location not found"})
        
        get_employee.employee_branch = get_location
        get_employee.save()
        values["branch_name"] = get_location.name
        return values
    
class BranchSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = ['id', 'name', 'latitude', 'longitude']

class BranchRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = Employee
        fields = [
            'id', 
            'employee', 
            ]        
    def to_representation(self, instance):
        serializer_data = super().to_representation(instance)
        today = timezone.now().date()
        employee = instance
        company = instance.company
        branch = instance.employee_branch
        get_employee_record = Record.objects.filter(
            employee=employee, 
            company=company, 
            location=branch, 
            date_created__date=today, 
            is_deleted=False).first()
        if get_employee_record:
            serializer_data["first_name"] = instance.employee_first_name
            serializer_data["last_name"] = instance.employee_last_name
            serializer_data["email"] = instance.employee_email
            serializer_data["branch_name"] = instance.employee_branch.name if instance.employee_branch else ""
            serializer_data["work_type"] = instance.work_type
            serializer_data["clock_in_time"] = get_employee_record.clock_in_time
            serializer_data["clock_in_latitude"] = get_employee_record.clock_in_latitude
            serializer_data["clock_in_longitude"] = get_employee_record.clock_in_longitude
            serializer_data["clock_out_time"] = get_employee_record.clock_out_time
            serializer_data["clock_out_longitude"] = get_employee_record.clock_out_longitude
            serializer_data["clock_out_latitude"] = get_employee_record.clock_out_latitude
            serializer_data["location_type"] = get_employee_record.location_type
            serializer_data["location"] = get_employee_record.location.name
        else:
            serializer_data["first_name"] = instance.employee_first_name
            serializer_data["last_name"] = instance.employee_last_name
            serializer_data["email"] = instance.employee_email
            serializer_data["branch_name"] = instance.employee_branch.name if instance.employee_branch else ""
            serializer_data["work_type"] = instance.work_type
            serializer_data["clock_in_time"] = ""
            serializer_data["clock_in_latitude"] = ""
            serializer_data["clock_in_longitude"] = ""
            serializer_data["clock_out_time"] = ""
            serializer_data["clock_out_longitude"] = ""
            serializer_data["clock_out_latitude"] = ""
            serializer_data["location_type"] = ""
            serializer_data["location"] = ""
        del serializer_data["employee"]
        del serializer_data["location"]
        return serializer_data
    
class AttendanceRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = Record
        fields = [
            "id", "location", "company", "employee", "clock_in_time", "clock_out_time", "date_created",
            "lateness_duration", "break_duration", "overtime_duration", "work_duration", "location"
            ]


    def to_representation(self, instance):

        serializer_data = super().to_representation(instance)

        serializer_data["clock_in"] = instance.clock_in_time
        serializer_data["clock_out"] = instance.clock_out_time
        serializer_data["date"] = instance.date_created
        serializer_data["name"] = instance.employee.full_name if instance.employee else ""
        serializer_data["company"] = instance.company.company_name if instance.company else ""
        serializer_data["lateness_duration"] = instance.lateness_duration if instance.lateness_duration else 0
        serializer_data["break_duration"] = instance.break_duration if instance.break_duration else 0
        serializer_data["overtime_duration"] = instance.overtime_duration if instance.overtime_duration else 0
        serializer_data["work_duration"] = instance.work_duration if instance.work_duration else 0
        serializer_data["location"] = instance.location.name if instance.location else ""

        return serializer_data
    
      
        
    # s_n = serializers.IntegerField()
    # id = serializers.CharField()
    # name = serializers.CharField()
    # company = serializers.CharField()
    # date = serializers.DateField()
    # location = serializers.CharField()
    # lateness_duration = serializers.CharField()
    # break_duration = serializers.CharField()
    # overtime_duration = serializers.CharField()
    # work_duration = serializers.CharField()