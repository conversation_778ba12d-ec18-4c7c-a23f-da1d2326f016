from .send_emails import send_mails

def send_bulk_emails(subject, message, sender_email, recipient_emails, sender_password):

    failed_recipients = []

    for recipient_email in recipient_emails:
        try:
            send_mails(
                subject,
                message,
                sender_email,
                [recipient_email],
                sender_password,
            )
            return (f"Email sent successfully to {recipient_email}")
        except Exception as e:
            failed_recipients.append(recipient_email)
            return (f"Failed to send email to {recipient_email}: {str(e)}")

    if failed_recipients:
        return {
            "status": "Partial success",
            "failed_recipients": failed_recipients
        }
    
    return {"status": "All emails sent successfully"}