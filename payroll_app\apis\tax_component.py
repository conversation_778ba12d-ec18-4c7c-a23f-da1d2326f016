
def tax_band(taxable_income):
    if taxable_income <= 300000:
        tax_amount = 0.07 * taxable_income
        return tax_amount
    elif taxable_income >=300001 and taxable_income <= 600000:
        first = 0.07 * 300000
        first_deduction = taxable_income - 300000
        second = 0.11 * first_deduction
        return first + second
    elif taxable_income >= 600001 and taxable_income <= 1100000:
        first = 0.07 * 300000
        first_deduction = taxable_income - 300000
        second = 0.11 * 300000
        second_deduction = first_deduction - 300000
        if second_deduction > 0:
            third = 0.15 * second_deduction
        else:
            third = 0.15 * first_deduction
        return first + second + third
    elif taxable_income >=1100001 and taxable_income <= 1600000:
        first = 0.07 * 300000
        first_deduction = taxable_income - 300000
        second = 0.11 * 300000
        second_deduction = first_deduction - 300000
        third = 0.15 * 500000
        third_deduction = second_deduction - 500000
        if third_deduction > 0:
            fourth = 0.19 * third_deduction
        else:
            fourth = 0.19 * second_deduction
        return first + second + third + fourth
    elif taxable_income >= 1600001 and taxable_income <= 3200000:
        first = 0.07 * 300000
        first_deduction = taxable_income - 300000
        second = 0.11 * 300000
        second_deduction = first_deduction - 300000
        third = 0.15 * 500000
        third_deduction = second_deduction - 500000
        fourth = 0.19 * 500000
        fourth_deduction = third_deduction - 500000
        if fourth_deduction > 0:
            fifth = 0.21 * fourth_deduction
        else:
            fifth = 0.21 * third_deduction
        return first + second + third + fourth + fifth
    else:
        first = 0.07 * 300000
        first_deduction = taxable_income - 300000
        second = 0.11 * 300000
        second_deduction = first_deduction - 300000
        third = 0.15 * 500000
        third_deduction = second_deduction - 500000
        fourth = 0.19 * 500000
        fourth_deduction = third_deduction - 500000
        fifth = 0.21 * 1600000
        fifth_deduction = fourth_deduction - 1600000
        if fifth_deduction > 0:
            sixth = 0.24 * fifth_deduction
        else:
            sixth = 0.24 * fourth_deduction
        return first + second + third + fourth + fifth + sixth
    
def custom_tax_band(taxable_income, company):
    from payroll_app.models import CompanyTaxBand
    if company:
        remaining_income = taxable_income
        total_tax = 0

        get_all_band = CompanyTaxBand.objects.filter(company=company, is_active=True).order_by("start_band")

        for band in get_all_band:
            start_band = band.start_band
            end_band = band.end_band
            rate = band.tax_rate

            # If there's a limit to this tax band, use the difference between start and end band
            if end_band:
                taxable_at_this_rate = min(end_band - start_band, remaining_income)
            else:
                taxable_at_this_rate = remaining_income

            # Calculate tax for the current band
            total_tax += taxable_at_this_rate * (rate / 100)
            remaining_income -= taxable_at_this_rate
            
            # If there's no remaining income, break out of the loop
            if remaining_income <= 0:
                break
        return total_tax  
    else:
        return 0

def consolidated_tax(employee, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, net_calculation_type):
    from payroll_app.models import SalaryComponentSettings
    from core.models import ConstantTable

    CONST = ConstantTable.get_constant_instance()
    basic_amount = 0
    housing_amount = 0
    transport_amount = 0
    try:
        employee_pension_contribution =  employee_contribution_pension_amount / 100
    except ZeroDivisionError:
        employee_pension_contribution = 0
    try:
        employer_pension_contribution = employer_contribution_pension_amount / 100
    except ZeroDivisionError:    
        employer_pension_contribution = 0
    all_salary_component = SalaryComponentSettings.objects.filter(company=employee.company, is_active=True)
    if all_salary_component:
        for salary_component in all_salary_component:
            if salary_component.salary_name=="Basic":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            basic_amount += salary_component.amount * 12

                        elif salary_component.frequency == "BI-MONTHLY":
                            basic_amount += salary_component.amount * 6
                        else:
                            basic_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                basic_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                basic_amount+= 0
                        else:
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount 
                            except ZeroDivisionError:
                                basic_amount+= 0
                        
            elif salary_component.salary_name=="Housing":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            housing_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            housing_amount += salary_component.amount * 6
                        else:
                            housing_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                housing_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                housing_amount+= 0
                        else:
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                housing_amount+= 0

            elif salary_component.salary_name=="Transport":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            transport_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            transport_amount += salary_component.amount * 6
                        else:
                            transport_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                transport_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                transport_amount+= 0
                        else:
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                transport_amount+= 0

    # print("transport amount ", transport)
    total_bht = housing_amount + basic_amount + transport_amount
    # print("total bht ", total_bht)
    
    if annual_gross > CONST.minimum_wage_amount: 
        one_percent_gross_income_or_200k = 0.01 * annual_gross
        if one_percent_gross_income_or_200k > 200000:
            amount = one_percent_gross_income_or_200k
        else:
            amount = 200000
        # print("one percentage gross income ", amount)
        twenty_percent_gross_income = 0.2 * annual_gross
        # print("twenty percent gross income", twenty_percent_gross_income)

        if net_calculation_type == "PERCENTAGE_NET":
            monthly_gross_salary = total_net_amount
        else:
            try:
                monthly_gross_salary = total_net_amount / 12
            except ZeroDivisionError:
                monthly_gross_salary = 0
       
        employee_pension_amount = employee_pension_contribution * total_bht
        # print("employee pension amount ", employee_pension_amount)
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0

        monthly_life_insurance = employee.employee_life_insurance_amount or 0
        life_insurance = monthly_life_insurance * 12
        monthly_hmo_amount = employee.employee_hmo_amount or 0
        hmo_amount = monthly_hmo_amount * 12
        # print("life insurance ", life_insurance)
        # other_deductions = 0
        # other_deductions = employee.employee_other_deductions
        ten_percent_bht = employer_pension_contribution * total_bht
        consolidated_relief_amount = amount + twenty_percent_gross_income + life_insurance + employee_pension_amount + hmo_amount
        # print("consolidated relief ", consolidated_relief_amount)
        taxable_income = annual_gross - consolidated_relief_amount
        # print("print total relief ", taxable_income)
        
        tax_amount = tax_band(taxable_income)
        # print("tax amount ", tax_amount)
        try:
            monthly_tax = tax_amount/12
        except ZeroDivisionError:
            monthly_tax = 0
        annual_gross_after_tax = annual_gross - tax_amount
        try:
            monthly_ten_percent_bht = ten_percent_bht / 12
        except ZeroDivisionError:
            monthly_ten_percent_bht = 0

        employee_voluntary_pension_amount = employee.employee_voluntary_pension_amount or 0
        total_monthly_deductions = monthly_tax + monthly_employee_pension_amount + employee_voluntary_pension_amount
        total_monthly_employee_pension_amount = monthly_employee_pension_amount + employee_voluntary_pension_amount + monthly_ten_percent_bht
        monthly_net =  monthly_gross_salary - total_monthly_deductions
        # print(employee.full_name, monthly_net, monthly_gross_salary, total_monthly_deductions, "\n\n")
 
        data = {
            "annual_tax": tax_amount,
            "monthly_tax": monthly_tax,
            "annual_gross_salary": annual_gross,
            "monthly_gross_salary": monthly_gross_salary,
            "one_percent_gross_income_or_200k": amount,
            "twenty_percent_gross_income": twenty_percent_gross_income,
            "housing_amount": housing_amount,
            "basic_amount": basic_amount,
            "transport_amount": transport_amount,
            "total_bht": total_bht,
            "pension_amount": employee_pension_amount,
            "monthly_pension_amount": monthly_employee_pension_amount,
            "non_taxable_relief": consolidated_relief_amount,
            "taxable_income": taxable_income,
            "one_percent_gross_income": one_percent_gross_income_or_200k,
            "ten_percent_bht": ten_percent_bht,
            "annual_gross_after_tax": annual_gross_after_tax,
            "total_monthly_deductions": total_monthly_deductions,
            "monthly_employer_pension_amount": monthly_ten_percent_bht,
            "total_monthly_employee_pension_amount": total_monthly_employee_pension_amount,
            "monthly_net": monthly_net,
            "employee_voluntary_pension_amount": employee_voluntary_pension_amount
        }
        return data
    else:
        tax_amount = 0
        monthly_tax = 0
        
        if net_calculation_type == "PERCENTAGE_NET":
            monthly_gross_salary = total_net_amount
        else:
            try:
                monthly_gross_salary = total_net_amount / 12
            except ZeroDivisionError:
                monthly_gross_salary = 0
        
        employee_pension_amount = employee_pension_contribution * total_bht
        # print("employee pension amount ", employee_pension_amount)
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0

        monthly_life_insurance = employee.employee_life_insurance_amount or 0
        life_insurance = monthly_life_insurance * 12
        monthly_hmo_amount = employee.employee_hmo_amount or 0
        hmo_amount = monthly_hmo_amount * 12

        ten_percent_bht = employer_pension_contribution * total_bht
    
        annual_gross_after_tax = annual_gross - tax_amount
        try:
            monthly_ten_percent_bht = ten_percent_bht / 12
        except ZeroDivisionError:
            monthly_ten_percent_bht = 0

        employee_voluntary_pension_amount = employee.employee_voluntary_pension_amount or 0
        total_monthly_deductions = monthly_tax + monthly_employee_pension_amount + employee_voluntary_pension_amount
        total_monthly_employee_pension_amount = monthly_employee_pension_amount + employee_voluntary_pension_amount + monthly_ten_percent_bht
        monthly_net =  monthly_gross_salary - total_monthly_deductions
        data = {
            "annual_tax": 0,
            "monthly_tax": 0,
            "annual_gross_salary": annual_gross,
            "monthly_gross_salary": monthly_gross_salary,
            "one_percent_gross_income_or_200k": 0,
            "twenty_percent_gross_income": 0,
            "housing_amount": housing_amount,
            "basic_amount": basic_amount,
            "transport_amount": transport_amount,
            "total_bht": 0,
            "pension_amount": employee_pension_amount,
            "monthly_pension_amount": monthly_employee_pension_amount,
            "non_taxable_relief": 0,
            "taxable_income": 0,
            "one_percent_gross_income": 0,
            "ten_percent_bht": 0,
            "annual_gross_after_tax": 0,
            "total_monthly_deductions": total_monthly_deductions,
            "monthly_employer_pension_amount": monthly_ten_percent_bht,
            "total_monthly_employee_pension_amount": total_monthly_employee_pension_amount,
            "monthly_net": monthly_net,
            "employee_voluntary_pension_amount": employee_voluntary_pension_amount
        }
        return data
    
def consolidated_tax_calculator(company, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, life_insurance, hmo, voluntary_pension, net_calculation_type):
    from payroll_app.models import SalaryComponentSettings
    from core.models import ConstantTable

    CONST = ConstantTable.get_constant_instance()
    basic_amount = 0
    housing_amount = 0
    transport_amount = 0
    try:
        employee_pension_contribution =  employee_contribution_pension_amount / 100
    except ZeroDivisionError:
        employee_pension_contribution = 0
    try:
        employer_pension_contribution = employer_contribution_pension_amount / 100
    except ZeroDivisionError:    
        employer_pension_contribution = 0
    all_salary_component = SalaryComponentSettings.objects.filter(company=company, is_active=True)
    if all_salary_component:
        for salary_component in all_salary_component:
            if salary_component.salary_name=="Basic":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            basic_amount += salary_component.amount * 12

                        elif salary_component.frequency == "BI-MONTHLY":
                            basic_amount += salary_component.amount * 6
                        else:
                            basic_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                basic_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                basic_amount+= 0
                        else:
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount 
                            except ZeroDivisionError:
                                basic_amount+= 0
                        
            elif salary_component.salary_name=="Housing":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            housing_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            housing_amount += salary_component.amount * 6
                        else:
                            housing_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                housing_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                housing_amount+= 0
                        else:
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                housing_amount+= 0

            elif salary_component.salary_name=="Transport":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            transport_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            transport_amount += salary_component.amount * 6
                        else:
                            transport_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                transport_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                transport_amount+= 0
                        else:
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                transport_amount+= 0

    # print("transport amount ", transport)
    total_bht = housing_amount + basic_amount + transport_amount
    # print("total bht ", total_bht)
    
    if annual_gross > CONST.minimum_wage_amount: 
        one_percent_gross_income_or_200k = 0.01 * annual_gross
        if one_percent_gross_income_or_200k > 200000:
            amount = one_percent_gross_income_or_200k
        else:
            amount = 200000
        # print("one percentage gross income ", amount)
        twenty_percent_gross_income = 0.2 * annual_gross
        # print("twenty percent gross income", twenty_percent_gross_income)

        if net_calculation_type == "PERCENTAGE_NET":
            monthly_gross_salary = total_net_amount
        else:
            try:
                monthly_gross_salary = total_net_amount / 12
            except ZeroDivisionError:
                monthly_gross_salary = 0
        
        employee_pension_amount = employee_pension_contribution * total_bht
        # print("employee pension amount ", employee_pension_amount)
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0

        monthly_life_insurance = life_insurance or 0
        life_insurance = monthly_life_insurance * 12
        monthly_hmo_amount = hmo or 0
        hmo_amount = monthly_hmo_amount * 12
        # print("life insurance ", life_insurance)
        # other_deductions = 0
        # other_deductions = employee.employee_other_deductions
        ten_percent_bht = employer_pension_contribution * total_bht
        consolidated_relief_amount = amount + twenty_percent_gross_income + life_insurance + employee_pension_amount + hmo_amount
        # print("consolidated relief ", consolidated_relief_amount)
        taxable_income = annual_gross - consolidated_relief_amount
        # print("print total relief ", taxable_income)
        tax_amount = tax_band(taxable_income)
        # print("tax amount ", tax_amount)
        try:
            monthly_tax = tax_amount/12
        except ZeroDivisionError:
            monthly_tax = 0
        annual_gross_after_tax = annual_gross - tax_amount
        try:
            monthly_ten_percent_bht = ten_percent_bht / 12
        except ZeroDivisionError:
            monthly_ten_percent_bht = 0

        employee_voluntary_pension_amount = voluntary_pension or 0
        total_monthly_deductions = monthly_tax + monthly_employee_pension_amount + employee_voluntary_pension_amount
        total_monthly_employee_pension_amount = monthly_employee_pension_amount + employee_voluntary_pension_amount + monthly_ten_percent_bht
        monthly_net =  monthly_gross_salary - total_monthly_deductions
        data = {
            "annual_tax": tax_amount,
            "monthly_tax": monthly_tax,
            "annual_gross_salary": annual_gross,
            "monthly_gross_salary": monthly_gross_salary,
            "one_percent_gross_income_or_200k": amount,
            "twenty_percent_gross_income": twenty_percent_gross_income,
            "housing_amount": housing_amount,
            "basic_amount": basic_amount,
            "transport_amount": transport_amount,
            "total_bht": total_bht,
            "pension_amount": employee_pension_amount,
            "monthly_pension_amount": monthly_employee_pension_amount,
            "non_taxable_relief": consolidated_relief_amount,
            "taxable_income": taxable_income,
            "one_percent_gross_income": one_percent_gross_income_or_200k,
            "ten_percent_bht": ten_percent_bht,
            "annual_gross_after_tax": annual_gross_after_tax,
            "total_monthly_deductions": total_monthly_deductions,
            "monthly_employer_pension_amount": monthly_ten_percent_bht,
            "total_monthly_employee_pension_amount": total_monthly_employee_pension_amount,
            "monthly_net": monthly_net
        }
        return data
    else:
        tax_amount = 0
        monthly_tax = 0
        
        if net_calculation_type == "PERCENTAGE_NET":
            monthly_gross_salary = total_net_amount
        else:
            try:
                monthly_gross_salary = total_net_amount / 12
            except ZeroDivisionError:
                monthly_gross_salary = 0
        
        employee_pension_amount = employee_pension_contribution * total_bht
        # print("employee pension amount ", employee_pension_amount)
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0

        monthly_life_insurance = life_insurance or 0
        life_insurance = monthly_life_insurance * 12
        monthly_hmo_amount = hmo or 0
        hmo_amount = monthly_hmo_amount * 12

        ten_percent_bht = employer_pension_contribution * total_bht
    
        annual_gross_after_tax = annual_gross - tax_amount
        try:
            monthly_ten_percent_bht = ten_percent_bht / 12
        except ZeroDivisionError:
            monthly_ten_percent_bht = 0

        employee_voluntary_pension_amount = voluntary_pension or 0
        total_monthly_deductions = monthly_tax + monthly_employee_pension_amount + employee_voluntary_pension_amount
        total_monthly_employee_pension_amount = monthly_employee_pension_amount + employee_voluntary_pension_amount + monthly_ten_percent_bht
        monthly_net =  monthly_gross_salary - total_monthly_deductions
        data = {
            "annual_tax": 0,
            "monthly_tax": 0,
            "annual_gross_salary": annual_gross,
            "monthly_gross_salary": monthly_gross_salary,
            "one_percent_gross_income_or_200k": 0,
            "twenty_percent_gross_income": 0,
            "housing_amount": housing_amount,
            "basic_amount": basic_amount,
            "transport_amount": transport_amount,
            "total_bht": 0,
            "pension_amount": employee_pension_amount,
            "monthly_pension_amount": monthly_employee_pension_amount,
            "non_taxable_relief": 0,
            "taxable_income": 0,
            "one_percent_gross_income": 0,
            "ten_percent_bht": 0,
            "annual_gross_after_tax": 0,
            "total_monthly_deductions": total_monthly_deductions,
            "monthly_employer_pension_amount": monthly_ten_percent_bht,
            "total_monthly_employee_pension_amount": total_monthly_employee_pension_amount,
            "monthly_net": monthly_net
        }
        return data
    
def get_bht_calculator(company, total_net_amount):
    from payroll_app.models import SalaryComponentSettings

    basic_amount = 0
    housing_amount = 0
    transport_amount = 0

    all_salary_component = SalaryComponentSettings.objects.filter(company=company, is_active=True)
    if all_salary_component:
        for salary_component in all_salary_component:
            if salary_component.salary_name=="Basic":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            basic_amount += salary_component.amount * 12

                        elif salary_component.frequency == "BI-MONTHLY":
                            basic_amount += salary_component.amount * 6
                        else:
                            basic_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                basic_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                basic_amount+= 0
                        else:
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount 
                            except ZeroDivisionError:
                                basic_amount+= 0
                        
            elif salary_component.salary_name=="Housing":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            housing_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            housing_amount += salary_component.amount * 6
                        else:
                            housing_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                housing_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                housing_amount+= 0
                        else:
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                housing_amount+= 0

            elif salary_component.salary_name=="Transport":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            transport_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            transport_amount += salary_component.amount * 6
                        else:
                            transport_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                transport_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                transport_amount+= 0
                        else:
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                transport_amount+= 0

    total_bht = housing_amount + basic_amount + transport_amount
    
    return total_bht

def get_bht_amount(company, total_net_amount):
    from payroll_app.models import SalaryComponentSettings

    basic_amount = 0
    housing_amount = 0
    transport_amount = 0

    all_salary_component = SalaryComponentSettings.objects.filter(company=company, is_active=True)
    if all_salary_component:
        for salary_component in all_salary_component:
            if salary_component.salary_name=="Basic":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            basic_amount += salary_component.amount * 12

                        elif salary_component.frequency == "BI-MONTHLY":
                            basic_amount += salary_component.amount * 6
                        else:
                            basic_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                basic_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                basic_amount+= 0
                        else:
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount 
                            except ZeroDivisionError:
                                basic_amount+= 0
                        
            elif salary_component.salary_name=="Housing":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            housing_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            housing_amount += salary_component.amount * 6
                        else:
                            housing_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                housing_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                housing_amount+= 0
                        else:
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                housing_amount+= 0

            elif salary_component.salary_name=="Transport":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            transport_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            transport_amount += salary_component.amount * 6
                        else:
                            transport_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                transport_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                transport_amount+= 0
                        else:
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                transport_amount+= 0

    total_bht = housing_amount + basic_amount + transport_amount
    
    return {
        "basic_amount": basic_amount,
        "housing_amount": housing_amount,
        "transport_amount": transport_amount,
        "total_bht": total_bht
    }
   
    
def custom_consolidated_tax(employee, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, deduction_component_amount, net_calculation_type):
    from payroll_app.models import SalaryComponentSettings
    from core.models import ConstantTable

    CONST = ConstantTable.get_constant_instance()
    try:
        employee_pension_contribution =  employee_contribution_pension_amount / 100
    except ZeroDivisionError:
        employee_pension_contribution = 0
    try:
        employer_pension_contribution = employer_contribution_pension_amount / 100
    except ZeroDivisionError:    
        employer_pension_contribution = 0

    all_salary_component = SalaryComponentSettings.objects.filter(company=employee.company, is_active=True)
    
    basic_amount = 0
    housing_amount = 0
    transport_amount = 0
    try:
        employee_pension_contribution =  employee_contribution_pension_amount / 100
    except ZeroDivisionError:
        employee_pension_contribution = 0
    try:
        employer_pension_contribution = employer_contribution_pension_amount / 100
    except ZeroDivisionError:    
        employer_pension_contribution = 0
    all_salary_component = SalaryComponentSettings.objects.filter(company=employee.company, is_active=True)
    if all_salary_component:
        for salary_component in all_salary_component:
            if salary_component.salary_name=="Basic":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            basic_amount += salary_component.amount * 12

                        elif salary_component.frequency == "BI-MONTHLY":
                            basic_amount += salary_component.amount * 6
                        else:
                            basic_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                basic_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                basic_amount+= 0
                        else:
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount 
                            except ZeroDivisionError:
                                basic_amount+= 0
                        
            elif salary_component.salary_name=="Housing":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            housing_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            housing_amount += salary_component.amount * 6
                        else:
                            housing_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                housing_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                housing_amount+= 0
                        else:
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                housing_amount+= 0

            elif salary_component.salary_name=="Transport":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            transport_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            transport_amount += salary_component.amount * 6
                        else:
                            transport_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                transport_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                transport_amount+= 0
                        else:
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                transport_amount+= 0

    total_bht = housing_amount + basic_amount + transport_amount
    # print("total bht ", total_bht)
    if annual_gross > CONST.minimum_wage_amount:

        if net_calculation_type == "PERCENTAGE_NET":
            monthly_gross_salary = total_net_amount
        else:
            try:
                monthly_gross_salary = total_net_amount / 12
            except ZeroDivisionError:
                monthly_gross_salary = 0

        employee_pension_amount = employee_pension_contribution * total_bht
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0

        # print("employee pension amount ", employee_pension_amount)
        monthly_life_insurance = employee.employee_life_insurance_amount or 0
        life_insurance = monthly_life_insurance * 12
        monthly_hmo_amount = employee.employee_hmo_amount or 0
        hmo_amount = monthly_hmo_amount * 12

        # print("life insurance ", life_insurance)
        # other_deductions = 0
        # other_deductions = employee.employee_other_deductions
        ten_percent_bht = employer_pension_contribution * total_bht
        consolidated_relief_amount = deduction_component_amount + life_insurance + employee_pension_amount + hmo_amount
        # print("consolidated relief ", consolidated_relief_amount)
        taxable_income = annual_gross - consolidated_relief_amount
        tax_amount = custom_tax_band(taxable_income, employee.company)
        try:
            monthly_tax = tax_amount/12
        except ZeroDivisionError:
            monthly_tax = 0

        annual_gross_after_tax = annual_gross - tax_amount
        try:
            monthly_ten_percent_bht = ten_percent_bht / 12
        except ZeroDivisionError:
            monthly_ten_percent_bht = 0

        employee_voluntary_pension_amount = employee.employee_voluntary_pension_amount or 0
        total_monthly_deductions = monthly_tax + monthly_employee_pension_amount + employee_voluntary_pension_amount
        total_monthly_employee_pension_amount = monthly_employee_pension_amount + employee_voluntary_pension_amount + monthly_ten_percent_bht
        monthly_net =  monthly_gross_salary - total_monthly_deductions

        data = {
            "annual_tax": tax_amount,
            "monthly_tax": monthly_tax,
            "annual_gross_salary": annual_gross,
            "monthly_gross_salary": monthly_gross_salary,
            "one_percent_gross_income_or_200k": 0,
            "twenty_percent_gross_income": 0,
            "housing_amount": housing_amount,
            "basic_amount": basic_amount,
            "transport_amount": transport_amount,
            "total_bht": total_bht,
            "pension_amount": employee_pension_amount,
            "monthly_pension_amount": monthly_employee_pension_amount,
            "non_taxable_relief": consolidated_relief_amount,
            "taxable_income": taxable_income,
            "one_percent_gross_income": 0,
            "ten_percent_bht": ten_percent_bht,
            "annual_gross_after_tax": annual_gross_after_tax,
            "total_monthly_deductions": total_monthly_deductions,
            "monthly_employer_pension_amount": monthly_ten_percent_bht,
            "total_monthly_employee_pension_amount": total_monthly_employee_pension_amount,
            "monthly_net": monthly_net,
            "employee_voluntary_pension_amount": employee_voluntary_pension_amount
        }
        return data
    else:
        tax_amount = 0
        monthly_tax = 0
          
        if net_calculation_type == "PERCENTAGE_NET":
            monthly_gross_salary = total_net_amount
        else:
            try:
                monthly_gross_salary = total_net_amount / 12
            except ZeroDivisionError:
                monthly_gross_salary = 0
        
        employee_pension_amount = employee_pension_contribution * total_bht
        # print("employee pension amount ", employee_pension_amount)
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0

        monthly_life_insurance = employee.employee_life_insurance_amount or 0
        life_insurance = monthly_life_insurance * 12
        monthly_hmo_amount = employee.employee_hmo_amount or 0
        hmo_amount = monthly_hmo_amount * 12

        ten_percent_bht = employer_pension_contribution * total_bht
    
        annual_gross_after_tax = annual_gross - tax_amount
        try:
            monthly_ten_percent_bht = ten_percent_bht / 12
        except ZeroDivisionError:
            monthly_ten_percent_bht = 0

        employee_voluntary_pension_amount = employee.employee_voluntary_pension_amount or 0
        total_monthly_deductions = monthly_tax + monthly_employee_pension_amount + employee_voluntary_pension_amount
        total_monthly_employee_pension_amount = monthly_employee_pension_amount + employee_voluntary_pension_amount + monthly_ten_percent_bht
        monthly_net =  monthly_gross_salary - total_monthly_deductions
        data = {
            "annual_tax": 0,
            "monthly_tax": 0,
            "annual_gross_salary": annual_gross,
            "monthly_gross_salary": monthly_gross_salary,
            "one_percent_gross_income_or_200k": 0,
            "twenty_percent_gross_income": 0,
            "housing_amount": housing_amount,
            "basic_amount": basic_amount,
            "transport_amount": transport_amount,
            "total_bht": 0,
            "pension_amount": employee_pension_amount,
            "monthly_pension_amount": monthly_employee_pension_amount,
            "non_taxable_relief": 0,
            "taxable_income": 0,
            "one_percent_gross_income": 0,
            "ten_percent_bht": 0,
            "annual_gross_after_tax": 0,
            "total_monthly_deductions": total_monthly_deductions,
            "monthly_employer_pension_amount": monthly_ten_percent_bht,
            "total_monthly_employee_pension_amount": total_monthly_employee_pension_amount,
            "monthly_net": monthly_net,
            "employee_voluntary_pension_amount": employee_voluntary_pension_amount
        }
        return data
    
def custom_consolidated_tax_calculator(company, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, deduction_component_amount, life_insurance, hmo, voluntary_pension, net_calculation_type):
    from payroll_app.models import SalaryComponentSettings
    from core.models import ConstantTable

    CONST = ConstantTable.get_constant_instance()
    try:
        employee_pension_contribution =  employee_contribution_pension_amount / 100
    except ZeroDivisionError:
        employee_pension_contribution = 0
    try:
        employer_pension_contribution = employer_contribution_pension_amount / 100
    except ZeroDivisionError:    
        employer_pension_contribution = 0

    all_salary_component = SalaryComponentSettings.objects.filter(company=company, is_active=True)
    
    basic_amount = 0
    housing_amount = 0
    transport_amount = 0
    try:
        employee_pension_contribution =  employee_contribution_pension_amount / 100
    except ZeroDivisionError:
        employee_pension_contribution = 0
    try:
        employer_pension_contribution = employer_contribution_pension_amount / 100
    except ZeroDivisionError:    
        employer_pension_contribution = 0
    all_salary_component = SalaryComponentSettings.objects.filter(company=company, is_active=True)
    if all_salary_component:
        for salary_component in all_salary_component:
            if salary_component.salary_name=="Basic":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            basic_amount += salary_component.amount * 12

                        elif salary_component.frequency == "BI-MONTHLY":
                            basic_amount += salary_component.amount * 6
                        else:
                            basic_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                basic_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                basic_amount+= 0
                        else:
                            try:
                                basic_amount+= (salary_component.amount/100) * total_net_amount 
                            except ZeroDivisionError:
                                basic_amount+= 0
                        
            elif salary_component.salary_name=="Housing":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            housing_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            housing_amount += salary_component.amount * 6
                        else:
                            housing_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                housing_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                housing_amount+= 0
                        else:
                            try:
                                housing_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                housing_amount+= 0

            elif salary_component.salary_name=="Transport":
                if salary_component.amount > 0:
                    if salary_component.calculation_type == "FIXED_AMOUNT":
                        if salary_component.frequency == "MONTHLY":
                            transport_amount += salary_component.amount * 12
                        elif salary_component.frequency == "BI-MONTHLY":
                            transport_amount += salary_component.amount * 6
                        else:
                            transport_amount += salary_component.amount
                    elif salary_component.calculation_type == "PERCENTAGE_NET":
                        if salary_component.frequency == "MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 12
                            except ZeroDivisionError:
                                transport_amount+= 0
                        elif salary_component.frequency == "BI-MONTHLY":
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount * 6
                            except ZeroDivisionError:
                                transport_amount+= 0
                        else:
                            try:
                                transport_amount+= (salary_component.amount/100) * total_net_amount
                            except ZeroDivisionError:
                                transport_amount+= 0

    total_bht = housing_amount + basic_amount + transport_amount
    # print("total bht ", total_bht)
    if annual_gross > CONST.minimum_wage_amount:
        
        if net_calculation_type == "PERCENTAGE_NET":
            monthly_gross_salary = total_net_amount
        else:
            try:
                monthly_gross_salary = total_net_amount / 12
            except ZeroDivisionError:
                monthly_gross_salary = 0
        
        employee_pension_amount = employee_pension_contribution * total_bht
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0

        # print("employee pension amount ", employee_pension_amount)
        monthly_life_insurance = life_insurance or 0
        life_insurance = monthly_life_insurance * 12
        monthly_hmo_amount = hmo or 0
        hmo_amount = monthly_hmo_amount * 12

        # print("life insurance ", life_insurance)
        # other_deductions = 0
        # other_deductions = employee.employee_other_deductions
        ten_percent_bht = employer_pension_contribution * total_bht
        consolidated_relief_amount = deduction_component_amount + life_insurance + employee_pension_amount + hmo_amount
        # print("consolidated relief ", consolidated_relief_amount)
        taxable_income = annual_gross - consolidated_relief_amount
        tax_amount = custom_tax_band(taxable_income, company)
        try:
            monthly_tax = tax_amount/12
        except ZeroDivisionError:
            monthly_tax = 0

        annual_gross_after_tax = annual_gross - tax_amount
        try:
            monthly_ten_percent_bht = ten_percent_bht / 12
        except ZeroDivisionError:
            monthly_ten_percent_bht = 0

        employee_voluntary_pension_amount = voluntary_pension or 0
        total_monthly_deductions = monthly_tax + monthly_employee_pension_amount + employee_voluntary_pension_amount
        total_monthly_employee_pension_amount = monthly_employee_pension_amount + employee_voluntary_pension_amount + monthly_ten_percent_bht
        monthly_net =  monthly_gross_salary - total_monthly_deductions

        data = {
            "annual_tax": tax_amount,
            "monthly_tax": monthly_tax,
            "annual_gross_salary": annual_gross,
            "monthly_gross_salary": monthly_gross_salary,
            "one_percent_gross_income_or_200k": 0,
            "twenty_percent_gross_income": 0,
            "housing_amount": housing_amount,
            "basic_amount": basic_amount,
            "transport_amount": transport_amount,
            "total_bht": total_bht,
            "pension_amount": employee_pension_amount,
            "monthly_pension_amount": monthly_employee_pension_amount,
            "non_taxable_relief": consolidated_relief_amount,
            "taxable_income": taxable_income,
            "one_percent_gross_income": 0,
            "ten_percent_bht": ten_percent_bht,
            "annual_gross_after_tax": annual_gross_after_tax,
            "total_monthly_deductions": total_monthly_deductions,
            "monthly_employer_pension_amount": monthly_ten_percent_bht,
            "total_monthly_employee_pension_amount": total_monthly_employee_pension_amount,
            "monthly_net": monthly_net
        }
        return data
    else:
        tax_amount = 0
        monthly_tax = 0
       
                
        if net_calculation_type == "PERCENTAGE_NET":
            monthly_gross_salary = total_net_amount
        else:
            try:
                monthly_gross_salary = total_net_amount / 12
            except ZeroDivisionError:
                monthly_gross_salary = 0
        
        employee_pension_amount = employee_pension_contribution * total_bht
        # print("employee pension amount ", employee_pension_amount)
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0

        monthly_life_insurance = life_insurance or 0
        life_insurance = monthly_life_insurance * 12
        monthly_hmo_amount = hmo or 0
        hmo_amount = monthly_hmo_amount * 12

        ten_percent_bht = employer_pension_contribution * total_bht
    
        annual_gross_after_tax = annual_gross - tax_amount
        try:
            monthly_ten_percent_bht = ten_percent_bht / 12
        except ZeroDivisionError:
            monthly_ten_percent_bht = 0

        employee_voluntary_pension_amount = voluntary_pension or 0
        total_monthly_deductions = monthly_tax + monthly_employee_pension_amount + employee_voluntary_pension_amount
        total_monthly_employee_pension_amount = monthly_employee_pension_amount + employee_voluntary_pension_amount + monthly_ten_percent_bht
        monthly_net =  monthly_gross_salary - total_monthly_deductions
        data = {
            "annual_tax": 0,
            "monthly_tax": 0,
            "annual_gross_salary": annual_gross,
            "monthly_gross_salary": monthly_gross_salary,
            "one_percent_gross_income_or_200k": 0,
            "twenty_percent_gross_income": 0,
            "housing_amount": housing_amount,
            "basic_amount": basic_amount,
            "transport_amount": transport_amount,
            "total_bht": 0,
            "pension_amount": employee_pension_amount,
            "monthly_pension_amount": monthly_employee_pension_amount,
            "non_taxable_relief": 0,
            "taxable_income": 0,
            "one_percent_gross_income": 0,
            "ten_percent_bht": 0,
            "annual_gross_after_tax": 0,
            "total_monthly_deductions": total_monthly_deductions,
            "monthly_employer_pension_amount": monthly_ten_percent_bht,
            "total_monthly_employee_pension_amount": total_monthly_employee_pension_amount,
            "monthly_net": monthly_net
        }
        return data