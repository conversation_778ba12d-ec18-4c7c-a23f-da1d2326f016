from django.shortcuts import render
from django.utils import timezone
import calendar
# Create your views here.
from django.conf import settings
from django.contrib.auth import get_user_model
# Create your views here.
from django.db import transaction
from django.db.models import Count, <PERSON>, Sum, Max
from django.db.models import Q, Subquery, OuterRef
from django.db.models.functions import Lower, TruncMonth
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status, generics, filters
from rest_framework.filters import SearchFilter
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from account.models import AccountSystem, Wallet

from core.auth.custom_auth import CustomUserAuthentication
from finance_system.models import AccountType, ChartOfAccount, CompanyManualBank, Journal
from finance_system.serializers import AddChartOfAccountSerializer, AddCompanyManualBankSerializer, AddJournalSerializer, AllAccountTypeSerializer, AllChartOfAccountSerializer, AllCompanyManualBankSerializer, AllJournalSerializer, EditCompanyManualBankSerializer
from payroll_app.permissions import AdminPermission, EmployeeIsActive, UserIsActive
from requisition.models import Company
# Create your views here.

class CreateBankDetailAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = CompanyManualBank.objects.filter(
            company__id=company_uuid, is_deleted=False).order_by("-created_at")
        if instance:
            serializer = AllCompanyManualBankSerializer(instance, many=True)
            response = {
                "message":"successful",
                "data": serializer.data
                }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "message": "pension fund unavailable",
                "data": []
            }
            return Response(response, status=status.HTTP_200_OK)
        
    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = AddCompanyManualBankSerializer(data=request.data, context={'company_uuid': company_uuid})
        company_ins = Company.objects.get(id=company_uuid)
        serializer.is_valid(raise_exception=True)

        account_name = serializer.validated_data.get("account_name")
        account_number = serializer.validated_data.get("account_number")
        bank_name = serializer.validated_data.get("bank_name")
        bank_code = serializer.validated_data.get("bank_code")
        
        CompanyManualBank.objects.create(
            account_name=account_name,
            account_number=account_number,
            bank_name=bank_name,
            bank_code=bank_code,
            company=company_ins,
            company_owner=company_ins.user,
            added_by = request.user
        )
        response = {
            "message": "Account added successfully"
        }
        return Response(response, status=status.HTTP_200_OK)
        
    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        account_id = request.query_params.get("account_id")
        instance = CompanyManualBank.objects.filter(id=account_id, company__id=company_uuid, is_deleted=False).last()
        if instance:
            serializer = EditCompanyManualBankSerializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["edited_by"]=request.user
            serializer.save()
            response = {
                "message":"Account updated successfully",
                "data": serializer.data
                }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "message": "Account unavailable"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        
    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        account_id = request.query_params.get("account_id")
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyManualBank.objects.filter(id=account_id, company__id=company_uuid, is_deleted=False).last()
        if instance:
            instance.is_deleted=True
            instance.save()
            response = {
                "message":"Account deleted successfully",
                }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "message": "Account unavailable"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

class GlobalWalletBalanceAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_ins = Company.objects.get(id=company_uuid)

        try:
            aggregate_balance = 0
            account_ins = AccountSystem.objects.get(company=company_ins)
            for account in account_ins:
                wallet_balance = Wallet.get_wallet_balance(company_ins.user, account)
                aggregate_balance += wallet_balance
        except AccountSystem.DoesNotExist:
            aggregate_balance = 0

        data = {
            "aggregate_balance": aggregate_balance,
        }
        return Response(data, status=status.HTTP_200_OK)
    
class GetAllWalletDetailsAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_ins = Company.objects.get(id=company_uuid)

        wallets = []
        try:
            aggregate_balance = 0
            account_ins = AccountSystem.objects.get(company=company_ins)
            for account in account_ins:
                wallet_balance = Wallet.get_wallet_balance(company_ins.user, account)
                wallets.append({
                    "wallet_name": account.wallet_type,
                    "wallet_balance": wallet_balance
                })
                aggregate_balance += wallet_balance
        except AccountSystem.DoesNotExist:
            aggregate_balance = 0

        data = {
            "wallets": wallets,
        }
        return Response(data, status=status.HTTP_200_OK)
    
class JournalAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = Journal.objects.filter(company__id=company_uuid).order_by("-journal_date")
        if instance:
            serializer = AllJournalSerializer(instance, many=True)
            response = {
                "message":"successful",
                "data": serializer.data
                }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "message": "journal unavailable",
                "data": []
            }
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = AddJournalSerializer(data=request.data, context={'company_uuid': company_uuid})
        serializer.is_valid(raise_exception=True)

        company_ins = Company.objects.get(id=company_uuid)

        journal_data = serializer.validated_data.get("journal_data")
        for journal in journal_data:
            journal_name = journal.get("journal_name")
            journal_debit_amount = journal.get("journal_debit_amount")
            journal_credit_amount = journal.get("journal_credit_amount")
            journal_description = journal.get("journal_description")
            journal_reference_no = journal.get("journal_reference_no")
            journal_date = journal.get("journal_date")
        
            journal_ins = Journal.objects.create(
                journal_name=journal_name, 
                journal_debit_amount=journal_debit_amount, 
                journal_credit_amount=journal_credit_amount,
                journal_description=journal_description,
                journal_reference_no=journal_reference_no,
                journal_date=journal_date,
                company_owner=company_ins.user,
                company=company_ins,
                added_by=request.user
            )
            
        response = {
            "message": "Journal added successfully"
        }
        return Response(response, status=status.HTTP_200_OK)
    
class AccountTypeModuleAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        instance = AccountType.objects.filter(is_active=True).order_by("account_type_name")
        if instance:
            serializer = AllAccountTypeSerializer(instance, many=True)
            response = {
                "message":"successful",
                "data": serializer.data
                }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "message": "account type unavailable",
                "data": []
            }
            return Response(response, status=status.HTTP_200_OK)
        
class ChartOfAccountAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication] 
    def get(self, request):
        instance = ChartOfAccount.objects.filter(iis_deleted=True).order_by("-created_at")
        if instance:
            serializer = AllChartOfAccountSerializer(instance, many=True)
            response = {
                "message":"successful",
                "data": serializer.data
                }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "message": "chart of account unavailable",
                "data": []
            }
            return Response(response, status=status.HTTP_200_OK)
      
    def post(self, request):
        serializer = AddChartOfAccountSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        chart_of_account_name = serializer.validated_data.get("chart_of_account_name")
        chart_of_account_description = serializer.validated_data.get("chart_of_account_description")
        account_type_id = serializer.validated_data.get("account_type_id")

        account_type_ins = AccountType.objects.filter(
            id=account_type_id, is_active=True
            ).first()
        
        if chart_of_account_description:
            description = None
        else:
            description = chart_of_account_description

        chart_of_account_ins = ChartOfAccount.objects.create(
            chart_of_account_name=chart_of_account_name,
            account_type=account_type_ins,
            chart_of_account_description=description,
            added_by=request.user
        )
        
        response = {
            "message": "Chart of Account added successfully"
        }
        return Response(response, status=status.HTTP_200_OK)