from django.urls import include, path

from invoicing import views
from sales_app.views import CustomerAPIView


# Create your urls pattern(s) here.
branch_urls = [
    path("branch_analytics", views.BranchAnalyticsAPIView.as_view()),
]

client_urls = [
    path("clients/", CustomerAPIView.as_view()),
]

company_urls = [
    path("company_summary", views.CompanySummaryAPIView.as_view()),
    path("company_branch_details", views.CompanyBranchDetailsAPIView.as_view()),
    path("company_analytics", views.CompanyAnalyticsAPIView.as_view()),
    path("company_branch_comparison", views.CompanyBranchComparisonAPIView.as_view()),
]

invoices_urls = [
    path("invoices/", views.InvoiceAPIView.as_view()),
    path("invoices/items", views.InvoiceItemsAPIView.as_view()),
]

tax_urls = [
    path("taxes/", views.TaxAPIView.as_view()),
]


urlpatterns = [
    path("", include(branch_urls)),
    path("", include(client_urls)),
    path("", include(company_urls)),
    path("", include(invoices_urls)),
    path("", include(tax_urls)),
]
