from django.urls import path

from payroll_app import views

transaction = [
    path("payroll_history/", views.PayrollHistoryAPIView.as_view()),
    path("transaction_history/", views.TransactionHistory.as_view()),
    path("company_transaction_history/", views.CompanyTransactionHistory.as_view()),
    path("wallet_history/", views.WalletHistoryAPIView.as_view()),
    path("company_wallet_history/", views.CompanyWalletHistoryAPIView.as_view()),
    path("group_payroll_history/", views.EmployerGroupPayrollHistoryAPIView.as_view()),
    path("payroll_transaction_history/", views.PayrollTransactionHistory.as_view()),
    path("pension_transaction_history/", views.PensionTransactionHistory.as_view()),

]

payroll = [
    path("run_payroll/", views.RunPayrollAPIVIew.as_view()),
    path("run_multiple_payroll/", views.RunMultiplePayrollAPIVIew.as_view()), #run multiple payroll
    path("get_payroll_approval_list/", views.GetApprovePayrollAPIView.as_view()),
    path("get_multiple_payroll_approval_list/", views.GetMultipleApprovePayrollAPIView.as_view()),
    path("edit_del_approval/", views.GetEditDelApprovePayrollAPIVIew.as_view()),
    path("get_payroll_disburse_list/", views.GetDisbursePayrollAPIView.as_view()),
    path("get_multiple_payroll_disburse_list/", views.GetMultipleDisbursePayrollAPIView.as_view()),
    path("edit_del_disburse/", views.GetEditDelDisbursePayrollAPIVIew.as_view()),
    path("run_payroll_approval/", views.RunApprovalPayrollAPIVIew.as_view()),
    path("run_multiple_payroll_approval/", views.RunMultipleApprovalPayrollAPIView.as_view()),
    path("payroll_status/", views.PayrollTypeAPIView.as_view()),
    path("payroll_summary/", views.CompanyPayrollSummaryAPIView.as_view()),
    path("all_payroll/", views.AllPayrollDataAPIView.as_view()),
    path("omit_many/", views.OmitManyAPIView.as_view()),
    path("omit_to_disburse_many/", views.OmitDisburseManyAPIView.as_view()),
    path("disburse-payroll/", views.DisbursePayrollAPIView.as_view()),
    path("disburse-payroll-record/", views.DisbursePayrollRecordAPIView.as_view()),
    path("reject_running_payroll/", views.RejectRunningPayrollAPIView.as_view()),
    path("apply_payroll_changes/", views.ApplyDeductionBonusAPIView.as_view()),
    path("run_selected_payroll/", views.RunSelectedPayrollAPIVIew.as_view()),
    path("add_employee_existing_payroll/", views.AddEmployeeExistingPayrollAPIView.as_view()),
    path("add_bonus_payroll/", views.AddBonusExistingPayrollAPIView.as_view()),
    path("all_multiple_payroll/", views.AllMultiplePayrollAPIView.as_view()),
]
    
employee = [
    path("create_employee/", views.CreateEmployeeAPIView.as_view()),
    path("employee-permission/", views.EmployeePermissionsAPIView.as_view()),
    path("company_employee_list/", views.CompanyEmployeeListAPIView.as_view()),
    path("company_employees/", views.CompanyEmployeesAPIView.as_view()),
    path("company_employees_data/", views.CompanyEmployeesDataAPIView.as_view()),
    path("manage_employees_permission_list/", views.CompanyPermissionEmployeesAPIView.as_view()),
    path("company_employees_off_board/", views.CompanyEmployeesOffBoardAPIView.as_view()),
    path("view_edit_employee_profile/", views.EmployeeProfileAPIView.as_view()),
    path("user_payslip/", views.UserPaySlipDataAPIView.as_view()),
    path("employee_dashboard/", views.EmployeeDashboardAPIView.as_view()),
    path("announcement_event/", views.AnnouncementsAPIView.as_view()),
    path("employee_instant_wage_overview/", views.EmployeeInstantWageDashboardAPIView.as_view()),
    path("employee_onboarding/", views.EmployeeOnboardingAPIView.as_view()),
    path("view_edit_employee_family_contact/", views.EmployeeFamilyContactAPIView.as_view()),
    path("view_edit_employee_account_details/", views.EmployeeAccountDetailsAPIView.as_view()),
    path("view_edit_employee_education_details/", views.EmployeeEducationDetailsAPIView.as_view()),
    path("view_edit_employee_experience_details/", views.EmployeeExperienceDetailsAPIView.as_view()),
    path("view_edit_employee_certifications/", views.EmployeeCertificationsAPIView.as_view()),
    path("re_invite_employee/", views.ReInviteEmployeeAPIView.as_view()),
    path("invite_employees/", views.CreateEmployeeByInviteAPIView.as_view()),
    path("new_employee_approval/", views.EmployeeInformationApproval.as_view()),
    path("invited_employee_list/", views.InvitedEmployeeListAPIView.as_view()),
    path("get_employee_percent/", views.GetEmployeeDataPercentAPIView.as_view()),
    path("edit_employee_profile/", views.EditEmployeeProfileAPIView.as_view()),
    path("add_employee_pfa/", views.AddEmployeePensionFundAdminAPIView.as_view()),
    path("add_employee_template/", views.AddEmployeeTemplateAPIView.as_view()),
    path("employee_invite_onboarding/", views.EmployeeInviteOnboardingAPIView.as_view()),
    path("employee_verify_utility_bill/", views.EmployeeVerifyUtilityBillAPIView.as_view()),
    path("send_guarantor_verification_message/", views.EmployeeSendGuarantorVerificationAPIView.as_view()),
    path("employee_guarantor_verification/", views.GuarantorVerifyUtilityBillAPIView.as_view()),
    path("update_verification_detail/", views.EmployeeVerificationAPIView.as_view()),

]

company_payroll_dashboard = [
    path("company_chart/", views.CompanyChartAPIView.as_view()),
    path("dashboard_data/", views.DashBoardDataAPIView.as_view()),
    path("payroll_spread/", views.PayrollSpreadAPIView.as_view()),
    path("company_employment_trend_chart/", views.CompanyEmploymentTrendChartAPIView.as_view()),
    path("employee_work_force/", views.EmployeeWorkForceAPIView.as_view()),
    path("payroll_spread_month/", views.PayrollSpreadByMonthAPIView.as_view()),
    path("payroll_spread_range/", views.PayrollSpreadByRangeAPIView.as_view()),
    path("company_list/", views.CompanyList.as_view()),
    path("company_list_dashboard/", views.CompanyListDashboard.as_view()),
    path("employee_dashboard_metric/", views.EmployeeMetricsAPIView.as_view()),
    path("employee_department_metric/", views.EmployeeDepartmentMetricAPIView.as_view()),
]

account = [
    path("create_payroll_pin/", views.CreatePayrollPinAPIView.as_view()),
    path("change_payroll_pin/", views.PayrollPinUpdateAPIView.as_view()),
    path("user_role/", views.UserTypeAPIView.as_view()),
    path("check_user/", views.CheckUserAPIView.as_view()),
]

instant_wage = [
    path("employee_request_instant_wage/", views.EmployeeInstantWageRequestAPIView.as_view()),
    path("all_instant_wage_request/", views.AllEmployeeInstantWageRequest.as_view()),
    path("accept_reject_instant_wage_request/", views.AcceptRejectInstantWageRequestAPIView.as_view()),
    path("accept_all_employee_instant_wage_request/", views.AcceptAllInstantWageRequestAPIView.as_view()),
    path("instant_wage_overview/", views.InstantWageOverview.as_view()),
    path("instant_wage_history/", views.InstantWageHistoryAPIView.as_view()),
]

pay_out = [
    path("payout/", views.PayOutAPIView.as_view()),
    path("company_payout/", views.CompanyPayOutAPIView.as_view()),
    path("instant_wage_withdraw/", views.InstantWageWithdrawAPIView.as_view()),
]

admin_settings = [
    path("admin_turn_on_off_instant_wage/", views.TurnOnOffInstantWageForCompanyEmployee.as_view()),
    path("fix_payroll_table_date/", views.FixALLPayrollDate.as_view()),
    path("fix_payroll_table_name/", views.FixALLPayrollNameAPIView.as_view()),
    path("fix_payroll_table_name_by_company/", views.FixALLPayrollNameByCompanyAPIView.as_view()),
    path("fix_employee_name_title/", views.FixEmployeeNameTitleAPIView.as_view()),
    path("multiple_employee_work_type/", views.AddEmployeeWorkTypeAPIView.as_view()),
    path("multiple_employee_staff_type/", views.AddEmployeeStaffTypeAPIView.as_view()),
    path("edit_employee_payroll_savings/", views.EditEmployeePayrollSavingsAPIView.as_view()),
    path("manual_send_automate_pension/", views.ManualCompanyPensionAPIView.as_view()),
    path("manual_send_automate_pension_no_payment/", views.ManualCompanyPensionNoPaymentAPIView.as_view()),
]

one_click = [
    path("create_edit_del_beneficiary/", views.CreateEditDelBeneficiary.as_view()),
    path("one_click_send_money/", views.OneClickSendMoneyAPIView.as_view()),
    path("one_click_history/", views.OneClickHistoryAPIView.as_view()),
]

payroll_settings = [
    path("payroll_settings/", views.PayrollSettingsAPIView.as_view()),
    path("pension_settings/", views.PayrollSettingsAPIView.as_view()),
    path("edit_payroll_settings/", views.EditPayrollSettingsAPIView.as_view()),
    path("edit_pension_settings/", views.EditPensionSettingsAPIView.as_view()),
    path("pay_group_settings/", views.PayGroupAPIView.as_view()),
    path("pay_grade_settings/", views.PayGradeAPIView.as_view()),
    path("other_deductions/", views.DeductionAPIView.as_view()),
    path("department_settings/", views.DepartmentAPIView.as_view()),
    path("create_department/", views.CreateDepartmentAPIView.as_view()),
    path("edit_department/", views.EditDepartmentAPIView.as_view()),
    path("delete_department/", views.DeleteDepartmentAPIView.as_view()),
    path("salary_components/", views.SalaryComponentAPIView.as_view()),
    path("custom_components/", views.CustomComponentAPIView.as_view()),
    path("benefit_components/", views.BenefitComponentAPIView.as_view()),
    path("company_report_sheet/", views.CompanyReportSheet.as_view()),
    path("add_employee_department/", views.AddEmployeeDepartmentAPIView.as_view()),
    path("add_employee_department_role/", views.AddEmployeeDepartmentRoleAPIView.as_view()),
    path("remove_multiple_employee_department/", views.RemoveMultipleEmployeeDepartmentAPIView.as_view()),
    path("remove_multiple_employee_department_role/", views.RemoveMultipleEmployeeDepartmentRoleAPIView.as_view()),
    path("get_tax_band/", views.GetTaxBandAPIView.as_view()),
    path("add_tax_band/", views.AddTaxBandAPIView.as_view()),
    path("edit_tax_band/", views.EditTaxBandAPIView.as_view()),
    path("del_tax_band/", views.DeleteTaxBandAPIView.as_view()),
    path("pfa_settings/", views.PensionFundAdminAPIView.as_view()),
    path("employee_pfa_settings/", views.AdminEmployeePensionFundAdminAPIView.as_view()),
    path("pfa_module/", views.ModulePensionFundAdminAPIView.as_view()),
    path("employee_pfa_list/", views.EmployeePFAListAPIView.as_view()),
    path("get_pfa_employee_list/", views.AllPFAEmployeeListAPIView.as_view()),
    path("get_employee_department_list/", views.EmployeeDepartmentListAPIView.as_view()),
    path("get_department_list/", views.FetchAllDepartmentAPIView.as_view()),
    path("get_single_department/", views.FetchSingleDepartmentAPIView.as_view()),
    path("department_module/", views.DepartmentModuleAPIView.as_view()),
    path("payroll_settings_calculator/", views.CompanyPayrollCalculatorAPIView.as_view()),
    
]

query_search = [
    path("query_employee/", views.SearchEmployee.as_view()),
    path("query_payroll_approval/", views.SearchPayrollApproval.as_view()),
    path("query_payroll_disburse/", views.SearchPayrollDisburse.as_view()),
]

admin = [
    path("admin_employee_profile/", views.AdminEditEmployeeProfileAPIView.as_view()),
    path("admin_employee_family/", views.AdminEmployeeFamilyContactAPIView.as_view()),
    path("admin_employee_account/", views.AdminEmployeeAccountDetailsAPIView.as_view()),
    path("admin_employee_education/", views.AdminEmployeeEducationDetailsAPIView.as_view()),
    path("admin_employee_experience/", views.AdminEmployeeExperienceDetailsAPIView.as_view()),
    path("admin_employee_certification/", views.AdminEmployeeCertificationsAPIView.as_view()),
    path("admin_reset_employee_pin/", views.AdminResetEmployeePayrollPinAPIView.as_view()),
    path("admin_block_employee/", views.AdminBlockEmployeeLoginAccessAPIView.as_view()),
    path("admin_nudge_employee/", views.AdminNudgeEmployeeUpdateProfileAPIView.as_view()),
    path("admin_disable_employee_work_notification/", views.AdminDisableEmployeeWorkNotificationAPIView.as_view()),
    path("admin_disable_employee_birthday_notification/", views.AdminDisableEmployeeBirthdayNotificationAPIView.as_view()),
    path("admin_enable_employee_work_notification/", views.AdminEnableEmployeeWorkNotificationAPIView.as_view()),
    path("admin_enable_employee_birthday_notification/", views.AdminEnableEmployeeBirthdayNotificationAPIView.as_view()),
    path("admin_redeploy_employee/", views.AdminRedeployEmployeeDepartmentAPIView.as_view()),
    path("admin_remove_employee_department/", views.AdminRemoveEmployeeDepartmentAPIView.as_view()),
    path("admin_add_employee_department_role/", views.AdminEmployeeDepartmentRoleAPIView.as_view()),
    path("admin_remove_employee_department_role/", views.AdminRemoveEmployeeDepartmentRoleAPIView.as_view()),
    path("admin_promote_employee/", views.AdminPromoteEmployeeAPIView.as_view()),
    path("admin_unblock_employee/", views.AdminUnBlockEmployeeLoginAccessAPIView.as_view()),
    path("resend_company_pfa/", views.ResendCompanyPensionAPIView.as_view()),
]

roles_permissions = [
    path("list_roles/", views.FetchCompanyPermissionRolesAPIView.as_view()),
    path("create_role/", views.CreateCompanyRolesAPIView.as_view()),
    path("edit_role/", views.EditCompanyRolesAPIView.as_view()),
    path("delete_role/", views.DeleteCompanyRolesAPIView.as_view()),
    path("list_department_roles/", views.FetchDepartmentRolesAPIView.as_view()),
    path("create_department_role/", views.CreateDepartmentRolesAPIView.as_view()),
    path("edit_department_role/", views.EditDepartmentRolesAPIView.as_view()),
    path("delete_department_role/", views.DeleteDepartmentRolesAPIView.as_view()),
    path("add_employee_role/", views.AddEmployeeRoleAPIView.as_view()),
    path("admin_remove_employee_role/", views.AdminRemoveEmployeeRoleAPIView.as_view()),
    path("remove_multiple_employee_role/", views.RemoveMultipleEmployeeRoleAPIView.as_view()),
]

on_boarding = [
   path("onboard_user/", views.OnboardUserAPIView.as_view()), 
   path("onboard_phone_user/", views.OnboardPhoneUserAPIView.as_view()), 
   path("check_agency_user/", views.OnboardCheckUserAPIView.as_view()), 
   path("check_agency_user_phone/", views.OnboardCheckUserPhoneAPIView.as_view()), 
]

urlpatterns = [
    *transaction,
    *payroll,
    *employee,
    *company_payroll_dashboard,
    *account,
    *instant_wage,
    *pay_out,
    *admin_settings,
    *one_click,
    *payroll_settings,
    *query_search,
    *admin,
    *roles_permissions,
    *on_boarding,
]
