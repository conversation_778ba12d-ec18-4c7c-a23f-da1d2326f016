
import requests
import os
import math
from string import Template

from datetime import datetime, timedelta

from celery import shared_task

from django.conf import settings
from django.contrib.auth import get_user_model
from clock_app.helpers import add_minutes_to_time, sort_by_total_days_late
from clock_app.models import Clock, Record
from payroll_app.models import CompanyEmployeeList as Employee
from django.utils import timezone
from django.db.models import Q, Count, Sum
from datetime import time

from requisition.models import Company
import pandas as pd
from pathlib import Path
from core.tasks import send_email_with_attachment, send_email

weekday_attributes = {
    0: ('hybrid_monday', 'Monday'),
    1: ('hybrid_tuesday', 'Tuesday'),
    2: ('hybrid_wednesday', 'Wednesday'),
    3: ('hybrid_thursday', 'Thursday'),
    4: ('hybrid_friday', 'Friday'),
    5: ('hybrid_saturday', 'Saturday'),
    6: ('hybrid_sunday', 'Sunday')
}

@shared_task
def send_attendance_by_branch():
    from clock_app.models import Location
    from core.models import ConstantTable
    from payroll_app.models import CompanyPayrollSettings as CompanyProfile

    file_path = os.path.join(settings.BASE_DIR, 'clock_app/media/')

    all_company = Company.objects.filter(is_active=True)
    for company_ins in all_company:
       
        company_profile = CompanyProfile.objects.filter(company=company_ins, is_deleted=False).first()
        if not company_profile:
            continue

        if not company_profile.send_daily_report:
            continue
        date_for_filtering = timezone.localdate() if timezone else timezone.now().date()
        const_table = ConstantTable.get_constant_instance()
        attendance_time = const_table.attendance_time
        company_name = company_ins.company_name

        # print("WE GOT HERE ", company_ins.company_name)
        sender_email = company_profile.hr_email if company_profile.hr_email else ""
        cc_email = company_profile.admin_email if company_profile.admin_email else ""
        recipient = company_ins.user.email if company_ins.user else ""
        # print(sender_email, cc_email, recipient, "RECEIPIENT")
        time_for_filtering = time(attendance_time, 0, 0) 
        locations_data = Record.objects.filter(
            Q(company=company_ins, is_deleted=False) & 
            Q(date_created__date=date_for_filtering) 
            # & Q(date_created__time__lte=time_for_filtering)
        )
        unique_locations_data = locations_data.values('location').annotate(total_records=Count('id'))
        all_data = []
        for item in unique_locations_data:
            location = item['location']
            total_records = item['total_records']
            get_all_employees = locations_data.filter(location__id=location)

            all_employees = []
            for this_employee in get_all_employees:
                if this_employee.is_late:
                    total_seconds = this_employee.lateness_duration.total_seconds()
                    minutes = int(total_seconds // 60)
                    seconds = int(total_seconds % 60)
                all_employees.append(
                    {
                        "email": this_employee.employee.employee_email,
                        "first_name": this_employee.employee.employee_first_name,
                        "last_name": this_employee.employee.employee_last_name,
                        "clocked_in": this_employee.clock_in_time.strftime("%I:%M %p"),
                        "is_late": "YES" if this_employee.is_late else "NO",
                        "lateness_duration": f"{minutes} mins: {seconds} secs" if this_employee.is_late else "",

                    }
                )
            this_branch = Location.objects.filter(id=location).first()
            if this_branch:
                if this_branch.branch:
                    branch_name = this_branch.branch.name
                else:
                    branch_name = this_branch.name
                all_data.append({
                    "branch": branch_name if this_branch else "REMOTE",
                    "employee_no": total_records,
                    "employees": all_employees
                })
        last_file = os.path.join(f"{file_path}{company_name}_attendance_sheet_{date_for_filtering}.xlsx") 
        my_file = Path(last_file) 
        if my_file.is_file(): 
            os.remove(last_file)
        try:
            os.mkdir(file_path)
        except Exception:
            pass 

        if all_data:
            # Flatten the nested data structure
            flattened_data = []
            for branch_data in all_data:
                branch_name = branch_data['branch']
                for employee in branch_data['employees']:
                    employee['branch'] = branch_name
                    flattened_data.append(employee)

            # Create a DataFrame from the flattened data
            this_attendance = pd.DataFrame(flattened_data)

            # Create an Excel writer object
            excel_file = f"{file_path}{company_name}_attendance_sheet_{date_for_filtering}.xlsx"
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                this_attendance.to_excel(writer, index=False, sheet_name='Attendance_Sheet', header=True)

            this_attendance.to_excel(f"{file_path}{company_name}_attendance_sheet_{date_for_filtering}.xlsx", index=False, header=True)
            with open(last_file, "rb") as excel_file:
                pension_schedule = excel_file.read()
                excel_file.close()
            send_email_with_attachment(
                file = pension_schedule,
                template_dir="attendance_schedule.html",
                file_name = f"{file_path}{company_name}_attendance_sheet_{date_for_filtering}.xlsx",
                subject=f"{company_name} Attendance for {date_for_filtering}",
                recipient=recipient,
                company_name=company_ins.company_name,
                sender_email=sender_email,
                cc_email=cc_email
            )
            file_name = f"{file_path}{company_name}_attendance_sheet_{date_for_filtering}.xlsx"
            
            try:
                os.remove(f"{file_name}") ## Delete file when done
            except PermissionError:
                pass
    
    return "ATTENDANCE EMAIL HAS BEEN SENT TO ADMIN"

@shared_task
def send_employee_weekly_attendance():
    from clock_app.models import Location
    from core.models import ConstantTable
    from payroll_app.models import  CompanyPayrollSettings as CompanyProfile

    file_path = os.path.join(settings.BASE_DIR, 'clock_app/media/')

    # Get the start and end dates for the past week (Monday to Friday)
    today = timezone.localdate()
    
    all_company = Company.objects.filter(is_active=True)
    for company_ins in all_company:
        # company_settings_time = 
        
        company_profile = CompanyProfile.objects.filter(company=company_ins, is_deleted=False).first()
        if not company_profile:
            continue

        if not company_profile.send_weekly_report:
            continue

        date_for_filtering = timezone.localdate() if timezone else timezone.now().date()
        const_table = ConstantTable.get_constant_instance()
        attendance_time = const_table.attendance_time
        company_name = company_ins.company_name
        company_profile = CompanyProfile.objects.filter(company=company_ins, is_deleted=False).first()
        start_date = today - timedelta(days=today.weekday())  # Start of current week
        end_date = start_date + timedelta(days=4)  # Friday of the current week

        sender_email = company_profile.hr_email if company_profile.hr_email else ""
        cc_email = company_profile.admin_email if company_profile.admin_email else ""
        recipient = company_ins.user.email if company_ins.user else ""
 
        employee_data = Employee.objects.filter(company=company_ins, is_deleted=False)
        all_employees = []
        for employee in employee_data:
            all_employees.append(employee)

        employees_record = []
        employee_lateness_record = []
        for employee in all_employees:
            ## Query attendance records for the employee within the past week (Monday to Friday)
            employee_attendance = Record.objects.filter(
                company=company_ins,
                employee=employee,
                date_created__date__range=[start_date, end_date])

            weekday_attendance = {
                'Monday': None,
                'Tuesday': None,
                'Wednesday': None,
                'Thursday': None,
                'Friday': None,
            }
            # lateness_report = []
            this_employee_attendance = 0
            days_late = []
            for this_employee in employee_attendance:
                day_of_week = this_employee.date_created.strftime('%A')
                if company_profile.resumption_time:
                    resumption_time = company_profile.resumption_time
                else:
                    resumption_time = time(8,00)
                on_time = add_minutes_to_time(resumption_time, 10)
                slightly_late = add_minutes_to_time(resumption_time, 25)
                if this_employee.clock_in_time < company_profile.resumption_time:
                    employee_status = "Early"
                elif this_employee.clock_in_time >= resumption_time and this_employee.clock_in_time <= on_time:
                    employee_status = "On Time"
                elif this_employee.clock_in_time > on_time and this_employee.clock_in_time <= slightly_late:
                    employee_status = "Slightly Late"
                else:
                    employee_status = "Late"
                weekday_attendance[day_of_week] = {
                    "email": this_employee.employee.employee_email,
                    "full_name": this_employee.employee.full_name,
                    "clock_in": this_employee.clock_in_time.strftime("%I:%M %p") if this_employee.clock_in_time else "",
                    "status": employee_status,
                    "day": this_employee.week_day,
                }
                days_late.append(day_of_week) if employee_status == "Late" else ""
            expected_clock_in = company_profile.start_resumption_time.strftime("%I:%M %p")
            if weekday_attendance["Monday"] is None:
                monday_actual_clock_in = "Absent"
                monday_status = "Absent"
            else:
                monday_actual_clock_in = weekday_attendance.get("Monday").get("clock_in")
                monday_status = weekday_attendance.get("Monday").get("status")
                this_employee_attendance += 1
            if weekday_attendance["Tuesday"] is None:
                tuesday_actual_clock_in = "Absent"
                tuesday_status = "Absent"
            else:
                tuesday_actual_clock_in = weekday_attendance.get("Tuesday").get("clock_in")
                tuesday_status = weekday_attendance.get("Tuesday").get("status")
                this_employee_attendance += 1
            if weekday_attendance["Wednesday"] is None:
                wednesday_actual_clock_in = "Absent"
                wednesday_status = "Absent"
            else:
                wednesday_actual_clock_in = weekday_attendance.get("Wednesday").get("clock_in")
                wednesday_status = weekday_attendance.get("Wednesday").get("status")
                this_employee_attendance += 1
            if weekday_attendance["Thursday"] is None:
                thursday_actual_clock_in = "Absent"
                thursday_status = "Absent"
            else:
                thursday_actual_clock_in = weekday_attendance.get("Thursday").get("clock_in")
                thursday_status = weekday_attendance.get("Thursday").get("status")
                this_employee_attendance += 1
            if weekday_attendance["Friday"] is None:
                friday_actual_clock_in = "Absent"
                friday_status = "Absent"
            else:
                friday_actual_clock_in = weekday_attendance.get("Friday").get("clock_in")
                friday_status = weekday_attendance.get("Friday").get("status")
                this_employee_attendance += 1

            send_email.delay(recipient=employee.employee_email, subject="Weekly Attendance Report",
                template_dir="employee_weekly_report.html", company_name=company_name,
                expected_clock_in=expected_clock_in, week_start_date=start_date,
                monday_clock_in=monday_actual_clock_in, monday_status=monday_status,
                tuesday_clock_in=tuesday_actual_clock_in, tuesday_status=tuesday_status,
                wednesday_clock_in=wednesday_actual_clock_in, wednesday_status=wednesday_status,
                thursday_clock_in=thursday_actual_clock_in, thursday_status=thursday_status,
                friday_clock_in=friday_actual_clock_in, friday_status=friday_status,

                )
            employees_record.append(
                {
                    "Staff Member": f"{employee.full_name}",
                    "Days Present": this_employee_attendance,
                    "Total Work Days": 5,
                    "Attendance Percentage": (this_employee_attendance/5)*100 
                }
            
            )
            employee_lateness_record.append(
                {
                    "Staff Member": f"{employee.full_name}",
                    "Days Present": this_employee_attendance,
                    "Total Days Late": len(days_late),
                    "Days Late": ",".join(days_late),
                    # "Total Work Days": 5,
                    # "Attendance Percentage": (this_employee_attendance/5)*100 
                }
            
            )
        # weekly attendance report
        last_file = os.path.join(f"{file_path}{company_name}_weekly_report_{date_for_filtering}.xlsx") 
        my_file = Path(last_file) 
        if my_file.is_file(): 
            os.remove(last_file)
        try:
            os.mkdir(file_path)
        except Exception:
            pass 
        
        if employees_record:
            # Create a DataFrame from the data
            weekly_attendance = pd.DataFrame(employees_record)
            # Specify the order of columns (optional, but useful for consistent order)

            try:
                # Calculate Overall Attendance Percentage
                overall_attendance_percent = round(weekly_attendance['Attendance Percentage'].mean(), 2)
                total_days_present = weekly_attendance['Days Present'].sum()
                total_work_days = weekly_attendance['Total Work Days'].sum()
                overall_prompt_attendance_percent = round(((total_days_present / total_work_days) * 100), 2)
            except KeyError as e:
                overall_attendance_percent =0
                overall_prompt_attendance_percent=0
            # Export DataFrame to Excel
            excel_file = f'{file_path}{company_name}_weekly_report_{date_for_filtering}.xlsx'
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                weekly_attendance.to_excel(writer, index=False, sheet_name='Weekly_Report', header=True)

            weekly_attendance.to_excel(f"{file_path}{company_name}_weekly_report_{date_for_filtering}.xlsx", index=False, header=True)
            with open(last_file, "rb") as excel_file:
                weekly_report = excel_file.read()
                excel_file.close()
            send_email_with_attachment(
                file = weekly_report,
                template_dir="company_weekly_report.html",
                file_name = f"{file_path}{company_name}_weekly_report_{date_for_filtering}.xlsx.xlsx",
                subject=f"{company_name} Weekly Report for {date_for_filtering}",
                recipient=recipient,
                company_name=company_ins.company_name,
                sender_email=sender_email,
                cc_email=cc_email,
                overall_attendance_percent=overall_attendance_percent,
                overall_prompt_attendance_percent=overall_prompt_attendance_percent,
                start_date=start_date,
            )
            file_name = f"{file_path}{company_name}_weekly_report_{date_for_filtering}.xlsx"
            try:
                os.remove(f"{file_name}") ## Delete file when done
            except PermissionError:
                pass

        # weekly lateness report
        last_file_lateness = os.path.join(f"{file_path}{company_name}_weekly_lateness_report_{date_for_filtering}.xlsx") 
        my_file = Path(last_file_lateness) 
        if my_file.is_file(): 
            os.remove(last_file_lateness)
        try:
            os.mkdir(file_path)
        except Exception:
            pass 
        
        if employee_lateness_record:
            # Filter out entries with Total Days Late equal to zero
            filtered_lateness_report = [entry for entry in employee_lateness_record if entry["Total Days Late"] > 0]

            # Sort the lateness_report list based on Total Days Late in descending order
            sorted_lateness_report = sorted(filtered_lateness_report, key=sort_by_total_days_late, reverse=True)
            
            # Create a DataFrame from the data
            weekly_attendance = pd.DataFrame(sorted_lateness_report)
            # Specify the order of columns (optional, but useful for consistent order)

            # try:
            #     # Calculate Overall Attendance Percentage
            #     overall_attendance_percent = round(weekly_attendance['Attendance Percentage'].mean(), 2)
            #     total_days_present = weekly_attendance['Days Present'].sum()
            #     total_work_days = weekly_attendance['Total Work Days'].sum()
            #     overall_prompt_attendance_percent = round(((total_days_present / total_work_days) * 100), 2)
            # except KeyError as e:
            #     overall_attendance_percent =0
            #     overall_prompt_attendance_percent=0
            # Export DataFrame to Excel
            excel_file = f'{file_path}{company_name}_weekly_lateness_report_{date_for_filtering}.xlsx'
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                weekly_attendance.to_excel(writer, index=False, sheet_name='Lateness_Report', header=True)

            weekly_attendance.to_excel(f"{file_path}{company_name}_weekly_lateness_report_{date_for_filtering}.xlsx", index=False, header=True)
            with open(last_file_lateness, "rb") as excel_file:
                weekly_report = excel_file.read()
                excel_file.close()
            send_email_with_attachment(
                file = weekly_report,
                template_dir="company_weekly_lateness_report.html",
                file_name = f"{file_path}{company_name}_weekly_lateness_report_{date_for_filtering}.xlsx.xlsx",
                subject=f"{company_name} Weekly Lateness Report for {date_for_filtering}",
                recipient=recipient,
                company_name=company_ins.company_name,
                sender_email=sender_email,
                cc_email=cc_email,
                # overall_attendance_percent=overall_attendance_percent,
                # overall_prompt_attendance_percent=overall_prompt_attendance_percent,
                start_date=start_date,
            )

            file_name = f"{file_path}{company_name}_weekly_lateness_report_{date_for_filtering}.xlsx"
            try:
                os.remove(f"{file_name}") ## Delete file when done
            except PermissionError:
                pass

 
    return "COMPANY WEEKLY ATTENDANCE REPORT EMAIL"


@shared_task
def update_clock_out_every_one():
    from clock_app.models import Record, AttendanceSheet, Shift, Break
    from payroll_app.models import  CompanyPayrollSettings as CompanyProfile

    today_date = timezone.now().date()
    
    all_records = Record.objects.filter(date_created__date__lt=today_date, clock_out_time__isnull=True)
    for record in all_records:
        company = record.company
        company_settings = CompanyProfile.objects.filter(company=company).first()
        if company_settings:
            closing_time = company_settings.closing_time if company_settings.closing_time else time(17,30)
        else:
            closing_time = time(17,30)

        ##### start

        employee_attendance = None
        attendance = AttendanceSheet.objects.filter(company=company, status="ACTIVE", is_deleted=False)
        for att in attendance:
            if att.employee.filter(pk=record.employee.pk).exists():
                employee_attendance = att
                break
        
        employee_has_shift = False
        employee_shift = None
        
        if employee_attendance:
            all_shift = Shift.objects.filter(company=company, attendance=employee_attendance, is_deleted=False)
            for shift in all_shift:
                this_attendance = shift.attendance.employee
                if this_attendance.filter(pk=record.employee.pk).exists():
                    employee_has_shift = True
                    employee_shift = shift
                    resumption_time = shift.shift_resumption_time
                    closing_time = shift.shift_closing_time
                    break

        # current_time = timezone.localtime()
        break_instance = Break.objects.filter(date_created__date=today_date, break_end_time__isnull=True,
                                                record=record, company=company).first()
        if break_instance:

            ##### end a an existing opened break
            break_start = timezone.localtime(break_instance.break_start_time)
            break_start_time = break_start.time()
            
            
            start_break_time_in_seconds = timedelta(hours=break_start_time.hour, minutes=break_start_time.minute, seconds=break_start_time.second).total_seconds()
            end_break_time_in_seconds = timedelta(hours=closing_time.hour, minutes=closing_time.minute, seconds=closing_time.second).total_seconds()
        
            # Calculate the difference between the resumption time and the clock in time
            time_difference = end_break_time_in_seconds - start_break_time_in_seconds
            # Extract the duration components
            hours, remainder = divmod(time_difference, 3600)
            minutes, seconds = divmod(remainder, 60)
            # Create a timedelta object from the duration components
            end_break_hours = int(hours)
            end_break_minutes = int(minutes)
            end_break_seconds = int(seconds)
            break_duration = timedelta(hours=end_break_hours, minutes=end_break_minutes, seconds=end_break_seconds)
            closing_date = f'{today_date} {closing_time}'
            closing_date_time = datetime.strptime(closing_date, '%Y-%m-%d %H:%M:%S')
            break_instance.break_end_time = closing_date_time
            break_instance.break_duration = break_duration
            break_instance.save()
            ######
        
        no_duration = timedelta(hours=0, minutes=0, seconds=0)
    
        ## clock employee out
        all_breaks = Break.objects.filter(date_created__date=today_date,
                                            record=record, company=company)
        total_break_duration = all_breaks.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration

        clock_in = record.clock_in_time
        if clock_in:
            clock_in_seconds = clock_in.hour * 3600 + clock_in.minute * 60 + clock_in.second
            today_closing_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
            current_time_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
            
            if company_settings.overtime_duration > no_duration:
                this_closing_time_seconds = today_closing_seconds + company_settings.overtime_duration.total_seconds()
            else:
                this_closing_time_seconds = today_closing_seconds + no_duration.total_seconds()
            

            break_duration_seconds = total_break_duration.total_seconds()
            work_duration_seconds = this_closing_time_seconds - clock_in_seconds - break_duration_seconds
            work_duration = timedelta(seconds=work_duration_seconds)
            break_exceeded = False
            exceeded_break_time = no_duration
            extra_time_duration = no_duration

            get_extra_time_duration = all_breaks.filter(date_created__time__gt=closing_time)
            if get_extra_time_duration:
                extra_time_duration = get_extra_time_duration.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration
            else:
                extra_time_duration = no_duration

            if current_time_seconds > this_closing_time_seconds:
                over_time_data = this_closing_time_seconds - today_closing_seconds
                # print(over_time_data ,this_closing_time_seconds, today_closing_seconds, ' seconds 1')
                over_time_duration = timedelta(seconds=over_time_data) - extra_time_duration
            else:
                over_time_data = this_closing_time_seconds - current_time_seconds
                # print(over_time_data ,this_closing_time_seconds, current_time_seconds, ' seconds 2')
                over_time_first_duration = timedelta(seconds=over_time_data) - extra_time_duration
                over_time_duration = company_settings.overtime_duration - over_time_first_duration 

            if total_break_duration > company_settings.break_duration:
                break_exceeded = True
                exceeded_break_time = total_break_duration - company_settings.break_duration
            else:
                break_exceeded = False
                exceeded_break_time = no_duration

            record.work_duration = work_duration
            record.break_duration = total_break_duration
            record.is_break_exceeded = break_exceeded
            record.break_excess = exceeded_break_time
            record.overtime_duration = over_time_duration
            record.clock_out_time = closing_time
            record.save()

    return "UPDATE EVERYONE CLOCK OUT TIME"

# @shared_task
# def update_total_break_duration():
#     from clock_app.models import Break
#     all_break = Break.objects.all()
#     for breaks in all_break:
#         start_break = breaks.break_start_time
#         break_start_in_seconds = start_break.hour * 3600 + start_break.minute * 60 + start_break.second

#         end_break = breaks.break_end_time
#         break_end_in_seconds = end_break.hour * 3600 + end_break.minute * 60 + end_break.second      

#         break_duration_seconds = break_end_in_seconds - break_start_in_seconds
#         break_duration = timedelta(seconds=break_duration_seconds)
#         breaks.break_duration = break_duration
#         breaks.save()

#     return "UPDATE BREAK DURATION"

# @shared_task
# def update_total_hours_worked():
#     from clock_app.models import Record, Break
#     all_record = Record.objects.all()
#     no_duration = timedelta(hours=0, minutes=0, seconds=0)
#     for record in all_record:
#         all_breaks = Break.objects.filter(record=record, company=record.company)
#         clock_in = record.clock_in_time
#         clock_in_seconds = clock_in.hour * 3600 + clock_in.minute * 60 + clock_in.second

#         clock_out = record.clock_out_time
#         clock_out_seconds = clock_out.hour * 3600 + clock_out.minute * 60 + clock_out.second

#         total_break_duration = all_breaks.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration      

#         break_duration_seconds = total_break_duration.total_seconds()
#         work_duration_seconds = clock_out_seconds - clock_in_seconds - break_duration_seconds
#         work_duration = timedelta(seconds=work_duration_seconds)
#         record.work_duration = work_duration
#         record.save()
#     return "UPDATE ALL RECORD WORK DURATION"

@shared_task
def biometric_signin(data):
    from clock_app.models import Record, Break
    from core.models import ConstantTable
    from clock_app.models import AttendanceSheet, CompanyMeeting, Location, Record, Shift
    from payroll_app.models import  CompanyPayrollSettings as CompanyProfile
    today_date_time = timezone.now()
    today_date = today_date_time.date()

    company = Company.objects.filter(id="f92f464b-b10a-46b9-bfd2-74b6956e85e5").first()
    if not company:
        return "COMPANY NOT FOUND"
    company_profile = CompanyProfile.objects.filter(company=company).first()
    if not company_profile:
        return "COMPANY PROFILE NOT SET UP"
    
    company_location = Location.objects.filter(id="913c0ed8-4d33-4ecd-839a-e5976d7f20c3", company=company).first()
    if not company_location:
        return "CANNOT FIND COMPANY LOCATION"
    
    for employee in data:
        this_date = employee.get("date")
        this_time = employee.get("time")
        time_object = datetime.strptime(this_time, "%H:%M:%S").time()
        date_created = employee.get("created_at")

        datetime_object = datetime.strptime(date_created, '%Y-%m-%d %H:%M:%S')
        employee_email = employee.get("name").lower()
        employee = Employee.objects.filter(employee_email=employee_email, is_deleted=False).first()
        if not employee:
            continue
        record = Record.objects.filter(employee=employee, company=company, date_created__date=this_date).first()
        if record:
            if datetime_object < record.date_created:
                record.clock_in_time = time_object
                record.date_created = datetime_object
                record.save(update_fields=['clock_in_time', 'date_created'])
            else:
                pass
        else:
            if not employee.work_type:
                work_type = "REMOTE"
            else:
                work_type = employee.work_type
    
            work_type = employee.work_type

            # Get the current weekday
            current_weekday = today_date_time.weekday()
            this_day_name = ""
            # Check if the current weekday exists in the dictionary
            if current_weekday in weekday_attributes:
                attr_name, day_name = weekday_attributes[current_weekday]

                this_day_name = day_name.upper()

            location_type = ""
            if work_type == "REMOTE":
                location_type = "REMOTE"
            elif work_type == "HYBRID": 
                # Check if the attribute is True in the company profile
                if getattr(company_profile, attr_name):
                    location_type = "ONSITE"
                else:
                    location_type = "REMOTE"
            else:
                location_type = "ONSITE"
            
            employee_attendance = None
            attendance = AttendanceSheet.objects.filter(company=company, status="ACTIVE", is_deleted=False)
            for att in attendance:
                if att.employee.filter(pk=employee.pk).exists():
                    employee_attendance = att
                    break
            
            resumption_time = None
            closing_time = None
            employee_has_shift = False
            employee_shift = None

            if employee_attendance:
                all_shift = Shift.objects.filter(company=company, attendance=employee_attendance, is_deleted=False)
                for shift in all_shift:
                    this_attendance = shift.attendance.employee
                    if this_attendance.filter(pk=employee.pk).exists():
                        employee_has_shift = True
                        employee_shift = shift
                        resumption_time = shift.shift_resumption_time
                        closing_time = shift.shift_closing_time
                        break

            if not employee_has_shift:
                resumption_time = company_profile.start_resumption_time
                closing_time = company_profile.closing_time

            resumption_time_in_seconds = timedelta(hours=resumption_time.hour, minutes=resumption_time.minute, seconds=0).total_seconds()
            closing_time_in_seconds = timedelta(hours=closing_time.hour, minutes=closing_time.minute, seconds=closing_time.second).total_seconds()
            
            clock_in_time_in_seconds = timedelta(hours=time_object.hour, minutes=time_object.minute, seconds=time_object.second).total_seconds()
        
            if clock_in_time_in_seconds >= closing_time_in_seconds:
                pass
            else:
                if clock_in_time_in_seconds > resumption_time_in_seconds:
                    is_late = True
                    # Calculate the difference between the resumption time and the clock in time
                    time_difference = clock_in_time_in_seconds - resumption_time_in_seconds
                    # Extract the duration components
                    hours, remainder = divmod(time_difference, 3600)
                    minutes, seconds = divmod(remainder, 60)
                    # Create a timedelta object from the duration components
                    late_hours = int(hours)
                    late_minutes = int(minutes)
                    late_seconds = int(seconds)
                    lateness_duration = timedelta(hours=late_hours, minutes=late_minutes, seconds=late_seconds)
                else:
                    is_late=False
                    lateness_duration = None

                if employee_attendance:
                    attendance_choice = employee_attendance.title.upper()
                else:
                    attendance_choice = "MAIN"
                
                record = Record.objects.create(
                    employee=employee, 
                    company=company, 
                    date_created=datetime_object,
                    location=company_location,
                    clock_in_longitude=3.3847654,
                    clock_in_latitude=6.5079359,
                    clock_in_time=this_time,
                    is_late=is_late,
                    attendance=attendance_choice,
                    lateness_duration=lateness_duration,
                    shift=employee_shift,
                    location_type=location_type,
                    week_day=this_day_name
                )
                record.date_created = datetime_object
                record.save(update_fields=['date_created'])
                
                if is_late:
                    send_email.delay(recipient=employee.employee_email, subject="Late Clock In",
                                employee_name=employee.full_name,
                                template_dir="late_clock_in.html", company_name=company.company_name,
                    )

                this_meeting = CompanyMeeting.objects.filter(company=company, meeting_date=today_date).first()
                if this_meeting:
                    send_email.delay(recipient=employee.employee_email, subject="Today Meeting",
                                employee_name=employee.full_name, call_back_url=this_meeting.meeting_link,
                                template_dir="meeting_link.html", company_name=company.company_name,
                                meeting_time=this_meeting.meeting_time.strftime("%I:%M %p")
                    )
    
    return "BIOMETRIC SIGN-IN UPDATED"

@shared_task
def biometric_signout(data):
    from clock_app.models import Record, Break
    from core.models import ConstantTable
    from clock_app.models import AttendanceSheet, CompanyMeeting, Location, Record, Shift
    from payroll_app.models import  CompanyPayrollSettings as CompanyProfile
    today_date_time = timezone.now()
    today_date = today_date_time.date()
    current_time = timezone.localtime()

    no_duration = timedelta(hours=0, minutes=0, seconds=0)

    company = Company.objects.filter(id="f92f464b-b10a-46b9-bfd2-74b6956e85e5").first()
    if not company:
        return "COMPANY NOT FOUND"
    company_profile = CompanyProfile.objects.filter(company=company).first()
    if not company_profile:
        return "COMPANY PROFILE NOT SET UP"
    
    company_location = Location.objects.filter(id="913c0ed8-4d33-4ecd-839a-e5976d7f20c3", company=company).first()
    if not company_location:
        return "CANNOT FIND COMPANY LOCATION"
    
    for this_employee in data:
        this_date = this_employee.get("date")
        this_time = this_employee.get("time")
        time_object = datetime.strptime(this_time, "%H:%M:%S").time()
        date_created = this_employee.get("created_at")

        datetime_object = datetime.strptime(date_created, '%Y-%m-%d %H:%M:%S')
        employee_email = this_employee.get("name").lower()
        
        employee = Employee.objects.filter(employee_email=employee_email, is_deleted=False).first()
        if not employee:
            continue

        employee_attendance = None
        attendance = AttendanceSheet.objects.filter(company=company, status="ACTIVE", is_deleted=False)
        for att in attendance:
            if att.employee.filter(pk=employee.pk).exists():
                employee_attendance = att
                break

        resumption_time = None
        closing_time = None
        employee_has_shift = False
        employee_shift = None

        if employee_attendance:
            all_shift = Shift.objects.filter(company=company, attendance=employee_attendance, is_deleted=False)
            for shift in all_shift:
                this_attendance = shift.attendance.employee
                if this_attendance.filter(pk=employee.pk).exists():
                    employee_has_shift = True
                    employee_shift = shift
                    resumption_time = shift.shift_resumption_time
                    closing_time = shift.shift_closing_time
                    break

        if not employee_has_shift:
            resumption_time = company_profile.resumption_time
            closing_time = company_profile.closing_time

        record = Record.objects.filter(employee=employee, company=company, date_created__date=this_date).first()
        if record:
            seven_pm = timezone.make_aware(datetime.combine(today_date, time(19, 0)))
            if today_date_time >= seven_pm:
                if record.clock_out_time:
                    if time_object <= record.clock_out_time:
                        pass
                    else:
                        break_instance = Break.objects.filter(date_created__date=today_date, break_end_time__isnull=True, record=record, company=company).first()
                        if break_instance:
                            break_start = timezone.localtime(break_instance.break_start_time)
                            break_start_time = break_start.time()

                            if current_time.time() < closing_time:  
                                start_break_time_in_seconds = timedelta(hours=break_start_time.hour, minutes=break_start_time.minute, seconds=break_start_time.second).total_seconds()
                                end_break_time_in_seconds = timedelta(hours=current_time.hour, minutes=current_time.minute, seconds=current_time.second).total_seconds()

                                # Calculate the difference between the resumption time and the clock in time
                                time_difference = end_break_time_in_seconds - start_break_time_in_seconds
                                # Extract the duration components
                                hours, remainder = divmod(time_difference, 3600)
                                minutes, seconds = divmod(remainder, 60)
                                # Create a timedelta object from the duration components
                                late_hours = int(hours)
                                late_minutes = int(minutes)
                                late_seconds = int(seconds)
                                break_duration = timedelta(hours=late_hours, minutes=late_minutes, seconds=late_seconds)
                                
                                break_instance.break_end_time = current_time
                                break_instance.break_duration = break_duration
                                break_instance.end_break_longitude=3.3847654
                                break_instance.end_break_latitude=6.5079359
                                break_instance.save()
                            
                            if current_time.time() >= closing_time:
                                start_break_time_in_seconds = timedelta(hours=break_start_time.hour, minutes=break_start_time.minute, seconds=break_start_time.second).total_seconds()
                                end_break_time_in_seconds = timedelta(hours=closing_time.hour, minutes=closing_time.minute, seconds=closing_time.second).total_seconds()
                            
                                print(break_start_time, closing_time, current_time.time())
                                # Calculate the difference between the resumption time and the clock in time
                                time_difference = end_break_time_in_seconds - start_break_time_in_seconds
                                # Extract the duration components
                                hours, remainder = divmod(time_difference, 3600)
                                minutes, seconds = divmod(remainder, 60)
                                # Create a timedelta object from the duration components
                                end_break_hours = int(hours)
                                end_break_minutes = int(minutes)
                                end_break_seconds = int(seconds)
                                break_duration = timedelta(hours=end_break_hours, minutes=end_break_minutes, seconds=end_break_seconds)
                                closing_date = f'{today_date} {closing_time}'
                                closing_date_time = datetime.strptime(closing_date, '%Y-%m-%d %H:%M:%S')
                                break_instance.break_end_time = closing_date_time
                                break_instance.break_duration = break_duration
                                break_instance.end_break_longitude=3.3847654
                                break_instance.end_break_latitude=6.5079359
                                break_instance.save()
                                

                            if time_object < closing_time:  
                                all_breaks = Break.objects.filter(date_created__date=today_date,
                                                                    record=record, company=company)
                                total_break_duration = all_breaks.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration

                                clock_in = record.clock_in_time
                                clock_in_seconds = clock_in.hour * 3600 + clock_in.minute * 60 + clock_in.second
                                today_closing_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
                                current_time_seconds = time_object.hour * 3600 + time_object.minute * 60 + time_object.second

                                resumption_time_in_seconds = timedelta(hours=resumption_time.hour, minutes=resumption_time.minute, seconds=0).total_seconds()
                                this_closing_time_seconds = today_closing_seconds + no_duration.total_seconds()    

                                break_duration_seconds = total_break_duration.total_seconds()
                                work_duration_seconds = current_time_seconds - clock_in_seconds - break_duration_seconds
                                work_duration = timedelta(seconds=work_duration_seconds)

                                break_exceeded = False
                                exceeded_break_time = no_duration
                                if total_break_duration > company_profile.break_duration:
                                    
                                    break_exceeded = True
                                    exceeded_break_time = total_break_duration - company_profile.break_duration
                                else:
                                    break_exceeded = False
                                    exceeded_break_time = no_duration
                        
                                if current_time_seconds <= resumption_time_in_seconds:
                                    record.work_duration = no_duration
                                    record.break_duration = no_duration
                                    record.is_break_exceeded = False
                                    record.break_excess = no_duration
                                    record.overtime_duration = no_duration
                                    record.clock_out_longitude = 3.3847654
                                    record.clock_out_latitude = 6.5079359
                                    record.clock_out_time = time_object
                                    record.save()
                                else:
                                    record.work_duration = work_duration
                                    record.break_duration = total_break_duration
                                    record.is_break_exceeded = break_exceeded
                                    record.break_excess = exceeded_break_time
                                    record.overtime_duration = no_duration
                                    record.clock_out_longitude = 3.3847654
                                    record.clock_out_latitude = 6.5079359
                                    record.clock_out_time = time_object
                                    record.save()

                            if time_object >= closing_time:
                                all_breaks = Break.objects.filter(date_created__date=today_date,
                                                    record=record, company=company)
                                total_break_duration = all_breaks.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration

                                clock_in = record.clock_in_time
                                clock_in_seconds = clock_in.hour * 3600 + clock_in.minute * 60 + clock_in.second
                                today_closing_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
                                current_time_seconds = time_object.hour * 3600 + time_object.minute * 60 + time_object.second
                                
                                if company_profile.overtime_duration > no_duration:
                                    this_closing_time_seconds = today_closing_seconds + company_profile.overtime_duration.total_seconds()
                                else:
                                    this_closing_time_seconds = today_closing_seconds + no_duration.total_seconds()
                                

                                break_duration_seconds = total_break_duration.total_seconds()
                                work_duration_seconds = this_closing_time_seconds - clock_in_seconds - break_duration_seconds
                                work_duration = timedelta(seconds=work_duration_seconds)
                                break_exceeded = False
                                exceeded_break_time = no_duration
                                extra_time_duration = no_duration

                                get_extra_time_duration = all_breaks.filter(date_created__time__gt=closing_time)
                                if get_extra_time_duration:
                                    extra_time_duration = get_extra_time_duration.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration
                                else:
                                    extra_time_duration = no_duration

                                if current_time_seconds > this_closing_time_seconds:
                                    over_time_data = this_closing_time_seconds - today_closing_seconds
                                    # print(over_time_data ,this_closing_time_seconds, today_closing_seconds, ' seconds 1')
                                    over_time_duration = timedelta(seconds=over_time_data) - extra_time_duration
                                else:
                                    over_time_data = this_closing_time_seconds - current_time_seconds
                                    # print(over_time_data ,this_closing_time_seconds, current_time_seconds, ' seconds 2')
                                    over_time_first_duration = timedelta(seconds=over_time_data) - extra_time_duration
                                    over_time_duration = company_profile.overtime_duration - over_time_first_duration 

                                if total_break_duration > company_profile.break_duration:
                                    break_exceeded = True
                                    exceeded_break_time = total_break_duration - company_profile.break_duration
                                else:
                                    break_exceeded = False
                                    exceeded_break_time = no_duration

                                record.work_duration = work_duration
                                record.break_duration = total_break_duration
                                record.is_break_exceeded = break_exceeded
                                record.break_excess = exceeded_break_time
                                record.overtime_duration = over_time_duration
                                record.clock_out_longitude = 3.3847654
                                record.clock_out_latitude = 6.5079359
                                record.clock_out_time = time_object
                                record.save()
                            
                else:
                    break_instance = Break.objects.filter(date_created__date=today_date, break_end_time__isnull=True, record=record, company=company).first()
                    if break_instance:
                        break_start = timezone.localtime(break_instance.break_start_time)
                        break_start_time = break_start.time()

                        if current_time.time() < closing_time:  
                            start_break_time_in_seconds = timedelta(hours=break_start_time.hour, minutes=break_start_time.minute, seconds=break_start_time.second).total_seconds()
                            end_break_time_in_seconds = timedelta(hours=current_time.hour, minutes=current_time.minute, seconds=current_time.second).total_seconds()

                            # Calculate the difference between the resumption time and the clock in time
                            time_difference = end_break_time_in_seconds - start_break_time_in_seconds
                            # Extract the duration components
                            hours, remainder = divmod(time_difference, 3600)
                            minutes, seconds = divmod(remainder, 60)
                            # Create a timedelta object from the duration components
                            late_hours = int(hours)
                            late_minutes = int(minutes)
                            late_seconds = int(seconds)
                            break_duration = timedelta(hours=late_hours, minutes=late_minutes, seconds=late_seconds)
                            
                            break_instance.break_end_time = current_time
                            break_instance.break_duration = break_duration
                            break_instance.end_break_longitude=3.3847654
                            break_instance.end_break_latitude=6.5079359
                            break_instance.save()
                        
                        if current_time.time() >= closing_time:
                            start_break_time_in_seconds = timedelta(hours=break_start_time.hour, minutes=break_start_time.minute, seconds=break_start_time.second).total_seconds()
                            end_break_time_in_seconds = timedelta(hours=closing_time.hour, minutes=closing_time.minute, seconds=closing_time.second).total_seconds()
                        
                            print(break_start_time, closing_time, current_time.time())
                            # Calculate the difference between the resumption time and the clock in time
                            time_difference = end_break_time_in_seconds - start_break_time_in_seconds
                            # Extract the duration components
                            hours, remainder = divmod(time_difference, 3600)
                            minutes, seconds = divmod(remainder, 60)
                            # Create a timedelta object from the duration components
                            end_break_hours = int(hours)
                            end_break_minutes = int(minutes)
                            end_break_seconds = int(seconds)
                            break_duration = timedelta(hours=end_break_hours, minutes=end_break_minutes, seconds=end_break_seconds)
                            closing_date = f'{today_date} {closing_time}'
                            closing_date_time = datetime.strptime(closing_date, '%Y-%m-%d %H:%M:%S')
                            break_instance.break_end_time = closing_date_time
                            break_instance.break_duration = break_duration
                            break_instance.end_break_longitude=3.3847654
                            break_instance.end_break_latitude=6.5079359
                            break_instance.save()
                            

                        if time_object < closing_time:  
                            all_breaks = Break.objects.filter(date_created__date=today_date,
                                                                record=record, company=company)
                            total_break_duration = all_breaks.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration

                            clock_in = record.clock_in_time
                            clock_in_seconds = clock_in.hour * 3600 + clock_in.minute * 60 + clock_in.second
                            today_closing_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
                            current_time_seconds = time_object.hour * 3600 + time_object.minute * 60 + time_object.second

                            resumption_time_in_seconds = timedelta(hours=resumption_time.hour, minutes=resumption_time.minute, seconds=0).total_seconds()
                            this_closing_time_seconds = today_closing_seconds + no_duration.total_seconds()    

                            break_duration_seconds = total_break_duration.total_seconds()
                            work_duration_seconds = current_time_seconds - clock_in_seconds - break_duration_seconds
                            work_duration = timedelta(seconds=work_duration_seconds)

                            break_exceeded = False
                            exceeded_break_time = no_duration
                            if total_break_duration > company_profile.break_duration:
                                
                                break_exceeded = True
                                exceeded_break_time = total_break_duration - company_profile.break_duration
                            else:
                                break_exceeded = False
                                exceeded_break_time = no_duration
                    
                            if current_time_seconds <= resumption_time_in_seconds:
                                record.work_duration = no_duration
                                record.break_duration = no_duration
                                record.is_break_exceeded = False
                                record.break_excess = no_duration
                                record.overtime_duration = no_duration
                                record.clock_out_longitude = 3.3847654
                                record.clock_out_latitude = 6.5079359
                                record.clock_out_time = time_object
                                record.save()
                            else:
                                record.work_duration = work_duration
                                record.break_duration = total_break_duration
                                record.is_break_exceeded = break_exceeded
                                record.break_excess = exceeded_break_time
                                record.overtime_duration = no_duration
                                record.clock_out_longitude = 3.3847654
                                record.clock_out_latitude = 6.5079359
                                record.clock_out_time = time_object
                                record.save()

                        if time_object >= closing_time:
                            all_breaks = Break.objects.filter(date_created__date=today_date,
                                                record=record, company=company)
                            total_break_duration = all_breaks.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration

                            clock_in = record.clock_in_time
                            clock_in_seconds = clock_in.hour * 3600 + clock_in.minute * 60 + clock_in.second
                            today_closing_seconds = closing_time.hour * 3600 + closing_time.minute * 60 + closing_time.second
                            current_time_seconds = time_object.hour * 3600 + time_object.minute * 60 + time_object.second
                            
                            if company_profile.overtime_duration > no_duration:
                                this_closing_time_seconds = today_closing_seconds + company_profile.overtime_duration.total_seconds()
                            else:
                                this_closing_time_seconds = today_closing_seconds + no_duration.total_seconds()
                            

                            break_duration_seconds = total_break_duration.total_seconds()
                            work_duration_seconds = this_closing_time_seconds - clock_in_seconds - break_duration_seconds
                            work_duration = timedelta(seconds=work_duration_seconds)
                            break_exceeded = False
                            exceeded_break_time = no_duration
                            extra_time_duration = no_duration

                            get_extra_time_duration = all_breaks.filter(date_created__time__gt=closing_time)
                            if get_extra_time_duration:
                                extra_time_duration = get_extra_time_duration.aggregate(total_duration=Sum('break_duration'))['total_duration'] or no_duration
                            else:
                                extra_time_duration = no_duration

                            if current_time_seconds > this_closing_time_seconds:
                                over_time_data = this_closing_time_seconds - today_closing_seconds
                                # print(over_time_data ,this_closing_time_seconds, today_closing_seconds, ' seconds 1')
                                over_time_duration = timedelta(seconds=over_time_data) - extra_time_duration
                            else:
                                over_time_data = this_closing_time_seconds - current_time_seconds
                                # print(over_time_data ,this_closing_time_seconds, current_time_seconds, ' seconds 2')
                                over_time_first_duration = timedelta(seconds=over_time_data) - extra_time_duration
                                over_time_duration = company_profile.overtime_duration - over_time_first_duration 

                            if total_break_duration > company_profile.break_duration:
                                break_exceeded = True
                                exceeded_break_time = total_break_duration - company_profile.break_duration
                            else:
                                break_exceeded = False
                                exceeded_break_time = no_duration

                            record.work_duration = work_duration
                            record.break_duration = total_break_duration
                            record.is_break_exceeded = break_exceeded
                            record.break_excess = exceeded_break_time
                            record.overtime_duration = over_time_duration
                            record.clock_out_longitude = 3.3847654
                            record.clock_out_latitude = 6.5079359
                            record.clock_out_time = time_object
                            record.save()
                            
            else:
                pass
        else:
            if not employee.work_type:
                work_type = "REMOTE"
            else:
                work_type = employee.work_type
    
            work_type = employee.work_type

            # Get the current weekday
            current_weekday = today_date_time.weekday()
            this_day_name = ""
            # Check if the current weekday exists in the dictionary
            if current_weekday in weekday_attributes:
                attr_name, day_name = weekday_attributes[current_weekday]

                this_day_name = day_name.upper()

            location_type = ""
            if work_type == "REMOTE":
                location_type = "REMOTE"
            elif work_type == "HYBRID": 
                # Check if the attribute is True in the company profile
                if getattr(company_profile, attr_name):
                    location_type = "ONSITE"
                else:
                    location_type = "REMOTE"
            else:
                location_type = "ONSITE"
            
            employee_attendance = None
            attendance = AttendanceSheet.objects.filter(company=company, status="ACTIVE", is_deleted=False)
            for att in attendance:
                if att.employee.filter(pk=employee.pk).exists():
                    employee_attendance = att
                    break
            
            resumption_time = None
            closing_time = None
            employee_has_shift = False
            employee_shift = None

            if employee_attendance:
                all_shift = Shift.objects.filter(company=company, attendance=employee_attendance, is_deleted=False)
                for shift in all_shift:
                    this_attendance = shift.attendance.employee
                    if this_attendance.filter(pk=employee.pk).exists():
                        employee_has_shift = True
                        employee_shift = shift
                        resumption_time = shift.shift_resumption_time
                        closing_time = shift.shift_closing_time
                        break

            if not employee_has_shift:
                resumption_time = company_profile.start_resumption_time
                closing_time = company_profile.closing_time

            resumption_time_in_seconds = timedelta(hours=resumption_time.hour, minutes=resumption_time.minute, seconds=0).total_seconds()
            closing_time_in_seconds = timedelta(hours=closing_time.hour, minutes=closing_time.minute, seconds=closing_time.second).total_seconds()
            
            clock_in_time_in_seconds = timedelta(hours=time_object.hour, minutes=time_object.minute, seconds=time_object.second).total_seconds()
        
            if clock_in_time_in_seconds >= closing_time_in_seconds:
                pass
            else:
                if clock_in_time_in_seconds > resumption_time_in_seconds:
                    is_late = True
                    # Calculate the difference between the resumption time and the clock in time
                    time_difference = clock_in_time_in_seconds - resumption_time_in_seconds
                    # Extract the duration components
                    hours, remainder = divmod(time_difference, 3600)
                    minutes, seconds = divmod(remainder, 60)
                    # Create a timedelta object from the duration components
                    late_hours = int(hours)
                    late_minutes = int(minutes)
                    late_seconds = int(seconds)
                    lateness_duration = timedelta(hours=late_hours, minutes=late_minutes, seconds=late_seconds)
                else:
                    is_late=False
                    lateness_duration = None

                if employee_attendance:
                    attendance_choice = employee_attendance.title.upper()
                else:
                    attendance_choice = "MAIN"
                
                record = Record.objects.create(
                    employee=employee, 
                    company=company, 
                    date_created=datetime_object,
                    location=company_location,
                    clock_in_longitude=3.3847654,
                    clock_in_latitude=6.5079359,
                    clock_in_time=this_time,
                    is_late=is_late,
                    attendance=attendance_choice,
                    lateness_duration=lateness_duration,
                    shift=employee_shift,
                    location_type=location_type,
                    week_day=this_day_name
                )
                record.date_created = datetime_object
                record.save(update_fields=['date_created'])
                
                if is_late:
                    send_email.delay(recipient=employee.employee_email, subject="Late Clock In",
                                employee_name=employee.full_name,
                                template_dir="late_clock_in.html", company_name=company.company_name,
                    )

                this_meeting = CompanyMeeting.objects.filter(company=company, meeting_date=today_date).first()
                if this_meeting:
                    send_email.delay(recipient=employee.employee_email, subject="Today Meeting",
                                employee_name=employee.full_name, call_back_url=this_meeting.meeting_link,
                                template_dir="meeting_link.html", company_name=company.company_name,
                                meeting_time=this_meeting.meeting_time.strftime("%I:%M %p")
                    )
    
    return "BIOMETRIC SIGN-OUT UPDATED"

@shared_task
def fix_every_one_work_duration():
    from clock_app.models import Record
    from django.utils.timezone import make_aware

    all_records = Record.objects.filter(work_duration__isnull=False)
    for record in all_records:
        date = record.date_created.date()
        datetime_obj = datetime.combine(date, record.clock_in_time)
        break_duration  = record.break_duration or timedelta(hours=0, minutes=0, seconds=0)
        clock_in_time = make_aware(datetime_obj)
        work_duration = record.work_duration
        record.clock_out_time = clock_in_time + work_duration + break_duration
        record.save()
    return "FIX EMPLOYEE WORK DURATION"

@shared_task
def remove_every_incorrect_clock_out_time():
    from clock_app.models import Record

    all_records = Record.objects.filter(clock_in_time__isnull=False, clock_out_time__isnull=False, work_duration__isnull=True)
    for record in all_records:
        record.clock_out_time = None
        record.save()
    return "REMOVE INCORRECT CLOCK-OUT TIME"