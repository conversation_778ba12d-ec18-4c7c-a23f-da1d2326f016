from datetime import timedelta

from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response

from performance_sales_metrics_dashboard.tasks import send_task_deadline_reminder
from requisition.helpers.enums import UserRole
from requisition.models import TeamMember
from .models import Pipeline, Stage, Category, Task, SalesLead, SalesOfficer, Emails


def verify_if_sales_lead_instance_exists(instance):
    user_role = instance.role
    existing_instance = TeamMember.member_exists(email=instance.email, team_ins=instance.team)

    if existing_instance and user_role == UserRole.SALES_LEAD:
        sales_lead_existing_instance = SalesLead.objects.get(team_member=instance)
        sales_lead_existing_instance.is_sales_lead = False
        sales_lead_existing_instance.save()


def verify_if_sales_officer_instance_exists(instance):
    user_role = instance.role
    existing_instance = TeamMember.member_exists(email=instance.email, team_ins=instance.team)

    if existing_instance and user_role == UserRole.SALES_OFFICER:
        sales_officer_existing_instance = SalesOfficer.objects.get(team_member=instance)
        sales_officer_existing_instance.is_sales_officer = False
        sales_officer_existing_instance.save()


@receiver(post_save, sender=Pipeline)
def create_default_stages(sender, instance, created, **kwargs):
    if created:
        import logging
        logger = logging.getLogger(__name__)
        logger.info("Signal called for Pipeline creation.")
        if instance.id and instance.type == 'DEFAULT':

            category_objects_id = [
                1,
                2,
                3,
                4,
                5,
                6,
                7,
                8,
                9,
                10,
            ]
            category_instances = Category.objects \
                .filter(company=instance.sales_officer.team.company) \
                .order_by('created_at')

            for category in category_instances:
                logger.info("Category id check started.")
                try:
                    category_instance = Category.objects.get(id=category.id)
                    print(category_instance.name)
                except Category.DoesNotExist:
                    return Response({"Message": f"Category with id {category.id} does not exist."},
                                    status=status.HTTP_404_NOT_FOUND)

                data = {
                    "name": category_instance.name,
                    "pipeline": instance,
                    "category": category_instance,
                }

                try:
                    stage_instance = Stage.create(data)
                    logger.info(f"Default Stage {stage_instance.category.name} have been created.")

                except:
                    return Response({"Message": "An error occurred while creating the Stages"},
                                    status=status.HTTP_204_NO_CONTENT)
            print("Default Stages have been created.")


# @receiver(post_save, sender=TeamMember)
# def create_sales_team_objects(sender, instance, created, **kwargs):
#     if instance:
#         if instance.role == UserRole.SALES_LEAD:
#             create_sales_lead(sender, instance, created, **kwargs)
#
#         elif instance.role == UserRole.SALES_OFFICER:
#             create_sales_officer(sender, instance, created, **kwargs)


# @receiver(post_save, sender=TeamMember)
def create_sales_lead(sender, instance, created, **kwargs):

    try:
        if created:

            if instance.role == UserRole.SALES_LEAD and instance.is_active and instance.is_deleted is False:

                # verify_if_sales_officer_instance_exists(instance)

                user = instance.member
                data = {
                    "team_member": instance,
                    "company": instance.team.company,
                    "phone_number": instance.phone_no,
                    "email": instance.email
                }

                SalesLead.create_sales_lead(
                    user=user,
                    validated_data=data
                )

        else:
            verify_if_sales_officer_instance_exists(instance)

            if instance.role == UserRole.SALES_LEAD and instance.is_active and instance.is_deleted is False:
                user = instance.member
                data = {
                    "team_member": instance,
                    "company": instance.team.company,
                    "phone_number": instance.phone_no,
                    "email": instance.email
                }

                SalesLead.create_sales_lead(
                    user=user,
                    validated_data=data
                )

    except:
        print("SALES LEAD ALREADY EXISTS \n\n\n\n")
        pass


# @receiver(post_save, sender=TeamMember)
def create_sales_officer(sender, instance, created, **kwargs):

    try:
        if created:

            if instance.role == UserRole.SALES_OFFICER and instance.is_active and instance.is_deleted is False:

                # verify_if_sales_lead_instance_exists(instance)

                team_member = instance
                print("USER FROM TEAM MEMBER ::::::::::: > > > > ", team_member, "\n\n")
                print("USER_NAME FROM TEAM MEMBER ::::::::::: > > > > ", team_member.member.username, "\n\n")
                data = {
                    "phone_number": instance.phone_no,
                    "email": instance.email,
                    "sales_lead": None,
                    "product_module": None
                }

                SalesOfficer.create_sales_officer(
                    team_member=team_member,
                    validated_data=data
                )

        else:
            verify_if_sales_lead_instance_exists(instance)

            if instance.role == UserRole.SALES_OFFICER and instance.is_active and instance.is_deleted is False:
                team_member = instance
                data = {
                    "phone_number": instance.phone_no,
                    "email": instance.email,
                    "sales_lead": None,
                    "product_module": None
                }

                SalesOfficer.create_sales_officer(
                    team_member=team_member,
                    validated_data=data
                )

    except:
        print("SALES OFFICER ALREADY EXISTS \n\n\n\n")
        pass


@receiver(post_save, sender=Task)
def task_created_handler(sender, instance, created, **kwargs):
    if created:
        # Calculating reminder times 
        deadline = instance.deadline
        three_days_before = deadline - timedelta(days=3)
        two_days_before = deadline - timedelta(days=2)
        one_day_before = deadline - timedelta(days=1)

        now = timezone.now()
        # Calculate timedelta objects
        send_task_deadline_reminder.apply_async((instance.id,), eta=three_days_before)  # 3 days before deadline
        send_task_deadline_reminder.apply_async((instance.id,), eta=two_days_before)  # 2 days before deadline
        send_task_deadline_reminder.apply_async((instance.id,), eta=one_day_before)  # 1 day before deadline

# @receiver(post_save, sender=SalesOfficer)
# def create_default_pipeline_and_stages_for_sales_officer(sender, instance, created, **kwargs):
#     if created:
#         created_default_pipeline = Pipeline.objects.create(
#             sales_officer=instance.id,
#             name=f"{instance.user.name}'s Pipeline",
#             type="DEFAULT",
#             is_default=True
#         )
#
#         stages = [
#             'Lead Generation',
#             'Prospecting',
#             'Initial Contact',
#             'Proposal / Quotation',
#             'Closing the Deal',
#             'Onboarding',
#         ]
#
#         order = 1
#         for stage_name in stages:
#             Stage.objects.create(
#                 pipeline=created_default_pipeline.id,
#                 name=stage_name,
#                 order=order,
#             )
#             order += 1
