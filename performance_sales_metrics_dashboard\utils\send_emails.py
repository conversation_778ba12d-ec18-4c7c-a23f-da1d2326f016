from __future__ import print_function
from email import encoders
from email.mime.base import MIME<PERSON><PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from decouple import config
import smtplib
from django.contrib.auth.hashers import make_password
from .utils import decrypt_password
from ..models import WorkEmail

def send_mails(user_id, subject, message, recipient_email:list, cc:list, bcc:list):
    work_email = WorkEmail.objects.get(user=user_id)
    email_address = work_email.work_email
    password = decrypt_password(work_email.password)
    
    # Create a multipart message object
    msg = MIMEMultipart('alternative')
    msg['From'] = email_address
    msg['To'] = ", ".join(recipient_email)
    msg['subject'] = subject
    msg['Cc'] = ", ".join(cc)
    msg['Bcc'] = ", ".join(bcc)

    # Create plain text and html versions for emails
    text = f"{message}"
    html = f"<html><body>{message}</body></html>"
    part1 = MIMEText(text, 'plain')
    part2 = MIMEText(html, 'html')
    msg.attach(part1)
    msg.attach(part2)

    # SMTP server settings
    smtp_server = "mail.libertyng.com"
    smtp_port = 587

    try:
        # create a secured SSL/TLS connection
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        
        server.login(email_address, password)
        server.sendmail(email_address, recipient_email, msg.as_string())

        return "success"

    except smtplib.SMTPException as e:
        return {
                "message": 'Error sending email', 
                "error": str(e),
            }

    finally:
        server.quit()


# import base64
# from email.message import EmailMessage
# import google.auth
# import os
# from googleapiclient.discovery import build
# import google.oauth2.credentials
# from google_auth_oauthlib.flow import InstalledAppFlow
# from google.oauth2.credentials import Credentials
# from google.auth.transport.requests import Request
# from googleapiclient.errors import HttpError
# from email.mime.text import MIMEText
# import os.path
# import google_auth_oauthlib.flow

# credential_path = "credentials.json"
# SCOPES = ["https://mail.google.com/"]

# def gmail_create_draft():
#     creds = None
#     if os.path.exists('token.json'):
#         creds = Credentials.from_authorized_user_file('token.json', SCOPES)
#     # If there are no (valid) credentials available, let the user log in.
#     if not creds or not creds.valid:
#         if creds and creds.expired and creds.refresh_token:
#             creds.refresh(Request())
#         else:
#             flow = InstalledAppFlow.from_client_secrets_file(
#                 credential_path, SCOPES)
#             creds = flow.run_local_server(port=42607)
#         # Save the credentials for the next run
#         with open('token.json', 'w') as token:
#             token.write(creds.to_json())
#     try:
#         service = build('gmail', 'v1', credentials=creds)
#         msg = MIMEText("test123\n<h1>Test email</h1>", "html")
#         msg["Subject"] = "Email Test Oauth Flow"
#         msg.add_header("Content-Type", "text/html")
#         msg["From"] = "<EMAIL>"
#         msg["To"] = "<EMAIL>"
#         encoded_message = base64.urlsafe_b64encode(msg.as_bytes()).decode()
#         create_message = { 'raw': encoded_message }
#         send_message = (service.users().messages().send(userId="me", body=create_message).execute())
#         print(F'Message Id: {send_message["id"]}')
#     except HttpError as error:
#         print(F"an error has occurred: {error}")
#         send_message = None
#     return send_message
# if __name__ == '__main__':
#     gmail_create_draft()