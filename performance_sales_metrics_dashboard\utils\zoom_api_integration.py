# from decouple import config
# import assemblyai as aai
# from zoom_main import ZoomClient

# ZOOM_ACCOUNT_ID = config("ZOOM_ACCOUNT_ID")
# ZOOM_CLIENT_ID = config("ZOOM_CLIENT_ID")
# ZOOM_CLIENT_SECRET = config("ZOOM_CLIENT_SECRET")
# ASSEMBLYAI_API_KEY = config("ASSEMBLYAI_API_KEY")

# aai.settings.api_key = ASSEMBLYAI_API_KEY

# transcriber = aai.Transcriber()

# client = ZoomClient(account_id=ZOOM_ACCOUNT_ID, client_id=ZOOM_CLIENT_ID, client_secret=ZOOM_CLIENT_SECRET)

# recs = client.get_recordings()
# if recs['meetings']:
#     rec_id = recs['meetings'][0]['id']
#     my_url = client.get_download_url(rec_id)
#     transcript = transcriber.transcribe(my_url)
#     print(transcript.text)
#     with open("transcript.txt", "w") as f:
#         f.write(transcript.text)

# else:
#     print('No meetings to transcribe!!!')


# ZOOM WITH JWT IMPLEMENTATION 
# import jwt
# import requests
# import json
# from time import time
# from decouple import config

# ZOOM_API_KEY = config("ZOOM_API_KEY")
# ZOOM_API_SECRET_KEY = config("ZOOM_API_SECRET_KEY")

# def generateToken():
#     token = jwt.encode(
#         # Payload containing Zoom API key and expiry date
#         {'iss': ZOOM_API_KEY, 'exp': time() + 5000},

#         # Secret used to generate token signature
#         ZOOM_API_SECRET_KEY,

#         # Hashing Algorithm
#         algorithm="HS256"
#     )
#     return token.decode("utf-8")


# # JSON data for post requests
# meetingdetails = {
#                     "topic": "Zoom Meeting Title",
#                     "type": 2,
#                     "start_time": "2024-10-25T12:21:57",
#                     "timezone": "Africa/Lagos",
#                     "agenda": "Test Meeting",

#                     "recurrence": {
#                                     "type": 1,
#                                     "repeat_interval": 1
#                                 },
#                     "settings": {
#                                     "host_video": "true", # True
#                                     "participant_video": "true",
#                                     "join_before_host":  "False",
#                                     "mute_upon_entry": "False",
#                                     "watermark": "true",
#                                     "audio": "voip",
#                                     "auto_recording": "cloud"
#                                 }
#                 }

# # Request with headers including a token and meeting details
# def createMeeting():
#     headers = {
#                 "authorization": "Bearer" + generateToken(),
#                 "content-type": "application/json"
#             }
    
#     r = requests.post(
#         f"https://api.zoom.us/v2/users/me/meetings",
#         headers=headers, data=json.dumps(meetingdetails))
    
#     print("\n creating zoom meeting ... \n")
#     # print(r.text)

#     # Convert output into json and extract the details
#     y = json.loads(r.text)
#     join_URL = y["join_url"]
#     meetingPassword = y["password"]

#     print(f"\n Here's your zoom meeting link {join_URL} and \n password: {meetingPassword}\n ")

# createMeeting()


