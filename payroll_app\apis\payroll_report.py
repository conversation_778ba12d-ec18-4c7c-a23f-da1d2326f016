import os
from django.conf import settings
import pandas as pd
from pathlib import Path
from core.tasks import upload_file_aws_s3_bucket_using_file_path
from payroll_app.apis.enable_payroll_settings import employee_benefit_report, employee_deduction_report, employee_salary_report, enable_payroll_settings, enable_payroll_settings_report
from payroll_app.apis.tax_component import consolidated_tax

from payroll_app.models import BenefitComponentSettings, CompanyEmployeeList, OtherDeductionSettings, SalaryComponentSettings
from requisition.models import Company
data = {
    "REVISED SALARY STRUCTURE" : [ 
        "", ""
    ],
}
def company_report(company_id, company_tax, pay_settings_check):
    file_path = os.path.join(settings.BASE_DIR, 'payroll_app/media/')

    this_company = Company.objects.filter(id=company_id).first()
    company_name = this_company.company_name
    
    all_employees = CompanyEmployeeList.objects.filter(company__id=company_id, is_active=True)
    all_salary_component = SalaryComponentSettings.objects.filter(company__id=company_id, is_active=True)
    index = 1
    total_amount = 0
    salary_component = []
    if all_salary_component:
        for components in all_salary_component:
            if components.calculation_type == "FIXED_AMOUNT":
                index += 1
                if components.frequency == "MONTHLY":
                    frequency = "MONTHLY"
                    amount = components.amount
                    total_amount = amount
                elif components.frequency == "BI-MONTHLY":
                    frequency = "BI-MONTHLY"
                    amount = components.amount
                    total_amount = amount
                elif components.frequency == "ANNUALLY":
                    frequency = "ANNUALLY"
                    amount = components.amount
                    total_amount = amount
                elif components.frequency == "ONE-OFF":
                    frequency = "ONE-OFF"
                    amount = components.amount
                    total_amount = amount 

                salary_component.append(
                    {components.salary_name:{
                        "index":index, 
                        "fixed": True, 
                        "amount": amount,
                        "frequency": frequency,
                        "total_amount": total_amount,
                        "salary_data": {
                            "fixed": True, 
                            "amount": amount,
                            "name": components.salary_name,
                            "frequency": frequency,
                            "calculation_type": "FIXED_AMOUNT"
                    
                        }
                    }}
                )
                
            elif components.calculation_type == "PERCENTAGE_NET":
                index += 1
                if components.frequency == "MONTHLY":
                    frequency = "MONTHLY"
                    amount = components.amount
                elif components.frequency == "BI-MONTHLY":
                    frequency = "BI-MONTHLY"
                    amount = components.amount
                elif components.frequency == "ANNUALLY":
                    frequency = "ANNUALLY"
                    amount = components.amount
                elif components.frequency == "ONE-OFF":
                    frequency = "ONE-OFF"
                    amount = components.amount

                salary_component.append(
                    {components.salary_name:{
                        "index":index, 
                        "fixed": False,
                        "percentage": amount,
                        "frequency": frequency,
                        "PERCENTAGE_NET": True ,
                        "salary_data": {
                            "fixed": False,
                            "percentage": amount,
                            "PERCENTAGE_NET": True,
                            "name": components.salary_name,
                            "frequency": frequency,
                            "calculation_type": "PERCENTAGE_NET"
                        }
                    }}
                )
    salary_component.append({"Annual Gross Pay": ""})
    salary_component.append({"": ""})
    salary_component.append({"Others": ""})

    s_index = index + 3      
    benefit_component = []
    all_benefit_component = BenefitComponentSettings.objects.filter(company__id=company_id, is_active=True)
    if all_benefit_component:
        for components in all_benefit_component:
            if components.calculation_type == "FIXED_AMOUNT":
                s_index += 1
                if components.frequency == "MONTHLY":
                    frequency = "MONTHLY"
                    amount = components.amount
                elif components.frequency == "BI-MONTHLY":
                    frequency = "BI-MONTHLY"
                    amount = components.amount
                elif components.frequency == "ANNUALLY":
                    frequency = "ANNUALLY"
                    amount = components.amount
                elif components.frequency == "ONE-OFF":
                    frequency = "ONE-OFF"
                    amount = components.amount
                
                benefit_component.append(
                    {components.benefit_name:{
                        "index":s_index, 
                        "fixed": True, 
                        "amount": amount,
                        "frequency": frequency,
                        "total_amount": total_amount,
                        "benefit_data": {
                            "fixed": True, 
                            "amount": amount,
                            "name": components.benefit_name,
                            "frequency": frequency,
                            "calculation_type": "FIXED_AMOUNT"
                        }
                    }}
                )
            elif components.calculation_type == "CUSTOM_PERCENTAGE":
                s_index += 1
                if components.frequency == "MONTHLY":
                    frequency = "MONTHLY"
                    amount = components.amount
                elif components.frequency == "BI-MONTHLY":
                    frequency = "BI-MONTHLY"
                    amount = components.amount
                elif components.frequency == "ANNUALLY":
                    frequency = "ANNUALLY"
                    amount = components.amount
                elif components.frequency == "ONE-OFF":
                    frequency = "ONE-OFF"
                    amount = components.amount

                benefit_component.append(
                    {components.benefit_name:{
                        "index":s_index, 
                        "fixed": False,
                        "percentage": amount,
                        "custom_percentage": True,
                        "frequency": frequency,
                        "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                        "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                        "benefit_data": {
                            "fixed": False,
                            "percentage": amount,
                            "custom_percentage": True,
                            "name": components.benefit_name,
                            "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                            "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                            "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
                            "frequency": frequency,
                            "calculation_type": "CUSTOM_PERCENTAGE"
                        }
                    }}
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                s_index += 1
                if components.frequency == "MONTHLY":
                    frequency = "MONTHLY"
                    amount = components.amount
                elif components.frequency == "BI-MONTHLY":
                    frequency = "BI-MONTHLY"
                    amount = components.amount
                elif components.frequency == "ANNUALLY":
                    frequency = "ANNUALLY"
                    amount = components.amount
                elif components.frequency == "ONE-OFF":
                    frequency = "ONE-OFF"
                    amount = components.amount

                benefit_component.append(
                    {components.benefit_name:{
                        "index":s_index, 
                        "fixed": False,
                        "frequency": frequency,
                        "custom_percentage": False,
                        "percentage": amount,
                        "benefit_data": {
                            "fixed": False,
                            "custom_percentage": False,
                            "name": components.benefit_name,
                            "percentage": amount,
                            "frequency": frequency,
                            "calculation_type": "PERCENTAGE_NET"
                        }
                    }}
                )
    # print(benefit_component)
    benefit_component.append({"": ""})
    benefit_component.append({"BHT": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"Consolidated Relief": ""})
    benefit_component.append({"Basic (Higher of 200k or 1% of G.I.)": ""})
    benefit_component.append({"20% of Gross Income": ""})
    benefit_component.append({"Tax-exempt Deductions": ""})
    benefit_component.append({"Employee Pension Cont. (8% of BHT)": ""})
    benefit_component.append({"Life Insurance": ""})
    benefit_component.append({"HMO": ""})
    benefit_component.append({"Total non-taxable Reliefs": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"TAXABLE INCOME": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"ANNUAL TAX LIABILITY": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"Minimum Tax (1% of Gross Income)": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"Monthly Tax Liability": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"MONTHLY DEDUCTIONS": ""})
    benefit_component.append({"Tax": ""})
    benefit_component.append({"Pension": ""})
    benefit_component.append({"Total Monthly Deductions": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"Monthly Net Pay (On-Payroll)": ""})
    benefit_component.append({"": ""})
    benefit_component.append({"ALL DEDUCTIONS": ""})

    t_index = s_index + 2
    deduction_component = []
    all_deduction_component = OtherDeductionSettings.objects.filter(company__id=company_id, is_active=True)
    if all_deduction_component:
        for components in all_deduction_component:
            if components.calculation_type == "FIXED_AMOUNT":
                t_index += 1
                if components.frequency == "MONTHLY":
                    frequency = "MONTHLY"
                    amount = components.amount
                    total_amount = amount
                elif components.frequency == "BI-MONTHLY":
                    frequency = "BI-MONTHLY"
                    amount = components.amount
                    total_amount = amount
                elif components.frequency == "ANNUALLY":
                    frequency = "ANNUALLY"
                    amount = components.amount
                    total_amount = amount
                elif components.frequency == "ONE-OFF":
                    frequency = "ONE-OFF"
                    amount = components.amount
                    total_amount = amount 

                deduction_component.append(
                    {components.deduction_name:{
                        "index":t_index, 
                        "fixed": True, 
                        "frequency": frequency,
                        "amount": amount,
                        "yearly_amount": total_amount,
                        "deduction_data": {
                            "fixed": True, 
                            "amount": total_amount,
                            "name": components.deduction_name,
                            "frequency": components.frequency,
                            "calculation_type": "FIXED_AMOUNT",
                            "deduction_id": components.id
                        }
                    }}
                )
            elif components.calculation_type == "CUSTOM_PERCENTAGE":
                t_index += 1
                if components.frequency == "MONTHLY":
                    frequency = "MONTHLY"
                    amount = components.amount
                elif components.frequency == "BI-MONTHLY":
                    frequency = "BI-MONTHLY"
                    amount = components.amount
                elif components.frequency == "ANNUALLY":
                    frequency = "ANNUALLY"
                    amount = components.amount
                elif components.frequency == "ONE-OFF":
                    frequency = "ONE-OFF"
                    amount = components.amount

                deduction_component.append(
                    {components.deduction_name:{
                        "index":t_index, 
                        "fixed": False,
                        "percentage": amount,
                        "custom_percentage": True,
                        "frequency": frequency,
                        "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                        "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                        "deduction_data": {
                            "fixed": False,
                            "percentage": components.amount,
                            "custom_percentage": True,
                            "name": components.deduction_name,
                            "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                            "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                            "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
                            "frequency": components.frequency,
                            "calculation_type": "CUSTOM_PERCENTAGE",
                            "deduction_id": components.id
                        
                        }
                    }}
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                s_index += 1
                if components.frequency == "MONTHLY":
                    frequency = "MONTHLY"
                    amount = components.amount
                elif components.frequency == "BI-MONTHLY":
                    frequency = "BI-MONTHLY"
                    amount = components.amount
                elif components.frequency == "ANNUALLY":
                    frequency = "ANNUALLY"
                    amount = components.amount
                elif components.frequency == "ONE-OFF":
                    frequency = "ONE-OFF"
                    amount = components.amount

                deduction_component.append(
                    {components.deduction_name:{
                        "index":s_index, 
                        "fixed": False,
                        "frequency": frequency,
                        "custom_percentage": False,
                        "percentage": amount
                    }}
                )
    deduction_component.append({"": ""})
    deduction_component.append({"": ""})

    # salary component
    salary_component_keys = [list(d.keys())[0] for d in salary_component]
    update_salary_comp = {
        "REVISED SALARY STRUCTURE": salary_component_keys
    }
    data["REVISED SALARY STRUCTURE"].extend(update_salary_comp["REVISED SALARY STRUCTURE"])

    # benefit salary component
    benefit_component_keys = [list(d.keys())[0] for d in benefit_component]
    update_benefit_comp = {
        "REVISED SALARY STRUCTURE": benefit_component_keys
    }
    data["REVISED SALARY STRUCTURE"].extend(update_benefit_comp["REVISED SALARY STRUCTURE"])

    # deduction component
    deduction_component_keys = [list(d.keys())[0] for d in deduction_component]
    update_deduction_comp = {
        "REVISED SALARY STRUCTURE": deduction_component_keys
    }
    data["REVISED SALARY STRUCTURE"].extend(update_deduction_comp["REVISED SALARY STRUCTURE"])

    # print(data)
    # data.update(
    #     {" ": ["" for x in range(0, len(data.get("REVISED SALARY STRUCTURE")))]}
    # )
    # data.update(
    #     {"  ": ["" for x in range(0, len(data.get("REVISED SALARY STRUCTURE")))]}
    # )
 
    if all_employees:
        # for i in range(1, len(all_employees)):
        s = "    "
        for employee in all_employees:
            pay_value = enable_payroll_settings_report(company=this_company, employee=employee, company_tax=company_tax, net_calculation_type=pay_settings_check)
            life_insurance_amount = employee.employee_life_insurance_amount
            employee_gross_amount = employee.employee_gross_amount
            employee_name = f"{employee.employee_first_name} {employee.employee_last_name}"
            # payee_data = consolidated_tax(employee)
            # if payee_data:
            total_net_amount = pay_value.get("total_net_amount")
            annual_tax = pay_value.get("annual_tax")
            basic_amount = pay_value.get("basic_amount")
            housing_amount = pay_value.get("housing_amount")
            transport_amount = pay_value.get("transport_amount")
            one_percent_gross_income_or_200k = pay_value.get("one_percent_gross_income_or_200k") 
            total_bht = pay_value.get("total_bht")
            pension_amount = pay_value.get("pension_amount")
            non_taxable_relief = pay_value.get("non_taxable_relief")
            taxable_income = pay_value.get("taxable_income")
            twenty_percent_gross_income = pay_value.get("twenty_percent_gross_income")
            one_percent_gross_income = pay_value.get("one_percent_gross_income")
            ten_percent_bht = pay_value.get("ten_percent_bht")
            monthly_gross = pay_value.get("monthly_gross")
            annual_gross = pay_value.get("annual_gross")
            net_amount = pay_value.get("employee_net_amount")
            try:
                monthly_tax = pay_value.get("annual_tax") / 12
            except ZeroDivisionError:
                monthly_tax = 0

            monthly_pension = pay_value.get("pension_amount")
            total_monthly_deductions = pay_value.get("total_monthly_deductions")
                
             
            s+= " "
            data.update(
                {
                    s:[ 
                        "", employee_name
                    ],
                }
            )
            annual_gross = annual_gross
            annual_gross_due_month = 0
            # payable_net_pay = 0
            net_pay = 0
            total_deduction = 0
            total_salary_component = 0
            total_benefit_component = 0
            total_deduction_component = 0
            total_other_deduction = employee.employee_other_deductions or 0 + employee.employee_hmo_amount or 0 + employee.employee_pension_amount or 0 + employee.employee_life_insurance_amount or 0

            for component in salary_component:
                
                key  = list(component.keys())[0]
                if key == "" or key == "Others":
                    data[s].append("")
                elif key == "Basic" or key == "Housing" or key == "Transport":
                    yearly_amount = employee_salary_report(component[key]['salary_data'], total_net_amount)
                    data[s].append(yearly_amount)
                    total_benefit_component+=yearly_amount

                elif key == "Annual Gross Pay":
                    data[s].append(annual_gross)  
                else:
                    yearly_amount = employee_salary_report(component[key]['salary_data'], total_net_amount)
                    data[s].append(yearly_amount)
                    total_benefit_component+=yearly_amount
    

            total_deductions = monthly_tax + total_monthly_deductions
            
            for component in benefit_component:
                
                key  = list(component.keys())[0]
                if key == "" or key == "Others" or key == "Consolidated Relief" or key == "Tax-exempt Deductions" or key == "MONTHLY DEDUCTIONS" or key == "ALL DEDUCTIONS":
                    data[s].append("")
                elif key == "Annual Gross Pay":
                    data[s].append(annual_gross)
                elif key == "BHT":
                    data[s].append(total_bht) 
                elif key == "Basic (Higher of 200k or 1% of G.I.)":
                    data[s].append(one_percent_gross_income_or_200k)
                elif key == "20% of Gross Income":
                    data[s].append(twenty_percent_gross_income)
                elif key == "Employee Pension Cont. (8% of BHT)":
                    data[s].append(pension_amount)
                elif key == "Life Insurance":
                    data[s].append(life_insurance_amount)
                elif key == "HMO":
                    data[s].append(employee.employee_hmo_amount * 12)
                elif key == "Total non-taxable Reliefs":
                    data[s].append(non_taxable_relief)
                elif key == "TAXABLE INCOME":
                    data[s].append(taxable_income)
                elif key == "ANNUAL TAX LIABILITY":
                    data[s].append(annual_tax)
                elif key == "Minimum Tax (1% of Gross Income)":
                    data[s].append(one_percent_gross_income)
                elif key == "Monthly Tax Liability":
                    data[s].append(monthly_tax)
                elif key == "Tax":
                    data[s].append(monthly_tax)
                elif key == "Pension":
                    data[s].append(monthly_pension)
                elif key == "Total Monthly Deductions":
                     
                    data[s].append(total_deductions)
                elif key == "Monthly Net Pay (On-Payroll)":
                    # monthly_net = (annual_gross / 12) 
                    data[s].append(net_amount) 
                else:
                    yearly_amount = employee_benefit_report(component[key]['benefit_data'], total_net_amount)
                    data[s].append(yearly_amount)
                    total_benefit_component+=yearly_amount

            total_payroll_setting_gross_amount = total_salary_component + total_benefit_component
            total_payroll_settings_gross_after_tax = total_payroll_setting_gross_amount - annual_tax - total_other_deduction
            try:
                net_payable_amount = total_payroll_settings_gross_after_tax / 12
            except ZeroDivisionError:
                net_payable_amount = 0
            for component in deduction_component:
                key  = list(component.keys())[0]
                if key == "":
                    data[s].append("")
                elif key == "PAYABLE NET PAY (AFTER DEDUCTION)":
                    payable_net_pay = net_payable_amount
                    data[s].append(payable_net_pay)
                else:
                    yearly_amount = employee_deduction_report(component[key]['deduction_data'], total_net_amount)
                    data[s].append(yearly_amount)
                    total_deduction_component+=yearly_amount
            
        report = pd.DataFrame(data)

        last_file = os.path.join(f"{file_path}{company_name}_report.xlsx")
        my_file = Path(last_file) 
        if my_file.is_file(): 
            os.remove(last_file)
        try:
            os.mkdir(file_path)
        except Exception:
            pass

        report.to_excel(f"{file_path}{company_name}_report.xlsx", index=False)

        file_name = f"{file_path}{company_name}_report.xlsx"
        
        uploaded_file = upload_file_aws_s3_bucket_using_file_path(
                model_instance_id=1, file_path=file_name, model_name="CompanyReport"
                )
        try:
            os.remove(f"{file_name}") ## Delete file when done
        except PermissionError:
            pass

        return uploaded_file

