import calendar
from datetime import datetime, timedelta
import json
import os
import time
from typing import Dict, List, Optional, Tuple
import uuid

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from django.db import IntegrityError, models, transaction
from django.db.models import F, Sum, Q
from django.utils import timezone

from account.enums import *
from account.helpers.vbank_handler import VfdBankMgr
from account.tasks import (
    fund_wallet_update,
    handle_company_wema_inflow,
    notify_liberty_credit_float_deduction,
    transfer_commission,
    verify_add_fund_by_libertypay,
    vfd_send_money,
    wema_send_money,
)
from core.helpers.apis.request_cls import LibertyPayPlus
from core.models import BaseModel, BlackListEntry, ConstantTable, KycDetails
from helpers.disbursment_helper import LibertyPay
from helpers.reusable_functions import convert_date_format
from payroll_app.apis.func import round_amount
from requisition.models import Company, CompanyVerificationInfo
from stock_inventory.models import Branch


User = get_user_model()
logger = settings.LOGGER


# Create your model(s) here.
class AccountServiceProvider(BaseModel):
    user = models.ForeignKey(
        User,
        related_name="account_services",
        on_delete=models.CASCADE,
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_account_services",
    )
    branch = models.ForeignKey(
        Branch,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="branch_account_services",
    )
    account_number = models.CharField(max_length=250, unique=True)
    account_name = models.CharField(max_length=250)
    account_provider = models.CharField(max_length=300, choices=AcctProvider.choices)
    payload = models.TextField(null=True)

    def __str__(self) -> str:
        return f"{self.account_provider}: {self.account_name}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ACCOUNT SERVICE PROVIDER"
        verbose_name_plural = "ACCOUNT SERVICE PROVIDERS"


class AccountSystem(BaseModel):
    user = models.ForeignKey(User, related_name="accounts", on_delete=models.CASCADE)
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="company_acct",
    )
    branch = models.ForeignKey(
        Branch,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="branch_acct",
    )
    account_provider = models.CharField(
        max_length=300, choices=AcctProvider.choices, default=AcctProvider.VFD
    )
    account_number = models.CharField(
        max_length=250, unique=True, null=False, blank=False
    )
    account_name = models.CharField(max_length=250, null=True, blank=True)
    account_type = models.CharField(
        max_length=250,
        choices=AccountType.choices,
        default=AccountType.SPEND_MGMT,
    )
    bank_name = models.CharField(max_length=250, null=True, blank=True)
    bank_code = models.CharField(max_length=250, null=True, blank=True)
    is_test = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    request_status = models.CharField(
        max_length=250,
        choices=TransactionStatus.choices,
        null=True,
        blank=True,
    )
    updated = models.BooleanField(default=False)
    available_balance = models.FloatField(
        default=0.0, validators=[MinValueValidator(0.0)]
    )
    payload = models.TextField(null=True)
    service_provider = models.ForeignKey(
        AccountServiceProvider,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )

    def __str__(self) -> str:
        return f"{self.account_type.lower()}-{self.id}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ACCOUNT SYSTEM"
        verbose_name_plural = "ACCOUNT SYSTEM"

        # constraints = [
        #     models.UniqueConstraint(
        #         fields=["account_type", "account_name", "user"],
        #         condition=Q(company__isnull=True),
        #         name="unique_account_constraint_company_null",
        #     )
        # ]

    @classmethod
    def account_match(cls, account_instance, requisition_type):
        return account_instance.account_type == requisition_type

    @classmethod
    def get_user_prev_acct(cls):
        KycDetails.object.filter()
        account_queryset = AccountSystem.objects.filter(
            user_id=user_id,
            account_provider=AcctProvider.VFD,
            is_active=True,
        )

        previous_account_number = (
            account_queryset.first().account_number
            if account_queryset.exists()
            else None
        )


class AccountCreationFailure(BaseModel):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="creation_fails",
    )
    account_type = models.CharField(
        max_length=250,
        choices=AccountType.choices,
        null=True,
        blank=True,
    )
    account_provider = models.CharField(max_length=300, null=True, blank=True)
    is_test = models.BooleanField(default=False)
    payload = models.TextField(null=True, blank=True)
    request_payload = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ACCOUNT CREATION FAILURE"
        verbose_name_plural = "ACCOUNT CREATION FAILURE"


class Wallet(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    account = models.ForeignKey(
        AccountSystem, on_delete=models.CASCADE, null=True, blank=True
    )
    balance = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    previous_balance = models.FloatField(
        default=0.0, validators=[MinValueValidator(0.0)]
    )
    wallet_type = models.CharField(
        max_length=250,
        choices=AccountType.choices,
        default=AccountType.SPEND_MGMT,
    )
    is_active = models.BooleanField(default=True)

    def __str__(self) -> str:
        return f"wallet-{self.id}"

    def save(self, *args, **kwargs):

        try:
            if self.pk:
                self.previous_balance = self.__original_amount

            return super(Wallet, self).save(*args, **kwargs)
        except Exception as err:
            raise Exception(f"{err}")

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "WALLET SYSTEM"
        verbose_name_plural = "WALLET SYSTEM"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__original_amount = self.balance

    @classmethod
    def transfer_to_wallet(
        cls,
        sender_number_account_number: str,
        recipient_account_number: str,
        amount: float,
        narration: str = "",
        lookup_method="ACCOUNT_NUMBER",
    ):

        if amount <= 0:
            raise ValueError("Transfer amount must be greater than zero.")

        if lookup_method == "ACCOUNT_NUMBER":

            sender_wallet = Wallet.objects.filter(
                account__account_number=sender_number_account_number, is_active=True
            ).first()
            recipient_wallet = Wallet.objects.filter(
                account__account_number=recipient_account_number, is_active=True
            ).first()

            if not sender_wallet:
                return {
                    "status": False,
                    "message": f"Sender's wallet not found or inactive for account number: {sender_number_account_number}.",
                }
            if not recipient_wallet:
                return {
                    "status": False,
                    "message": f"Recipient's wallet not found or inactive for account number: {recipient_account_number}.",
                }
            if sender_wallet.balance < amount:
                return {
                    "status": False,
                    "message": f"Insufficient balance in sender's wallet. Current balance: {sender_wallet.balance}, attempted transfer amount: {amount}.",
                }

            sender_acct_instance = sender_wallet.account
            sender = sender_acct_instance.user
            #########################################################
            recipient_wallet_acct_instance = recipient_wallet.account
            recipient = recipient_wallet_acct_instance.user

        # Perform atomic transaction
        with transaction.atomic():
            sender_balance_before = sender_wallet.balance
            sender_wallet.balance = F("balance") - amount
            sender_wallet.save()
            sender_wallet.refresh_from_db()

            sender_balance_after = sender_wallet.balance

            sender_debit_txn_instance = Transaction.objects.create(
                transaction_type=TransactionType.WALLET_TRANSFER,
                user=sender,
                # company_name=company_name,
                user_full_name=sender.full_name,
                user_uuid=sender.id,
                user_email=sender.email,
                payout_type=sender_acct_instance.account_type,
                balance_before=sender_balance_before,
                balance_after=sender_balance_after,
                # float_to_external_ref=float_to_external_ref,
                # float_to_internal_ref=float_to_internal_ref,
                amount=amount,
                # commission=commission,
                # stamp_duty=vfd_stamp_duty,
                # provider_fee=provider_fee,
                debit_amount=amount,
                beneficiary_account_number=recipient_account_number,
                beneficiary_account_name=recipient_wallet_acct_instance.account_name,
                # bank_name="PAYBOX",
                status=TransactionStatus.SUCCESSFUL,
                narration=f"Debit: Wallet transfer to {recipient_account_number} from {sender_number_account_number}",
                # bank_code=bank_code,
                source_account_name=sender_acct_instance.account_name,
                source_account_number=sender_acct_instance.account_number,
                # company_id=company_id,
                # transfer_provider=AcctProvider.VFD,
                # pension_data_id=pension_data_id,
            )

            # Create sender's debit record
            DebitCreditRecordOnAccount.objects.create(
                user=sender,
                wallet=sender_wallet,
                entry=DebitCreditEntry.DEBIT,
                requisition_type=sender_wallet.wallet_type,
                balance_before=sender_balance_before,
                balance_after=sender_balance_after,
                amount=amount,
                type_of_trans="Pay Buddy",
                transaction_instance_id=str(sender_debit_txn_instance.transaction_ref),
                # date_credited=now(),
            )

            #####################################################################################################################

            recipient_balance_before = recipient_wallet.balance

            recipient_wallet.balance = F("balance") + amount
            recipient_wallet.save()
            recipient_wallet.refresh_from_db()

            recipient_balance_after = recipient_wallet.balance

            recipient_credit_txn_instance = Transaction.objects.create(
                transaction_type=TransactionType.FUND_BUDDY,
                user=recipient,
                # company_name=company_name,
                user_full_name=recipient.full_name,
                user_uuid=recipient.id,
                user_email=recipient.email,
                payout_type=recipient_wallet_acct_instance.account_type,
                balance_before=recipient_balance_before,
                balance_after=recipient_balance_after,
                # float_to_external_ref=float_to_external_ref,
                # float_to_internal_ref=float_to_internal_ref,
                amount=amount,
                # commission=commission,
                # stamp_duty=vfd_stamp_duty,
                # provider_fee=provider_fee,
                debit_amount=amount,
                beneficiary_account_number=recipient_account_number,
                beneficiary_account_name=recipient_wallet_acct_instance.account_name,
                # bank_name="User wallet",
                status=TransactionStatus.SUCCESSFUL,
                narration=f"Credit: Wallet transfer from {sender_acct_instance.account_name} to your wallet {recipient_account_number}",
                # bank_code=bank_code,
                source_account_name=sender_acct_instance.account_name,
                source_account_number=sender_acct_instance.account_number,
                # company_id=company_id,
                # transfer_provider=AcctProvider.VFD,
                # pension_data_id=pension_data_id,
            )

            # Create sender's debit record
            DebitCreditRecordOnAccount.objects.create(
                user=recipient,
                wallet=recipient_wallet,
                entry=DebitCreditEntry.CREDIT,
                requisition_type=recipient_wallet.wallet_type,
                balance_before=recipient_balance_before,
                balance_after=recipient_balance_after,
                amount=amount,
                type_of_trans="Pay Buddy",
                transaction_instance_id=str(
                    recipient_credit_txn_instance.transaction_ref
                ),
                # date_credited=now(),
            )

            return {
                "status": True,
                "message": "Transaction completed successfully.",
                "amount_funded": amount,
                "recipient_account_number": recipient_account_number,
                "recipient_account_name": recipient_wallet_acct_instance.account_name,
            }

    @classmethod
    def has_sufficient_funds(
        cls, user, wallet_type: AccountType, amount: float, account: AccountSystem
    ) -> dict:
        """
        Determines if a user has sufficient funds in a specified wallet to cover a given amount.

        This method checks if the user has a wallet of the specified type with a balance
        that is greater than or equal to the amount specified.

        Args:
            user (User): The user whose wallet balance is being checked.
            wallet_type (AccountType): The type of wallet to check (e.g., savings, current).
            amount (float): The amount to verify against the wallet's balance.

        Returns:
            dict: A dictionary containing:
                - "is_chargeable" (bool): True if the wallet has sufficient funds, otherwise False.
                - "wallet_instance" (Wallet or None): The wallet instance that meets the criteria,
                or None if no such wallet exists.
        """
        # Query the database for a wallet instance that matches the user, wallet type, and amount criteria.

        wallet_query = cls.objects.filter(
            user=user, wallet_type=wallet_type, account=account
        )

        # Determine if the wallet is chargeable (has sufficient funds) and get the first matching wallet instance.
        if wallet_query.exists():
            wallet_instance = wallet_query.first()
        else:
            wallet_instance = None

        result = {
            "is_chargeable": (
                wallet_instance.balance >= amount if wallet_instance else False
            ),
            "balance": wallet_instance.balance if wallet_instance else 0.0,
            "expected_debit": amount,
            "wallet_instance": wallet_instance,
        }
        # logger.info(f"{result}")
        return result

    @classmethod
    def charge_wallet(
        cls, wallet_instance, amount: float, transaction_instance
    ) -> dict:
        if amount > 0:

            wallet_type = wallet_instance.wallet_type
            user = wallet_instance.user

            balance_before = wallet_instance.balance

            transaction_instance.balance_before = balance_before
            # transaction_instance.float_to_internal_ref = reference
            transaction_instance.balance_after = balance_before - amount
            transaction_instance.transfer_stage = TransferStage.DEBIT
            transaction_instance.save()

            # Deduct the amount from the wallet's balance and save the updated balance
            wallet_instance.balance = F("balance") - amount
            wallet_instance.save()
            wallet_instance.refresh_from_db()

            # Record the balance after the charge
            balance_after = wallet_instance.balance

            # Create a debit entry in the DebitCreditRecordOnAccount
            DebitCreditRecordOnAccount.objects.create(
                user=user,
                entry=DebitCreditEntry.DEBIT,
                requisition_type=wallet_type,
                wallet=wallet_instance,
                balance_before=balance_before,
                amount=amount,
                balance_after=balance_after,
                transaction_instance_id=transaction_instance.id,
            )

            return {
                "amount_before": balance_before,
                "amount_after": balance_after,
                "wallet": wallet_instance,
            }
        else:
            raise ValueError(f"Cannot Charge {amount} from User wallet")

    @classmethod
    def wallet_reversal(cls, transaction_instance: "Transaction"):
        expectd_reversal_status = [TransactionStatus.FAILED, TransactionStatus.REVERSED]
        # print(transaction_instance.is_reversed)
        # print(transaction_instance.status, "\n\n")
        if (
            not transaction_instance.is_reversed
            and transaction_instance.status in expectd_reversal_status
        ):
            user = transaction_instance.user
            wallet_type = transaction_instance.payout_type
            # account system
            account = AccountSystem.objects.filter(
                user=user, account_number=transaction_instance.source_account_number
            ).first()

            wallet_instance = cls.objects.filter(
                user=user, wallet_type=wallet_type, account=account
            ).first()
            if wallet_instance:
                # amount = transaction_instance.debit_amount
                amount = transaction_instance.amount + transaction_instance.commission + transaction_instance.provider_fee - transaction_instance.stamp_duty  # type: ignore

                reversal_transaction = Transaction.objects.create(
                    amount=amount,
                    transaction_type=TransactionType.REVERSAL,
                    total_amount_received=amount,
                    reversal_for=transaction_instance.id,
                    user=transaction_instance.user,
                    company_name=transaction_instance.company_name,
                    user_full_name=transaction_instance.user_full_name,
                    user_email=transaction_instance.user_email,
                    payout_type=transaction_instance.payout_type,
                    beneficiary_account_number=transaction_instance.beneficiary_account_number,
                    bank_name=transaction_instance.bank_name,
                    # narration=transaction_instance.narration,
                    bank_code=transaction_instance.bank_code,
                    transfer_provider=transaction_instance.transfer_provider,
                    source_account_name=transaction_instance.source_account_name,
                    source_account_number=transaction_instance.source_account_number,
                )
                balance_before = wallet_instance.balance

                # Deduct the amount from the wallet's balance and save the updated balance
                wallet_instance.balance = F("balance") + amount  # type: ignore
                wallet_instance.save()  # type: ignore
                wallet_instance.refresh_from_db()  # type: ignore

                # Record the balance after the charge
                balance_after = wallet_instance.balance

                # Create a debit entry in the DebitCreditRecordOnAccount
                DebitCreditRecordOnAccount.objects.create(
                    user=user,
                    entry=DebitCreditEntry.REVERSAL,
                    requisition_type=wallet_type,
                    wallet=wallet_instance,
                    balance_before=balance_before,
                    amount=amount,
                    balance_after=balance_after,
                    transaction_instance_id=transaction_instance.id,
                )

                # transaction_instance.balance_before = balance_before
                # transaction_instance.balance_after = balance_after
                transaction_instance.is_reversed = True
                transaction_instance.save()

                reversal_transaction.balance_before = balance_before
                reversal_transaction.balance_after = balance_after
                reversal_transaction.status = TransactionStatus.SUCCESSFUL
                reversal_transaction.is_reversed = True
                reversal_transaction.save()

                # send reversal mail

                cls.send_reversal_mail(
                    recipient=reversal_transaction.user.email,
                    amount=reversal_transaction.amount,
                    balance_after=reversal_transaction.balance_after,
                    acct_name=reversal_transaction.source_account_name,
                    account_number=reversal_transaction.source_account_number,
                    wallet_type=reversal_transaction.payout_type,
                )

                return {
                    "amount_before": balance_before,
                    "amount_after": balance_after,
                    "wallet_id": str(wallet_instance.id),
                }
            else:
                return None

    @classmethod
    def get_wallet_instance(cls, user, account=None):
        if account:
            try:
                wallet_ins = cls.objects.get(user=user, account=account)
                return wallet_ins
            except cls.DoesNotExist:
                return None
        else:
            return None
            # try:
            #     wallet_ins = cls.objects.get(user=user, is_active=True)
            #     return wallet_ins
            # except cls.DoesNotExist:
            #     return None

    @classmethod
    def get_wallet_balance(cls, user, account=None):
        if account:
            this_wallet = cls.get_wallet_instance(user, account)
            if this_wallet:
                return this_wallet.balance
            else:
                return 0
        else:
            this_wallet = cls.get_wallet_instance(user)
            if this_wallet:
                return this_wallet.balance
            else:
                return 0

    @classmethod
    def fund_wallet(cls, account_id, transaction_id, webhook_notification=False):

        with transaction.atomic():
            initial_transaction_record = Transaction.objects.get(id=transaction_id)
            account = AccountSystem.objects.get(id=account_id)

            if webhook_notification:
                Transaction.objects.filter(txn_id=str(transaction_id)).update(
                    status=TransactionStatus.SUCCESSFUL
                )

            amount = initial_transaction_record.amount
            debit_credit_record = DebitCreditRecordOnAccount.objects.create(
                user=account.user,
                entry=DebitCreditEntry.CREDIT,
                requisition_type=account.account_type,
                amount=amount,
                transaction_instance_id=initial_transaction_record.id,
                date_credited=timezone.now(),
            )

            # fund wallet
            wallet_instance = cls.objects.get(account=account)
            previous_balance = wallet_instance.balance
            wallet_instance.balance = F("balance") + amount
            wallet_instance.save()

            wallet_instance.refresh_from_db()
            updated_balance = wallet_instance.balance

            debit_credit_record.wallet = wallet_instance
            debit_credit_record.balance_before = previous_balance
            debit_credit_record.balance_after = updated_balance
            debit_credit_record.save()

            initial_transaction_record.balance_before = previous_balance
            initial_transaction_record.balance_after = updated_balance
            initial_transaction_record.status = TransactionStatus.SUCCESSFUL
            initial_transaction_record.date_credited = timezone.now()
            initial_transaction_record.save()

            if webhook_notification:
                cls.bank_deposit_email_notification(
                    recipient=account.user.email,
                    amount=amount,
                    balance_after=updated_balance,
                    acct_name=account.account_name,
                    account_number=account.account_number,
                    wallet_type=account.account_type,
                    source_nuban=initial_transaction_record.source_account_number,
                    source_acct_name=initial_transaction_record.source_account_name,
                    narration=initial_transaction_record.narration,
                )

            return True

    @classmethod
    def reversal(cls, account, initial_transaction_record, check_stmp_amt=False):
        with transaction.atomic():
            stmp_result = initial_transaction_record.get_vfd_stamp_duty_result()
            charge_stmp = stmp_result.get("charge_stmp")
            amount = stmp_result.get("amount")
            stamp_duty = stmp_result.get("stamp_duty")

            debit_credit_record = DebitCreditRecordOnAccount.objects.create(
                user=account.user,
                entry="REVERSAL",
                requisition_type=account.account_type,
                amount=amount,
                transaction_instance_id=initial_transaction_record.id,
                # date_credited=initial_transaction_record.date_credited
            )

            # fund wallet
            wallet_instance = cls.objects.get(account=account)
            previous_balance = wallet_instance.balance
            wallet_instance.balance = F("balance") + amount
            wallet_instance.save()

            wallet_instance.refresh_from_db()
            updated_balance = wallet_instance.balance

            debit_credit_record.wallet = wallet_instance
            debit_credit_record.balance_before = previous_balance
            debit_credit_record.balance_after = updated_balance
            debit_credit_record.save()

            initial_transaction_record.balance_before = previous_balance
            initial_transaction_record.balance_after = updated_balance
            initial_transaction_record.is_reversed = True
            initial_transaction_record.save()
            reversal_data = {
                "previous_balance": previous_balance,
                "updated_balance": updated_balance,
            }

            recipient_email = account.user.email
            cls.send_reversal_mail(
                recipient=recipient_email,
                amount=amount,
                balance_after=updated_balance,
                acct_name=account.account_name,
                account_number=account.account_number,
                wallet_type=account.account_type,
            )
            return reversal_data

    @classmethod
    def fund_wallet_vfd_bank_call_back(cls, response_result):

        benficiary_account_number = response_result.get("benficiary_account_number")
        source_nuban = response_result.get("source_nuban")
        source_account_name = response_result.get("source_account_name")
        source_bank_code = response_result.get("source_bank_code")
        unique_reference = response_result.get("unique_reference")
        timestamp = response_result.get("timestamp")
        narration = response_result.get("narration")
        session_id = response_result.get("session_id")

        received_amount = float(response_result.get("received_amount"))

        account_instance = AccountSystem.objects.get(
            account_number=benficiary_account_number
        )
        user = account_instance.user

        # verify that uniques reference does not exist
        txn = Transaction.objects.filter(bank_deposit_ref=unique_reference)

        if txn.exists():
            meta_data_instance = TransactionMetaData.objects.create(
                user=user, fund_wallet_payload=response_result
            )

            transaction_instance = Transaction.objects.create(
                user=user,
                beneficiary_account_number=benficiary_account_number,
                beneficiary_account_name=account_instance.account_name,
                bank_code=source_bank_code,
                source_account_name=source_account_name,
                source_account_number=source_nuban,
                user_full_name=user.full_name,
                user_email=user.email,
                transaction_type=TransactionType.DUPLICATE_FUND_WALLET,
                amount=received_amount,
                total_amount_received=received_amount,
                bank_deposit_ref=unique_reference,
                status=TransactionStatus.IGNORE_HISTORY,
                transfer_provider=AcctProvider.VFD,
                narration=narration,
                payout_type=account_instance.account_type,
                date_credited=timestamp,
                bank_deposit_session_id=session_id,
            )
            meta_data_instance.transaction = transaction_instance
            meta_data_instance.save()
        else:

            meta_data_instance = TransactionMetaData.objects.create(
                user=user, fund_wallet_payload=response_result
            )
            company_name = (
                account_instance.company.company_name
                if account_instance.company
                else None
            )
            company_id = (
                account_instance.company.id if account_instance.company else None
            )
            transaction_instance = Transaction.objects.create(
                user=user,
                company_name=company_name,
                beneficiary_account_number=benficiary_account_number,
                beneficiary_account_name=account_instance.account_name,
                bank_code=source_bank_code,
                source_account_name=source_account_name,
                source_account_number=source_nuban,
                user_full_name=user.full_name,
                user_email=user.email,
                transaction_type=TransactionType.DEPOSIT,
                amount=received_amount,
                total_amount_received=received_amount,
                bank_deposit_ref=unique_reference,
                status=TransactionStatus.PENDING,
                transfer_provider=AcctProvider.VFD,
                narration=narration,
                payout_type=account_instance.account_type,
                company_id=company_id,
                date_credited=timestamp,
                bank_deposit_session_id=session_id,
            )

            stmp_result = transaction_instance.get_vfd_stamp_duty_result()
            charge_stmp = stmp_result.get("charge_stmp")
            amount = stmp_result.get("amount")
            stamp_duty = stmp_result.get("stamp_duty")

            if charge_stmp:
                # create a stamp duty transaction record on user's deposit
                transaction_instance.amount = (
                    amount  # update amount to result amount after stampduty charge
                )
                transaction_instance.save()

                Transaction.objects.create(
                    user=transaction_instance.user,
                    beneficiary_account_number=transaction_instance.beneficiary_account_number,
                    beneficiary_account_name=transaction_instance.beneficiary_account_name,
                    bank_code=transaction_instance.bank_code,
                    source_account_name=transaction_instance.source_account_name,
                    source_account_number=transaction_instance.source_account_number,
                    user_full_name=transaction_instance.user_full_name,
                    user_email=transaction_instance.user_email,
                    transaction_type=TransactionType.STAMP_DUTY,
                    amount=stamp_duty,
                    status=TransactionStatus.PENDING,
                    transfer_provider=AcctProvider.VFD,
                    narration=f"STAMP DUTY|TransactionId{transaction_instance.id}{transaction_instance.bank_deposit_ref}",
                    payout_type=transaction_instance.payout_type,
                    txn_id=transaction_instance.id,
                )

            meta_data_instance.transaction = transaction_instance
            meta_data_instance.save()
            fund_wallet_update.delay(
                account_id=account_instance.id,
                transaction_id=transaction_instance.id,
            )

            return True

    @classmethod
    def deduct_balance(cls, user, amount, account):
        with transaction.atomic():
            wallet = cls.get_wallet_instance(user=user, account=account)
            initial_balance = wallet.balance

            if 0 < amount <= initial_balance:
                wallet.balance = F("balance") - amount
                wallet.save()
                wallet.refresh_from_db()

                wallet_data = {
                    "succeeded": True,
                    "amount_before": initial_balance,
                    "amount_after": wallet.balance,
                    "wallet": wallet,
                }
                return wallet_data

            else:
                failed = {
                    "succeeded": False,
                    "amount_before": None,
                    "amount_after": None,
                    "wallet": wallet,
                }
                return failed

    @classmethod
    def deduct_sms_balance(cls, amount, wallet):
        with transaction.atomic():
            initial_balance = wallet.balance

            if 0 < amount <= initial_balance:
                wallet.balance = F("balance") - amount
                wallet.save()
                wallet.refresh_from_db()

                wallet_data = {
                    "succeeded": True,
                    "amount_before": initial_balance,
                    "amount_after": wallet.balance,
                    "wallet": wallet,
                }
                return wallet_data

            else:
                failed = {
                    "succeeded": False,
                    "amount_before": None,
                    "amount_after": None,
                    "wallet": wallet,
                }
                return failed

    @classmethod
    def create_wallet_object(cls, account_ins, user_id, wallet_type):
        wallet_ins = cls.objects.create(
            user_id=user_id, account=account_ins, wallet_type=wallet_type
        )
        return wallet_ins

    @classmethod
    def fetch_wallets(cls, user):
        """
        This function takes in a user instance and selected wallets and returns all user wallets
        """
        wallets = cls.objects.select_related("user").filter(user=user, is_active=True)
        return wallets

    @classmethod
    def get_wallet(cls, user, wallet_type) -> "Wallet":
        """
        This function takes in a user instance and a wallet type and return the wallet object
        """
        wallets_queryset = cls.fetch_wallets(user=user)
        wallet = wallets_queryset.filter(wallet_type=wallet_type).first()
        return wallet

    @classmethod
    def fund_wallet_pay_buddy(
        cls,
        sender_wallet: "Wallet",
        receiver_wallet: "Wallet",
        amount,
        sender_transaction_ins: "Transaction",
        payout_type,
    ):

        wallet = receiver_wallet
        if receiver_wallet.account.company:
            company_name = receiver_wallet.account.company.company_name
        else:
            company_name = None

        # create Transaction for receiver
        transaction = Transaction.objects.create(
            user=wallet.user,
            wallet_id=wallet.id,
            user_uuid=wallet.user.id,
            wallet_type=payout_type,
            transaction_type="BUDDY",
            user_full_name=wallet.user.full_name,
            user_email=wallet.user.email,
            payout_type=payout_type,
            amount=amount,
            company_name=company_name,
            source_wallet_id=sender_wallet.id,
            source_wallet_type=sender_wallet.wallet_type,
            transfer_stage="INTERNAL_TO_INTERNAL",
            narration=sender_transaction_ins.narration,
            internal_to_internal_ref=sender_transaction_ins.fund_internal_buddy_ref,
            status="SUCCESSFUL",
        )
        # get receiver balance after and credit
        balance_before = wallet.balance
        balance_after = wallet.balance + amount
        wallet.balance = balance_after
        wallet.save()

        # update wallet balance after for transaction
        transaction.balance_before = balance_before
        transaction.balance_after = balance_after
        transaction.save()

        # credit receiver
        credit_record = DebitCreditRecordOnAccount.objects.create(
            user=wallet.user,
            entry="CREDIT",
            requisition_type=payout_type,
            wallet=wallet,
            balance_before=balance_before,
            amount=amount,
            balance_after=balance_after,
            # type_of_trans=trans_type,
            transaction_instance_id=transaction.id,
        )

        return {
            "balance_before": balance_before,
            "balance_after": balance_after,
            "record": credit_record,
            "wallet_instance": wallet,
            "transaction_ref": transaction.transaction_ref,
        }

    @classmethod
    def send_money_via_buddy(
        cls,
        narration,
        amount,
        user,
        payout_type,
        transaction_pin,
        account_inst,
        receiver,
        charge_fee,
    ):
        from accounting.utils import categorize_transaction

        # verify sender wallet
        sender_wallet_type = payout_type
        sender_wallet_instance = cls.objects.filter(
            account=account_inst, wallet_type=sender_wallet_type
        ).first()
        if sender_wallet_instance is None:
            result = {
                "succeded": False,
                "status_code": "02",
                "message": "Sender account does not match",
                "results": None,
            }
            return result

        # verify receiver account
        receiver_wallet_type = "PERSONAL_PAYROLL"

        receiver_wallet_instance = cls.objects.filter(
            user=receiver, wallet_type=receiver_wallet_type
        ).first()

        if receiver_wallet_instance is None:
            result = {
                "succeded": False,
                "status_code": "04",
                "message": "Receiver account does not match",
                "results": None,
            }
            return result

        provider_fee = 0.0

        total_amount_to_debit = provider_fee + amount + charge_fee
        company_name = (
            account_inst.company.company_name if account_inst.company else None
        )
        company_id = account_inst.company.id if account_inst.company else None
        transaction = Transaction.objects.create(
            user=user,
            user_uuid=user.id,
            wallet_id=sender_wallet_instance.id,
            wallet_type=sender_wallet_type,
            transaction_type="BUDDY",
            user_full_name=user.full_name,
            user_email=user.email,
            payout_type=sender_wallet_type,
            amount=amount,
            debit_amount=total_amount_to_debit,
            commission=charge_fee,
            provider_fee=provider_fee,
            narration=narration,
            status="PENDING",
            company_id=company_id,
            company_name=company_name,
        )

        # Charge wallet
        debit_wallet = cls.deduct_balance(
            user=user,
            amount=total_amount_to_debit,
            account=account_inst,
        )

        if debit_wallet.get("succeeded") is True:

            reference = Transaction.create_unique_transaction_ref(suffix=payout_type)
            fund_buddy_reference = Transaction.create_unique_transaction_ref(
                suffix=payout_type
            )

            wallet_instance = debit_wallet.get("wallet")
            balance_after = debit_wallet.get("amount_after")
            balance_before = debit_wallet.get("amount_before")
            transaction.balance_before = balance_before
            transaction.internal_to_internal_ref = reference
            transaction.balance_after = balance_after
            transaction.fund_internal_buddy_ref = fund_buddy_reference
            transaction.transfer_stage = "INTERNAL_TO_INTERNAL"
            transaction.beneficiary_wallet_id = receiver_wallet_instance.id
            transaction.beneficiary_wallet_type = receiver_wallet_type
            transaction.status = "SUCCESSFUL"
            transaction.save()

            debit_record = DebitCreditRecordOnAccount.objects.create(
                user=user,
                entry="DEBIT",
                requisition_type=sender_wallet_type,
                wallet=wallet_instance,
                balance_before=balance_before,
                amount=total_amount_to_debit,
                balance_after=balance_after,
                transaction_instance_id=transaction.id,
            )

            send_money_to_buddy = cls.fund_wallet_pay_buddy(
                sender_wallet=sender_wallet_instance,
                receiver_wallet=receiver_wallet_instance,
                amount=amount,
                sender_transaction_ins=transaction,
                payout_type=receiver_wallet_type,
            )

            result = {
                "succeded": True,
                "status_code": "00",
                "message": "success",
                "results": {
                    "unique_ref": transaction.transaction_ref,
                    "receiver_record": send_money_to_buddy,
                    "sender_record": debit_record,
                    "sender_wallet": sender_wallet_instance,
                    "amount_sent": amount,
                    "charge_fee": provider_fee,
                    "total_amount": total_amount_to_debit,
                    "status": "SUCCESS",
                },
            }
            try:
                categorize_transaction(
                    transaction, source="hr", transaction_type="payroll_expenses"
                )
            except Exception as e:
                logger.info(f"PAYROLL TO BUDDY WALLET -> {str(e)}")
            return result

        else:
            result = {
                "succeded": False,
                "status_code": "03",
                "message": "insufficient funds",
                "results": None,
            }
            return result

    @classmethod
    def send_pension_money_via_buddy(
        cls,
        narration,
        amount,
        user,
        payout_type,
        account_inst,
        receiver,
        charge_fee,
    ):

        # verify sender wallet
        sender_wallet_type = payout_type
        sender_wallet_instance = cls.objects.filter(
            account=account_inst, wallet_type=sender_wallet_type
        ).first()
        if sender_wallet_instance is None:
            result = {
                "succeded": False,
                "status_code": "02",
                "message": "Sender account does not match",
                "results": None,
            }
            return result

        # verify receiver account
        receiver_wallet_type = "PENSION"

        receiver_wallet_instance = cls.objects.filter(
            account=receiver, wallet_type=receiver_wallet_type
        ).first()
        if receiver_wallet_instance is None:
            result = {
                "succeded": False,
                "status_code": "04",
                "message": "Receiver account does not match",
                "results": None,
            }
            return result

        provider_fee = 0.0

        total_amount_to_debit = provider_fee + amount + charge_fee
        company_name = (
            account_inst.company.company_name if account_inst.company else None
        )
        transaction = Transaction.objects.create(
            user=user,
            user_uuid=user.id,
            wallet_id=sender_wallet_instance.id,
            wallet_type=sender_wallet_type,
            transaction_type="BUDDY",
            user_full_name=user.full_name,
            user_email=user.email,
            payout_type=sender_wallet_type,
            amount=amount,
            debit_amount=total_amount_to_debit,
            commission=charge_fee,
            provider_fee=provider_fee,
            narration=narration,
            status="PENDING",
            company_name=company_name,
        )

        # Charge wallet
        debit_wallet = cls.deduct_balance(
            user=user,
            amount=total_amount_to_debit,
            account=account_inst,
        )

        if debit_wallet.get("succeeded") is True:

            reference = Transaction.create_unique_transaction_ref(suffix=payout_type)
            fund_buddy_reference = Transaction.create_unique_transaction_ref(
                suffix=payout_type
            )

            wallet_instance = debit_wallet.get("wallet")
            balance_after = debit_wallet.get("amount_after")
            balance_before = debit_wallet.get("amount_before")
            transaction.balance_before = balance_before
            transaction.internal_to_internal_ref = reference
            transaction.balance_after = balance_after
            transaction.fund_internal_buddy_ref = fund_buddy_reference
            transaction.transfer_stage = "INTERNAL_TO_INTERNAL"
            transaction.beneficiary_wallet_id = receiver_wallet_instance.id
            transaction.beneficiary_wallet_type = receiver_wallet_type
            transaction.status = "SUCCESSFUL"
            transaction.save()

            debit_record = DebitCreditRecordOnAccount.objects.create(
                user=user,
                entry="DEBIT",
                requisition_type=sender_wallet_type,
                wallet=wallet_instance,
                balance_before=balance_before,
                amount=total_amount_to_debit,
                balance_after=balance_after,
                transaction_instance_id=transaction.id,
            )

            send_money_to_buddy = cls.fund_wallet_pay_buddy(
                sender_wallet=sender_wallet_instance,
                receiver_wallet=receiver_wallet_instance,
                amount=amount,
                sender_transaction_ins=transaction,
                payout_type=receiver_wallet_type,
            )

            result = {
                "succeded": True,
                "status_code": "00",
                "message": "success",
                "results": {
                    "unique_ref": transaction.transaction_ref,
                    "receiver_record": send_money_to_buddy,
                    "sender_record": debit_record,
                    "sender_wallet": sender_wallet_instance,
                    "amount_sent": amount,
                    "charge_fee": provider_fee,
                    "total_amount": total_amount_to_debit,
                    "status": "SUCCESS",
                },
            }
            return result

        else:
            result = {
                "succeded": False,
                "status_code": "03",
                "message": "insufficient funds",
                "results": None,
            }
            return result

    @classmethod
    def charge_company_for_sms_campaign(cls, user, amount, wallet_ins):

        CONST = ConstantTable.get_constant_instance()

        if CONST.send_sms_campaign is True:

            narration = f"{wallet_ins.account.company.company_name}:SMS"

            provider_fee = 0
            total_amount_to_debit = amount
            company_name = (
                wallet_ins.account.company.company_name
                if wallet_ins.account.company
                else None
            )

            transaction = Transaction.objects.create(
                user=user,
                user_uuid=user.id,
                wallet_id=wallet_ins.id,
                wallet_type=wallet_ins.wallet_type,
                transaction_type="BUDDY",
                user_full_name=user.full_name,
                user_email=user.email,
                # payout_type=sender_wallet_type,
                amount=amount,
                debit_amount=total_amount_to_debit,
                provider_fee=provider_fee,
                narration=narration,
                status="FAILED",
                company_name=company_name,
            )

            # Charge wallet
            debit_wallet = cls.deduct_sms_balance(
                amount=total_amount_to_debit, wallet=wallet_ins
            )

            if debit_wallet.get("succeeded") is True:

                reference = Transaction.create_unique_transaction_ref(suffix="WHISPER")
                # fund_buddy_reference = Transaction.create_unique_transaction_ref(
                #     suffix="WHISPER"
                # )

                wallet_instance = debit_wallet.get("wallet")
                balance_after = debit_wallet.get("amount_after")
                balance_before = debit_wallet.get("amount_before")
                transaction.balance_before = balance_before
                transaction.internal_to_internal_ref = reference
                transaction.balance_after = balance_after
                # transaction.fund_internal_buddy_ref = fund_buddy_reference
                transaction.transfer_stage = "INTERNAL_TO_INTERNAL"
                # transaction.beneficiary_wallet_id = receiver_wallet_instance.id
                # transaction.beneficiary_wallet_type = receiver_wallet_type
                transaction.status = "SUCCESSFUL"
                transaction.save()

                debit_record = DebitCreditRecordOnAccount.objects.create(
                    user=user,
                    entry="DEBIT",
                    requisition_type="SMS",
                    wallet=wallet_instance,
                    balance_before=balance_before,
                    amount=total_amount_to_debit,
                    balance_after=balance_after,
                    transaction_instance_id=transaction.id,
                )

                # send_money_to_buddy = cls.fund_wallet_pay_buddy(
                #     sender_wallet=wallet_ins,
                #     # receiver_wallet=receiver_wallet_instance,
                #     amount=amount,
                #     sender_transaction_ins=transaction,
                #     # payout_type=receiver_wallet_type,
                # )

                result = {
                    "success": True,
                    "status_code": "00",
                    "message": "success",
                    "results": {
                        "unique_ref": transaction.transaction_ref,
                        # "receiver_record": send_money_to_buddy,
                        "sender_record": debit_record,
                        "amount_sent": amount,
                        "charge_fee": provider_fee,
                        "total_amount": total_amount_to_debit,
                        "status": "SUCCESS",
                    },
                }
                return result

            else:
                result = {
                    "success": False,
                    "status_code": "03",
                    "message": "insufficient funds",
                    "results": None,
                }
                return result

        else:
            result = {
                "success": False,
                "status_code": "02",
                "message": "Service is currently not available",
                "results": None,
            }
            return result

    @classmethod
    def send_money_via_buddy_instant_wage_company(
        cls, amount, user, payout_type, account_inst, employee
    ):
        from payroll_app.models import (
            CompanyEmployeeList,
            InstantWagePayroll,
            InstantWagePayrollRefund,
        )
        from accounting.utils import categorize_transaction

        narration = "Instant Wage Refund"
        # instant_wage_account_id = settings.INSTANT_WAGE_ACCOUNT_ID
        instant_wage_user_id = settings.INSTANT_WAGE_USER_ID

        instant_wage_user_ins = User.objects.get(id=instant_wage_user_id)
        receiver = instant_wage_user_ins
        # verify sender wallet
        sender_wallet_type = "PAYROLL"
        sender_wallet_instance = cls.objects.filter(
            user=user, wallet_type=sender_wallet_type
        ).first()
        if sender_wallet_instance is None:
            result = {
                "succeded": False,
                "status_code": "04",
                "message": "Sender account does not match",
                "results": None,
            }
            return result

        # verify receiver account
        receiver_wallet_type = "INSTANT_WAGE"
        receiver_wallet_instance = cls.objects.filter(
            user=receiver, wallet_type=receiver_wallet_type
        ).first()
        if receiver_wallet_instance is None:
            result = {
                "succeded": False,
                "status_code": "04",
                "message": "Receiver account does not match",
                "results": None,
            }
            return result

        provider_fee = 0

        total_amount_to_debit = provider_fee + amount
        company_name = (
            account_inst.company.company_name if account_inst.company else None
        )
        company_id = account_inst.company.id if account_inst.company else None
        transaction = Transaction.objects.create(
            user=user,
            user_uuid=user.id,
            wallet_id=sender_wallet_instance.id,
            wallet_type=sender_wallet_type,
            transaction_type="BUDDY",
            user_full_name=user.full_name,
            user_email=user.email,
            payout_type=sender_wallet_type,
            amount=amount,
            debit_amount=total_amount_to_debit,
            provider_fee=provider_fee,
            narration=narration,
            status="FAILED",
            company_name=company_name,
            company_id=company_id,
        )

        # Charge wallet
        debit_wallet = cls.deduct_balance(
            user=user, amount=total_amount_to_debit, account=account_inst
        )

        if debit_wallet.get("succeeded") is True:

            reference = Transaction.create_unique_transaction_ref(suffix=payout_type)
            fund_buddy_reference = Transaction.create_unique_transaction_ref(
                suffix=payout_type
            )

            wallet_instance = debit_wallet.get("wallet")
            balance_after = debit_wallet.get("amount_after")
            balance_before = debit_wallet.get("amount_before")
            transaction.balance_before = balance_before
            transaction.internal_to_internal_ref = reference
            transaction.balance_after = balance_after
            transaction.fund_internal_buddy_ref = fund_buddy_reference
            transaction.transfer_stage = "INTERNAL_TO_INTERNAL"
            transaction.beneficiary_wallet_id = receiver_wallet_instance.id
            transaction.beneficiary_wallet_type = receiver_wallet_type
            transaction.status = "SUCCESSFUL"
            transaction.save()

            debit_record = DebitCreditRecordOnAccount.objects.create(
                user=user,
                entry="DEBIT",
                requisition_type=sender_wallet_type,
                wallet=wallet_instance,
                balance_before=balance_before,
                amount=total_amount_to_debit,
                balance_after=balance_after,
                transaction_instance_id=transaction.id,
            )

            send_money_to_buddy = cls.fund_wallet_pay_buddy(
                sender_wallet=sender_wallet_instance,
                receiver_wallet=receiver_wallet_instance,
                amount=amount,
                sender_transaction_ins=transaction,
                payout_type=receiver_wallet_type,
            )
            create_refund_instant_wage_transaction = (
                InstantWagePayrollRefund.objects.create(
                    employee=employee,
                    receiver=receiver,
                    company=account_inst.company,
                    transaction_reference=reference,
                    fund_reference=fund_buddy_reference,
                    amount=amount,
                )
            )

            result = {
                "succeded": True,
                "status_code": "00",
                "message": "success",
                "results": {
                    "unique_ref": transaction.transaction_ref,
                    "receiver_record": send_money_to_buddy,
                    "sender_record": debit_record,
                    "amount_sent": amount,
                    "charge_fee": provider_fee,
                    "total_amount": total_amount_to_debit,
                    "status": "SUCCESS",
                },
            }
            get_instant_wage_paid_amount = amount
            instant_wage_employee = InstantWagePayroll.objects.filter(
                company=account_inst.company,
                receiver=employee,
                company_paid=False,
            )
            user_all_payment_sum = (
                instant_wage_employee.aggregate(Sum("amount"))["amount__sum"] or 0
            )

            if get_instant_wage_paid_amount >= user_all_payment_sum:
                total_to_pay_wage = get_instant_wage_paid_amount
                for wage in instant_wage_employee:
                    # deduct_amount = total_to_pay_wage - wage.amount
                    payable_amount = wage.amount
                    wage.paid_amount = payable_amount
                    wage.company_paid = True
                    wage.employee_due_payment = False
                    wage.refund_paid_date = datetime.now()
                    wage.save()
                    total_to_pay_wage -= wage.amount
                    create_refund_instant_wage_transaction.balance = total_to_pay_wage
                    create_refund_instant_wage_transaction.refund_completed = True
                    create_refund_instant_wage_transaction.save()

                employee_instant_wage_ins = CompanyEmployeeList.objects.filter(
                    company=account_inst.company,
                    employee=employee,
                ).first()
                if employee_instant_wage_ins:
                    used_wage = (
                        employee_instant_wage_ins.employee_instant_used_wage_amount
                    )
                    balance = round_amount(used_wage - get_instant_wage_paid_amount)
                    employee_instant_wage_ins.employee_instant_used_wage_amount = (
                        balance
                    )
                    employee_instant_wage_ins.save()

            else:
                pass

            # for wage in instant_wage_employee:
            #     payable_amount = wage.amount
            #     wage
            try:
                categorize_transaction(
                    transaction, source="hr", transaction_type="employee_loans"
                )
            except Exception as e:
                logger.info(f"REFUND INSTANT WAGE PAYMENT -> {str(e)}")
            return result

        else:
            result = {
                "succeded": False,
                "status_code": "03",
                "message": "insufficient funds",
                "results": None,
            }
            return result

    @classmethod
    def send_money_via_instant_wage(
        cls,
        withdraw_amount,
        charge_fee,
        amount,
        user,
        account_inst,
        employer,
        company,
        payout_type,
        receiver,
    ):
        from payroll_app.models import InstantWagePayroll
        from accounting.utils import categorize_transaction

        # verify sender wallet
        sender_wallet_type = "INSTANT_WAGE"
        sender_wallet_instance = cls.get_wallet(
            user=user, wallet_type=sender_wallet_type
        )

        # verify receiver account
        receiver_wallet_type = "INSTANT_WAGE"
        receiver_wallet_instance = cls.get_wallet(
            user=receiver, wallet_type=receiver_wallet_type
        )
        if receiver_wallet_instance is None:
            result = {
                "succeded": False,
                "status_code": "04",
                "message": "Receiver account does not match",
                "results": None,
            }
            return result

        provider_fee = 0

        total_amount_to_debit = provider_fee + amount
        company_name = ConstantTable.get_constant_instance().instant_wage_company_name
        this_date = datetime.today()
        month = this_date.month
        year = this_date.year
        day = this_date.day

        today_date = f"{year}-{month}-{day}"
        narration = f"INSTANT WAGE {today_date} to {company.id}"

        transaction = Transaction.objects.create(
            user=user,
            user_uuid=user.id,
            wallet_id=sender_wallet_instance.id,
            wallet_type=sender_wallet_type,
            transaction_type="BUDDY",
            user_full_name=user.full_name,
            user_email=user.email,
            payout_type=sender_wallet_type,
            amount=withdraw_amount,
            debit_amount=total_amount_to_debit,
            provider_fee=provider_fee,
            commission=charge_fee,
            narration=narration,
            status="FAILED",
            company_name=company_name,
            company_id=company.id,
        )

        # Charge wallet
        debit_wallet = cls.deduct_balance(
            user=user, amount=withdraw_amount, account=account_inst
        )

        if debit_wallet.get("succeeded") is True:

            reference = Transaction.create_unique_transaction_ref(suffix=payout_type)
            fund_buddy_reference = Transaction.create_unique_transaction_ref(
                suffix=payout_type
            )

            wallet_instance = debit_wallet.get("wallet")
            balance_after = debit_wallet.get("amount_after")
            balance_before = debit_wallet.get("amount_before")
            transaction.balance_before = balance_before
            transaction.internal_to_internal_ref = reference
            transaction.balance_after = balance_after
            transaction.fund_internal_buddy_ref = fund_buddy_reference
            transaction.transfer_stage = "INTERNAL_TO_INTERNAL"
            transaction.beneficiary_wallet_id = receiver_wallet_instance.id
            transaction.beneficiary_wallet_type = receiver_wallet_type
            transaction.status = "SUCCESSFUL"
            transaction.save()

            debit_record = DebitCreditRecordOnAccount.objects.create(
                user=user,
                entry="DEBIT",
                requisition_type=sender_wallet_type,
                wallet=wallet_instance,
                balance_before=balance_before,
                amount=total_amount_to_debit,
                balance_after=balance_after,
                transaction_instance_id=transaction.id,
            )

            send_money_to_buddy = cls.fund_wallet_pay_buddy(
                sender_wallet=sender_wallet_instance,
                receiver_wallet=receiver_wallet_instance,
                amount=withdraw_amount,
                sender_transaction_ins=transaction,
                payout_type=receiver_wallet_type,
            )

            create_instant_wage_transaction = InstantWagePayroll.objects.create(
                instant_wage_user=user,
                employer=employer,
                receiver=receiver,
                company=company,
                transaction_reference=reference,
                fund_reference=fund_buddy_reference,
                amount=withdraw_amount,
                date_collected=today_date,
                paid=True,
            )

            result = {
                "succeded": True,
                "status_code": "00",
                "message": "success",
                "results": {
                    "unique_ref": transaction.transaction_ref,
                    "receiver_record": send_money_to_buddy,
                    "sender_record": debit_record,
                    "amount_sent": withdraw_amount,
                    "charge_fee": charge_fee,
                    "total_amount": total_amount_to_debit,
                    "status": "SUCCESS",
                },
            }
            try:
                categorize_transaction(
                    transaction, source="hr", transaction_type="employee_loans"
                )
            except Exception as e:
                logger.info(f"INSTANT WAGE PAYMENT -> {str(e)}")

            return result

        else:
            result = {
                "succeded": False,
                "status_code": "03",
                "message": "insufficient funds",
                "results": None,
            }
            return result

    @staticmethod
    def fund_Wallet_from_LibertyPay(amount, company_user, transaction_pin, auth_token):

        transaction_ref = uuid.uuid4()
        user_main_balance = LibertyPay().get_agent_balance(
            user_ids=[company_user.user.liberty_pay_id]
        )
        for item in user_main_balance:
            for wallet in item["wallets"]:
                if wallet["wallet_type"] == "COLLECTION":
                    avail_balance = wallet["available_balance"]
                    if avail_balance < amount:
                        result = {
                            "succeded": False,
                            "status_code": "03",
                            "message": "insufficient balance",
                            "results": None,
                        }
                        return result

        bank_transfer = LibertyPayPlus().send_money_bank_transfer(
            account_number=company_user.account_number,
            auth_token=auth_token,
            account_name=company_user.account_name,
            bank_code=company_user.bank_code,
            bank_name=company_user.bank_name,
            amount=amount,
            from_account_number="",
            transaction_ref=transaction_ref,
            transaction_pin=transaction_pin,
        )
        # print(bank_transfer)
        if bank_transfer.get("message") == "success":
            data = bank_transfer.get("data")
            amount_sent = data.get("amount_sent")

            escrow = data.get("escrow_id")

            balance_before = Wallet.get_wallet_balance(
                user=company_user.user, account=company_user
            )

            transaction_ins = Transaction.objects.create(
                user=company_user.user,
                company_name=company_user.company.company_name,
                user_full_name=company_user.user.full_name,
                user_uuid=company_user.user.id,
                user_email=company_user.user.email,
                amount=amount,
                beneficiary_account_number=company_user.account_number,
                beneficiary_account_name=company_user.account_name,
                bank_name=company_user.bank_name,
                narration="Fund Spend Management Wallet",
                bank_code=company_user.bank_code,
                transaction_type="ADD_FUND_LIBERTYPAY",
                source_account_name=company_user.user.account_name,
                source_account_number=company_user.user.account_no,
                transfer_provider="VFD",
                float_to_internal_ref=escrow,
                transaction_ref=transaction_ref,
                fund_wallet_by_libertypay=True,
                commission=0.0,
                balance_before=balance_before,
            )
            verify_add_fund_by_libertypay.delay(transaction_id=transaction_ins.id)

            result = {
                "succeded": True,
                "status_code": "00",
                "message": "Transaction Successful",
                "results": {
                    "unique_ref": transaction_ref,
                    "amount_sent": amount_sent,
                    "charge_fee": 0.0,
                    "status": "SUCCESS",
                },
            }
            return result

        elif bank_transfer.get("error") == "error":
            data = bank_transfer.get("message")
            if data == "Incorrect Pin":
                result = {
                    "succeded": False,
                    "status_code": "01",
                    "message": "invalid transaction pin",
                    "results": None,
                }
                return result

        elif bank_transfer.get("error") == "143":
            result = {
                "succeded": False,
                "status_code": "02",
                "message": "Service is currently not available, Please try again after 5 minutes!",
                "results": None,
            }
            return result

        elif bank_transfer.get("error") == "319":
            result = {
                "succeded": False,
                "status_code": "03",
                "message": "You have reached your transfer count for the hour. Please contact support to increase your limit",
                "results": None,
            }
            return result

    @classmethod
    def send_reversal_mail(
        cls,
        recipient: str,
        amount: float,
        balance_after: float,
        acct_name: str,
        account_number: str,
        wallet_type: str,
    ):
        from core.tasks import send_email

        """
        Send an email to notify a user about a transaction reversal.

        Parameters:
        recipient (str): The email address of the recipient.
        amount (float): The amount that has been reversed.
        balance_after (float): The account balance after the reversal.
        acct_name (str): The name of the account holder.
        account_number (str): The account number with some digits masked for security.
        wallet_type (str): The type of wallet to which the amount has been credited.

        Example:
        send_reversal_email(
            recipient="<EMAIL>",
            amount=1500.00,
            balance_after=5000.00,
            acct_name="John Doe",
            account_number="**********",
            wallet_type="Savings",
        )
        """

        # Format the amount and balance after to include commas and two decimal places
        format_amount = "N{:,.2f}".format(amount)
        format_balance_after = "N{:,.2f}".format(balance_after)
        try:

            acct_num = f"{account_number[0]}XX...{account_number[6]}X"
        except IndexError:
            acct_num = "XX... X"

        # Mask the account number for security
        acct_num = f"{account_number[0]}XX...{account_number[-1]}X"

        # Get the current date and time and format it
        date_time_instance = datetime.now()
        format_date = date_time_instance.strftime("%d-%b-%Y %H:%M")

        wtype = wallet_type.title()
        split_wtype = wtype.split("_")
        wtype_result = " ".join(split_wtype)

        # Send the email using the send_email function
        send_email.delay(
            recipient=recipient,
            subject="Reversal",
            template_dir="reversal.html",
            acct_name=acct_name,
            acct_num=acct_num,
            wallet_type=wtype_result,
            amount=format_amount,
            balance_after=format_balance_after,
            reversal_date=format_date,
        )

    @classmethod
    def bank_deposit_email_notification(
        cls,
        recipient: str,
        amount: float,
        balance_after: float,
        acct_name: str,
        account_number: str,
        wallet_type: str,
        source_nuban: str,
        source_acct_name: str,
        narration: str,
    ):
        from core.tasks import send_email

        # Format the amount and balance after to include commas and two decimal places
        format_amount = "N{:,.2f}".format(amount)
        format_balance_after = "N{:,.2f}".format(balance_after)

        # Mask the account number for security
        try:

            acct_num = f"{account_number[0]}XX...{account_number[6]}X"
            sender_acct_num = f"{source_nuban[0]}XX...{source_nuban[-3:]}"
        except IndexError:
            acct_num = "XX... X"
            sender_acct_num = "XX... X"

        # Get the current date and time and format it
        date_time_instance = datetime.now()
        format_date = date_time_instance.strftime("%d-%b-%Y %H:%M")

        wtype = wallet_type.title()
        split_wtype = wtype.split("_")
        wtype_result = " ".join(split_wtype)

        # Send the email using the send_email function
        send_email.delay(
            recipient=recipient,
            subject="Deposit",
            template_dir="bank_deposit.html",
            acct_name=acct_name,
            acct_num=acct_num,
            source_nuban=sender_acct_num,
            source_acct_name=source_acct_name,
            wallet_type=wtype_result,
            narration=narration,
            amount=format_amount,
            balance_after=format_balance_after,
            date=format_date,
        )

    @classmethod
    def send_money_email_notifiaction(
        cls,
        recipient: str,
        amount: float,
        balance_after: float,
        acct_name: str,
        account_number: str,
        wallet_type: str,
        beneficiary_acct_num: str,
        beneficiary: str,
        narration: str,
        user_email=None,
    ):
        from core.tasks import send_email

        # Format the amount and balance after to include commas and two decimal places
        format_amount = "N{:,.2f}".format(amount)
        format_balance_after = "N{:,.2f}".format(balance_after)

        # Mask the account number for security
        try:

            acct_num = f"{account_number[0]}XX...{account_number[6]}X"
            sender_acct_num = (
                f"{beneficiary_acct_num[0]}XX...{beneficiary_acct_num[-3:]}"
            )
        except IndexError:
            acct_num = "XX... X"
            sender_acct_num = "XX... X"

        # Get the current date and time and format it
        date_time_instance = datetime.now()
        format_date = date_time_instance.strftime("%d-%b-%Y %H:%M")

        wtype = wallet_type.title()
        split_wtype = wtype.split("_")
        wtype_result = " ".join(split_wtype)

        if user_email is not None or user_email != "":
            # Send the email user
            send_email.delay(
                recipient=user_email,
                subject=f"Liberty Pay - Debit Alert [{format_amount}]",
                template_dir="send_money.html",
                acct_name=acct_name,
                acct_num=acct_num,
                # beneficiary_acct_num=sender_acct_num,
                beneficiary_acct_num=beneficiary_acct_num,
                beneficiary=beneficiary,
                wallet_type=wtype_result,
                narration=narration,
                amount=format_amount,
                balance_after="XXXXXX",
                date=format_date,
            )

        # send email to company owner
        send_email.delay(
            recipient=recipient,
            subject=f"Liberty Pay - Debit Alert [{format_amount}]",
            template_dir="send_money.html",
            acct_name=acct_name,
            acct_num=acct_num,
            # beneficiary_acct_num=sender_acct_num,
            beneficiary_acct_num=beneficiary_acct_num,
            beneficiary=beneficiary,
            wallet_type=wtype_result,
            narration=narration,
            amount=format_amount,
            balance_after=format_balance_after,
            date=format_date,
        )

    @classmethod
    def create_wema_virtual_account(
        cls,
        user,
        company,
        company_name,
        company_email,
        company_phone,
        unique_id,
        account_type: str,
        branch: Optional[Branch] = None,
        company_address: Optional[str] = None,
    ):
        from account.helpers.core_banking import CoreBankingService

        core_banking_handler = CoreBankingService()
        response = core_banking_handler.register_sub_company(
            company_name=company_name,
            company_email=company_email,
            company_phone=company_phone,
            unique_id=unique_id,
            company_address=company_address,
        )
        if not isinstance(response, str):
            if response.get("status_code") == 201:
                response_data = response.get("data").get("data").get("sub_company")
                _account = AccountSystem.objects.create(
                    user=user,
                    account_provider=AcctProvider.WEMA,
                    company=company,
                    branch=branch,
                    account_number=response_data.get("nuban"),
                    account_name=response_data.get("company_name"),
                    account_type=account_type,
                    bank_name="Wema Bank Plc",
                    bank_code="000017",
                    request_status=TransactionStatus.SUCCESSFUL,
                    payload=response_data,
                )
                _create_wallet = cls.objects.create(
                    user=user,
                    account=_account,
                    wallet_type=account_type,
                )
                return _create_wallet
            else:
                AccountCreationFailure.objects.create(
                    user=user,
                    payload=response,
                    account_type=account_type,
                    account_provider=AcctProvider.WEMA,
                )
                return None


class Transaction(models.Model):
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    # User
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="transactions"
    )
    company_name = models.CharField(max_length=500, null=True, blank=True)
    wallet_id = models.CharField(max_length=500, null=True, blank=True)
    user_full_name = models.CharField(max_length=150, null=True, blank=True)
    user_email = models.EmailField(null=True, blank=True)

    amount = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    commission = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    # --------------------balances-------------------------
    balance_before = models.FloatField(null=True, blank=True)
    balance_after = models.FloatField(null=True, blank=True)
    # ------------------------------------------------------
    status = models.CharField(
        max_length=150,
        choices=TransactionStatus.choices,
        default=TransactionStatus.PENDING,
    )

    transaction_type = models.CharField(
        max_length=50,
        choices=TransactionType.choices,
        default=TransactionType.BANK_TRANSFER,
    )
    debit_amount = models.FloatField(null=True, blank=True)
    total_amount_received = models.FloatField(null=True, blank=True)
    transfer_provider = models.CharField(
        max_length=50, choices=AcctProvider.choices, null=True, blank=True
    )
    payout_type = models.CharField(
        max_length=50,
        choices=AccountType.choices,
        default=AccountType.SPEND_MGMT,
    )
    transfer_stage = models.CharField(
        max_length=50, choices=TransferStage.choices, null=True, blank=True
    )
    # reference
    provider_fee = models.FloatField(null=True, blank=True)
    stamp_duty = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    internal_acct_bal = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    internal_acct_reversal_bal = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    internal_to_outwards_session_id = models.CharField(
        max_length=500, editable=False, null=True, blank=True
    )
    float_to_outwards_session_id = models.CharField(
        max_length=500, editable=False, null=True, blank=True
    )
    float_to_internal_session_id = models.CharField(
        max_length=500, editable=False, null=True, blank=True
    )
    transaction_ref = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    float_to_external_ref = models.CharField(max_length=500, null=True, blank=True)
    float_to_internal_ref = models.CharField(max_length=500, null=True, blank=True)
    internal_to_outwards_ref = models.CharField(max_length=500, null=True, blank=True)
    internal_to_float_ref = models.CharField(max_length=500, null=True, blank=True)
    bank_deposit_ref = models.CharField(max_length=500, null=True, blank=True)
    bank_deposit_session_id = models.CharField(max_length=500, null=True, blank=True)
    commission_ref = models.CharField(max_length=500, null=True, blank=True)
    reversal_for = models.CharField(
        max_length=500,
        null=True,
        blank=True,
        help_text="This is the ID that references a transaction that was reversed or failed and is now reversed to the user's wallet.",
    )

    # bank_float_balance_before = models.FloatField(null=True, blank=True)
    # bank_float_balance_after = models.FloatField(null=True, blank=True)

    user_uuid = models.CharField(max_length=500, null=True, blank=True)
    company_id = models.CharField(max_length=500, null=True, blank=True)
    # total_amount_charged = models.FloatField(null=True, blank=True)
    float_to_internal = models.BooleanField(default=False)
    internal_to_outwards = models.BooleanField(default=False)
    is_reversed = models.BooleanField(default=False)

    # For BANK_TRANSFER transactions
    beneficiary_account_number = models.CharField(max_length=250, null=True, blank=True)
    beneficiary_account_name = models.CharField(max_length=250, null=True, blank=True)
    bank_name = models.CharField(max_length=250, null=True, blank=True)
    bank_code = models.CharField(max_length=250, null=True, blank=True)
    source_account_name = models.CharField(max_length=250, null=True, blank=True)
    source_account_number = models.CharField(max_length=250, null=True, blank=True)
    total_amount_resolved = models.FloatField(null=True, blank=True)
    txn_id = models.CharField(
        max_length=250,
        null=True,
        blank=True,
        help_text="Holds the transaction ID for a transaction, which could either be a deposit or any other existing transaction.",
    )
    commission_from = models.CharField(max_length=250, null=True, blank=True)

    reversal_to_float = models.BooleanField(default=False)
    narration = models.CharField(max_length=1000, null=True, blank=True)
    date_credited = models.DateTimeField(null=True, blank=True)

    # For AIRTIME_TOPUP transactions
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    network_provider = models.CharField(max_length=100, null=True, blank=True)

    # FOR INTERNAL BUDDY WALLET transactions
    wallet_id = models.CharField(max_length=250, null=True, blank=True)
    wallet_type = models.CharField(max_length=250, null=True, blank=True)
    beneficiary_wallet_id = models.CharField(max_length=250, null=True, blank=True)
    beneficiary_wallet_type = models.CharField(max_length=250, null=True, blank=True)
    source_wallet_id = models.CharField(max_length=250, null=True, blank=True)
    source_wallet_type = models.CharField(max_length=250, null=True, blank=True)
    internal_to_internal_ref = models.CharField(max_length=250, null=True, blank=True)
    fund_internal_buddy_ref = models.CharField(max_length=250, null=True, blank=True)

    # FOR FUND WALLET BY LIBERTYPAY VERIFICATION AND FILTER
    fund_wallet_by_libertypay = models.BooleanField(
        default=False, null=True, blank=True
    )
    jounral = models.ForeignKey(
        "accounting.JournalEntry", on_delete=models.PROTECT, null=True, blank=True
    )

    # FOR SEND PENSION DATA ID
    pension_data_id = models.CharField(max_length=250, null=True, blank=True)
    bulk_transfer_id = models.CharField(max_length=250, null=True, blank=True)

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "TRANSACTION"
        verbose_name_plural = "TRANSACTIONS"

    # @property
    # def total_sum(self):
    #     if self.payout_type == AccountType.PAYROLL:
    #         return "**********"
    #     return self.amount

    # @property
    # def closing_balance(self):
    #     if self.payout_type == AccountType.PAYROLL:
    #         return "**********"
    #     return self.balance_before

    # @property
    # def opening_balance(self):
    #     if self.payout_type == AccountType.PAYROLL:
    #         return "**********"
    #     return self.balance_after

    # @property
    # def amount_charged(self):
    #     if self.payout_type == AccountType.PAYROLL:
    #         return "**********"
    #     return self.debit_amount

    # @property
    # def amount_received(self):
    #     if self.payout_type == AccountType.PAYROLL:
    #         return "**********"
    #     return self.total_amount_received

    def __str__(self):
        return f"{self.user.email} - Transaction id: {self.id}"

    # def save(self, *args, **kwargs):
    #     if not self.pk:  # on create
    #         pass
    #     super(Transaction, self).save(*args, **kwargs)

    @classmethod
    def create_unique_transaction_ref(cls, suffix):
        epoch = int(time.time())
        ref = f"{suffix}-{str(epoch)[-10:]}-{uuid.uuid4()}"
        return ref

    @property
    def minutes_ago(self):
        current_time = datetime.now(timezone.utc)
        time_difference = current_time - self.date_created
        return time_difference.total_seconds() / 60

    @property
    def session_id(self):
        if self.transfer_stage == TransferStage.INTERNAL_TO_OUTWARDS:
            id_str = self.internal_to_outwards_session_id
        elif self.transfer_stage == TransferStage.FLOAT_TO_EXTERNAL:
            id_str = self.float_to_outwards_session_id
        else:
            id_str = ""
        return id_str

    def update_transaction_status(self, status):
        """
        Updates the transaction status and saves it.

        Args:
            status (str): The new status of the transaction.

        Returns:
            None
        """
        self.status = status
        self.save()

    def update_transfer_stage(self, stage: TransferStage):
        """
        Updates the current transfer stage for an instance and saves the change to the database.

        Args:
            stage (str): The new transfer stage to be set.

        Returns:
            None
        """
        self.transfer_stage = stage
        # self.internal_to_outwards = stage == TransferStage.INTERNAL_TO_OUTWARDS
        self.save()

    def get_account_by_txn(self):
        """
        Retrieve the AccountSystem instance based on the source account number and payout type.

        Returns:
            AccountSystem: The last AccountSystem instance matching the source account number
                        and payout type, or None if no matching instance is found.
        """
        return AccountSystem.objects.filter(
            account_number=self.source_account_number,
            account_type=self.payout_type,
        ).last()

    def get_vfd_stamp_duty_result(self) -> dict:
        """
        Handle stamp duty calculation for VFD transactions.

        Returns:
            dict: A dictionary containing information about stamp duty charge, adjusted amount,
                and stamp duty amount.
                Example:
                {
                    "charge_stmp": True,   # Whether stamp duty is charged
                    "amount": 1000.00,     # Adjusted amount after stamp duty deduction
                    "stamp_duty": 10.00    # Stamp duty amount deducted
                }
        """
        # Get constant instance for stamp duty rates
        const = ConstantTable.get_constant_instance()

        # Retrieve stamp duty amount and base amount from constants
        stamp_duty_amt = const.vfd_stamp_duty
        stamp_duty_base_amount = const.stamp_duty_base_amount

        # Determine transaction amount based on transaction type
        txn_amount = self.amount
        if self.transaction_type == TransactionType.DEPOSIT:
            txn_amount = self.total_amount_received
        elif self.transaction_type == TransactionType.BANK_TRANSFER:
            txn_amount = self.amount

        # Check if stamp duty should be charged
        charge_stmp = txn_amount >= stamp_duty_base_amount

        # Adjust amount by deducting stamp duty if applicable
        amount = txn_amount - stamp_duty_amt if charge_stmp else txn_amount

        # Prepare result dictionary
        result = {
            "charge_stmp": charge_stmp,
            "amount": amount,
            "stamp_duty": stamp_duty_amt,
        }
        return result

    @classmethod
    def vfd_funds_transfer(
        cls,
        bank_code: str,
        bank_name: str,
        account_name: str,
        account_number: str,
        narration: str,
        amount: float,
        account: AccountSystem,
        user,
        charge_fee=None,
        company_owner=None,
        pension_data_id=None,
        bulk_transfer_id=None,
    ):

        payout_type = account.account_type

        const = ConstantTable.get_constant_instance()  # fetch constant variables

        # check if amount is greater than stamp duty base amount
        if amount > const.stamp_duty_base_amount:
            vfd_stamp_duty = const.vfd_stamp_duty  # charge stamp duty
        else:
            vfd_stamp_duty = 0

        if bank_code not in ["090110", "999999"]:
            if not charge_fee:
                fee = const.withdrawal_fee
                commission = fee - const.vfd_fee
            else:
                fee = charge_fee
                commission = fee - const.vfd_fee

            provider_fee = const.vfd_fee
            debit_amount = amount + fee + vfd_stamp_duty  # include stamp duty
        else:
            commission = 0.0
            provider_fee = 0.0
            debit_amount = amount + vfd_stamp_duty  # include stamp duty

        charge_wallet_status = Wallet.has_sufficient_funds(
            user=account.user,
            wallet_type=payout_type,
            amount=debit_amount,
            account=account,
        )

        if not charge_wallet_status.get("is_chargeable"):
            return charge_wallet_status

        else:
            company_name = account.company.company_name if account.company else None
            company_id = account.company.id if account.company else None
            account_user = user if company_owner is None else company_owner

            if const.use_float_to_internal:
                float_to_internal_ref = cls.create_unique_transaction_ref(
                    suffix="pbx360-"
                )
                float_to_external_ref = None
            else:
                float_to_external_ref = cls.create_unique_transaction_ref(
                    suffix="pbx360-"
                )
                float_to_internal_ref = None
                included_account_name = account.account_name.lower()
                narration = f"{included_account_name[:7]}/{narration}"

            transaction_instance = Transaction.objects.create(
                user=account_user,
                company_name=company_name,
                user_full_name=user.full_name,
                user_uuid=user.id,
                user_email=user.email,
                payout_type=payout_type,
                float_to_external_ref=float_to_external_ref,
                float_to_internal_ref=float_to_internal_ref,
                amount=amount,
                commission=commission,
                stamp_duty=vfd_stamp_duty,
                provider_fee=provider_fee,
                debit_amount=debit_amount,
                beneficiary_account_number=account_number,
                beneficiary_account_name=account_name,
                bank_name=bank_name,
                narration=narration,
                bank_code=bank_code,
                source_account_name=account.account_name,
                source_account_number=account.account_number,
                company_id=company_id,
                transfer_provider=AcctProvider.VFD,
                pension_data_id=pension_data_id,
                bulk_transfer_id=bulk_transfer_id,
                wallet_id=charge_wallet_status.get("wallet_instance").id,
            )

            Wallet.charge_wallet(
                wallet_instance=charge_wallet_status.get("wallet_instance"),
                amount=transaction_instance.debit_amount,
                transaction_instance=transaction_instance,
            )
            vfd_send_money.delay(transaction_id=transaction_instance.id)
            return str(transaction_instance.transaction_ref)

    def vfd_initiate_payout_float_to_beneficiary_account(self):
        """
        Initiates a payout from a float to the beneficiary's bank account.

        This function manages the process of transferring money from a VFD float
        account to a beneficiary's bank account. It performs the following steps:

        1. Retrieves the user's account name and appends the first 7 characters
        of the account name to the narration for the transaction.
        2. Prepares the payload for the payout request, including beneficiary details,
        narration, amount, and transaction reference.
        3. Creates a log entry in the TransactionMetaData model to record the transaction
        details and payload.
        4. Executes the payout by interacting with the VFD bank API.
        5. Logs the result of the payout in the TransactionMetaData model.
        6. Verifies the transaction status with VFD bank.
        7. Handles the verification response to update the transaction status accordingly.

        Returns:
            dict: The result of the payout request to the beneficiary's account.
        """
        expected_transfer_types = [
            TransactionType.BANK_TRANSFER,
            TransactionType.STAMP_DUTY_REFUND,
            TransactionType.COMMISSION,
        ]

        if (
            self.transfer_provider != AcctProvider.VFD
            or self.transaction_type not in expected_transfer_types
        ):
            return False

        self.update_transfer_stage(TransferStage.FLOAT_TO_EXTERNAL)

        # Initialize VfdBankMgr instance for interacting with VFD bank APIs
        vbank_object = VfdBankMgr()

        # Prepare the payload for the payout request
        if (
            self.transaction_type == TransactionType.BANK_TRANSFER
            or self.transaction_type == TransactionType.STAMP_DUTY_REFUND
        ):
            reference = str(self.float_to_external_ref)

        else:
            # self.transaction_type == TransactionType.COMMISSION:
            reference = str(self.commission_ref)

        payload = {
            "beneficiary_nuban": self.beneficiary_account_number,
            "beneficiary_bank_code": self.bank_code,
            "narration": self.narration,
            "amount": self.amount,
            "reference": reference,
        }

        # Create a transaction metadata log entry
        transactionmeta_log = TransactionMetaData.objects.create(
            user=self.user,
            transaction=self,
            payout_payload=payload,
        )

        # Execute the payout to the beneficiary's account
        transfer_result = vbank_object.payout_from_float_to_beneficiary(**payload)

        # Log the result of the payout in the TransactionMetaData model
        transactionmeta_log.payout_result = transfer_result
        transactionmeta_log.save()

        # Check if there was an error in the payout process
        if transfer_result.get("error"):
            self.update_transaction_status(TransactionStatus.FAILED)
            return

        return self.vfd_verify_update_transaction(reference=reference)

    def vfd_initiate_payout_float_to_internal_account(self):

        expected_transfer_types = [
            TransactionType.BANK_TRANSFER,
            # TransactionType.COMMISSION,
        ]

        if (
            self.transfer_provider != AcctProvider.VFD
            or self.transaction_type not in expected_transfer_types
        ):
            return False
        if self.transfer_stage == TransferStage.DEBIT:

            self.update_transfer_stage(TransferStage.FLOAT_TO_INTERNAL)

            # Initialize VfdBankMgr instance for interacting with VFD bank APIs
            vbank_object = VfdBankMgr()

            reference = self.float_to_internal_ref

            payload = {
                "beneficiary_nuban": self.source_account_number,
                "beneficiary_bank_code": "999999",
                "narration": self.narration,
                "amount": self.amount + self.stamp_duty + self.provider_fee,  # type: ignore
                "reference": self.float_to_internal_ref,
            }

            # Create a transaction metadata log entry
            transactionmeta_log = TransactionMetaData.objects.create(
                user=self.user,
                transaction=self,
                payout_payload=payload,
            )

            # Execute the payout to the beneficiary's account
            transfer_result = vbank_object.payout_from_float_to_beneficiary(**payload)

            # Log the result of the payout in the TransactionMetaData model
            transactionmeta_log.payout_result = transfer_result
            transactionmeta_log.save()

            # Check if there was an error in the payout process
            if transfer_result.get("error"):
                self.update_transaction_status(TransactionStatus.FAILED)
                return

            return self.vfd_verify_update_transaction(reference=reference)

    def _vfd_initiate_payout_internal_account_to_external(self):

        expected_transfer_types = [
            TransactionType.BANK_TRANSFER,
        ]

        if (
            self.transfer_provider != AcctProvider.VFD
            or self.transaction_type not in expected_transfer_types
        ):
            return False
        if (
            self.transfer_stage == TransferStage.FLOAT_TO_INTERNAL
            and self.float_to_internal is True
        ):
            self.update_transfer_stage(TransferStage.INTERNAL_TO_OUTWARDS)

            # Initialize VfdBankMgr instance for interacting with VFD bank APIs
            vbank_object = VfdBankMgr()

            # ITE INTERNAL TO EXTERNAL
            reference = self.create_unique_transaction_ref(suffix="pbx360ITE-")

            self.internal_to_outwards_ref = reference
            self.save()

            payload = {
                "beneficiary_nuban": self.beneficiary_account_number,
                "beneficiary_bank_code": self.bank_code,
                "narration": self.narration,
                "amount": self.amount,
                "reference": reference,
                "source_account_number": self.source_account_number,
                "source_bank_code": "999999",
            }

            # Create a transaction metadata log entry
            transactionmeta_log = TransactionMetaData.objects.filter(transaction=self)

            transactionmeta_log.update(internal_to_external_payload=payload)

            # Execute the payout to the beneficiary's account
            transfer_result = vbank_object.payout_from_float_to_beneficiary(**payload)
            transactionmeta_log.update(internal_to_external_result=transfer_result)

            # Check if there was an error in the payout process
            if transfer_result.get("error"):
                self.update_transaction_status(TransactionStatus.FAILED)
                return

            return self.vfd_verify_update_transaction(reference=reference)

    def _vfd_initiate_payout_internal_account_to_float(self):

        expected_transfer_types = [
            TransactionType.BANK_TRANSFER,
        ]

        if (
            self.transfer_provider != AcctProvider.VFD
            or self.transaction_type not in expected_transfer_types
        ):
            return False

        # self.update_transfer_stage(TransferStage.INTERNAL_TO_FLOAT)

        # Initialize VfdBankMgr instance for interacting with VFD bank APIs
        vbank_object = VfdBankMgr()

        # ITE INTERNAL TO EXTERNAL
        reference = self.create_unique_transaction_ref(suffix="pbx360ITF-")

        self.internal_to_float_ref = reference
        self.save()

        payload = {
            "beneficiary_nuban": settings.FLOAT_ACCOUNT_NUMBER_VFD,
            "beneficiary_bank_code": "999999",
            "narration": self.narration,
            "amount": self.amount - self.stamp_duty,
            "reference": reference,
            "source_account_number": self.source_account_number,
            "source_bank_code": "999999",
        }

        # Create a transaction metadata log entry
        transactionmeta_log = TransactionMetaData.objects.create(
            user=self.user,
            transaction=self,
            payout_payload=payload,
        )

        # Execute the payout to the beneficiary's account
        transfer_result = vbank_object.payout_from_float_to_beneficiary(**payload)

        # Log the result of the payout in the TransactionMetaData model
        transactionmeta_log.payout_result = transfer_result
        transactionmeta_log.save()

        # Check if there was an error in the payout process
        if transfer_result.get("error"):
            self.update_transaction_status(TransactionStatus.FAILED)
            return

        # reversal_codes = vbank_object.get_list_of_reversal_code()
        verification_result = vbank_object.vfd_transaction_verification_handler(
            reference=reference
        )
        transactionmeta_log.float_verification_result = verification_result
        transactionmeta_log.save()

        response = verification_result.get("response")
        status = verification_result.get("status")

        if status == "success":
            transaction_status = response.get("data", {}).get("transactionStatus")
            response_status = response.get("status")

            if response_status == "00":
                if transaction_status == "00":
                    internal_acct_enquiry = vbank_object.vfd_account_enquiry(
                        account_number=self.source_account_number
                    )
                    response = internal_acct_enquiry.get("response")
                    data = response.get("data", None)
                    status_code = internal_acct_enquiry.get("status_code")
                    response_status = response.get("status")

                    if (
                        status_code == 200
                        and response_status == "00"
                        and data is not None
                    ):

                        account_balance = float(data.get("accountBalance", 0))
                        self.internal_acct_reversal_bal = account_balance
                        self.save()
                    self.reversal_to_float = True
                    self.save()
                    return True

        return False

    def _float_to_internal_transferred(self, vbank_object: VfdBankMgr) -> bool:
        """
        Verifies if the internal account associated with this transaction has a sufficient
        balance to cover the transaction amount, using the VFD Bank service for account enquiry.

        Args:
            vbank_object (VfdBankMgr): An object that manages interactions with the VFD Bank API,
                                    allowing for account balance enquiries.

        Returns:
            bool: True if the internal account has enough balance to cover the transaction amount,
                False otherwise.

        Workflow:
        1. Calls the VFD Bank's `vfd_account_enquiry` method to check the balance of the internal NUBAN account.
        2. Extracts the account balance and status details from the response.
        3. Updates the `TransactionMetaData` table with the account enquiry result for record-keeping.
        4. If the enquiry is successful and the account balance is greater than or equal to the transaction amount:
            - Saves the internal account balance (`self.internal_acct_bal`) for this transaction.
            - Returns `True` indicating the balance is sufficient.
        5. Otherwise, returns `False` indicating insufficient balance or an unsuccessful enquiry.

        Note:
        - The VFD Bank API response is expected to have a structure with `response`, `status`, and nested fields like `data` and `accountBalance`.
        - The transaction amount is compared with the `accountBalance` from the enquiry result.
        - Success conditions are defined as `status == "success"` and `response_status == "00"`, based on the VFD Bank API specifications.
        """

        internal_acct_enquiry = vbank_object.vfd_account_enquiry(
            account_number=self.source_account_number
        )

        response = internal_acct_enquiry.get("response")
        data = response.get("data", None)
        status_code = internal_acct_enquiry.get("status_code")
        response_status = response.get("status")

        TransactionMetaData.objects.filter(transaction=self).update(
            account_enquiry=internal_acct_enquiry
        )

        if (
            status_code == 200
            and response_status == "00"
            and data is not None
            and float(data.get("accountBalance", 0)) >= self.amount
        ):

            account_balance = float(data.get("accountBalance", 0))
            self.internal_acct_bal = account_balance
            self.save()
            return True
        else:
            return False

    def _save_verification_result_to_send_money_logs(self, verification_response):

        send_money_log = TransactionMetaData.objects.filter(transaction=self)
        if send_money_log.exists():
            if (
                self.transfer_stage == TransferStage.FLOAT_TO_INTERNAL
                or self.transfer_stage == TransferStage.FLOAT_TO_EXTERNAL
            ):
                send_money_log.update(float_verification_result=verification_response)
            elif self.transfer_stage == TransferStage.INTERNAL_TO_OUTWARDS:
                send_money_log.update(
                    internal_verification_result=verification_response
                )
            return

    def vfd_verify_update_transaction(self, reference=None):
        """
        Verifies a transaction with the VFD Bank system and updates
        the transaction status accordingly.

        Args:
            reference (str, optional): The reference for the transaction. Defaults to None.
            If not provided, it uses the instance's transaction reference.

        Returns:
            dict: A dictionary containing the status and transaction ID if the conditions are met.
            Returns False if the transaction provider is not VFD
            or the transaction type is not a bank transfer.
        """

        # Check if the transfer provider is VFD and the transaction type is a bank transfer
        expected_transfer_types = [
            TransactionType.BANK_TRANSFER,
            TransactionType.COMMISSION,
        ]
        if (
            self.transfer_provider != AcctProvider.VFD
            or self.transaction_type not in expected_transfer_types
        ):
            return False

        # Set the verification reference, defaulting to the transaction's reference if not provided
        verification_reference = reference or self.float_to_external_ref

        vbank_object = VfdBankMgr()

        # Retrieve reversal codes and handle transaction verification
        reversal_codes = vbank_object.get_list_of_reversal_code()
        verification_result = vbank_object.vfd_transaction_verification_handler(
            reference=verification_reference
        )
        # ---------------------------> UPDATE SEND MONEY LOGS
        # ---------------------------> UPDATE SEND MONEY LOGS
        # ---------------------------> UPDATE SEND MONEY LOGS
        self._save_verification_result_to_send_money_logs(
            verification_response=verification_result
        )

        # Extract the response and status from the verification result
        response = verification_result.get("response")
        status = verification_result.get("status")

        if status == "success":
            verification_data = response.get("data", {})
            transaction_status = verification_data.get("transactionStatus")
            response_status = response.get("status")
            vfd_session_id = verification_data.get("sessionId")

            # Update the transaction status based on the response and transaction status
            if response_status == "00":
                if transaction_status == "00":
                    # check all bank transfer
                    if self.transaction_type == TransactionType.BANK_TRANSFER:

                        if (
                            self.transfer_stage == TransferStage.FLOAT_TO_INTERNAL
                            and self.float_to_internal is False
                        ):
                            if self._float_to_internal_transferred(
                                vbank_object=vbank_object
                            ):
                                self.float_to_internal_session_id = vfd_session_id
                                self.float_to_internal = True
                                self.save()
                                # initiate internal to external transfer
                                # Notify liberty credi float debit
                                notify_liberty_credit_float_deduction.delay(
                                    amount=self.amount
                                )
                                self._vfd_initiate_payout_internal_account_to_external()

                        # INTERNAL ACCOUNT TO OUTWARDS(WHICH IS THE EXPECTED BENEFICIARY)
                        elif (
                            self.transfer_stage == TransferStage.INTERNAL_TO_OUTWARDS
                            and self.internal_to_outwards is False
                        ):

                            self.internal_to_outwards_session_id = vfd_session_id
                            self.internal_to_outwards = True
                            self.save()
                            self.update_transaction_status(TransactionStatus.SUCCESSFUL)

                        # FLOAT TO EXTERNAL(DIRECT TRANSFER TO THE EXPECTED BENEFICIARY)
                        elif self.transfer_stage == TransferStage.FLOAT_TO_EXTERNAL:
                            self.float_to_outwards_session_id = vfd_session_id
                            self.save()
                            notify_liberty_credit_float_deduction.delay(
                                amount=self.amount
                            )
                            self.update_transaction_status(TransactionStatus.SUCCESSFUL)

                        # EMAIL NOTIFICATION(AFTER ACTUAL TRANSFER)
                        if (
                            self.internal_to_outwards is True
                            or self.transfer_stage == TransferStage.FLOAT_TO_EXTERNAL
                        ):
                            self.create_vfd_commission_transaction()
                            account_system_instance = AccountSystem.objects.filter(
                                user=self.user, account_type=self.payout_type
                            ).first()
                            # email notification
                            Wallet.send_money_email_notifiaction(
                                user_email=self.user_email,
                                recipient=self.user.email,
                                amount=self.amount,
                                balance_after=self.balance_after,
                                acct_name=account_system_instance.account_name,
                                account_number=account_system_instance.account_number,
                                wallet_type=account_system_instance.account_type,
                                beneficiary_acct_num=self.beneficiary_account_name,
                                beneficiary=self.beneficiary_account_name,
                                narration=self.narration,
                            )  # Note that all email notification is handled by celery
                    elif self.transaction_type == TransactionType.COMMISSION:
                        self.update_transaction_status(TransactionStatus.SUCCESSFUL)

                elif transaction_status in reversal_codes:
                    self.update_transaction_status(TransactionStatus.REVERSED)
                elif transaction_status not in ["01", "02", "09", "19"]:
                    self.update_transaction_status(TransactionStatus.FAILED)

            elif response_status == "108":
                self.update_transaction_status(TransactionStatus.FAILED)

        # Refresh the instance from the database and update the transaction metadata
        self.refresh_from_db()

        # Return the transaction status and ID as the result
        result = {
            "status": self.status,
            "transaction_id": self.id,
            "verification_result": verification_result,
        }
        return result

    def create_vfd_commission_transaction(self):
        if (
            self.commission > 0
            and not Transaction.objects.filter(commission_from=self.id).exists()
        ):
            # create commision transaction
            commission_beneficiary_nuban = settings.COMMISSION_ACCOUNT_NUMBER_VFD
            float_beneficiary_nuban = settings.FLOAT_ACCOUNT_NUMBER_VFD
            reference = self.create_unique_transaction_ref(suffix="paybx360-comm-")
            user = self.user
            commision_transaction_instance_created = Transaction.objects.create(
                user=user,
                company_name="LIBERTY ASSURED COMMISSION",
                user_full_name=user.full_name,
                commission_from=self.id,
                user_email=user.email,
                payout_type=self.payout_type,
                amount=self.commission,
                commission_ref=reference,
                beneficiary_account_number=commission_beneficiary_nuban,
                bank_name=AcctProvider.VFD,
                narration="SEND TO COMMISSION WALLET",
                bank_code="999999",
                transfer_provider=AcctProvider.VFD,
                source_account_name="Liberty credi float",
                source_account_number=float_beneficiary_nuban,
                transaction_type=TransactionType.COMMISSION,
            )

            transfer_commission.delay(
                transaction_id=commision_transaction_instance_created.id
            )

    def reverse_vfd_failed_or_reversed_transaction(self):
        """
        Reverses a failed transaction for this specific transaction instance.
        Returns a tuple containing result and message.
        """
        from account.models import Wallet

        # Validate transaction can be reversed
        if self.status not in [TransactionStatus.FAILED, TransactionStatus.REVERSED]:
            return None, "Transaction status must be FAILED or REVERSED"

        if self.transaction_type != TransactionType.BANK_TRANSFER:
            return None, "Transaction type must be BANK_TRANSFER"

        if self.transfer_provider != AcctProvider.VFD:
            return None, "Transfer provider must be VFD"

        if self.is_reversed:
            return None, "Transaction is already reversed"

        # Get appropriate reference based on transfer stage
        if self.transfer_stage == TransferStage.FLOAT_TO_INTERNAL:
            reference = self.float_to_internal_ref
        elif self.transfer_stage == TransferStage.INTERNAL_TO_OUTWARDS:
            reference = self.internal_to_outwards_ref
        elif self.transfer_stage == TransferStage.FLOAT_TO_EXTERNAL:
            reference = self.float_to_external_ref
        else:
            return None, "Invalid transfer stage"

        reverify_status = self.vfd_verify_update_transaction(reference=reference)

        if isinstance(reverify_status, dict):
            reversal_status = [TransactionStatus.FAILED, TransactionStatus.REVERSED]
            if reverify_status.get("status") in reversal_status:
                if self.transfer_stage == TransferStage.INTERNAL_TO_OUTWARDS:
                    send_funds_to_float = (
                        self._vfd_initiate_payout_internal_account_to_float()
                    )
                    if send_funds_to_float is True:
                        result = Wallet.wallet_reversal(transaction_instance=self)
                        return (
                            result,
                            "Successfully reversed internal to outwards transaction",
                        )
                    return send_funds_to_float, "Failed to send funds to float"
                else:
                    result = Wallet.wallet_reversal(transaction_instance=self)
                    return result, "Successfully reversed transaction"

        return None, "Failed to verify transaction status"

    @classmethod
    def wema_funds_transfer(
        cls,
        request_id: str,
        bank_code: str,
        bank_name: str,
        account_name: str,
        account_number: str,
        narration: str,
        amount: int,
        account: AccountSystem,
        user,
        charges,
        company_owner=None,
    ):
        payout_type = account.account_type
        debit_amount = int(amount) + int(charges)
        charge_wallet_status = Wallet.has_sufficient_funds(
            user=account.user,
            wallet_type=payout_type,
            amount=debit_amount,
            account=account,
        )
        if not charge_wallet_status.get("is_chargeable"):
            return charge_wallet_status
        else:
            company_name = account.company.company_name if account.company else None
            company_id = account.company.id if account.company else None
            account_user = user if company_owner is None else company_owner

            transaction_instance = Transaction.objects.create(
                user=account_user,
                company_name=company_name,
                user_full_name=user.full_name,
                user_uuid=user.id,
                user_email=user.email,
                transaction_ref=request_id,
                transaction_type=TransactionType.BANK_TRANSFER,
                payout_type=payout_type,
                source_account_name=account.account_name,
                source_account_number=account.account_number,
                bank_name=bank_name,
                bank_code=bank_code,
                beneficiary_account_number=account_number,
                beneficiary_account_name=account_name,
                amount=amount,
                debit_amount=debit_amount,
                commission=charges,
                narration=narration,
                company_id=company_id,
                transfer_provider=AcctProvider.WEMA,
            )
            wallet = Wallet.charge_wallet(
                wallet_instance=charge_wallet_status.get("wallet_instance"),
                amount=transaction_instance.debit_amount,
                transaction_instance=transaction_instance,
            )
            transaction_instance.balance_before = wallet.get("amount_before")
            transaction_instance.balance_after = wallet.get("amount_after")
            transaction_instance.save()
            wema_send_money.delay(transaction_id=str(transaction_instance.id))
            return str(transaction_instance.transaction_ref)


class DebitCreditRecordOnAccount(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    entry = models.CharField(max_length=200, choices=DebitCreditEntry.choices)
    requisition_type = models.CharField(
        max_length=255, choices=AccountType.choices, null=True, blank=True
    )
    wallet = models.ForeignKey(Wallet, on_delete=models.SET_NULL, null=True)
    balance_before = models.FloatField(default=0.00)
    balance_after = models.FloatField(default=0.00)
    amount = models.FloatField(validators=[MinValueValidator(0.0)])
    type_of_trans = models.CharField(max_length=200, null=True, blank=True)
    transaction_instance_id = models.CharField(max_length=400, null=True, blank=True)
    date_credited = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    date_created = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.entry.lower()} - {self.requisition_type.lower()}"

    class Meta:
        verbose_name = "DEBIT CREDIT RECORD"
        verbose_name_plural = "DEBIT CREDIT RECORDS"


class TransactionMetaData(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction = models.ForeignKey(
        Transaction,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    verification_ref = models.CharField(max_length=255, null=True, blank=True)
    payout_payload = models.TextField(null=True, blank=True)
    fund_wallet_payload = models.TextField(null=True, blank=True)
    payout_result = models.TextField(null=True, blank=True)
    float_verification_result = models.TextField(null=True, blank=True)
    internal_to_external_payload = models.TextField(null=True, blank=True)
    internal_to_external_result = models.TextField(null=True, blank=True)
    internal_verification_result = models.TextField(null=True, blank=True)
    commission_payload = models.TextField(null=True, blank=True)
    commission_result = models.TextField(null=True, blank=True)
    commission_ver_result = models.TextField(null=True, blank=True)
    account_enquiry = models.TextField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-date_added"]
        verbose_name = "SEND MONEY LOGS"
        verbose_name_plural = "SEND MONEY LOGS"


class CoreBankingCallback(BaseModel):
    one_time = models.BooleanField(default=False)
    request_reference = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    company = models.CharField(max_length=255)
    sub_company = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    sub_company_email = models.EmailField(
        max_length=255,
        null=True,
        blank=True,
    )
    sub_company_unique_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    recipient_account_name = models.CharField(max_length=255)
    recipient_account_number = models.CharField(max_length=255)
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.0)],
    )
    fee = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.0)],
    )
    amount_payable = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.0)],
    )
    reference = models.CharField(max_length=255, unique=True)
    transaction_type = models.CharField(max_length=255)
    payer_account_name = models.CharField(max_length=255)
    payer_account_number = models.CharField(max_length=255)
    payer_bank_code = models.CharField(max_length=255)
    paid_at = models.CharField(max_length=255)
    narration = models.CharField(max_length=255)
    session_id = models.CharField(max_length=255, unique=True)
    transaction_reference = models.CharField(max_length=255, unique=True)
    settlement_status = models.BooleanField(
        default=False,
        editable=False,
        help_text="indicates if the inflow has settled in the company's/branch's wallet.",
    )
    currency = models.CharField(max_length=25)
    payload = models.TextField(null=True, blank=True)

    def __str__(self) -> str:
        return f"sessionID: {self.session_id} && amount: {self.amount}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CORE BANKING CALLBACK"
        verbose_name_plural = "CORE BANKING CALLBACKS"

    @classmethod
    def register_wema_inflow(cls, data):
        try:
            inflow = cls.objects.create(
                one_time=data.get("one_time"),
                request_reference=data.get("request_reference"),
                company=data.get("company"),
                sub_company=data.get("sub_company"),
                sub_company_email=data.get("sub_company_email"),
                sub_company_unique_id=data.get("sub_company_unique_id"),
                recipient_account_name=data.get("recipient_account_name"),
                recipient_account_number=data.get("recipient_account_number"),
                amount=data.get("amount"),
                fee=data.get("fee"),
                amount_payable=data.get("amount_payable"),
                reference=data.get("reference"),
                transaction_type=data.get("transaction_type"),
                payer_account_name=data.get("payer_account_name"),
                payer_account_number=data.get("payer_account_number"),
                payer_bank_code=data.get("payer_bank_code"),
                paid_at=data.get("paid_at"),
                narration=data.get("narration"),
                session_id=data.get("session_id"),
                transaction_reference=data.get("transaction_reference"),
                currency=data.get("currency"),
                payload=json.dumps(data),
            )
            handle_company_wema_inflow.delay(inflow.id)
            response = {"message": "event notification received."}
        except IntegrityError as inflow_error:
            response = {"message": str(inflow_error)}
        return response


class Beneficiary(models.Model):
    user = models.ForeignKey(
        User, related_name="bank_beneficiary", on_delete=models.CASCADE
    )
    beneficiary_name = models.CharField(max_length=255)
    account_number = models.CharField(max_length=20)
    bank_name = models.CharField(max_length=255)
    bank_code = models.CharField(max_length=10)
    email = models.EmailField(blank=True, null=True)
    phone_number = models.CharField(
        max_length=15,
        blank=True,
        null=True,
    )
    is_active = models.BooleanField(default=False)
    saved_as_beneficiary = models.BooleanField(default=False)
    is_removed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "BENEFICIARY"
        verbose_name_plural = "BENEFICIARIES"
        unique_together = ("user", "account_number", "bank_code")

    def __str__(self):
        return f"{self.beneficiary_name} - {self.account_number} ({self.bank_code})"

    def get_total_deductable(self, amount) -> dict:
        const = ConstantTable.get_constant_instance()

        # Retrieve stamp duty amount and base amount from constants
        stamp_duty_amt = const.vfd_stamp_duty
        stamp_duty_base_amount = const.stamp_duty_base_amount
        vfd_fee = const.vfd_fee
        withdrawal_fee = const.withdrawal_fee

        charge_stmp = amount >= stamp_duty_base_amount

        if self.bank_code in ["090110", "999999"]:
            commission = 0.0
            provider_fee = 0.0
            debit_amount = amount + stamp_duty_amt if charge_stmp else amount

        else:
            commission = withdrawal_fee - vfd_fee
            provider_fee = vfd_fee
            debit_amount = (
                amount + stamp_duty_amt + withdrawal_fee
                if charge_stmp
                else amount + withdrawal_fee
            )
        return {
            "amount": amount,
            "stamp_duty_amt": stamp_duty_amt if charge_stmp else 0,
            "debit_amount": debit_amount,
            "amount_transferable": amount,
            "commission": commission,
            "provider_fee": provider_fee,
        }

    @classmethod
    def get_total_deductable_and_actual_transfer_for_multiple_beneficiary(
        cls, beneficiaries: list
    ) -> dict:
        summary = []
        total_deductable = 0
        actual_transfer = 0

        for _beneficiary in beneficiaries:
            deductable_summary = cls.objects.get(
                id=_beneficiary.get("id")
            ).get_total_deductable(amount=_beneficiary.get("amount"))
            summary.append(
                {
                    "id": _beneficiary.get("id"),
                    **deductable_summary,
                }
            )

            total_deductable += deductable_summary.get("debit_amount")
            actual_transfer += deductable_summary.get("amount")
        return {
            "count": len(summary),
            "total_deductable": total_deductable,
            "actual_transfer": actual_transfer,
            "result": summary,
        }

    @classmethod
    def get_existing_beneficiaries(
        cls, user: User, beneficiaries: List[Dict[str, str]]
    ) -> Dict[Tuple[str, str], "Beneficiary"]:
        """
        Fetch existing beneficiaries that belong to the given user.

        :param user: The user whose beneficiaries we want to fetch.
        :param beneficiaries: A list of dictionaries containing 'account_number' and 'bank_code'.
        :return: A dictionary mapping (account_number, bank_code) to Beneficiary instances.
        """
        account_bank_pairs = [
            (b["account_number"], b["bank_code"]) for b in beneficiaries
        ]

        return {
            (b.account_number, b.bank_code): b
            for b in cls.objects.filter(
                user=user,
                account_number__in=[pair[0] for pair in account_bank_pairs],
                bank_code__in=[pair[1] for pair in account_bank_pairs],
            )
        }

    @classmethod
    def get_create_beneficiaries(cls, user, beneficiaries: list):
        """
        Bulk creates new beneficiaries while ensuring no duplicates exist for the given user.
        If an existing beneficiary is found, it updates the `saved_as_beneficiary` field if changed.

        :param user: The user adding the beneficiaries.
        :param beneficiaries: List of beneficiary dictionaries.
        :return: List of all valid Beneficiary objects (both new and updated), including their respective amounts.
        """
        if not beneficiaries:
            return []  # Return empty list if no beneficiaries provided

        # Create a mapping of (account_number, bank_code) → amount from the input data
        amount_mapping = {
            (b["account_number"], b["bank_code"]): b.get("amount", 0.0)
            for b in beneficiaries
        }
        narration_mapping = {
            (b["account_number"], b["bank_code"]): b.get("narration")
            for b in beneficiaries
        }

        # Fetch existing beneficiaries that belong to the user
        existing_beneficiaries = cls.get_existing_beneficiaries(
            user=user, beneficiaries=beneficiaries
        )

        new_beneficiaries = []
        beneficiaries_to_update = []

        for b in beneficiaries:
            key = (b["account_number"], b["bank_code"])
            if key in existing_beneficiaries:
                existing_beneficiary = existing_beneficiaries[key]
                # If `save_as_beneficiary` has changed, update it
                if (
                    existing_beneficiary.saved_as_beneficiary
                    != b["save_as_beneficiary"]
                ):
                    existing_beneficiary.saved_as_beneficiary = b["save_as_beneficiary"]
                    beneficiaries_to_update.append(existing_beneficiary)
            else:
                # Prepare new beneficiary for bulk creation
                new_beneficiaries.append(
                    cls(
                        user=user,
                        beneficiary_name=b["beneficiary_name"],
                        account_number=b["account_number"],
                        bank_name=b["bank_name"],
                        bank_code=b["bank_code"],
                        email=b.get("email", None),
                        is_active=True,
                        saved_as_beneficiary=b["save_as_beneficiary"],
                        phone_number=b.get("phone_number", None),
                    )
                )

        # Bulk update existing beneficiaries whose `save_as_beneficiary` changed
        if beneficiaries_to_update:
            cls.objects.bulk_update(beneficiaries_to_update, ["saved_as_beneficiary"])

        # Bulk create new beneficiaries
        created_beneficiaries = cls.objects.bulk_create(new_beneficiaries)

        # Combine both new and existing beneficiaries in the response
        all_valid_beneficiaries = created_beneficiaries + list(
            existing_beneficiaries.values()
        )

        # Return the final list with the corresponding `amount`
        return [
            {
                "id": b.id,
                "beneficiary_name": b.beneficiary_name,
                "account_number": b.account_number,
                "bank_name": b.bank_name,
                "bank_code": b.bank_code,
                "saved_as_beneficiary": b.saved_as_beneficiary,
                "amount": amount_mapping.get(
                    (b.account_number, b.bank_code), 0
                ),  # Fetch amount or default to 0
                "narration": narration_mapping.get(
                    (b.account_number, b.bank_code), None
                ),  # Fetch narration or default to None
            }
            for b in all_valid_beneficiaries
        ]


class BulkTransfer(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="bulk_transfers"
    )
    reference_id = models.CharField(max_length=50, unique=True)
    user_wallet = models.ForeignKey(
        Wallet, on_delete=models.CASCADE, related_name="bulk_wallet_transfer"
    )
    status = models.CharField(
        max_length=100,
        choices=TransactionStatus.choices,
        default=TransactionStatus.PENDING,
    )
    expected_debit = models.FloatField(default=0.0)
    total_debit = models.FloatField(default=0.0)
    total_sent = models.FloatField(default=0.0)
    total_expected_transfer = models.FloatField(default=0.0)
    total_expected_transfer_count = models.IntegerField(default=0)
    total_transfer_count = models.IntegerField(default=0)
    charge_wallet = models.BooleanField(default=False)
    is_scheduled = models.BooleanField(default=False)
    schedule_type = models.CharField(
        max_length=10, choices=ScheduleTypes.choices, null=True, blank=True
    )
    schedule_name = models.CharField(max_length=300, null=True, blank=True)
    schedule_day = models.PositiveIntegerField(
        null=True, blank=True, help_text="Day of month (1-31)"
    )
    start_date = models.DateField(null=True, blank=True)
    next_execution_date = models.DateField(
        null=True, blank=True, help_text="Tracks the next run date"
    )
    last_notification_time = models.DateTimeField(null=True, blank=True)
    end_date = models.DateField(
        null=True, blank=True, help_text="Optional, for stopping"
    )
    is_active = models.BooleanField(
        default=False, help_text="Controls if Sheduled transfer is still running"
    )
    last_executed = models.DateTimeField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "BULK TRANSFER"
        verbose_name_plural = "BULK TRANSFERS"

    def __str__(self):
        return f"Bulk Transfer {self.reference_id} - {self.status}"

    def save(self, *args, **kwargs):
        if not self.reference_id:
            self.reference_id = f"BULK-{uuid.uuid4().hex[:10].upper()}"

        if self.is_scheduled and not self.next_execution_date:
            self.next_execution_date = self.start_date
            self.schedule_day = self.start_date.day  # Default to the day of start_date

        super().save(*args, **kwargs)

    def calculate_next_execution(self):
        """Calculate the next execution date based on schedule type."""

        if not self.is_scheduled or self.schedule_type == ScheduleTypes.ONE_OFF:
            return None  # No scheduling required

        today = datetime.today().date()

        if self.schedule_type == ScheduleTypes.DAILY:
            return today + timedelta(days=1)

        elif self.schedule_type == ScheduleTypes.WEEKLY:
            return today + timedelta(weeks=1)

        elif self.schedule_type == ScheduleTypes.MONTHLY:
            # Get the scheduled day or default to the start_date day
            scheduled_day = self.schedule_day or self.start_date.day

            # Determine the next month and year
            next_month = (self.next_execution_date.month % 12) + 1
            next_year = self.next_execution_date.year + (
                1 if self.next_execution_date.month == 12 else 0
            )

            # Get the last day of the next month
            _, last_day_of_next_month = calendar.monthrange(next_year, next_month)

            # Ensure the scheduled day is within the valid range of the next month
            next_day = min(scheduled_day, last_day_of_next_month)

            return datetime(next_year, next_month, next_day).date()

        elif self.schedule_type == ScheduleTypes.MONTHLY:
            return datetime(
                self.next_execution_date.year + 1,
                self.schedule_day or self.start_date.day,
                self.start_date.day,
            ).date()

        return None

    def update_next_execution(self):
        """Update next execution date without modifying the start date."""
        next_date = self.calculate_next_execution()
        if self.end_date and next_date > self.end_date:
            self.is_active = False  # Stop scheduling if end date is reached
            self.status = TransactionStatus.COMPLETED
        else:
            self.next_execution_date = next_date
            self.status = TransactionStatus.RUNNING
        self.save(update_fields=["next_execution_date", "is_active", "status"])
        return

    @classmethod
    def create_bulk_transfer_record(cls, user, validated_data: dict):

        wallet_id = validated_data["wallet_id"]
        beneficiary_data = validated_data["updated_beneficiaries_with_ids"]
        is_scheduled = validated_data.get("is_scheduled")
        schedule_type = validated_data.get("schedule_type")
        start_date = validated_data.get("start_date")
        end_date = validated_data.get("end_date")
        schedule_name = validated_data.get("schedule_name")

        beneficiary_summary = Beneficiary.get_total_deductable_and_actual_transfer_for_multiple_beneficiary(
            beneficiaries=beneficiary_data
        )
        with transaction.atomic():
            bulk_transfer = cls.objects.create(
                user=user,
                user_wallet_id=wallet_id,
                expected_debit=beneficiary_summary["total_deductable"],
                total_expected_transfer_count=beneficiary_summary["count"],
                total_expected_transfer=beneficiary_summary["actual_transfer"],
                is_scheduled=is_scheduled,
                is_active=is_scheduled,
                status=(
                    TransactionStatus.SCHEDULED
                    if is_scheduled
                    else TransactionStatus.PENDING
                ),
                schedule_type=schedule_type,
                start_date=start_date,
                end_date=end_date,
                schedule_name=schedule_name,
            )

            summary = beneficiary_summary["result"]

            items = []
            for index, _transfer_summary in enumerate(summary):
                items.append(
                    BulkTransferItem(
                        bulk_transfer=bulk_transfer,
                        benficiary_id=_transfer_summary["id"],
                        is_scheduled=is_scheduled,
                        status=(
                            TransactionStatus.SCHEDULED
                            if is_scheduled
                            else TransactionStatus.PENDING
                        ),
                        narration=beneficiary_data[index].get("narration"),
                        debit_amount=_transfer_summary["debit_amount"],
                        amount=_transfer_summary["amount"],
                        commission=_transfer_summary["commission"],
                        provider_fee=_transfer_summary["provider_fee"],
                        stamp_duty_amt=_transfer_summary["stamp_duty_amt"],
                    ),
                )

            BulkTransferItem.objects.bulk_create(items)
            return bulk_transfer.id

    @classmethod
    def transfer_funds(cls, transfer_id=None):
        BATCH_SIZE = 50  # Number of transactions to process per batch
        MAX_ITERATIONS = 50  # Safety limit to prevent infinite loops
        iteration = 0

        while iteration < MAX_ITERATIONS:
            logger.info(f"Processing (oneoff) batch {iteration}...")
            logger.info(f"Processing (oneoff) batch {iteration}...")
            logger.info(f"Processing (oneoff) batch {iteration}...")
            iteration += 1

            # Fetch PENDING transactions in small batches
            with transaction.atomic():
                if not transfer_id:

                    pending_transfers = (
                        cls.objects.select_for_update(skip_locked=True)
                        .filter(status=TransactionStatus.PENDING, is_scheduled=False)
                        .order_by("id")[:BATCH_SIZE]
                    )
                else:
                    pending_transfers = (
                        cls.objects.select_for_update(skip_locked=True)
                        .filter(
                            id=transfer_id,
                            status=TransactionStatus.PENDING,
                            is_scheduled=False,
                        )
                        .order_by("id")[:BATCH_SIZE]
                    )

                if not pending_transfers:
                    break  # No more records to process

                pending_transfer_ids = [t.id for t in pending_transfers]

                # Bulk update status to IN_PROGRESS
                cls.objects.filter(id__in=pending_transfer_ids).update(
                    status=TransactionStatus.IN_PROGRESS
                )

            for pending_transfer in pending_transfers:
                reference_id = pending_transfer.reference_id
                account = pending_transfer.user_wallet.account

                # Fetch PENDING bulk transfer items related to this transaction
                bulk_transfer_items = BulkTransferItem.objects.filter(
                    status=TransactionStatus.PENDING, bulk_transfer=pending_transfer
                ).order_by("id")

                if not bulk_transfer_items:
                    continue  # No items to process

                bulk_transfer_ids = [item.id for item in bulk_transfer_items]

                # Mark all bulk transfer items as IN_PROGRESS in one query
                BulkTransferItem.objects.filter(id__in=bulk_transfer_ids).update(
                    status=TransactionStatus.IN_PROGRESS
                )

                amount_debited = 0
                count_of_successful_debit = 0
                total_sent = 0
                failed_items = []

                for bulk_transfer_item in bulk_transfer_items:
                    transaction_ref = Transaction.vfd_funds_transfer(
                        bank_code=bulk_transfer_item.benficiary.bank_code,
                        bank_name=bulk_transfer_item.benficiary.bank_name,
                        account_name=bulk_transfer_item.benficiary.beneficiary_name,
                        account_number=bulk_transfer_item.benficiary.account_number,
                        narration=bulk_transfer_item.narration,
                        amount=bulk_transfer_item.amount,
                        account=account,
                        user=account.user,
                        bulk_transfer_id=reference_id,
                    )

                    if isinstance(transaction_ref, dict):  # Transaction failed
                        bulk_transfer_item.status = TransactionStatus.FAILED
                        bulk_transfer_item.transfer_result = transaction_ref
                        failed_items.append(bulk_transfer_item)
                        continue

                    # Successful transaction
                    amount_debited += bulk_transfer_item.debit_amount
                    total_sent += bulk_transfer_item.amount
                    count_of_successful_debit += 1
                    # bulk_transfer_item.transfer_result = transaction_ref
                    bulk_transfer_item.status = TransactionStatus.COMPLETED
                    linked_transaction = Transaction.objects.get(
                        transaction_ref=transaction_ref
                    )
                    BulkTransferItemTransaction.create_instance(
                        bulk_transfer_item=bulk_transfer_item,
                        linked_transaction=linked_transaction,
                        date_transferred=datetime.now(),
                    )

                # Bulk update successful transactions
                BulkTransferItem.objects.bulk_update(
                    bulk_transfer_items,
                    ["status"],
                    batch_size=BATCH_SIZE,
                )

                # Bulk update failed transactions separately
                if failed_items:
                    BulkTransferItem.objects.bulk_update(
                        failed_items, ["status"], batch_size=BATCH_SIZE
                    )

                # Update pending transfer summary atomically
                if amount_debited > 0:
                    all_items_completed = not BulkTransferItem.objects.filter(
                        bulk_transfer=pending_transfer,
                        status__in=[
                            TransactionStatus.PENDING,
                            TransactionStatus.IN_PROGRESS,
                        ],
                    ).exists()
                    cls.objects.filter(id=pending_transfer.id).update(
                        total_debit=F("total_debit") + amount_debited,
                        total_sent=F("total_sent") + total_sent,
                        total_transfer_count=F("total_transfer_count")
                        + count_of_successful_debit,
                        charge_wallet=True,
                        status=(
                            TransactionStatus.COMPLETED
                            if all_items_completed
                            else TransactionStatus.PARTIALLY_COMPLETED
                        ),
                    )

    @classmethod
    def process_scheduled_transfers(cls, transfer_id=None):
        from core.tasks import send_email

        BATCH_SIZE = 50  # Process in batches
        MAX_ITERATIONS = 50  # Safety limit to prevent infinite loops
        today = datetime.today().date()
        retry_window = today - timedelta(days=5)  # Maximum retry period
        thirty_minutes_ago = timezone.now() - timedelta(minutes=30)
        iteration = 0

        while iteration < MAX_ITERATIONS:

            logger.info(f"Processing (scheduled) batch {iteration}...")
            logger.info(f"Processing (scheduled) batch {iteration}...")
            logger.info(f"Processing (scheduled) batch {iteration}...")

            iteration += 1
            with transaction.atomic():
                # Get eligible scheduled transfers that:
                # - Are within the retry window or due for execution
                # - Have not been successfully executed

                filters = (
                    Q(is_scheduled=True)
                    & Q(is_active=True)
                    & Q(
                        status__in=[
                            TransactionStatus.SCHEDULED,
                            TransactionStatus.RUNNING,
                        ]
                    )
                    & Q(next_execution_date__lte=today)  # Due for execution
                    & Q(
                        next_execution_date__gte=retry_window
                    )  # Still within retry window
                    & ~Q(
                        last_notification_time__gte=thirty_minutes_ago
                    )  # Exclude recently notified items
                )

                if transfer_id:
                    filters &= Q(
                        id=transfer_id
                    )  # Add id filter dynamically if provided

                due_scheduled_transfers = (
                    cls.objects.select_for_update(skip_locked=True)
                    .filter(filters)
                    .order_by("id")[:BATCH_SIZE]
                )

                logger.info(
                    f"Processing (scheduled) query-set {due_scheduled_transfers.count()}..."
                )
                logger.info(
                    f"Processing (scheduled) query-set {due_scheduled_transfers.count()}..."
                )
                logger.info(
                    f"Processing (scheduled) query-set {due_scheduled_transfers.count()}..."
                )

                if not due_scheduled_transfers:
                    break  # No more transfers to process

            for due_scheduled_transfer in due_scheduled_transfers:

                reference_id = due_scheduled_transfer.reference_id
                user_wallet = due_scheduled_transfer.user_wallet
                account = user_wallet.account
                available_balance = user_wallet.balance
                next_execution_date = due_scheduled_transfer.next_execution_date
                # Check if the account has enough balance
                if available_balance < due_scheduled_transfer.expected_debit:
                    # Update last notification time
                    due_scheduled_transfer.last_notification_time = timezone.now()
                    due_scheduled_transfer.last_executed = timezone.now()
                    due_scheduled_transfer.save(
                        update_fields=["last_notification_time"]
                    )

                    # Insufficient funds - Retry within the execution window
                    ## email notification
                    ScheduledTransferLog.create_scheduled_transfer_log(
                        bulk_transfer_instance=due_scheduled_transfer,
                        status=TransactionStatus.FAILED,
                        reason=f"insufficient wallet balance: {user_wallet.balance} expected debit: {due_scheduled_transfer.expected_debit}",
                    )
                    template_dir = os.path.join(
                        "account", "insufficient_wallet_balance.html"
                    )

                    send_email.delay(
                        recipient=account.user.email,
                        subject="Insufficient Funds for Scheduled Transfer",
                        user_fullname=due_scheduled_transfer.user.get_full_name(),
                        expected_transfer=due_scheduled_transfer.total_expected_transfer,
                        template_dir=template_dir,
                        scheduled_name=due_scheduled_transfer.schedule_name,
                        available_balance=available_balance,
                        expected_debit=due_scheduled_transfer.expected_debit,
                        company_name=(
                            account.company.company_name if account.company else ""
                        ),
                        account_number=account.account_number,
                        bank_name="" if not account.bank_name else account.bank_name,
                    )
                    continue

                # Start the transfer process
                with transaction.atomic():
                    # Mark transfer as IN_PROGRESS only for this attempt
                    due_scheduled_transfer.status = TransactionStatus.IN_PROGRESS
                    due_scheduled_transfer.save(update_fields=["status"])

                bulk_transfer_items = BulkTransferItem.objects.filter(
                    bulk_transfer=due_scheduled_transfer
                ).order_by("id")

                if not bulk_transfer_items:
                    continue

                amount_debited = 0
                count_of_successful_debit = 0
                total_sent = 0
                failed_items = []

                for bulk_transfer_item in bulk_transfer_items:
                    transaction_ref = Transaction.vfd_funds_transfer(
                        bank_code=bulk_transfer_item.benficiary.bank_code,
                        bank_name=bulk_transfer_item.benficiary.bank_name,
                        account_name=bulk_transfer_item.benficiary.beneficiary_name,
                        account_number=bulk_transfer_item.benficiary.account_number,
                        narration=bulk_transfer_item.narration,
                        amount=bulk_transfer_item.amount,
                        account=account,
                        user=account.user,
                        bulk_transfer_id=reference_id,
                    )

                    if isinstance(transaction_ref, dict):  # Transaction failed
                        bulk_transfer_item.status = TransactionStatus.FAILED
                        bulk_transfer_item.transfer_result = transaction_ref
                        failed_items.append(bulk_transfer_item)
                        continue

                    # Successful transaction
                    amount_debited += bulk_transfer_item.debit_amount
                    total_sent += bulk_transfer_item.amount
                    count_of_successful_debit += 1
                    bulk_transfer_item.transfer_result = transaction_ref
                    bulk_transfer_item.status = TransactionStatus.RUNNING

                    linked_transaction = Transaction.objects.get(
                        transaction_ref=transaction_ref
                    )

                    BulkTransferItemTransaction.create_instance(
                        bulk_transfer_item=bulk_transfer_item,
                        linked_transaction=linked_transaction,
                        date_transferred=datetime.now(),
                    )

                ScheduledTransferLog.create_scheduled_transfer_log(
                    bulk_transfer_instance=due_scheduled_transfer,
                    status=TransactionStatus.COMPLETED,
                )

                # Bulk update successful transactions
                BulkTransferItem.objects.bulk_update(
                    bulk_transfer_items,
                    ["status"],
                    batch_size=BATCH_SIZE,
                )

                # Bulk update failed transactions separately
                if failed_items:
                    BulkTransferItem.objects.bulk_update(
                        failed_items, ["status"], batch_size=BATCH_SIZE
                    )

                # Update pending transfer summary atomically
                if amount_debited > 0:

                    due_scheduled_transfer.update_next_execution()
                    due_scheduled_transfer.refresh_from_db()
                    #########################################
                    #########################################

                    due_scheduled_transfer.total_transfer_count += (
                        count_of_successful_debit
                    )
                    due_scheduled_transfer.charge_wallet = True
                    due_scheduled_transfer.total_debit += amount_debited
                    due_scheduled_transfer.total_sent += total_sent
                    due_scheduled_transfer.last_executed = timezone.now()

                    # Save transfer status update
                    due_scheduled_transfer.save(
                        update_fields=[
                            "total_debit",
                            "total_sent",
                            "total_transfer_count",
                            "charge_wallet",
                        ]
                    )

                    template_dir = os.path.join(
                        "account", "processed_scheduled_transfer.html"
                    )

                    send_email.delay(
                        recipient=account.user.email,
                        subject="Your Scheduled Transfer Is Being Processed",
                        template_dir=template_dir,
                        scheduled_date=next_execution_date,
                        user_fullname=due_scheduled_transfer.user.get_full_name(),
                        scheduled_name=due_scheduled_transfer.schedule_name,
                        expected_debit=due_scheduled_transfer.expected_debit,
                        count_of_benficiaries=count_of_successful_debit,
                        total_expected_transfer_amount=due_scheduled_transfer.total_expected_transfer,
                    )


class BulkTransferItem(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    bulk_transfer = models.ForeignKey(
        BulkTransfer, on_delete=models.CASCADE, related_name="items"
    )
    benficiary = models.ForeignKey(
        Beneficiary, on_delete=models.CASCADE, related_name="transfer_beneficiaries"
    )
    amount = models.FloatField()
    provider_fee = models.FloatField()
    debit_amount = models.FloatField()
    commission = models.FloatField()
    stamp_duty_amt = models.FloatField()
    updated_at = models.DateTimeField(auto_now=True)
    narration = models.CharField(max_length=300, null=True, blank=True)
    status = models.CharField(
        max_length=100,
        choices=TransactionStatus.choices,
        default=TransactionStatus.PENDING,
    )
    is_scheduled = models.BooleanField(default=False)
    # transaction_reference = models.CharField(
    #     max_length=300,
    #     help_text="Represent the record id or reference on the actual transaction(Transaction model) table that holds all bank transfers and deposit",
    #     null=True,
    #     blank=True,
    # )
    transfer_result = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = "BULK TRANSFER ITEM"
        verbose_name_plural = "BULK TRANSFER ITEMS"
        unique_together = ("bulk_transfer", "benficiary")

    def __str__(self):
        return f"{self.benficiary.beneficiary_name} - {self.amount}"

    def clean(self):
        if self.amount < 100:
            raise ValueError("Amount must be greater than 100")


class BulkTransferItemTransaction(models.Model):
    bulk_transfer_item = models.ForeignKey(
        BulkTransferItem, on_delete=models.CASCADE, related_name="transactions"
    )
    linked_transaction = models.ForeignKey(
        Transaction, on_delete=models.CASCADE, related_name="bulk_transfer_items"
    )
    date_transferred = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "BULK TRANSFER ITEM TRANSACTION"
        verbose_name_plural = "BULK TRANSFER ITEM TRANSACTION"

    def __str__(self):
        return f"Bulk Transfer {self.bulk_transfer_item.bulk_transfer} Transfer item id: {self.id}"

    @property
    def transaction_status(self):
        return f"{self.linked_transaction .status}"

    @classmethod
    def create_instance(
        cls,
        bulk_transfer_item: BulkTransferItem,
        linked_transaction: Transaction,
        date_transferred: datetime,
    ):
        return cls.objects.create(
            bulk_transfer_item=bulk_transfer_item,
            linked_transaction=linked_transaction,
            date_transferred=date_transferred,
        )


class ScheduledTransferLog(models.Model):
    bulk_transfer = models.ForeignKey(
        BulkTransfer, on_delete=models.CASCADE, related_name="transfer_logs"
    )
    executed_at = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=100,
        choices=TransactionStatus.choices,
        default=TransactionStatus.IN_PROGRESS,
    )
    reason = models.CharField(max_length=300, null=True, blank=True)

    class Meta:
        verbose_name = "SCHEDULED TRANSFER LOG"
        verbose_name_plural = "SCHEDULED TRANSFER LOG"

    def __str__(self):
        return f"Bulk Transfer {self.bulk_transfer} - {self.status}"

    @classmethod
    def create_scheduled_transfer_log(
        cls, bulk_transfer_instance, status: TransactionStatus, reason=None
    ):

        return cls.objects.create(
            bulk_transfer=bulk_transfer_instance, status=status, reason=reason
        )
