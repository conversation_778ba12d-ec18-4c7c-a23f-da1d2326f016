
from django.core.management.base import BaseCommand
from django.conf import settings
import random
from datetime import datetime
import pytz

from performance_sales_metrics_dashboard.models import SalesLead, SalesOfficer, ProductVerticals


class Command(BaseCommand):
    help = 'Populate the database with initial data for sales leads and sales officers.'

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        # Get all product verticals from the database
        product_verticals = list(ProductVerticals.objects.all())

        if len(product_verticals) < 2:
            self.stdout.write(self.style.ERROR(
                "Not enough product verticals available. Please add more product verticals to the database."))
            return

        # Create Sales Leads
        sales_leads = []
        for i in range(3):
            if i == 0:
                selected_verticals = random.sample(product_verticals, k=2)
            else:
                selected_verticals = random.sample(product_verticals, k=1)

            lead = SalesLead.objects.create(
                name=f"Sales Lead {i + 1}",
                phone_number=f"123456789{i}",
                email=f"sales_lead_{i + 1}@example.com",
                date_hired=datetime.now().date(),
            )
            # Assign the product verticals using the set method
            lead.product_verticals.set(selected_verticals)
            sales_leads.append(lead)

        # Create Sales Officers and assign them to Sales Leads
        sales_officers = []
        officer_count = 0
        for lead in sales_leads:
            num_officers = 30 if lead.product_verticals.count() == 2 else 15
            for i in range(num_officers):
                selected_vertical = random.choice(lead.product_verticals.all())
                officer = SalesOfficer.objects.create(
                    sales_lead=lead,
                    name=f"Sales Officer {officer_count + 1}",
                    phone_number=f"098765432{officer_count}",
                    email=f"sales_officer_{officer_count + 1}@example.com",
                    date_hired=datetime.now().date(),
                    product_module=selected_vertical,  # Assigning the product_module field
                )
                sales_officers.append(officer)
                officer_count += 1

        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"Data population completed in {(END_TIME - START_TIME).total_seconds()} seconds"))
