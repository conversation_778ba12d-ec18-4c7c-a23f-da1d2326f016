from requisition.helpers.enums import User<PERSON><PERSON>
from requisition.models import TeamMember
from ..models import Emails

def save_read_emails(
        is_read, 
        sales_officer, 
        date, 
        subject,
        sender, 
        senders_name,
        receiver, 
        content, 
    ):
    try:
        date_sent = Emails.objects.get(date=date)
        Emails.objects.get(date=date_sent.date)
            # continue
    # except Emails.DoesNotExist:
    except:
        sales_officer_instance = TeamMember.objects.get(
                id=sales_officer, role=UserRole.SALES_OFFICER
            )
        Emails.save_as_read(
            is_read=is_read,
            sales_officer=sales_officer_instance, 
            date=date, 
            subject=subject, 
            sender=sender,
            senders_name=senders_name,
            recipients=receiver, 
            content=content, 
        )
