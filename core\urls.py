from django.urls import include, path

from core import views
from core.serializers import SalesCustomObtainPairView
from requisition.views import GetTeamMembersListFilterTeam


# Create your url pattern(s) here.
user_identifier_urls = [
    path("set_default_company/", views.SetDefaultCompanyAPIView.as_view()),
    path("set_default_branch/", views.SetDefaultBranchAPIView.as_view()),
    path("set_secret_question/", views.SecretQuestionAPIView.as_view()),
]

sales_web_pos_urls = [
    path("sales_login/", SalesCustomObtainPairView.as_view()),
    # path("sales_login/", views.SalesLoginAPIView.as_view()),
    path("create_sales_user/", views.SalesUserRegistrationAPIView.as_view()),
    path(
        "reset_sales_default_passcode/",
        views.ResetSalesDefaultPasscodeAPIView.as_view(),
    ),
    path("filter_sales_employees/", views.GetAllFilterSalesUser.as_view()),
    path("start_sales_shift/", views.StartSalesShiftAPIView.as_view()),
    path("end_sales_shift/", views.EndSalesShiftAPIView.as_view()),
    path("start_sales_shift_break/", views.StartSalesShiftBreakAPIView.as_view()),
    path("end_sales_shift_break/", views.EndSalesShiftBreakAPIView.as_view()),
    path("filter_sales_break/", views.GetAllFilterSalesShiftBreak.as_view()),
    path("filter_sales_user_break/", views.GetAllFilterSalesShiftUserBreak.as_view()),
    path("filter_sales_attendance/", views.GetAllFilterSalesShift.as_view()),
    path("filter_sales_user_attendance/", views.GetAllFilterSalesUserShift.as_view()),
    path("sales_user_shift_status/", views.SalesUserShiftStatusAPIView.as_view()),
    path(
        "admin_reset_default_passcode/",
        views.AdminResetSalesDefaultPasscodeAPIView.as_view(),
    ),
    path("admin_restrict_sales_user/", views.AdminRestrictSalesUserAPIView.as_view()),
    path(
        "admin_unrestrict_sales_user/", views.AdminUnRestrictSalesUserAPIView.as_view()
    ),
    path("admin_delete_sales_user/", views.AdminDeleteSalesUserAPIView.as_view()),
    path("upload_apk/", views.UploadOfflineAppAPIView.as_view()),
    path("download_apk", views.DownloadOfflineAppAPIView.as_view()),
    path("edit_sales_user/", views.UpdateSalesUserRoleAPIView.as_view()),
]

sms_campaign_urls = [
    path("create_sender_id/", views.CreateSenderIdAPIView.as_view()),
    path("send_campaign_message/", views.SendCampaignMessageAPIView.as_view()),
    path("confirm_campaign_message/", views.ConfirmCampaignMessageAPIView.as_view()),
    path("campaign_dashboard/", views.CampaignDashboardAPIView.as_view()),
    path(
        "campaign_account_dashboard/", views.CampaignAccountDashboardAPIView.as_view()
    ),
    path("get_all_sender_id/", views.GetAllFilterSenderId.as_view()),
    path("campaign_history/", views.GetCampaignMessageHistory.as_view()),
]

settings_and_profile = [
    # path("populate_user_data/", views.populate_user_data),
    # path("update_profile_picture/", views.update_profile_picture),
    path('reset_login_pin/', views.reset_transactional_pin),
    path('verify_bvn_liberty/', views.verify_bvn_liberty),
    # path('confirm_bvn_otp/', views.confirm_bvn_otp),
    path('match_face_liberty/', views.match_face_liberty),
    path('submit_guarantor_form/', views.submit_guarantor_form),
    path('confirm_kyc_verification/', views.confirm_kyc_verification),
    path('update_preference/', views.UpdatePreferenceAPIView.as_view()),
    path('update_notification/', views.UpdateNotificationAPIView.as_view()),
    path('system_access/', views.PlatformAccessVerificationView.as_view()),

]

urlpatterns = [
    path("", include(user_identifier_urls)),
    path("", include(sales_web_pos_urls)),
    path("", include(sms_campaign_urls)),
    path("", include(settings_and_profile)),
    path("upload-docs/", views.UpLoadMediaDocs.as_view()),
    path("query-users/", GetTeamMembersListFilterTeam.as_view()),
    path("sign-in/", views.MirrorLibertyPaySignIn.as_view()),
    path("create_wait_list/", views.WaitListApiView.as_view()),
    path("notifications/", views.NotificationAPIView.as_view()),
    path("paystack/", views.PaystackAPIView.as_view()),
    path('schedule_meeting/', views.VfdMeetingView.as_view(), name='schedule_meeting'),
    path('create_sales_agent/', views.SalesAgentView.as_view(), name='create_sales_agent'),
    path('create_campaign_lead/', views.CreateCampaignLeadView.as_view(), name='create_campaign_lead'),
    path('delete_sales_agent/', views.SalesAgentView.as_view(), name='delete_sales_agent'),
    path("upload_sales_team_emails/", views.ConstantTableSalesTeamView.as_view(), name="upload_sales_team_emails"),
    path(
        "server/logs/",
        views.view_logs, name="view_logs"
    )

]
