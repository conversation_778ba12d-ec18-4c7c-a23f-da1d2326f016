from datetime import datetime
import json
import pytz
import random
import uuid

from django.conf import settings
from django.core.management.base import BaseCommand
import requests

from account.enums import AcctProvider, AccountType
from account.helpers.core_banking import CoreBankingService
from account.models import AccountServiceProvider, AccountSystem
from helpers.reusable_functions import is_valid_string
from stock_inventory.models import Branch


URL = "https://banking.libertypayng.com/api/v1/fidelity/create-account/"


class Command(BaseCommand):
    help = "FIDELITY ACCOUNTS."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        branches = Branch.objects.filter(
            company__test_account=False
        ).reverse()
        print(f"\n\n\nTOTAL BRANCHES:   {branches.count()}\n\n\n")
        if not branches.exists():
            self.stdout.write(self.style.SUCCESS(f"THERE ARE NO AVAILABLE BRANCHES."))
        else:
            count = 0
            for branch in branches:
                fidelity_provider = AccountServiceProvider.objects.filter(
                    branch=branch,
                    account_provider=AcctProvider.FIDELITY
                ).last()
                if fidelity_provider is None:
                    if (
                        branch.company.user.date_of_birth is None
                        or not is_valid_string(branch.company.user.date_of_birth)
                    ):
                        date_of_birth = "1960-10-01"
                    else:
                        date_of_birth = branch.company.user.date_of_birth
                    if (
                        branch.company.user.bvn_middle_name is None
                        or not is_valid_string(branch.company.user.bvn_middle_name)
                    ):
                        middle_name = branch.company.user.first_name
                    else:
                        middle_name = branch.company.user.bvn_middle_name
                    if (
                        branch.company.user.bvn_number is None
                        or not is_valid_string(branch.company.user.bvn_number)
                    ):
                        bvn = "**********"
                    else:
                        bvn = branch.company.user.bvn_number
                    if (
                        branch.company.user.gender is None
                        or not is_valid_string(branch.company.user.gender)
                    ):
                        gender = random.choice(["Male", "Female"])
                    else:
                        gender = branch.company.user.gender
                    # Core Banking Service
                    handler = CoreBankingService()
                    token = handler.login()

                    payload = json.dumps({
                        "customer_firstname": branch.company.user.first_name,
                        "customer_surname": branch.company.user.last_name,
                        "customer_email": branch.company.user.email,
                        "customer_mobile_no": branch.company.user.phone_no,
                        "name_on_account": str(branch.__str__()),
                        "customer_middle_name": middle_name,
                        "date_of_birth": date_of_birth,
                        "gender": gender,
                        "title": random.choice(["Mr", "Mrs"]),
                        "address_line_1": "Lagos",
                        "address_line_2": "Nigeria",
                        "city": "Lagos",
                        "state": "Lagos",
                        "country": "Nigeria",
                        "transaction_ref": str(uuid.uuid4()),
                        "customer_bvn": bvn,
                    })
                    print(f"\n\n\nPAYLOAD:    {payload}\n\n\n")
                    headers = {
                        "Authorization": f"Bearer {token}",
                        "Content-Type": "application/json"
                    }
                    response = requests.request("POST", URL, headers=headers, data=payload)
                    try:
                        data = response.json()
                    except json.JSONDecodeError:
                        data = response.text
                    print(f"\n\n\nRESPONSE DATA:    {data}\n\n\n")
                    if not isinstance(data, str) and response.status_code == 200:
                        response_data = (
                            data.get("data")
                            .get("data")
                            .get("provider_response")
                        )
                        response_data = response_data
                        service_provider = AccountServiceProvider.objects.create(
                            user=branch.company.user,
                            company=branch.company,
                            branch=branch,
                            account_number=response_data.get("account_number"),
                            account_name=response_data.get("account_name"),
                            account_provider=AcctProvider.FIDELITY,
                            payload=json.dumps(data),
                        )
                        print(f"\n\n\nLOCAL SERVICE PROVIDER:    {service_provider}\n\n\n")
                        sales_account = AccountSystem.objects.filter(
                            branch=branch,
                            account_type=AccountType.SALES,
                        ).last()
                        print(f"\n\n\nSALES ACCOUNT:    {sales_account}\n\n\n")
                        if sales_account is not None:
                            sales_account.service_provider = service_provider
                            sales_account.save()
            END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
            self.stdout.write(
                self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
            )
            self.stdout.write(
                self.style.SUCCESS(f"COUNT:  {count}")
            )
            self.stdout.write(
                self.style.SUCCESS(
                    "SUCCESSFULLY CREATED VIRTUAL ACCOUNTS FOR ALL BRANCHES."
                )
            )
