from import_export import resources
from cart_management.models import Cart, CartI<PERSON>, Buyer, OrderPipeline, OrderStage, Order, OrderProduct, \
    IncompleteOrderRecord


class CartResource(resources.ModelResource):
    class Meta:
        model = Cart
        # fields = ('id', 'buyer__first_name', 'session_id')
        # export_order = ('id', 'buyer__first_name', 'session_id')
        fields = "__all__"

class CartItemResource(resources.ModelResource):
    class Meta:
        model = CartItem
        fields = ('id', 'cart__id', 'product__name', 'quantity')
        export_order = ('id', 'cart__id', 'product__name', 'quantity')

class BuyerResource(resources.ModelResource):
    class Meta:
        model = Buyer
        fields = ('id', 'first_name', 'middle_name', 'last_name', 'country', 'city', 'state', 'email', 'phone_number', 'address', 'postal_code', 'status')
        export_order = ('id', 'first_name', 'middle_name', 'last_name', 'country', 'city', 'state', 'email', 'phone_number', 'address', 'postal_code', 'status')

# class OrderPipelineResource(resources.ModelResource):
#     class Meta:
#         model = OrderPipeline
#         fields = ('id', 'name', 'is_default', 'company__team', 'branch__name')
#         export_order = ('id', 'name', 'is_default', 'company__team', 'branch__name')

class OrderStageResource(resources.ModelResource):
    class Meta:
        model = OrderStage
        fields = ('id', 'name', 'pipeline__name', 'position')
        export_order = ('id', 'name', 'pipeline__name', 'position')

class OrderResource(resources.ModelResource):
    class Meta:
        model = Order
        fields = ('id', 'order_id', 'amount_paid', 'status', 'payment_status', 'buyer__first_name', 'order_date', 'order_time', 'current_stage__name')
        export_order = ('id', 'order_id', 'amount_paid', 'status', 'payment_status', 'buyer__first_name', 'order_date', 'order_time', 'current_stage__name')

class OrderProductResource(resources.ModelResource):
    class Meta:
        model = OrderProduct
        fields = ('id', 'product_id', 'order', 'product_name', 'quantity')
        export_order = ('id', 'product_id', 'order', 'product_name', 'quantity')

class IncompleteOrderRecordResource(resources.ModelResource):
    class Meta:
        model = IncompleteOrderRecord
        fields = ("id", "products", "name", "phone")
        export_order = ("id", "products", "name", "phone")








