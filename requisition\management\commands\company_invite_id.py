from django.core.management.base import BaseCommand, CommandError
from requisition.models import Company


class Command(BaseCommand):
    help = "This command will populate invite IDs for companies that do not have it"

    def handle(self, *args, **options):
        companies = Company.objects.filter(supplier_invite_id="")
        if not companies:
            self.stderr.write("No company found for update")
        else:
            for company in companies:
                company.save()
                self.stdout.write(self.style.SUCCESS('Successfully updated invite_id for "%s"' % company.company_name))


