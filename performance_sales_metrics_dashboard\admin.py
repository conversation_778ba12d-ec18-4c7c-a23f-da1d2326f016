from collections.abc import Sequence
from django.contrib import admin
from django.http.request import HttpRequest

from import_export.admin import ImportExportModelAdmin
from performance_sales_metrics_dashboard import models, resources
from .models import *


class SalesLeadResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.SalesLeadResource
    search_fields = [
        "name"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ProductVerticalsResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ProductVerticalsResource
    search_fields = [
        "name"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CategoryResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CategoryResource
    search_fields = [
        "name"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RevenueLinesResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.RevenueLinesResource
    search_fields = [
        ""
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesOfficerResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.SalesOfficerResource
    search_fields = [
        "name"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PipelineResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.PipelineResource
    search_fields = [
        "name"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StageResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.StageResource
    search_fields = [
        "name"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MerchantResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.MerchantResource
    search_fields = [
        "name"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ActivityResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ActivityResource
    search_fields = [
        "merchant",
        "sales_officer",
        "stage",
        "title",
        "status",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TaskResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TaskResource
    search_fields = [
        "activity",
        "title",
        "priority",
        "is_reminder",
        "deadline",
        "deadline_reminder",
        "description",
        "assigned",
        "is_done",
        "status",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NotesResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.NotesResource
    search_fields = [
        "sales_officer",
        "title",
        "description",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ProductVerticalResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ProductVerticalResource
    search_fields = [
        "name"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RevenueLineResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.RevenueLineResource
    search_fields = [
        "merchant"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MerchantResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.MerchantResource
    search_fields = [
        "company"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LeadResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.LeadResource
    search_fields = [
        "contact_name",
        "contact_email",
        "contact_phone_number",
        "company_name",
        "company_email",
        "company_phone_number",
        "company_address",
        "sales_officer",
        "pipeline",
        "stage",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LeadResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.LeadResource
    search_fields = [
        "contact_name"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class EmailResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.EmailResource
    search_fields = [
        "is_read",
        "is_sent",
        "is_inbox",
        "is_draft",
        "is_deleted",
        "is_scheduled",
        "date",
        "senders_name",
        "cc",
        "bcc",
        "sender",
    ]

    list_filter = [
        "sender",
        "senders_name",
        "date",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AttachmentResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.AttachmentResource
    search_fields = [
        "filename",
        "content_type",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DemoResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.DemoResource
    search_fields = [
        "team_member",
        "lead",
        "start_time",
        "is_assigned",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NewsLetterSubscriptionResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.NewsLetterSubscriptionResource
    search_fields = [
        "full_name",
        "email"
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


# class SalesOfficerResourceAdmin(ImportExportModelAdmin):
#     resource_class = resources.SalesOfficerResource
#     search_fields = [
#         "name"
#     ]
#
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


# class SalesOfficerResourceAdmin(ImportExportModelAdmin):
#     resource_class = resources.SalesOfficerResource
#     search_fields = [
#         "name"
#     ]
#
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


# class SalesOfficerResourceAdmin(ImportExportModelAdmin):
#     resource_class = resources.SalesOfficerResource
#     search_fields = [
#         "name"
#     ]
#
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


# class SalesOfficerResourceAdmin(ImportExportModelAdmin):
#     resource_class = resources.SalesOfficerResource
#     search_fields = [
#         "name"
#     ]
#
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


# class SalesOfficerResourceAdmin(ImportExportModelAdmin):
#     resource_class = resources.SalesOfficerResource
#     search_fields = [
#         "name"
#     ]
#
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


# # class MerchantResourceAdmin(ImportExportModelAdmin):
# #     resource_class = resources.MerchantResource
# #     search_fields = [
# #         "name",
# #         "is_active"
# #     ]
# #     def get_list_display(self, request):
# #         return [field.name for field in self.model._meta.concrete_fields]


# # class SystemUsageMetricssResourceAdmin(ImportExportModelAdmin):
# #     resource_class = resources.SystemUsageMetricssResource
# #     search_fields = [
# #         "product_vertical",
# #     ]
# #     def get_list_display(self, request):
# #         return [field.name for field in self.model._meta.concrete_fields]

admin.site.register(ProductVerticals, ProductVerticalsResourceAdmin)
admin.site.register(RevenueLines, RevenueLinesResourceAdmin)
admin.site.register(Category, CategoryResourceAdmin)
admin.site.register(SalesLead, SalesLeadResourceAdmin)
admin.site.register(SalesOfficer, SalesOfficerResourceAdmin)
admin.site.register(Pipeline, PipelineResourceAdmin)
admin.site.register(Stage, StageResourceAdmin)
admin.site.register(Notes, NotesResourceAdmin)
admin.site.register(Merchants, MerchantResourceAdmin)
admin.site.register(Activity, ActivityResourceAdmin)
admin.site.register(Task, TaskResourceAdmin)
admin.site.register(Lead, LeadResourceAdmin)
admin.site.register(Emails, EmailResourceAdmin)
admin.site.register(Attachment, AttachmentResourceAdmin)
admin.site.register(BookADemo, DemoResourceAdmin)
admin.site.register(NewsLetterSubscription, NewsLetterSubscriptionResourceAdmin)
