from django.utils import timezone
import random
import uuid
from django.core.management.base import BaseCommand
from faker import Faker

from cart_management.models import Buyer, Order, OrderPipeline, OrderProduct, OrderStage
from core.models import User
from stock_inventory.models import Company, Branch, Product


class Command(BaseCommand):
    help = 'Generate fake orders for testing'

    def handle(self, *args, **kwargs):
        fake = Faker()

        company_id = uuid.UUID('2566ab36-c83c-4a4a-92ee-0650f204067b')
        branch_id = uuid.UUID('1fc9bdcf-0964-45db-8a02-fe8f6176401e')

        try:
            company = Company.objects.get(id=company_id)
            branch = Branch.objects.get(id=branch_id)
        except Company.DoesNotExist:
            self.stdout.write(self.style.ERROR('Company not found.'))
            return
        except Branch.DoesNotExist:
            self.stdout.write(self.style.ERROR('Branch not found.'))
            return

        pipeline, created = OrderPipeline.objects.get_or_create(
            company=company,
            branch=branch,
            defaults={'name': 'Default Pipeline', 'is_default': True}
        )

        if created:
            stage_names = ['Pending', 'Processing', 'Shipped', 'Delivered']
            for i, name in enumerate(stage_names):
                OrderStage.objects.create(
                    name=name,
                    position=i+1,
                    pipeline=pipeline
                )

        first_stage = OrderStage.objects.filter(pipeline=pipeline).order_by('position').first()

        for _ in range(10):  # Change the range for the number of orders you need
            buyer = Buyer.objects.create(
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                email=fake.email(),
                phone_number=fake.phone_number(),
                address=fake.address(),
                city=fake.city(),
                state=fake.state(),
                country=fake.country(),
                postal_code=fake.zipcode(),
                ship_to_different_address=fake.boolean()
            )

            order = Order.objects.create(
                company=company,
                branch=branch,
                amount_paid=0,
                status='open',
                payment_status='unpaid',
                buyer=buyer,
                order_date=timezone.now().date(),
                order_time=timezone.now().time(),
                current_stage=first_stage,
                shipping=fake.random_number(digits=2),
                discount=fake.random_number(digits=2),
                tax=fake.random_number(digits=2),
                total_price=0,
                contact={},
                vendor_whatsapp_url=fake.url(),
                trail={"events": []} 
            )

            subtotal = 0
            products = Product.objects.filter(company=company)
            if not products.exists():
                self.stdout.write(self.style.ERROR('No products found for the company.'))
                return

            for _ in range(random.randint(1, 5)):
                product = random.choice(products)
                quantity = random.randint(1, 5)
                product_price = product.selling_price
                product_subtotal = product_price * quantity

                OrderProduct.objects.create(
                    orders=order,
                    product_name=product.name,
                    product_description=product.product_description,
                    product_img=product.product_image_1,
                    quantity=quantity,
                    price=product_price,
                    sub_total=product_subtotal,
                    payment_option='card'  # assuming card as default payment option
                )

                subtotal += product_subtotal

            order.amount_paid = subtotal
            order.total_price = subtotal + order.shipping + order.tax - order.discount
            order.save()

            # Update trail
            trail_entry = {
                "event": "new order",
                "timestamp": timezone.now().isoformat(),
                "stage_name": first_stage.name,
            }
            order.trail["events"].append(trail_entry)
            order.save()

            self.stdout.write(self.style.SUCCESS(f'Created order {order.order_id}'))

        self.stdout.write(self.style.SUCCESS('Successfully generated fake orders.'))
