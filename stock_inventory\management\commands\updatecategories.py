from django.conf import settings
from django.core.management.base import BaseCommand

from stock_inventory.models import Category, Product


class Command(BaseCommand):
    help = "UPDATE ALL CATEGORIES WITH THEIR PRODUCT STATUS."

    def handle(self, *args, **kwargs):
        categories = Category.objects.filter(company__isnull=False)
        for category in categories:
            if Product.objects.filter(
                company=category.company, category=category
            ).exists():
                category.has_products = True
                category.save()
