# Performance Management App Documentation

## Overview

The Performance Management app facilitates employee performance evaluation, goal setting, and professional development for companies on the Paybox360 platform. It provides tools for conducting reviews, tracking objectives, and managing employee growth.

## Core Features

### 1. Performance Review Management

- Review cycle configuration
- Review form customization
- Multi-stage review process
- 360-degree feedback collection

### 2. Goal Management

- Objective setting and tracking
- Key Result Areas (KRAs)
- Goal alignment across organization
- Progress monitoring

### 3. Competency Assessment

- Skill matrix definition
- Competency evaluation
- Skill gap analysis
- Development planning

### 4. Feedback Management

- Continuous feedback collection
- Peer recognition
- Manager observations
- Self-assessment

### 5. Performance Analytics

- Performance trend analysis
- Team and individual dashboards
- Comparative reporting
- Performance distribution visualization

## How It Works

1. **Setup**: Companies configure review cycles and evaluation criteria
2. **Goal Setting**: Employees and managers establish performance objectives
3. **Ongoing Feedback**: Continuous feedback is collected throughout the period
4. **Review Process**: Formal evaluations are conducted at scheduled intervals
5. **Development Planning**: Performance data informs development activities
6. **Analysis**: Performance trends are analyzed for insights

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `ReviewCycle` | Review period | `start_date`, `end_date`, `status` | Company |
| `PerformanceReview` | Evaluation record | `review_date`, `overall_rating`, `status` | User, ReviewCycle |
| `PerformanceGoal` | Objectives | `title`, `description`, `due_date`, `progress` | User, Company |
| `Competency` | Skill definition | `name`, `description`, `category` | Company |
| `CompetencyRating` | Skill assessment | `rating`, `comments`, `evaluation_date` | User, Competency, PerformanceReview |
| `Feedback` | Ongoing input | `content`, `feedback_type`, `date_given` | Sender, Recipient |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `initiate_review_cycle` | Starts review process | `services.py` | Called during cycle creation |
| `calculate_overall_rating` | Computes final scores | `models.py` (PerformanceReview) | Used in review completion |
| `track_goal_progress` | Updates objective status | `services.py` | Used in goal management |
| `generate_performance_report` | Creates analysis | `reports.py` | Used in analytics views |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/performance-management/reviews/` | Manage reviews | POST/GET | `PerformanceReviewViewSet` |
| `/performance-management/goals/` | Manage objectives | POST/GET | `PerformanceGoalViewSet` |
| `/performance-management/competencies/` | Manage skills | POST/GET | `CompetencyViewSet` |
| `/performance-management/feedback/` | Provide feedback | POST/GET | `FeedbackViewSet` |
| `/performance-management/analytics/` | View performance data | GET | `PerformanceAnalyticsView` |

### Integration Points

- **Core App**: For user and organization data
- **Clock App**: For attendance correlation
- **Payroll App**: For performance-based compensation
- **Notification System**: For review reminders

## Key Considerations

### 1. Review Process Management

- **Responsible App**: Performance Management app
- **Key Functions**:
  - Workflow configuration in ReviewCycle model
  - Stage progression tracking
  - Deadline management and notifications

### 2. Goal Alignment

- **Responsible App**: Performance Management app
- **Key Functions**:
  - Hierarchical goal structure
  - Cascading objectives
  - Goal weighting and prioritization

### 3. Feedback Privacy

- **Responsible App**: Performance Management app
- **Key Functions**:
  - Anonymous feedback options
  - Access control for sensitive feedback
  - Aggregated feedback presentation

### 4. Rating Consistency

- **Responsible App**: Performance Management app
- **Key Functions**:
  - Calibration tools for managers
  - Rating guidelines and definitions
  - Historical comparison data

### 5. Development Integration

- **Responsible App**: Performance Management app
- **Key Functions**:
  - Development plan creation from reviews
  - Learning resource recommendations
  - Skill development tracking