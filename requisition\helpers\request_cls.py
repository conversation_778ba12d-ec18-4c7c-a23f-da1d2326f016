import json

import requests
from django.conf import settings
from decouple import config

NOT_FOUND = {
    "success": True,
    "statusCode": 200,
    "message": "success",
    "data": {
        "parentId": None,
        "reason": "Company details not found",
        "name": None,
        "registrationNumber": "00000000",
        "registryNumber": None,
        "vatNumber": None,
        "registrationDate": None,
        "dateDisolved": None,
        "email": None,
        "phone": None,
        "typeOfEntity": None,
        "activity": None,
        "address": None,
        "branchAddress": None,
        "headOfficeAddress": None,
        "status": "not_found",
        "companyStatus": "Unknown",
        "isConsent": True,
        "lastUpdatedAt": None,
        "paidShareCapital": None,
        "subscribedShareCapital": None,
        "sharesValue": None,
        "activityDescription": None,
        "sharesIssued": None,
        "parentCountry": None,
        "businessId": "61d880f1e8e15aaf24558f1a",
        "type": "advance_company_check",
        "requestedAt": "2022-08-28T23:56:37.447Z",
        "requestedById": "61d880f2e8e15aaf24558f9b",
        "countryCode": "BE",
        "searchTerm": "00000000",
        "companyContactPersons": [],
        "keyPersonnel": [],
        "activities": [],
        "legalEntityIdentifierRegister": [],
        "centralIndexKeyRegister": [],
        "filings": [],
        "affiliates": [],
        "createdAt": "2022-08-28T23:56:37.668Z",
        "lastModifiedAt": "2022-08-28T23:56:37.668Z",
        "_createdAt": "2022-08-29T00:56:3737+01:00",
        "_lastModifiedAt": "2022-08-29T00:56:3737+01:00",
        "id": "630c00b3c0f18977d763855e",
        "requestedBy": {
            "firstName": "Famous",
            "lastName": "Ehichioya",
            "middleName": "Prior",
            "id": "61d880f2e8e15aaf24558f9b"
        }
    },
    "links": []
}

FOUND_RESPONSE = {
    "success": True,
    "statusCode": 200,
    "message": "success",
    "data": {
        "parentId": None,
        "searchBusinessName": None,
        "name": "John Doe Inc",
        "formerName": None,
        "registrationNumber": "**********",
        "registryNumber": "00000000",
        "vatNumber": "00000000-0001",
        "registrationDate": "2017-06-09T00:00:00.000+00:00",
        "registrationSubmissionDate": "2017-05-31T11:22:07.713+00:00",
        "dateDisolved": None,
        "tin": "00000000-0001",
        "jtbTin": None,
        "taxOffice": None,
        "email": "<EMAIL>",
        "phone": "****** 000 0000",
        "websiteEmail": None,
        "typeOfEntity": "PRIVATE COMPANY LIMITED BY SHARES",
        "activity": "Information service activities",
        "address": "1ST FLOOR,SUM HOUSE,Fake Street",
        "state": "LAGOS",
        "lga": "Lagos Mainland",
        "city": "YABA",
        "branchAddress": "1ST FLOOR,SUM HOUSE,Fake Street, YABA, LAGOS STATE ",
        "headOfficeAddress": None,
        "objectives": "",
        "status": "found",
        "companyStatus": "ACTIVE",
        "isConsent": True,
        "lastUpdatedAt": "2022-03-30T14:19:35.571+00:00",
        "shareCapitalInWords": "TEN MILLION NAIRA",
        "paidShareCapital": "10000000",
        "subscribedShareCapital": "10000000",
        "sharesValue": "0.01 NGN",
        "activityDescription": "Information service activities",
        "sharesIssued": "10000000",
        "parentCountry": None,
        "companyContactPersons": [
            {
                "contacts": {
                    "email": [
                        "<EMAIL>"
                    ],
                    "phone": [
                        "08034926354"
                    ]
                },
                "name": "Eboikpomwen  Eki "
            }
        ],
        "country": "Nigeria",
        "countryCode": "NG",
        "keyPersonnel": [
            {
                "name": "PETER DOE",
                "designation": "WITNESS",
                "isCorporate": False,
                "appointedOn": None,
                "resignedOn": None,
                "sharesType": "",
                "sharesValue": None,
                "sharesCount": None,
                "occupation": "LAWYER",
                "nationality": None,
                "birthYear": None,
                "birthMonth": None,
                "birthDate": None,
                "dateOfBirth": None,
                "gender": "",
                "address": "8C, NONE STREET, ZONE 4",
                "countryOfResidence": "NIGERIA",
                "number": "",
                "isForeign": "False",
                "documentType": "",
                "documentIssuedOn": None,
                "documentIssuedBy": None,
                "documentNumber": "",
                "email": "",
                "phone": "",
                "status": "ACTIVE",
                "companies": []
            },
            {
                "name": "SUSAN DOE",
                "designation": "SECRETARY_COMPANY",
                "isCorporate": False,
                "appointedOn": None,
                "resignedOn": None,
                "sharesType": "",
                "sharesValue": None,
                "sharesCount": None,
                "occupation": "SECRETARY",
                "nationality": "NIGERIA",
                "birthYear": None,
                "birthMonth": None,
                "birthDate": None,
                "dateOfBirth": None,
                "gender": "FEMALE",
                "address": "11, NONE CRESCENT",
                "countryOfResidence": "NIGERIA",
                "number": "",
                "isForeign": "False",
                "documentType": "",
                "documentIssuedOn": None,
                "documentIssuedBy": None,
                "documentNumber": "",
                "email": "",
                "phone": "",
                "status": "ACTIVE",
                "companies": []
            },
            {
                "name": "Adepoju Ejuaye",
                "designation": "DIRECTOR",
                "isCorporate": None,
                "appointedOn": "2022-04-19T23:00:00.000+00:00",
                "resignedOn": None,
                "sharesType": None,
                "sharesValue": None,
                "sharesCount": None,
                "occupation": None,
                "nationality": "NIGERIA",
                "birthYear": None,
                "birthMonth": None,
                "birthDate": None,
                "dateOfBirth": "2017-06-09T00:00:00.000+00:00",
                "gender": "FEMALE",
                "address": "Alara Street, Off Commercial Avenue, Off Herbert Macaulay Road",
                "countryOfResidence": None,
                "number": "1234567",
                "isForeign": None,
                "documentType": "National ID Card",
                "documentIssuedOn": None,
                "documentIssuedBy": None,
                "documentNumber": "1234567",
                "email": "",
                "phone": "",
                "status": "ACTIVE",
                "companies": [

                ]
            },
            {
                "name": "MICHAEL DOE",
                "designation": "DIRECTOR",
                "isCorporate": None,
                "appointedOn": None,
                "resignedOn": None,
                "sharesType": None,
                "sharesValue": None,
                "sharesCount": None,
                "occupation": "BUSINESS",
                "nationality": "NIGERIA",
                "birthYear": None,
                "birthMonth": None,
                "birthDate": None,
                "dateOfBirth": None,
                "gender": "MALE",
                "address": "1, NONE STREET, ",
                "countryOfResidence": "NIGERIA",
                "number": None,
                "isForeign": "False",
                "documentType": None,
                "documentIssuedOn": None,
                "documentIssuedBy": None,
                "documentNumber": None,
                "email": None,
                "phone": None,
                "status": "ACTIVE",
                "companies": []
            },
            {
                "name": "JANE DOE  INC",
                "designation": "SHAREHOLDER",
                "isCorporate": True,
                "appointedOn": "2021-03-17T14:07:43.000+00:00",
                "resignedOn": None,
                "sharesType": "ORDINARY",
                "sharesValue": "9990000",
                "sharesCount": "9990000",
                "occupation": None,
                "nationality": None,
                "birthYear": None,
                "birthMonth": None,
                "birthDate": None,
                "dateOfBirth": None,
                "gender": None,
                "address": "01 COASTAL HIGHWAY,LEWES DE 000000  USA,COUNTY OF SUSSEX",
                "countryOfResidence": "UNITED STATES",
                "number": None,
                "isForeign": "True",
                "documentType": None,
                "documentIssuedOn": None,
                "documentIssuedBy": None,
                "documentNumber": None,
                "email": None,
                "phone": None,
                "status": "ACTIVE",
                "companies": []
            },
            {
                "name": "JOHN DOE",
                "designation": "SHAREHOLDER",
                "isCorporate": None,
                "appointedOn": None,
                "resignedOn": None,
                "sharesType": "ORDINARY",
                "sharesValue": "10000",
                "sharesCount": "10000",
                "occupation": None,
                "nationality": None,
                "birthYear": None,
                "birthMonth": None,
                "birthDate": None,
                "dateOfBirth": None,
                "gender": "MALE",
                "address": "1 NONE STREET,LEKKI",
                "countryOfResidence": "NIGERIA",
                "number": None,
                "isForeign": "False",
                "documentType": None,
                "documentIssuedOn": None,
                "documentIssuedBy": None,
                "documentNumber": None,
                "email": None,
                "phone": None,
                "status": "ACTIVE",
                "companies": []
            },
            {
                "name": "JOHN DOE INC",
                "designation": "PERSONS WITH SIGNIFICANT CONTROL",
                "isCorporate": True,
                "appointedOn": "2022-03-30T14:42:16.554+00:00",
                "resignedOn": None,
                "sharesType": None,
                "sharesValue": None,
                "sharesCount": None,
                "occupation": None,
                "nationality": None,
                "birthYear": None,
                "birthMonth": None,
                "birthDate": None,
                "dateOfBirth": None,
                "gender": None,
                "address": "01 COASTAL HIGHWAY,LEWES DE 000000 ",
                "countryOfResidence": "UNITED STATES",
                "number": None,
                "isForeign": "True",
                "documentType": None,
                "documentIssuedOn": None,
                "documentIssuedBy": None,
                "documentNumber": None,
                "email": None,
                "phone": None,
                "status": "ACTIVE",
                "companies": []
            }
        ],
        "activities": [],
        "legalEntityIdentifierRegister": [
            {
                "leiCode": "0000000000000000",
                "legalName": "John Doe PLC",
                "legalAddressFirstAddressLine": "1 FAKE CRICK AVENUE",
                "legalAddressAdditionalAddressLine1": "CAMBRIDGE BIOMEDICAL CAMPUS",
                "legalAddressAdditionalAddressLine2": "",
                "legalAddressAdditionalAddressLine3": "",
                "legalAddressCity": "CAMBRIDGE",
                "legalAddressRegion": "GB-CAM",
                "legalAddressCountry": "GB",
                "legalAddressPostalCode": "CB2 0AA",
                "registrationAuthorityID": "R0000000",
                "registrationAuthorityEntityID": "00000000",
                "legalJurisdiction": "GB",
                "entityCategory": "GENERAL",
                "registrationInitialRegistrationDate": "2012-06-06",
                "registrationLastUpdateDate": "2021-08-21",
                "registrationNextRenewalDate": "2022-08-11",
                "registrationManagingLOU": "0000000000000000",
                "registrationValidationSources": "FULLY_CORROBORATED",
                "validationAuthorityID": "RA000000",
                "validationAuthorityEntityID": "02700000",
                "entityStatus": "ACTIVE",
                "status": "ISSUED"
            }
        ],
        "centralIndexKeyRegister": [
            {
                "cik": "000000",
                "entityType": "operating",
                "sic": "3711",
                "sicDescription": "Motor Vehicles & Passenger Car Bodies",
                "name": "John Doe, Inc.",
                "ein": "000000000000",
                "description": "",
                "website": "",
                "investorWebsite": "",
                "tickers": [
                    "JDL"
                ],
                "stateOfIncorporation": "DE",
                "businessAddressStreet1": "3500 FAKE STREET RD",
                "mailingAddressCity": "PALO ALTO",
                "businessAddressStreet2": None,
                "businessAddressCity": "PALO ALTO",
                "businessAddressStateOrCountry": "CA",
                "businessAddressZipCode": "123454",
                "businessAddressStateOrCountryDescription": "CA",
                "phone": "************",
                "_id": "630ac466a4da96dee1fcae97",
                "formerNames": [
                    {
                        "from": "2005-02-17T00:00:00.000Z",
                        "to": "2017-01-27T00:00:00.000Z",
                        "name": "JDL MOTORS INC",
                        "_id": "630ac466a4da96dee1fcae98"
                    }
                ]
            }
        ],
        "filings": [
            {
                "date": "2020-10-31",
                "name": "Dissolution",
                "type": "Dissolution",
                "status": None
            },
            {
                "date": "2017-09-27",
                "name": "Incorporation",
                "type": "Incorporation",
                "status": None
            }
        ],
        "affiliates": [
            {
                "name": "John Doe 1",
                "brandName": None,
                "shortName": None,
                "companyNumber": "0000.000.000",
                "countryCode": "NG"
            },
            {
                "name": "John Doe 2",
                "brandName": None,
                "shortName": None,
                "companyNumber": "0000.000.0000",
                "countryCode": "NG"
            }
        ],
        "businessId": "62c2a4868d319373a6280152",
        "type": "advance_company_check",
        "requestedAt": "2023-05-12T08:31:33.145Z",
        "requestedById": "62c2a4868d3193700828014e",
        "searchTerm": "**********",
        "createdAt": "2022-11-03T14:16:54.235Z",
        "lastModifiedAt": "2022-11-03T14:16:54.235Z",
        "_createdAt": "2022-11-03T14:16:5454+00:00",
        "_lastModifiedAt": "2022-11-03T14:16:5454+00:00",
        "id": "645df96520aa6c7da72458a0",
        "requestedBy": {
            "firstName": "API",
            "lastName": "User",
            "middleName": "",
            "id": "62c2a4868d3193700828014e"
        }
    },
    "links": []
}


def verify_company(registration_no):
    url = "http://api.youverify.co/v2/api/verifications/global/company-advance-check"

    payload = json.dumps({
        "registrationNumber": f'{registration_no}',
        "countryCode": "NG",
        "isConsent": True
    })

    headers = {
        'Content-Type': 'application/json',
        'token': f'{settings.YOU_VERIFY_COMPANY_TOKEN}'
    }
    if settings.ENVIRONMENT == "prod":
        response = requests.post(url, headers=headers, data=payload)
        return response.json()
    else:
        # use response sample
        if registration_no == "**********":
            return FOUND_RESPONSE
        else:
            return NOT_FOUND



def drop_a_notification_on_paybox360_calendly_slack_channel(msg):
    url = f"{config('LIBERTY_PAY_MARKETING_BASE_URL')}/api/notify_paybox360_slack_channel/"
    # url = f"http://127.0.0.1:8001/api/notify_paybox360_slack_channel/"

    payload = {
        "message": msg
    }

    headers = {
        'Content-Type': 'application/json',
    }

    response = requests.request("POST", url, headers=headers, json=payload)