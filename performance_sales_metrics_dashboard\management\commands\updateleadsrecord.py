from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db.models import Subquery
import pytz

from performance_sales_metrics_dashboard.models import Lead


class Command(BaseCommand):
    help = "UPDATES LEADS RECORD."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        # Query for all distinct company email
        distinct_leads = Lead.objects.values("company_email").distinct()

        for lead in distinct_leads:
            # Get all records with the current company email
            records = Lead.objects.filter(
                company_email=lead["company_email"],
            ).order_by("-created_at")
            # Delete all but the latest record
            if records.count() > 1:
                duplicate_records = records[1:]
                Lead.objects.filter(
                    id__in=Subquery(duplicate_records.values("id"))
                ).delete()
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
        )
        self.stdout.write(self.style.SUCCESS("SUCCESSFULLY UPLOADED ITEM DETAILS."))
