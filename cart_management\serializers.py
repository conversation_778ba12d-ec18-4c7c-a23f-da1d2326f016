from rest_framework import serializers

from cart_management import models
from requisition.models import Company
from stock_inventory.models import Branch, Product
from stock_inventory.serializers import ProductSerializer


# Create your serializer(s) here.
class CreateOrderProductSerializer(serializers.ModelSerializer):
    id = serializers.PrimaryKeyRelatedField(queryset=Product.objects.filter(unlist=True))
    subTotal = serializers.DecimalField(max_digits=13, decimal_places=2)

    class Meta:
        model = models.OrderProduct
        fields = [
            "id",
            "price",
            "product_description",
            "product_img",
            "product_name",
            "quantity",
            "subTotal",
        ]


class BuyerSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Buyer
        fields = "__all__"
        depth = 1


class CartItemSerializer(serializers.ModelSerializer):
    product = ProductSerializer()

    class Meta:
        model = models.CartItem
        fields = ["id", "product", "quantity"]


class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    buyer = BuyerSerializer()

    class Meta:
        model = models.Cart
        fields = [
            "id",
            "buyer",
            "session_id",
            "items",
        ]


class OrderProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.OrderProduct
        fields = [
            "product_name",
            "product_description",
            "product_img",
            "quantity",
            "price",
            "sub_total",
            "payment_option",
        ]


class OrderSerializer(serializers.ModelSerializer):
    order_products = OrderProductSerializer(
        many=True, read_only=True, source="orderproduct_set"
    )
    trail = serializers.JSONField()
    contact_name = serializers.SerializerMethodField()
    contact_phone_number = serializers.SerializerMethodField()
    mode_of_transaction = serializers.SerializerMethodField()

    class Meta:
        model = models.Order
        fields = [
            "id",
            "amount_paid",
            "status",
            "payment_status",
            "channels",
            "buyer",
            "order_id",
            "order_date",
            "order_time",
            "current_stage",
            "order_products",
            "trail",
            "shipping",
            "discount",
            "tax",
            "contact_name",
            "contact_phone_number",
            "additional_information",
            "ship_to_different_address",
            "total_price",
            "vendor_whatsapp_url",
            "mode_of_transaction",
            "is_delivered",
            "is_cancelled",
            "payment_requested",
            "payment_request_date",
        ]
        depth = 1

    def get_contact_name(self, obj):
        if obj.contact:
            return obj.contact.get("name")
        return None

    def get_contact_phone_number(self, obj):
        if obj.contact:
            return obj.contact.get("phone_number")
        return None
    
    def get_mode_of_transaction(self, obj):
        # Retrieve the most recent payment record
        payment_record = models.PaymentRecord.objects.filter(order=obj).last()
        if payment_record:
            return payment_record.mode_of_transaction
        return None


class ProductOrderSerializer(serializers.ModelSerializer):
    order_products = OrderProductSerializer(
        many=True, read_only=True, source="orderproduct_set"
    )
    trail = serializers.JSONField()
    contact_name = serializers.SerializerMethodField()
    contact_phone_number = serializers.SerializerMethodField()
    buyer = BuyerSerializer()

    class Meta:
        model = models.Order
        fields = [
            "id",
            "amount_paid",
            "status",
            "payment_status",
            "buyer",
            "order_date",
            "order_time",
            "current_stage",
            "order_products",
            "trail",
            "shipping",
            "discount",
            "tax",
            "contact_name",
            "contact_phone_number",
            "additional_information",
            "ship_to_different_address",
            "total_price",
            "vendor_whatsapp_url",
            "is_delivered",
            "is_cancelled",
        ]
        depth = 1

    def get_contact_name(self, obj):
        return obj.contact.get("name")

    def get_contact_phone_number(self, obj):
        return obj.contact.get("phone_number")


class OrderStageSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.OrderStage
        fields = [
            "id",
            "name",
            "email_subject",
            "email_body",
            "email_text",
            "email_notification_enabled",
            "order_count",
            "position",
            "pipeline",
        ]


class OrderPipelineSerializer(serializers.ModelSerializer):
    stages = OrderStageSerializer(many=True, read_only=True)

    class Meta:
        model = models.OrderPipeline
        fields = [
            "id",
            "name",
            "is_default",
            "company",
            "branch",
            "stages",
        ]


class CustomerDashboardSerializer(serializers.Serializer):
    name = serializers.CharField()
    email = serializers.EmailField()
    phone_number = serializers.CharField()
    total_orders = serializers.IntegerField()
    last_order = serializers.DateField()


class OrderHistorySerializer(serializers.Serializer):
    item = serializers.CharField()
    email = serializers.EmailField()
    current_stage = serializers.CharField()
    payment_status = serializers.CharField()
    last_updated = serializers.DateField()


class OrderListSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.OrderProduct
        fields = [
            "product_name",
            "payment_option",
            "product_description",
            "price",
            "quantity",
            "sub_total",
            "product_img",
            "orders",
            # "is_active",
            "created_at",
        ]

    def to_representation(self, instance):
        serializer_data = super().to_representation(instance)
        branch_name = instance.orders.current_stage.pipeline.branch.name
        serializer_data["branch_name"] = branch_name
        serializer_data["buyer"] = (
            f"{instance.orders.buyer.first_name} {instance.orders.buyer.last_name}"
        )
        return serializer_data


class PaymentRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.PaymentRecord
        fields = "__all__"


class CreateOrderSerializer(serializers.Serializer):
    company_id = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch_id = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    cart = CreateOrderProductSerializer(many=True)
    shipping = serializers.DecimalField(max_digits=13, decimal_places=2)
    discount = serializers.DecimalField(max_digits=13, decimal_places=2)
    tax = serializers.DecimalField(max_digits=13, decimal_places=2)
    total_price = serializers.DecimalField(max_digits=13, decimal_places=2)
    buyer = BuyerSerializer()


class IncompleteOrderRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.IncompleteOrderRecord
        fields = "__all__"
