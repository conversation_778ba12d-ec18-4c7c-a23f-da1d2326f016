from django.core.management.base import BaseCommand

from sales_app.models import OfflineVirtualAccount
from sales_app.utils import generate_unique_code


class Command(BaseCommand):
    help = "UPDATE OFFLINE VIRTUAL ACCOUNTS."

    def handle(self, *args, **kwargs):
        virtual_accounts = OfflineVirtualAccount.objects.all()
        for virtual_account in virtual_accounts:
            code = generate_unique_code()
            checker = OfflineVirtualAccount.objects.filter(
                confirmation_code=code
            )
            if checker.exists():
                pass
            virtual_account.confirmation_code = code
            virtual_account.save()
        self.stdout.write(
            self.style.SUCCESS("UPDATE SUCCESSFUL.")
        )
