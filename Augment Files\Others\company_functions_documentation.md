# Company Model Functions Documentation

## Overview

The Company model is a core entity in the Paybox360 system, representing businesses that use the platform. It's defined in the `requisition` app but is referenced across multiple apps for various functionalities.

## Model Fields by Category

### Basic Information

- `company_name` (CharField): Name of the company
- `industry` (CharField): Industry sector of the company
- `size` (PositiveIntegerField): Size of the company (number of employees)
- `cac_num` (CharField): Company registration number
- `incorporation_date` (DateField): Date of company incorporation

### Ownership and Relationships

- `user` (Foreign<PERSON>ey to User): Owner of the company
- `teams` (ManyToManyField to Team): Teams within the company

### Configuration

- `company_wallet_type` (CharField): Type of wallet ("MAIN" or "CORPORATE")
- `channel` (CharField): Channel through which the company was created (e.g., "WEB")
- `corporate_id` (CharField): ID for corporate accounts
- `supplier_invite_id` (CharField): Unique ID for supplier invitations

### Status Flags

- `is_active` (BooleanField): Whether the company is active
- `is_deleted` (BooleanField): Whether the company is deleted
- `test_account` (BooleanField): Whether this is a test account
- `instant_wage` (BooleanField): Whether instant wage is enabled
- `auto_approve_suppliers` (BooleanField): Whether suppliers are auto-approved
- `corporate_account_created` (BooleanField): Whether a corporate account has been created

## Core Functions

### Instance Methods

#### `save()`

- **Description**: Overrides the default save method to ensure a supplier_invite_id is generated if not present
- **Usage**: Called automatically when saving a Company instance
- **References**: Used internally by Django ORM
- **Code**:
  ```python
  def save(self, *args, **kwargs):
      if not self.supplier_invite_id:
          self.supplier_invite_id = str(uuid.uuid4()).replace("-", "").upper()[:9]
      super(Company, self).save(*args, **kwargs)
  ```

#### `__str__()`

- **Description**: Returns a string representation of the company
- **Usage**: Used for display in admin interface and debugging
- **References**: Used internally by Django
- **Code**:
  ```python
  def __str__(self):
      return str(self.company_name)
  ```

### Properties

#### `overall_running_budget`

- **Description**: Calculates the total budget amount for the company
- **Usage**: Used in financial reporting and dashboard displays
- **References**: Used in `ListOfCompanySerializer` for API responses
- **Code**:
  ```python
  @property
  def overall_running_budget(self):
      total_budget_amount = Budget.objects.filter(
          company=self, is_active=True
      ).aggregate(sum_amount=Sum("budget_amount"))["sum_amount"]
      return total_budget_amount
  ```

#### `total_current_month_expenses`

- **Description**: Calculates the total expenses for the current month
- **Usage**: Used in financial reporting and dashboard displays
- **References**: Used in `ListOfCompanySerializer` for API responses
- **Code**:
  ```python
  @property
  def total_current_month_expenses(self):
      today = date.today()
      expenses = Expense.objects.filter(
          company=self, status="SUCCESSFUL", created_at__month=today.month
      )
      total_current_month_expenses = (
          expenses.aggregate(sum_amount=Sum("expense_amount"))["sum_amount"] or 0
      )
      return total_current_month_expenses
  ```

#### `current_month_expenses`

- **Description**: Returns serialized data for current month expenses
- **Usage**: Used in API responses for detailed expense information
- **References**: Used in `ListOfCompanySerializer` for API responses
- **Code**:
  ```python
  @property
  def current_month_expenses(self):
      from requisition.serializers import ExpenseDashboardSerializerList
      today = date.today()
      expenses = Expense.objects.filter(
          company=self, status="SUCCESSFUL", created_at__month=today.month
      )
      expense_serializer = ExpenseDashboardSerializerList(expenses, many=True)
      return expense_serializer.data
  ```

### Class Methods

#### `create_company()`

- **Description**: Creates a new company instance with the provided data
- **Usage**: Used during company registration and onboarding
- **References**:
  - Called in `CreateListAndDelCompany.post()` view
  - Called in `CompanyOnboarding.create_company()` in subscription_and_invoicing app
  - Called in `JotFormDataSync.process_datasync()` for form-based onboarding
- **Code**:

  ```python
  @classmethod
  def create_company(cls, user, validated_data, transaction_pin):
      wallet_type = validated_data.get("wallet_type")

      if transaction_pin:
          pin = encrypt_string(pass_code=transaction_pin)
          user.agency_banking_transaction_pin = pin
          user.save()

      cac_num = validated_data.get("cac_num")
      incorporation_date = validated_data.get("incorporation_date")
      company = cls.objects.create(
          user=user,
          company_wallet_type=wallet_type,
          company_name=validated_data.get("company_name").title(),
          industry=validated_data.get("industry"),
          cac_num=cac_num,
          size=validated_data.get("size"),
          channel=validated_data.get("wallet_type"),
          is_active=True,
          incorporation_date=incorporation_date
      )

      if ENVIRONMENT == "dev":  # update cac number on dev to make it reusable
          company.cac_num = f"RC-{company.id}NUM"
          company.save()

      return company
  ```

#### `retrieve_company()`

- **Description**: Retrieves a company by ID and optionally filters by user
- **Usage**: Used to fetch company details in various views
- **References**: Used in multiple views to retrieve company information
- **Code**:
  ```python
  @classmethod
  def retrieve_company(cls, id, user=None):
      try:
          if user is not None:
              company = cls.objects.get(id=id, user=user, is_active=True)
          else:
              company = cls.objects.get(id=id, is_active=True)
      except cls.DoesNotExist:
          company = None
      return company
  ```

## API Endpoints

### Company Creation and Management

- `POST /requisition/cdg-company/`: Create a new company
- `GET /requisition/cdg-company/`: List companies for the current user
- `GET /requisition/get-companies/`: Get filtered list of companies
- `GET /requisition/get-all-companies/`: Get all companies (with filtering)
- `POST /requisition/set-default-company/`: Set a company as default for the user

### Company Verification

- `POST /requisition/company-verification/`: Verify company information
- `POST /requisition/verify-reg-number/`: Verify company registration number

### Industry Management

- `GET /requisition/industries/`: Get list of industries
- `POST /requisition/industries/create`: Create a new industry

## Cross-App References

### Apps that Reference the Company Model

1. **Core App**: User authentication and permissions
2. **Account App**: Financial accounts and transactions
3. **Payroll App**: Employee management and payroll processing
4. **Stock Inventory App**: Inventory management
5. **Clock App**: Time tracking and attendance
6. **Leave Management App**: Leave requests and approvals
7. **Finance System App**: Financial management
8. **Performance Sales Metrics Dashboard App**: Sales tracking
9. **Subscription and Invoicing App**: Subscription management
10. **Sales App**: Sales management
11. **Invoicing App**: Invoice generation
12. **Instant Web App**: Web store management

## Third-Party Integrations

### YouVerify API

- **Purpose**: Verify company registration information
- **Function**: `verify_company()` in `requisition/helpers/request_cls.py`
- **Usage**: Validates company registration numbers against official records
- **Code**:
  ```python
  def verify_company(registration_no):
      url = "http://api.youverify.co/v2/api/verifications/global/company-advance-check"
      payload = json.dumps({
          "registrationNumber": f'{registration_no}',
          "countryCode": "NG",
          "isConsent": True
      })
      headers = {
          'Content-Type': 'application/json',
          'token': f'{settings.YOU_VERIFY_COMPANY_TOKEN}'
      }
      if settings.ENVIRONMENT == "prod":
          response = requests.post(url, headers=headers, data=payload)
          return response.json()
      else:
          # use response sample
          if registration_no == "RC00000000":
              return FOUND_RESPONSE
          else:
              return NOT_FOUND
  ```

### Liberty Pay API

- **Purpose**: Financial account creation and management for companies
- **Function**: `register_sub_company()` in `account/helpers/core_banking.py`
- **Usage**: Creates financial accounts for companies in the Liberty Pay system
- **Code**:
  ```python
  @classmethod
  def register_sub_company(cls, company_name, company_email, company_phone, unique_id, company_address=None):
      absolute_url = f"{BASE_URL}/api/v1/companies/sub_companies/"
      bearer_token = cls.login()
      if bearer_token is None:
          return "NO BEARER TOKEN"
      cls.headers["Authorization"] = f"Bearer {bearer_token}"
      payload = json.dumps({
          "company_name": company_name,
          "company_email": company_email,
          "company_phone": company_phone,
          "unique_id": unique_id,
          "company_address": company_address or ""
      })
      response = requests.post(url=absolute_url, headers=cls.headers, data=payload)
      return response.json()
  ```

### OpenAI API (ChatGPT)

- **Purpose**: Document validation for company verification
- **Function**: `validate_utility_bill_with_chagpt()` in `payroll_app/apis/employee_verification.py`
- **Usage**: Validates utility bills and other documents during company verification
- **Code**:

  ```python
  def validate_utility_bill_with_chagpt(image):
      try:
          base64_image = base_64_image_encoder(image)
      except Exception as e:
          return {"status": False, "message": str(e), "image_data": ""}

      api_key = str(settings.CHAT_GPT_API)
      headers = {
          "Content-Type": "application/json",
          "Authorization": f"Bearer {api_key}"
      }
      # API call implementation follows
  ```

### Spacy NLP

- **Purpose**: Natural language processing for document analysis
- **Import**: `import spacy` and `nlp = spacy.load("en_core_web_sm")` in `requisition/serializers.py`
- **Usage**: Used for text extraction and analysis in document processing

### Pytesseract and PDF2Image

- **Purpose**: OCR and document processing
- **Import**:
  ```python
  from pytesseract import pytesseract
  from PIL import Image
  from pdf2image import convert_from_path
  ```
- **Usage**: Used for extracting text from images and PDFs during document verification

## Serializers

### CompanySerializer

- **Description**: Basic serializer for Company model with minimal fields
- **Usage**: Used for simple company representation
- **Fields**: `id`, `company_name`

### ListOfCompanySerializer

- **Description**: Detailed serializer for Company model with related data
- **Usage**: Used for API responses that need comprehensive company information
- **Fields**:
  - `id`, `company_name`, `created_at`
  - `overall_running_budget`, `total_current_month_expenses`, `current_month_expenses`
  - `teams` (nested ListTeamSerializer)
  - `account` (nested AccountSerializer)
- **Custom Representation**: Adds additional calculated fields:
  - `no_of_teams`: Count of teams in the company
  - `total_req`: Count of requisitions for the company
  - `approved_req`: Count of approved requisitions
  - `pending_req`: Count of pending requisitions
  - `no_of_members`: Total count of team members across all teams
  - `budget`: List of budget information
  - `default`: Whether this is the user's default company
  - `account_details`: Account information

### CreateCompanySerializer

- **Description**: Serializer for creating new companies
- **Usage**: Used in company creation API endpoints
- **Fields**:
  - `company_name`, `industry`, `size`, `cac_num`
  - `wallet_type`, `channel`
  - `transaction_pin` (for setting up financial transactions)
  - Additional fields for corporate verification: `id_number`, `paid_shared_capital`, `type_of_entity`, `director_date_of_birth`

## View Classes

### CreateListAndDelCompany

- **Description**: API view for creating, listing, and deleting companies
- **Endpoints**:
  - `GET /requisition/cdg-company/`: List companies for the current user
  - `POST /requisition/cdg-company/`: Create a new company
- **Key Functions**:
  - `get()`: Lists companies for the current user
  - `post()`: Creates a new company using `Company.create_company()`

### ListFilterCompanies

- **Description**: API view for listing companies with filtering
- **Endpoint**: `GET /requisition/get-companies/`
- **Filters**: `id`, `company_name`
- **Search Fields**: `company_name`
- **Key Function**: `get_queryset()`: Retrieves companies where the user is either the owner or a team member

### GetAllFilterCompanies

- **Description**: API view for listing all companies with more comprehensive filtering
- **Endpoint**: `GET /requisition/get-all-companies/`
- **Key Function**: `get_queryset()`: Retrieves companies where the user is the owner, a team member, or an employee

### VerifyCorporateCompanyInfo

- **Description**: API view for verifying company information
- **Endpoint**: `POST /requisition/company-verification/`
- **Purpose**: Validates company registration information against official records

### VerifyCorporateCompanyRegistrationNumber

- **Description**: API view for verifying company registration numbers
- **Endpoint**: `POST /requisition/verify-reg-number/`
- **Purpose**: Checks if a company registration number is valid and not already in use

### SetDefaultCompanyAPIView

- **Description**: API view for setting a company as the default for a user
- **Endpoint**: `POST /requisition/set-default-company/`
- **Purpose**: Updates the user's default_company field

## Related Models

### CompanyVerificationInfo

- **Description**: Stores verification information for companies
- **Key Fields**:
  - `user`: The user who initiated verification
  - `registration_no`: Company registration number
  - `tax_identification_number`: Tax ID
  - `type_of_entity`: Type of business entity
  - `verification_response`: JSON response from verification service
- **Property**: `response_data`: Truncated version of verification response for display

### CompanyVerificationMetaData

- **Description**: Stores metadata about company verification
- **Key Fields**:
  - `user`: The user who initiated verification
  - `registration_no`: Company registration number
  - `verification_response`: JSON response from verification service
- **Property**: `response_data`: Truncated version of verification response for display

### CompanyIndustry

- **Description**: Represents industry categories for companies
- **Key Fields**:
  - `industry_name`: Name of the industry
  - `industry_description`: Description of the industry

## Signal Handlers

### Company Creation Signal

- **Description**: Signal sent when a company is created
- **Purpose**: Creates a default branch in the stock_inventory app
- **Note**: Mentioned in the Company model docstring:
  ```
  NOTE [ACTIONS/SIGNALS]:
  - default branch creation: on company creation, a signal is sent to the
  'stock_inventory' App and this signal is used to create a default super branch.
  ```

## Integration with Other Apps

### Payroll App Integration

- **Function**: `CompanyDetailsData.create_default_components()`
- **Purpose**: Creates default salary components for a new company
- **Usage**: Called after company creation to set up payroll components

### Account App Integration

- **Function**: `register_sub_company()` in `account/helpers/core_banking.py`
- **Purpose**: Creates financial accounts for companies
- **Usage**: Called after company creation to set up financial accounts

### Subscription and Invoicing App Integration

- **Function**: `CompanyOnboarding.create_company()` in `subscription_and_invoicing/models.py`
- **Purpose**: Creates a company and updates related leads
- **Usage**: Called during the onboarding process

### Performance Sales Metrics Dashboard App Integration

- **Code**:
  ```python
  leads = perf.Lead.objects.filter(company_email=company_email)
  for lead in leads:
      if lead.onboard_status == "AWAITING_ONBOARDING":
          lead.onboard_status = "ONBOARDING"
          lead.save()
  ```
- **Purpose**: Updates lead status after company creation
- **Usage**: Called in `CompanyOnboarding.create_company()`

## Utility Functions

### Company Verification

#### `verify_company()`

- **Location**: `requisition/helpers/request_cls.py`
- **Description**: Verifies company registration information with YouVerify API
- **Parameters**: `registration_no` - Company registration number
- **Returns**: JSON response from verification service
- **Usage**: Called during company verification process
- **Cross-References**: Used in `VerifyCorporateCompanyRegistrationNumber` view

#### `encrypt_string()`

- **Description**: Encrypts sensitive data like transaction PINs
- **Parameters**: `pass_code` - String to encrypt
- **Returns**: Encrypted string
- **Usage**: Used in `create_company()` to encrypt transaction PINs
- **Cross-References**: Called in `Company.create_company()`

### User Identification

#### `UserIdentifier.get_company_ownership()`

- **Location**: `helpers/reusable_functions.py`
- **Description**: Gets companies owned by a user
- **Parameters**: `user` - User instance
- **Returns**: QuerySet of Company instances or None
- **Usage**: Used to determine company ownership for permissions
- **Cross-References**: Used in permission checks across the application

### Company Data Export

#### `CompanyResource`

- **Location**: `requisition/resources.py`
- **Description**: Resource class for exporting Company data
- **Usage**: Used with Django Import-Export for admin data export
- **Cross-References**: Registered in admin site for data export functionality

## Cross-Reference Map

This section maps how the Company model is referenced across different parts of the application.

### User Model References

- `default_company` field: References the user's default company
- Usage: Determines which company context is active for the user
- Cross-References: Used in permission checks and API responses

### Team Model References

- `company` field: References the company the team belongs to
- Usage: Establishes company ownership of teams
- Cross-References: Used in team creation and management

### Branch Model References

- `company` field: References the company the branch belongs to
- Usage: Establishes company ownership of branches
- Cross-References: Used in branch creation and management

### AccountSystem Model References

- `company` field: References the company the account belongs to
- Usage: Establishes company ownership of financial accounts
- Cross-References: Used in financial operations

### CompanyEmployeeList Model References

- `company` field: References the company the employee belongs to
- Usage: Establishes company ownership of employees
- Cross-References: Used in employee management and payroll

### Requisition Model References

- `company` field: References the company the requisition belongs to
- Usage: Establishes company ownership of requisitions
- Cross-References: Used in requisition approval workflows

### Budget Model References

- `company` field: References the company the budget belongs to
- Usage: Establishes company ownership of budgets
- Cross-References: Used in budget management and expense tracking

### CompanySubscription Model References

- `company` field: References the company the subscription belongs to
- Usage: Establishes company ownership of subscriptions
- Cross-References: Used in subscription management and access control

## Function Call Flow

This section illustrates the typical flow of function calls involving the Company model.

### Company Creation Flow

1. User submits company creation form
2. `CreateListAndDelCompany.post()` validates the request
3. `Company.create_company()` creates the company instance
4. Signal handlers create related resources:
   - Default branch creation in stock_inventory app
   - Default salary components in payroll app
   - Financial account creation in account app
5. Response returned to user with company details

### Company Verification Flow

1. User submits company verification request
2. `VerifyCorporateCompanyRegistrationNumber.post()` validates the request
3. `verify_company()` calls YouVerify API to verify registration number
4. `CompanyVerificationInfo` and `CompanyVerificationMetaData` instances created
5. Response returned to user with verification status

### Company Listing Flow

1. User requests company list
2. `ListFilterCompanies.get_queryset()` or `GetAllFilterCompanies.get_queryset()` retrieves companies
3. `ListOfCompanySerializer` serializes company data with related information
4. Response returned to user with company list

## Common Use Cases

### 1. Company Registration

- **Entry Point**: `POST /requisition/cdg-company/`
- **Key Functions**: `Company.create_company()`
- **Result**: New company created with default resources

### 2. Company Verification

- **Entry Point**: `POST /requisition/verify-reg-number/`
- **Key Functions**: `verify_company()`
- **Result**: Company registration information verified

### 3. Company Listing

- **Entry Point**: `GET /requisition/get-companies/`
- **Key Functions**: `ListFilterCompanies.get_queryset()`
- **Result**: List of companies for the current user

### 4. Setting Default Company

- **Entry Point**: `POST /requisition/set-default-company/`
- **Key Functions**: Updates User.default_company
- **Result**: User's default company updated
