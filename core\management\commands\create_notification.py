import uuid
from django.core.management import BaseCommand, CommandError
from core.helpers.enums import NotificationTypes
from core.models import Notification
from requisition.models import Company



class Command(BaseCommand):

    help = "Create a notification with validation for parameters"

    def add_arguments(self, parser):
        parser.add_argument('company_id', type=uuid.UUID, help="The ID of the company")
        parser.add_argument(
            'notification_type', 
            type=str, 
            choices=['HR_MANAGEMENT', 'SALES', 'SPEND_MANAGEMENT' 'STOCK'],
            help="The type of the notification")

    def handle(self, *args, **kwargs):
        company_id = kwargs['company_id']
        notification_type = kwargs['notification_type']
        # Validate that the company exists
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise CommandError(f"Company with ID {company_id} does not exist.")
        
        if notification_type not in NotificationTypes:
            raise CommandError(f"Invalid Notification Type")

        hr_module = [
            {"title": "Payment Reminder", "message": "Your payment for this month is due on January 30th."},
            {"title": "Payment Successful", "message": "Your payment of $200 has been successfully processed."},
            {"title": "Payment Overdue", "message": "Your payment is overdue. Please settle your balance immediately."},
            {"title": "Upcoming Payment", "message": "Your next payment is scheduled for February 15th."},
            {"title": "Payment Confirmation", "message": "We have received your payment. Thank you for your prompt action."}
        ]

        sales_module = [
            {"title": "Sales Target Achieved", "message": "Congratulations! The sales team has achieved this quarter's target."},
            {"title": "Sales Report Available", "message": "The monthly sales report is now available for review."},
            {"title": "Upcoming Sales Meeting", "message": "There will be a sales strategy meeting on Friday at 10 AM."},
            {"title": "Sales Performance Update", "message": "The latest sales performance metrics have been updated in the dashboard."},
            {"title": "Flash Sales Announcement", "message": "Don't miss out! Our flash sales event starts tomorrow at 8 AM."}
        ]

        spend_module = [
            {"title": "Spend Management Overview", "message": "The spend management report for January is now available."},
            {"title": "Spend Management Alert", "message": "Your department's spend management has exceeded the allocated budget."},
            {"title": "New Spend Management Policy", "message": "Please review the updated spend management policies for 2025."},
            {"title": "Spend Management Optimization", "message": "Discover ways to optimize your spend management with our new tools."},
            {"title": "Spend Management Training", "message": "A training session on effective spend management is scheduled for next week."}
        ]

        stock_module = [
            {"title": "Stock Replenishment Needed", "message": "The stock for Item A is running low. Please reorder soon."},
            {"title": "New Stock Arrival", "message": "Fresh stock has arrived for the latest product range."},
            {"title": "Stock Report Available", "message": "The weekly stock report is now available for your review."},
            {"title": "Stock Adjustment Completed", "message": "The stock adjustment for Warehouse B has been successfully completed."},
            {"title": "Stock Clearance Sale", "message": "We are offering discounts on surplus stock. Check it out now!"}
        ]


        # Create the notification
        if notification_type == "HR_MANAGEMENT":
            for i in hr_module:
                notification = Notification.objects.create(
                    company=company,
                    notification_type=notification_type,
                    title=i["title"],
                    message=i["message"]
                )
        if notification_type == "SALES":
            for i in sales_module:
                notification = Notification.objects.create(
                    company=company,
                    notification_type=notification_type,
                    title=i["title"],
                    message=i["message"]
                )
        if notification_type == "SPEND_MANAGEMENT":
            for i in spend_module:
                notification = Notification.objects.create(
                    company=company,
                    notification_type=notification_type,
                    title=i["title"],
                    message=i["message"]
                )
        if notification_type == "STOCK":
            for i in stock_module:
                notification = Notification.objects.create(
                    company=company,
                    notification_type=notification_type,
                    title=i["title"],
                    message=i["message"]
                )
        self.stdout.write(self.style.SUCCESS(f"Notification created successfully for company {company.company_name}."))

