from django.core.management import BaseCommand
from account.models import Wallet

# from account.enums import TransactionType
from django.conf import settings


class Command(BaseCommand):
    help = (
        "Create a STAMP DUTY REFUND transaction and initiate a FLOAT TO EXTERNAL REF."
    )

    def add_arguments(self, parser):
        parser.add_argument(
            "sender",
            type=str,
            help="The sender account number for the transaction",
        )
        parser.add_argument(
            "recipient",
            type=str,
            help="The beneficiary account number for the transaction",
        )

        parser.add_argument("amount", type=float, help="The amount for the transaction")

    def handle(self, *args, **kwargs):
        sender = kwargs["sender"]
        recipient = kwargs["recipient"]
        amount = kwargs["amount"]

        result = Wallet.transfer_to_wallet(
            sender_number_account_number=sender,
            recipient_account_number=recipient,
            amount=amount,
        )

        if not result["status"]:
            self.stdout.write(self.style.ERROR(result["message"]))
            return

        self.stdout.write(
            self.style.SUCCESS(f"Transaction created successfully, amount: {amount}, ")
        )
