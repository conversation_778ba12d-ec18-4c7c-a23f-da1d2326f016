from django.urls import path

from cart_management import views


# Create your url pattern(s) here.
whatsapp_integration_urls = [
    path("vendor-whatsapp-notification", views.WhatsAppOrderNotificationLinkView.as_view()),
]


order_urls = [
    path("filter-branch-order/", views.GetFilterOrderList.as_view()),
    path("transactions/", views.GetOrderProductTransactionDashboardAPIView.as_view()),
    path("get-customer-dashboard/", views.GetOrderCustomersDashboardAPIView.as_view()),
]

urlpatterns = [
    path("cart/", views.CartView.as_view(), name="cart-detail"),
    path("cart/add/<uuid:product_id>/", views.AddToCartView.as_view(), name="cart-add"),
    path(
        "cart/remove/<uuid:item_id>/",
        views.RemoveFromCartView.as_view(),
        name="cart-remove",
    ),
    path("cart/update/", views.UpdateCartbuyerView.as_view(), name="update-cart"),
    path("incomplete_order_record/", views.IncompleteOrderRecordAPIView.as_view(), name="incomplete-order-record"),
    path("fetch_incomplete_order_record/", views.FetchIncompleteOrderRecordAPIView.as_view(), name="fetch-incomplete-order-record"),
    path(
        "create_order_checkout/", views.CreateOrderView.as_view(), name="create-order"
    ),
    path('<uuid:order_id>/', views.GetOrderDetailAPIView.as_view(), name='order-detail'),
    path(
        "pipeline/<uuid:pipeline_id>/stages/",
        views.ManagePipelineStagesAPIView.as_view(),
        name="stages",
    ),
    path(
        "pipeline/<uuid:pipeline_id>/stages/<uuid:stage_id>/",
        views.ManagePipelineStagesAPIView.as_view(),
        name="stage_detail",
    ),
    path(
        "pipeline/details/<uuid:branch_id>/",
        views.PipelineDetailsView.as_view(),
        name="pipeline-details",
    ),
    path('export-pipeline/', views.ExportStageOrdersToExcelView.as_view(), name='export_stage_orders'),
    path("progression/", views.OrderProgression.as_view(), name="order-progression"),
    path(
        "pipelines/branch/<uuid:branch_id>/",
        views.PipelineForBranchView.as_view(),
        name="pipeline-for-branch",
    ),
    path(
        "branch/<uuid:branch_id>/customers/",
        views.CustomerDashboardView.as_view(),
        name="customer_dashboard",
    ),
    path(
        "branch/<uuid:branch_id>/history/",
        views.OrderHistoryView.as_view(),
        name="order_history",
    ),
    path(
        "accept_order/",
        views.AcceptOrderView.as_view(),
        name="accept-order",
    ),
    path(
        "cancel_order/",
        views.CancelOrderView.as_view(),
        name="cancel-order",
    ),
    path("payment-record/", views.PaymentRecordView.as_view(), name="payment-record"),
    path("verify_transfer", views.OrderVerifyTransferAPIView.as_view()),
    path('request-payment/', views.RequestPaymentAPIView.as_view(), name='request-payment'),
    *whatsapp_integration_urls,
    *order_urls,
]
