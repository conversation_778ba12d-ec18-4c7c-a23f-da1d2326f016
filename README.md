# [LIBERTYPAY - PAYBOX360](https://github.com/LibertytechX/liberty-requisition)

An API Service backing Client interfaces for the LibertyPay - Paybox360 Web Application.

## Technologies

* [Python 3.9](https://python.org) : Base programming language for development
* [PostgreSQL](https://www.postgresql.org/) : Application relational databases for development, staging, and production environments
* [Django Framework](https://www.djangoproject.com/) : Development framework used for the application
* [Django Rest Framework](https://www.django-rest-framework.org/) : Provides API development tools for easy API development
* [Github Actions](https://docs.github.com/en/free-pro-team@latest/actions) : Continuous Integration
* [Redis](https://redis.io/docs/getting-started/) : An in-memory data structure store, cache, and message broker
* [Celery](https://docs.celeryq.dev/en/stable/) : Task queues used as a mechanism to distribute work across threads or machines
* [Celery Beat](https://docs.celeryq.dev/en/stable/userguide/periodic-tasks.html) : A scheduler for periodic tasks

## Description

Your all in one financial solutions partner.

### Getting Started

Getting started with this project is very simple, all you need is to have Python, Git, and a Code Editor installed on your machine. Then open up your terminal and run this command `<NAME_EMAIL>:LibertytechX/liberty-requisition.git` to clone the project repository.

Change directory into the project folder `cd liberty-requisition` (if not in the directory already), after which you can choose to create a virtual environment first before installing the project requirements using the command `pip install -r requirements.txt`.

### Basic Project Commands

* Start Celery worker `celery -A config.celery worker --loglevel=INFO --concurrency 1 -P solo`

* Start Celery beat `celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler`

#### Additional Information

* use the function in the App core (tasks.py) to send emails

* the following was done on server side for packages required to enhance the ML receipt implementation;
  * apt-get update && apt-get install ffmpeg libsm6 libxext6  -y
  * pip uninstall fitz
  * pip install PyMuPDF


### 📂 **Error Log Access**
For system logs and debugging, visit the **error log dashboard**:

🔗 **Log URL**: `[BASE_URL]/core/server/logs/`

Replace `[BASE_URL]