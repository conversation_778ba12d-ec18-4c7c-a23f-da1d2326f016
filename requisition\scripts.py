import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import re
import spacy
nlp = spacy.load("en_core_web_sm")
import os

# my_config = r"--psm 6 --oem 3"


class DocumentScanner:
    def __init__(self, filepath):
        self.filepath = filepath
        self.text = ""

    def extract_text_from_image(self, image_path):
        image = Image.open(image_path)
        text = pytesseract.image_to_string(image)
        return text

    def extract_text_from_pdf(self, pdf_path):
        pages = convert_from_path(pdf_path)
        text = ""
        for page in pages:
            text += pytesseract.image_to_string(page)
        return text

    def process_doc(self):
        if self.filepath.lower().endswith(('.png', '.jpg', '.jpeg')):
            self.text = self.extract_text_from_image(self.filepath)
        elif self.filepath.lower().endswith('.pdf'):
            self.text = self.extract_text_from_pdf(self.filepath)
        else:
            raise ValueError("Unsupported file format. Please upload a PDF or image.")

    def extract_information(self):
        doc = nlp(self.text)

        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', self.text)

        dates = re.findall(r'\b(?:\d{1,2}[-/]\d{1,2}[-/]\d{2,4}|\d{4}-\d{2}-\d{2})\b', self.text)
        
        entities = {
            "ORG": [],
            "Total": [],
            "Amount": [],
            "Subtotal": [],
            "Invoice No": [],
            "Due date": [],
            "Name": [],
            "Description": [],
            "No of items": [],
            "No shipped": [],
        }
        for ent in doc.ents:
            if ent.label_ in entities:
                entities[ent.label_].append(ent.text)

        return {
            "emails": emails,
            "dates": dates,
            "entities": entities
        }


