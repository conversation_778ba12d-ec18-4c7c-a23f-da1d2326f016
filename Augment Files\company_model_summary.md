# Company Model Summary

## Fields by Type

### CharField
- `company_name`: Name of the company
- `company_wallet_type`: Type of wallet ("MAIN" or "CORPORATE")
- `industry`: Industry sector of the company
- `cac_num`: Company registration number
- `corporate_id`: ID for corporate accounts
- `channel`: Channel through which the company was created
- `supplier_invite_id`: Unique ID for supplier invitations

### PositiveIntegerField
- `size`: Size of the company (number of employees)

### DateField
- `incorporation_date`: Date of company incorporation

### BooleanField
- `is_active`: Whether the company is active
- `is_deleted`: Whether the company is deleted
- `test_account`: Whether this is a test account
- `instant_wage`: Whether instant wage is enabled
- `auto_approve_suppliers`: Whether suppliers are auto-approved
- `corporate_account_created`: Whether a corporate account has been created

### ForeignKey
- `user`: Owner of the company (references User model)

### ManyToManyField
- `teams`: Teams within the company (references Team model)

## Functions

### Instance Methods

#### `save()` (Line ~100-104)
- Overrides default save method to generate a supplier_invite_id if not present
- Called automatically by Django ORM when saving Company instances
- Referenced in: Django ORM operations throughout the project

#### `__str__()` (Line ~106-107)
- Returns string representation of the company (company_name)
- Used for display in admin interface and debugging
- Referenced in: Django admin, shell debugging

### Properties

#### `overall_running_budget` (Line ~109-114)
- Calculates total budget amount for the company from active budgets
- Referenced in: `ListOfCompanySerializer` for API responses in `requisition/serializers.py`
- Used in dashboard views and financial reporting

#### `total_current_month_expenses` (Line ~116-126)
- Calculates total expenses for the current month
- Referenced in: `ListOfCompanySerializer` for API responses in `requisition/serializers.py`
- Used in dashboard views and financial reporting

#### `current_month_expenses` (Line ~128-136)
- Returns serialized data for current month expenses
- Referenced in: `ListOfCompanySerializer` for API responses in `requisition/serializers.py`
- Used in dashboard views for detailed expense information

### Class Methods

#### `create_company()` (Line ~138-163)
- Creates a new company instance with provided data
- Referenced in:
  - `CreateListAndDelCompany.post()` view in `requisition/views.py`
  - `CompanyOnboarding.create_company()` in `subscription_and_invoicing/models.py`
  - `JotFormDataSync.process_datasync()` in `requisition/models.py`

#### `retrieve_company()` (Line ~165-182)
- Retrieves a company by ID and optionally filters by user
- Referenced in:
  - Multiple view functions across the project
  - Permission checks in various apps
  - API endpoints that need to validate company access

## Third-Party Integrations

### YouVerify API
- **Integration**: `verify_company()` function in `requisition/helpers/request_cls.py`
- **Purpose**: Verifies company registration information against official records
- **Usage**: When a user registers a company with a CAC number, this API validates that the company registration is legitimate
- **Implementation**: Makes HTTP requests to YouVerify's company verification endpoint with the registration number

### Liberty Pay API
- **Integration**: `register_sub_company()` in `account/helpers/core_banking.py`
- **Purpose**: Creates financial accounts for companies in the Liberty Pay system
- **Usage**: After company creation, this integration sets up the financial infrastructure needed for transactions
- **Implementation**: Makes authenticated API calls to Liberty Pay's company registration endpoints

### OpenAI API (ChatGPT)
- **Integration**: `validate_utility_bill_with_chagpt()` in `payroll_app/apis/employee_verification.py`
- **Purpose**: Document validation for company verification
- **Usage**: During company verification, this API helps validate uploaded documents like utility bills
- **Implementation**: Sends document images to OpenAI's API for analysis and validation

### Spacy NLP
- **Integration**: Imported in `requisition/serializers.py`
- **Purpose**: Natural language processing for document analysis
- **Usage**: Used for text extraction and analysis in document processing during company verification
- **Implementation**: Uses the `en_core_web_sm` model for text analysis

### Pytesseract and PDF2Image
- **Integration**: Imported in `requisition/serializers.py`
- **Purpose**: OCR and document processing
- **Usage**: Extracts text from images and PDFs during document verification
- **Implementation**: Converts PDFs to images and then uses OCR to extract text for verification

### Mono API
- **Integration**: Referenced in `MonoTopUpRecord` model and related views
- **Purpose**: Financial data aggregation and account verification
- **Usage**: Used for verifying bank accounts and facilitating financial transactions
- **Implementation**: Integrates with Mono's API for secure financial data access
