from celery import shared_task
import logging
from core.tasks import send_email
from django.core.mail import send_mail
from performance_sales_metrics_dashboard.utils.sent_emails import save_sent_emails
from performance_sales_metrics_dashboard.utils.scheduled_emails import save_to_scheduled
from performance_sales_metrics_dashboard.utils.gmail_outbox import send_email_with_gmail
from performance_sales_metrics_dashboard.utils.gmail_integration import GmailOAuthHandler 
from performance_sales_metrics_dashboard.utils.utils import decrypt_password
from requisition.models import TeamMember
from .utils.send_emails import send_mails
# from .utils.bulk_email import send_bulk_emails
from .utils.format_date import format_email_date
from datetime import timedelta, datetime
from .models import Task, Lead, WorkEmail
from django.utils import timezone
from django.template.loader import render_to_string
from .models import Emails, GmailAccount
from .utils.retrieve_emails import get_emails_from_server
import pytz
logger = logging.getLogger(__name__)

@shared_task
def sync_emails_with_db(user_id, sales_officer):
    gmail_oauth_handler = Gmail<PERSON><PERSON><PERSON>and<PERSON>()
    try:
        gmail = GmailAccount.objects.get(user=user_id)
        gmail_address = gmail.gmail_address
        mail = gmail_oauth_handler.retrieve_google_mails(gmail_address)
    except Exception as e:
        # logger.exception("AN ERROR OCCURRED", str(e))
        print("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE EMAILS FROM GMAIL SERVER", str(e))


    if gmail_address == None:
        try:
            work_email = WorkEmail.objects.get(user=user_id)
            username = work_email.work_email
            password = decrypt_password(work_email.password)

            try:
                mail = get_emails_from_server(username, password)
            except Exception as e:
                # logger.exception("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE EMAILS FROM LIBERTY SERVER", str(e))
                print("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE EMAILS FROM LIBERTY SERVER", str(e))
        except Exception as e:
            # logger.exception("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE EMAILS FROM GMAIL SERVER", str(e))
            print("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE EMAILS FROM GMAIL SERVER", str(e))

    # mail = None
    try:
        latest_email_date = Emails.objects.order_by('-date').first().date

        # If no emails for today, fetch new emails from the server
            
        for mail_object in mail:
            team_member = TeamMember.objects.get(id=sales_officer)

            # Email Date
            unformatted_date = mail_object.get("Date")
            formatted_date = format_email_date(unformatted_date)
            date = formatted_date

            # Email Subject
            subject = mail_object.get("Subject")
            subject = list(subject)
            subject = str(subject[0])

            # Email Sender
            sender = mail_object.get("Sender")
            sender = list(sender)
            sender = str(sender[0])

            # Sender's Name
            senders_name = mail_object.get("Senders_Name")
            senders_name = list(senders_name)
            senders_name = str(senders_name[0])

            # Cc
            cc = mail_object.get("Cc")
            cc = list(cc)
            cc = str(cc[0])

            # Bcc
            bcc = mail_object.get("Bcc")
            bcc = list(bcc)
            bcc = str(bcc[0])

            # Email Receiver
            receiver = mail_object.get("Receiver")
            receiver = list(receiver)
            receiver = str(receiver[0])

            # Email Content
            content = mail_object.get("Content")
            content = list(content)
            content = str(content[0])
            try:
                email_date = Emails.objects.get(date=date)
                if Emails.objects.get(date=email_date.date):
                    continue
            except Emails.DoesNotExist:
                try:
                    Emails.save_as_inbox(
                        sales_officer=team_member, 
                        is_inbox=True, 
                        date=date, 
                        subject=subject, 
                        sender=sender, 
                        cc=cc,
                        bcc=bcc,
                        senders_name=senders_name,
                        recipients=receiver, 
                        content=content,
                    )
                    print("NEW EMAILS SYNCED TO THE DATABASE")
                except Exception as e:
                    # logger.exception("AN ERROR OCCURRED WHILE TRYING TO REFRESH EMAILS FROM THE SERVER", str(e))
                    print("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE EMAILS FROM GMAIL SERVER", str(e))
    except(AttributeError, IndexError):
        try:
            mail = get_emails_from_server(username, password)
        except Exception as e:
            print("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE EMAILS FROM THE SERVER", str(e))

        for mail_object in mail:
            team_member = TeamMember.objects.get(id=sales_officer)

            # Email Date
            unformatted_date = mail_object.get("Date")
            formatted_date = format_email_date(unformatted_date)
            date = formatted_date

            # Email Subject
            subject = mail_object.get("Subject")
            subject = list(subject)
            subject = str(subject[0])

            # Email Sender
            sender = mail_object.get("Sender")
            sender = list(sender)
            sender = str(sender[0])
            
            # Sender's Name
            senders_name = mail_object.get("Senders_Name")
            senders_name = list(senders_name)
            senders_name = str(senders_name[0])

            # Cc
            cc = mail_object.get("Cc")
            cc = list(cc)
            cc = str(cc[0])

            # Bcc
            bcc = mail_object.get("Bcc")
            bcc = list(bcc)
            bcc = str(bcc[0])

            # Email Receiver
            receiver = mail_object.get("Receiver")
            receiver = list(receiver)
            receiver = str(receiver[0])

            # Email Content
            content = mail_object.get("Content")
            content = list(content)
            content = str(content[0])
            try:
                email_date = Emails.objects.get(date=date)
                if Emails.objects.get(date=email_date.date):
                    continue
            except Emails.DoesNotExist:
                try:
                    Emails.save_as_inbox(
                        sales_officer=team_member, 
                        is_inbox=True, 
                        date=date, 
                        subject=subject, 
                        sender=sender, 
                        cc=cc,
                        bcc=bcc,
                        senders_name=senders_name,
                        recipients=receiver, 
                        content=content,
                    )
                    print("NEW EMAILS SYNCED TO THE DATABASE")
                except Exception as e:
                    # logger.exception("AN ERROR OCCURRED WHILE TRYING TO REFRESH EMAILS FROM THE SERVER", str(e))
                    print("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE EMAILS FROM GMAIL SERVER", str(e))


@shared_task
def retrieve_emails(username, password):
    try:
        get_emails_from_server(username, password)
    except Exception as e:
        return ("An error occurred", str(e))


@shared_task
def send_task_deadline_reminder(task_id):
    try:
        task = Task.objects.get(id=task_id)
    except Task.DoesNotExist:
        print(f"Task with ID {task_id} not found")
        return

    if task.deadline_reminder:
        for sales_officer in task.sales_officer_id.split(""):
            subject = f"Reminder: Task '{task.title} deadline is approaching'"
            template_dir = "send_reminder_email.html"
            send_email(
                recipient=sales_officer,
                subject=subject,
                template_dir=template_dir,
                title=task.title,
                deadline=task.deadline.strftime("%Y-%m-%d %H:%M:%S")
            )
    else:
        print(f"Deadline reminder is disabled for task with ID {task_id}.")


@shared_task
def send_email_task(subject, message, from_email, recipient_list):
    send_mail(subject, message, from_email, recipient_list)


@shared_task
def send_single_mails(
        user_id,
        subject,
        message,
        sender_email,
        senders_name,
        cc,
        bcc,
        recipient_email,
        date,
        team_member,
):
    try:
        sent_emails = send_mails(
            user_id=user_id,
            subject=subject,
            message=message,
            recipient_email=recipient_email,
            cc=cc,
            bcc=bcc,
        )
        if sent_emails:
            print("LIBERTY MAIL SENT SUCCESSFULLY")
    except Exception as e:
        print("An error occurred while attempting to send emails.", str(e))
    try:
        if sent_emails is not None:
            recipient_emails = recipient_email[0]
            saved_emails = save_sent_emails(
                is_sent=True,
                sales_officer=team_member, 
                date=date, 
                subject=subject, 
                sender=sender_email, 
                senders_name=senders_name,
                cc=cc,
                bcc=bcc,
                receiver=recipient_emails, 
                content=message,
            )
            if saved_emails:
                print("Emails saved successfully.", saved_emails)
    except Exception as e:
        print("An error occurred while attempting to save sent emails.", str(e))


@shared_task
def send_bulk_emails(user_id, gmail_address, subject, message, recipient_emails, cc, bcc, senders_name, date, team_member):
    failed_recipients = []
    
    if gmail_address != None and "@gmail.com" in gmail_address:
        try:
            send_email_with_gmail(
                gmail_address=gmail_address,
                subject=subject,
                message=message,
                senders_name=senders_name,
                cc=cc,
                bcc=bcc,
                date=date,
                team_member=team_member,
                recipient_email=recipient_emails,
            )
            print(f"Email sent successfully to {recipient_emails}")
        except Exception as e:
            failed_recipients.append(recipient_emails)
            if failed_recipients:
                print(f"Failed to send email to {recipient_emails}: {str(e)}")
                print("AN ERROR OCCURRED WHILE ATTEMPTING TO SEND SCHEDULED EMAIL WITH GMAIL!\n", str(e))
                return {
                    "status": "Partial success",
                    "failed_recipients": failed_recipients
                }
    else:            
        try:
            send_mails(
                user_id,
                subject,
                message,
                recipient_emails,
                cc,
                bcc,
            )
            print(f"Email sent successfully to {recipient_emails}")
        except Exception as e:
            failed_recipients.append(recipient_emails)
            if failed_recipients:
                return {
                    "status": "Partial success",
                    "failed_recipients": failed_recipients
                }
            return (f"Failed to send email to {recipient_emails}: {str(e)}")


@shared_task
def schedule_bulk_emails(
    user_id,
    team_member, 
    subject, 
    message, 
    sender_email, 
    senders_name,
    cc,
    bcc,
    recipient_emails,
    scheduled_time: str,
    gmail_address,
    date,
    ):
    current_timezone = datetime.now().astimezone().tzinfo
    current_time = timezone.now().replace(microsecond=0)
    updated_scheduled_time = datetime.strptime(scheduled_time, "%Y-%m-%d %H:%M:%S").replace(tzinfo=current_timezone)

    delay = (updated_scheduled_time - current_time)
    new_schedule_time = delay.total_seconds()

    # Remove this!
    # work_email = WorkEmail.objects.get(user=user_id)
    # email_address = work_email.work_email

    try:
        send_bulk_emails.apply_async(
            args=[
                user_id,
                gmail_address,
                subject, 
                message,
                recipient_emails, 
                cc,
                bcc,
                senders_name,
                date,
                team_member,
            ],
            countdown=new_schedule_time
        )
        print("Bulk emails scheduled successfully")
    except Exception as e:
        return f"An error occurred while attempting to save scheduled emails., {str(e)}"
    
    try:
        if "@gmail.com" in gmail_address:
            new_recipient_emails = recipient_emails[0:]
            save_to_scheduled(
                is_scheduled=True,
                sales_officer=team_member, 
                date=updated_scheduled_time, 
                subject=subject, 
                sender=gmail_address, 
                senders_name=senders_name,
                cc=cc,
                bcc=bcc,
                receiver=new_recipient_emails, 
                content=message,   
            )
            print("Scheduled emails saved successfully.")
        elif "@libertyng.com" in sender_email:
            new_recipient_emails = recipient_emails[0:]
            save_to_scheduled(
                is_scheduled=True,
                sales_officer=team_member, 
                date=updated_scheduled_time, 
                subject=subject, 
                sender=sender_email, 
                senders_name=senders_name,
                cc=cc,
                bcc=bcc,
                receiver=new_recipient_emails, 
                content=message,   
            )
            print("Scheduled emails saved successfully.")
    except Exception as e:
        return f"An error occurred while attempting to save scheduled emails., {str(e)}"

@shared_task
def get_emails_from_db(date1, date2):
    this_email = Emails.objects.filter(date__date__lte=date1, date__date__gte=date2)
    return this_email


@shared_task
def update_leads_in_stage():
    leads = Lead.objects.all()

    for lead in leads:
        lead.update_countdown_and_lead_expiry_in_stage()
