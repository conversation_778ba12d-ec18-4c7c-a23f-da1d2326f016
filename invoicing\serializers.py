import calendar

from rest_framework import serializers

from helpers.reusable_functions import is_valid_string, is_valid_uuid
from invoicing import models, utils
from invoicing.helpers import enums
from sales_app.models import Customer
from sales_app.serializers import CustomerSerializer


# Create your serializer(s) here.
class InvoiceDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Invoice
        fields = [
            "created_at",
            "reference",
            "company",
            "branch",
            "customer",
            "amount",
            "due_date",
            "branch",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = (
            instance.branch.name if instance.branch is not None else None
        )
        representation["customer"] = (
            instance.customer.name if instance.customer is not None else None
        )
        representation["status"] = (
            enums.InvoiceTypes.DUE
            if instance.due
            else (
                enums.InvoiceTypes.PAID if instance.paid else enums.InvoiceTypes.PENDING
            )
        )
        return representation


class InvoiceItemsSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.InvoiceItem
        fields = "__all__"


class InvoiceRecordSerializer(serializers.ModelSerializer):
    company_name = serializers.CharField(source="company.company_name")
    branch_name = serializers.CharField(source="branch.name")
    branch_address = serializers.CharField(source="branch.address")

    class Meta:
        model = models.Invoice
        fields = [
            "company",
            "company_name",
            "branch",
            "branch_name",
            "branch_address",
            "reference",
            "customer",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.customer is not None:
            try:
                customer_details = Customer.objects.get(id=instance.customer.id)
            except Customer.DoesNotExist:
                customer_details = None
            representation["customer_details"] = CustomerSerializer(
                instance=customer_details).data
        else:
            representation["customer_details"] = {}
        representation["items"] = InvoiceItemsSerializer(
            instance.invoiceitem_set.all(), many=True
        ).data
        return representation


class ItemCreateSerializer(serializers.Serializer):
    item_description = serializers.CharField()
    unit_price = serializers.DecimalField(max_digits=13, decimal_places=2)
    quantity = serializers.IntegerField()
    discount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )


class InvoiceSerializer(serializers.ModelSerializer):
    items = ItemCreateSerializer(many=True)
    tax = serializers.UUIDField(required=False, allow_null=True)

    class Meta:
        model = models.Invoice
        fields = "__all__"
        read_only_fields = ["created_by"]

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if attrs.get("items") == []:
            raise serializers.ValidationError({"message": "items cannot be empty."})
        if "tax" in attrs:
            attrs["tax_rate"] = utils.get_tax_rate(tax_id=attrs.get("tax"))
            attrs.pop("tax")
        return attrs


class CustomerInvoiceSerializer(serializers.Serializer):
    invoice_number = serializers.CharField(max_length=25, source="reference")
    status = serializers.CharField(max_length=25)
    created_at = serializers.DateTimeField()
    due_date = serializers.DateField()
    amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["customer_name"] = (
            instance.customer.name if instance.customer is not None else ""
        )
        representation["branch"] = (
            instance.branch.name if instance.branch is not None else ""
        )
        return representation


class TaxSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.Tax
        exclude = [
            "updated_at",
            "created_by",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = (
            instance.branch.name if instance.branch is not None else ""
        )
        return representation


class InvoiceMetricsSerializer(serializers.Serializer):
    branch = serializers.CharField(required=False, source="branch_name")
    month = serializers.CharField(required=False)
    paid_invoices = serializers.IntegerField(required=False)
    paid_amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )
    due_invoices = serializers.IntegerField(required=False)
    due_amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )
    unpaid_invoices = serializers.IntegerField(required=False)
    unpaid_amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )
    count = serializers.IntegerField(required=False)
    total_draft = serializers.IntegerField(required=False)
    total_draft_amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["month"] = calendar.month_name[instance.get("month")]
        return representation
