# Payroll App Documentation

## Overview

The Payroll app manages employee data, salary processing, and payroll operations for companies on the Paybox360 platform. It handles the complete payroll lifecycle from employee onboarding to salary disbursement and tax reporting.

## Core Features

### 1. Employee Management

- Employee onboarding and profile management
- Department and role assignment
- Employee permissions and access control
- Employee data management (personal, bank, education, certifications)
- Employee offboarding process

### 2. Payroll Processing

- Run payroll for employees
- Multiple payroll support
- Payroll approval workflow
- Payroll disbursement
- Payroll history tracking
- Salary component management

### 3. Compensation Management

- Salary structure configuration
- Pay grade and pay group settings
- Deductions and benefits management
- Tax calculations
- Pension management

### 4. Instant Wage Access

- Employee instant wage requests
- Approval/rejection workflow
- Instant wage history
- Withdrawal processing

### 5. Reporting and Analytics

- Payroll summary reports
- Company dashboard metrics
- Employment trend analysis
- Payroll spread visualization
- Transaction history

## How It Works

1. **Employee Onboarding**: New employees are added to the system with their personal and financial details
2. **Payroll Configuration**: Company sets up payroll settings, departments, and compensation structures
3. **Payroll Run**: Payroll administrator initiates payroll process for a specific period
4. **Approval Process**: Authorized personnel review and approve payroll
5. **Disbursement**: Approved payroll is disbursed to employee accounts
6. **Reporting**: System generates reports and analytics for payroll activities

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `PayrollTable` | Core payroll record | `status`, `payable_amount`, `bulk_id` | Company, Employee |
| `CompanyEmployeeList` | Employee records | `employee_gross_amount`, `is_active` | Company, User |
| `CompanyDetailsData` | Company payroll settings | `payroll_date`, `payroll_month` | Company |
| `CompanyPayrollSettings` | Payroll configuration | `tax_settings`, `pension_settings` | Company |
| `InstantWagePayroll` | Instant wage requests | `amount`, `status` | Employee |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `create_approval_payroll` | Creates payroll for approval | `models.py` (PayrollTable) | Called by RunPayrollAPIVIew |
| `run_approval_payroll` | Processes payroll approval | `models.py` (PayrollTable) | Called by RunApprovalPayrollAPIVIew |
| `create_record_payroll` | Creates payroll records | `models.py` (PayrollTable) | Called by DisbursePayrollRecordAPIView |
| `add_employee_existing_payroll` | Adds employees to payroll | `models.py` (PayrollTable) | Called by AddEmployeeExistingPayrollAPIView |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/payroll_app/run_payroll/` | Initiate payroll process | POST | `RunPayrollAPIVIew` |
| `/payroll_app/run_payroll_approval/` | Approve payroll | POST | `RunApprovalPayrollAPIVIew` |
| `/payroll_app/disburse-payroll/` | Disburse approved payroll | POST | `DisbursePayrollAPIView` |
| `/payroll_app/create_employee/` | Add new employee | POST | `CreateEmployeeAPIView` |
| `/payroll_app/employee_request_instant_wage/` | Request instant wage | POST | `EmployeeInstantWageRequestAPIView` |

### Integration Points

- **Account System**: For wallet operations and fund transfers
- **Core App**: For user authentication and permissions
- **AWS S3**: For document storage
- **Email/SMS Services**: For notifications

## Key Considerations

### 1. Payroll Security

- **Responsible App**: Payroll app and Core app
- **Key Functions**:
  - PIN verification in payroll operations (e.g., `RunPayrollAPIVIew.post()`)
  - Role-based permissions like `PayrollApprovalPermission`
  - Secure handling of sensitive employee data

### 2. Payroll Workflow Management

- **Responsible App**: Payroll app
- **Key Functions**:
  - Status tracking in `PayrollTable` model (APPROVAL, DISBURSE, DISBURSED, OMITTED)
  - Workflow enforcement in view permissions
  - Bulk ID tracking for grouped operations

### 3. Tax and Pension Calculations

- **Responsible App**: Payroll app
- **Key Functions**:
  - Tax calculation in `consolidated_tax` function
  - Pension amount calculations in payroll creation
  - Configurable tax bands in `CompanyTaxBand` model

### 4. Instant Wage Management

- **Responsible App**: Payroll app
- **Key Functions**:
  - Request handling in `EmployeeInstantWageRequestAPIView`
  - Approval workflow in `AcceptRejectInstantWageRequestAPIView`
  - Withdrawal processing in `InstantWageWithdrawAPIView`

### 5. Multiple Payroll Support

- **Responsible App**: Payroll app
- **Key Functions**:
  - Multiple payroll creation in `RunMultiplePayrollAPIVIew`
  - Limit checking in `multiple_payroll_count`
  - Bulk operations with shared `bulk_id`




