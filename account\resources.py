from import_export import resources

from account import models


# Create your resource(s) here.
class AccountServiceProviderResource(resources.ModelResource):
    class Meta:
        model = models.AccountServiceProvider


class AccountSystemResource(resources.ModelResource):
    class Meta:
        model = models.AccountSystem


class DebitCreditRecordOnAccountResource(resources.ModelResource):
    class Meta:
        model = models.DebitCreditRecordOnAccount

    def dehydrate_user(self, obj):
        return obj.user.email if obj.user else ""

    # def dehydrate_wallet(self, obj):
    #     return obj.company.company_name if obj.company else ""


class TransactionResource(resources.ModelResource):
    class Meta:
        model = models.Transaction

    def dehydrate_user(self, obj):
        return obj.user.email if obj.user else ""

    def dehydrate_jounral(self, obj):
        return obj.jounral.journal_number if obj.jounral else ""


class WalletResource(resources.ModelResource):
    class Meta:
        model = models.Wallet


class AccountCreationFailureResource(resources.ModelResource):
    class Meta:
        model = models.AccountCreationFailure


class TransactionMetaDataResource(resources.ModelResource):
    class Meta:
        model = models.TransactionMetaData


class CoreBankingCallbackResource(resources.ModelResource):
    class Meta:
        model = models.CoreBankingCallback


class BeneficiaryResource(resources.ModelResource):
    class Meta:
        model = models.Beneficiary


class BulkTransferResource(resources.ModelResource):
    class Meta:
        model = models.BulkTransfer


class BulkTransferItemResource(resources.ModelResource):
    class Meta:
        model = models.BulkTransferItem


class BulkTransferItemTransactionResource(resources.ModelResource):
    class Meta:
        model = models.BulkTransferItemTransaction


class ScheduledTransferLogResource(resources.ModelResource):
    class Meta:
        model = models.ScheduledTransferLog
