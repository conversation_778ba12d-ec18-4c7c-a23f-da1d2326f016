from subscription_and_invoicing.models import ModuleSubscription, CompanySubscription
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Updates all subscription days for modules and companies'

    def handle(self, *args, **options):
        self.stdout.write('Starting subscription days update...')

        # First update module days
        count = 0
        self.stdout.write('Updating module subscription days...')
        for module_sub in ModuleSubscription.objects.all():
            module_sub.update_module_overall_days()
            count += 1
            if count % 100 == 0:
                self.stdout.write(f'Processed {count} module subscriptions')

        self.stdout.write(f'Completed updating {count} module subscriptions')

        # Then update company subscription days
        count = 0
        self.stdout.write('Updating company subscription days...')
        for company_sub in CompanySubscription.objects.all():
            company_sub.update_overall_subscription_days()
            count += 1
            if count % 100 == 0:
                self.stdout.write(f'Processed {count} company subscriptions')

        self.stdout.write(f'Completed updating {count} company subscriptions')
        self.stdout.write(self.style.SUCCESS('Successfully updated all subscription days'))