from django.contrib import admin
from import_export.admin import ImportExportModelAdmin

from payroll_app.models import SendPensionData
from payroll_app.resources import *

# Register your models here.


class PayrollTableResourceAdmin(ImportExportModelAdmin):
    resource_class = PayrollTableResource
    search_fields = [
        "payroll_admin__email",
        "payroll_user__email",
        "company_owner__email",
        "company__company_name",
        "bulk_id",
        "bank_name",
        "account_number",
        "account_name",
        "bank_code",
        "first_name",
        "last_name",
        "email",
    ]
    list_filter = ["payroll_type", "status", "payroll_disburse", "payroll_deleted"]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("gross_amount")
        data.remove("payable_amount")
        data.remove("net_amount")

        data.insert(7, "overall_amount")
        data.insert(8, "receivable_amount")
        data.insert(9, "settlement_amount")
        return data


class CompanyEmployeeListResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyEmployeeListResource
    search_fields = [
        "employer__email",
        "employee__email",
        "company__company_name",
        "employee_bank_name",
        "employee_account_number",
        "employee_email",
        "employee_first_name",
        "employee_last_name",
        "employee_phone_number",
        "employee_bank_code",
        "employee_account_name",
        "added_by__email",
        "paybox_id",
        "employee_staff_id",
    ]
    list_filter = [
        "pay_schedule",
        "pay_run",
        "staff_type",
        "employee_status",
        "work_type",
        "employee_instant_wage_status",
        "employee_instant_daily_wage",
        "is_active",
        "is_suspended",
        "is_deleted",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("employee_gross_amount")
        data.remove("employee_net_amount")
        data.remove("employee_payable_amount")

        data.insert(7, "overall_amount")
        data.insert(8, "settlement_amount")
        data.insert(9, "receivable_amount")
        return data


class CompanyDetailsDataResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyDetailsDataResource
    search_fields = ["bulk_id", "company__company_name"]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class InstantWagePayrollResourceAdmin(ImportExportModelAdmin):
    resource_class = InstantWagePayrollResource
    search_fields = [
        "instant_wage_user__email",
        "employer__email",
        "receiver__email",
        "company__company_name",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyAnnouncementResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyAnnouncementResource
    search_fields = ["company__company_name"]
    list_filter = ["announcement_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class InstantWagePayrollRefundResourceAdmin(ImportExportModelAdmin):
    resource_class = InstantWagePayrollRefundResource
    search_fields = ["company__company_name", "receiver__email", "employee__email"]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OneClickTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = OneClickTransactionResource
    search_fields = [
        "user__email",
        "account_number",
        "account_name",
        "bank_name",
        "transaction_id",
    ]
    list_filter = ["status", "disbursed"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BeneficiaryResourceAdmin(ImportExportModelAdmin):
    resource_class = BeneficiaryResource
    search_fields = ["user__email", "account_number", "account_name", "bank_name"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyPayrollSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyPayrollSettingsResource
    search_fields = ["employer__email", "company__company_name", "company_name"]
    list_filter = ["pay_schedule", "pay_run", "employee_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyPayGroupSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyPayGroupSettingsResource
    search_fields = ["employer__email", "company__company_name", "pay_group_name"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyPayGradeSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyPayGradeSettingsResource
    search_fields = ["employer__email", "company__company_name", "pay_grade_name"]
    list_filter = ["pay_schedule"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OtherDeductionSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = OtherDeductionSettingsResource
    search_fields = ["employer__email", "company__company_name", "deduction_name"]
    list_filter = [
        "deduction_type",
        "calculation_type",
        "frequency",
        "is_active",
        "is_deleted",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyDepartmentSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyDepartmentSettingsResource
    search_fields = ["employer__email", "company__company_name", "department_name"]
    list_filter = ["is_active", "is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalaryComponentSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = SalaryComponentSettingsResource
    search_fields = [
        "employer__email",
        "company__company_name",
        "salary_name",
    ]
    list_filter = ["frequency"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CustomComponentSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = CustomComponentSettingsResource
    search_fields = [
        "employer__email",
        "company__company_name",
        "custom_component_name",
    ]
    list_filter = ["is_active", "is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BenefitComponentSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = BenefitComponentSettingsResource
    search_fields = ["employer__email", "company__company_name", "benefit_name"]
    list_filter = ["frequency", "is_active", "is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyEmployeeOnboardingFormResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyEmployeeOnboardingFormResource
    search_fields = [
        "employee_email",
        "employee_company__company_name",
        "employee_first_name",
        "employee_last_name",
    ]
    list_filter = ["is_completed", "approval_status"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyEmployeeAccountDetailsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyEmployeeAccountDetailsResource
    search_fields = [
        "employee__email",
        "company__company_name",
        "account_name",
        "account_number",
    ]
    list_filter = ["is_active", "is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyEmployeeEducationDetailsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyEmployeeEducationDetailsResource
    search_fields = [
        "employee__email",
        "company__company_name",
        "school_name",
        "course_name",
    ]
    list_filter = ["is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyEmployeeExperienceDetailsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyEmployeeExperienceDetailsResource
    search_fields = ["employee__email", "company__company_name", "employer_name"]
    list_filter = ["is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyEmployeeCertificationsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyEmployeeCertificationsResource
    search_fields = [
        "employee__email",
        "company__company_name",
        "institution_name",
        "course_name",
        "license_name",
        "issuing_body",
    ]
    list_filter = ["is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OtherDependencySettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = OtherDependencySettingsResource
    search_fields = ["deduction__deduction_name"]
    list_filter = ["calculation_type", "dependency_operator"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyTaxBandResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyTaxBandResource
    search_fields = ["company__company_name"]
    list_filter = ["is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PensionFundAdminSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = PensionFundAdminSettingsResource
    search_fields = ["company__company_name", "pfa_name"]
    list_filter = ["is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ManagePermissionsRoleResourceAdmin(ImportExportModelAdmin):
    resource_class = ManagePermissionsRoleResource
    search_fields = ["company__company_name", "role_name"]
    list_filter = [
        "is_deleted",
        "can_disburse",
        "can_approve",
        "can_add_member",
        "can_edit_member",
        "can_delete_member",
        "can_delete_payroll",
        "can_run_payroll",
        "can_edit_payroll_settings",
        "can_create_leave_type",
        "can_edit_leave_type",
        "can_delete_leave_type",
        "can_create_leave_policy",
        "can_edit_leave_policy",
        "can_delete_leave_policy",
        "can_approve_leave",
        "can_create_role",
        "can_create_department",
        "can_edit_department",
        "can_delete_department",
        "can_create_department_role",
        "can_edit_department_role",
        "can_delete_department_role",
        "can_deploy_department_role",
        "can_deploy_department",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ManageDepartmentRoleResourceAdmin(ImportExportModelAdmin):
    resource_class = ManageDepartmentRoleResource
    search_fields = ["company__company_name", "role_name"]
    list_filter = ["is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SavingPayrollEditDataResourceAdmin(ImportExportModelAdmin):
    resource_class = SavingPayrollEditDataResource
    search_fields = ["company__company_name"]
    list_filter = ["interacted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class SendPensionDataResourceAdmin(ImportExportModelAdmin):
    resource_class = SendPensionDataResource
    search_fields = ["company__company_name"]
    list_filter = ["pension_paid", "pension_email_sent"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class EmployeeChargeResourceAdmin(ImportExportModelAdmin):
    resource_class = EmployeeChargeResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class VerificationDataResourceAdmin(ImportExportModelAdmin):
    resource_class = VerificationDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(PayrollTable, PayrollTableResourceAdmin)
admin.site.register(CompanyEmployeeList, CompanyEmployeeListResourceAdmin)
admin.site.register(CompanyDetailsData, CompanyDetailsDataResourceAdmin)
admin.site.register(InstantWagePayroll, InstantWagePayrollResourceAdmin)
admin.site.register(CompanyAnnouncement, CompanyAnnouncementResourceAdmin)
admin.site.register(InstantWagePayrollRefund, InstantWagePayrollRefundResourceAdmin)
admin.site.register(OneClickTransaction, OneClickTransactionResourceAdmin)
admin.site.register(Beneficiary, BeneficiaryResourceAdmin)
admin.site.register(CompanyPayrollSettings, CompanyPayrollSettingsResourceAdmin)
admin.site.register(CompanyPayGroupSettings, CompanyPayGroupSettingsResourceAdmin)
admin.site.register(CompanyPayGradeSettings, CompanyPayGradeSettingsResourceAdmin)
admin.site.register(OtherDeductionSettings, OtherDeductionSettingsResourceAdmin)
admin.site.register(CompanyDepartmentSettings, CompanyDepartmentSettingsResourceAdmin)
admin.site.register(SalaryComponentSettings, SalaryComponentSettingsResourceAdmin)
admin.site.register(CustomComponentSettings, CustomComponentSettingsResourceAdmin)
admin.site.register(BenefitComponentSettings, BenefitComponentSettingsResourceAdmin)
admin.site.register(
    CompanyEmployeeOnboardingForm, CompanyEmployeeOnboardingFormResourceAdmin
)
admin.site.register(
    CompanyEmployeeAccountDetails, CompanyEmployeeAccountDetailsResourceAdmin
)
admin.site.register(
    CompanyEmployeeEducationDetails, CompanyEmployeeEducationDetailsResourceAdmin
)
admin.site.register(
    CompanyEmployeeExperienceDetails, CompanyEmployeeExperienceDetailsResourceAdmin
)
admin.site.register(
    CompanyEmployeeCertifications, CompanyEmployeeCertificationsResourceAdmin
)
admin.site.register(OtherDependencySettings, OtherDependencySettingsResourceAdmin)
admin.site.register(CompanyTaxBand, CompanyTaxBandResourceAdmin)
admin.site.register(PensionFundAdminSettings, PensionFundAdminSettingsResourceAdmin)
admin.site.register(ManagePermissionsRole, ManagePermissionsRoleResourceAdmin)
admin.site.register(ManageDepartmentRole, ManageDepartmentRoleResourceAdmin)
admin.site.register(SavingPayrollEditData, SavingPayrollEditDataResourceAdmin)
admin.site.register(SendPensionData, SendPensionDataResourceAdmin)
admin.site.register(EmployeeCharge, EmployeeChargeResourceAdmin)
admin.site.register(VerificationData, VerificationDataResourceAdmin)
