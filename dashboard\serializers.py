from rest_framework import serializers
from decimal import Decimal

class TimeFrameMetricsSerializer(serializers.Serializer):
    today = serializers.DecimalField(max_digits=20, decimal_places=2)
    this_week = serializers.DecimalField(max_digits=20, decimal_places=2)
    last_week = serializers.DecimalField(max_digits=20, decimal_places=2)
    this_month = serializers.DecimalField(max_digits=20, decimal_places=2)

class DashboardMetricsSerializer(serializers.Serializer):
    wallet_balance = serializers.DecimalField(max_digits=20, decimal_places=2)
    total_savings_balance = serializers.DecimalField(max_digits=20, decimal_places=2)
    total_customers = serializers.IntegerField()
    total_pos = serializers.IntegerField()
    
    total_transfer = TimeFrameMetricsSerializer()
    total_inflow = TimeFrameMetricsSerializer()
    total_transactions = TimeFrameMetricsSerializer()
    transaction_counts = TimeFrameMetricsSerializer()
    total_cashout = TimeFrameMetricsSerializer()
    total_commission = TimeFrameMetricsSerializer()
    
    average_commission_per_transaction = serializers.DecimalField(max_digits=20, decimal_places=2)
    
    transaction_status = serializers.DictField(
        child=serializers.IntegerField()
    )
    
    gross_profit = serializers.DecimalField(max_digits=20, decimal_places=2)
    total_expenses = serializers.DecimalField(max_digits=20, decimal_places=2)

class TransactionComparativeSerializer(serializers.Serializer):
    sent_money = serializers.DecimalField(max_digits=20, decimal_places=2)
    cashout = serializers.DecimalField(max_digits=20, decimal_places=2)
    earnings = serializers.DecimalField(max_digits=20, decimal_places=2)
    debit = serializers.DecimalField(max_digits=20, decimal_places=2)

class SalesOverviewSerializer(serializers.Serializer):
    total_sales = serializers.DecimalField(max_digits=20, decimal_places=2)
    sales_categories = serializers.DictField(
        child=serializers.DecimalField(max_digits=20, decimal_places=2)
    )
    count_sales = serializers.IntegerField()
    average_sales = serializers.DecimalField(max_digits=20, decimal_places=2)
    total_products_sold = serializers.IntegerField()

class TransactionTypeMetricsSerializer(serializers.Serializer):
    transfer = serializers.IntegerField()
    data_purchase = serializers.IntegerField()
    airtime_purchase = serializers.IntegerField()
    utilities = serializers.IntegerField()
    lotto_games = serializers.IntegerField()

class TransactionAmountMetricsSerializer(serializers.Serializer):
    transfer = serializers.DecimalField(max_digits=20, decimal_places=2)
    data_purchase = serializers.DecimalField(max_digits=20, decimal_places=2)
    airtime_purchase = serializers.DecimalField(max_digits=20, decimal_places=2)
    utilities = serializers.DecimalField(max_digits=20, decimal_places=2)
    lotto_games = serializers.DecimalField(max_digits=20, decimal_places=2)

class TransactionStatusSerializer(serializers.Serializer):
    successful = serializers.IntegerField()
    failed = serializers.IntegerField()

class TrafficSourceSerializer(serializers.Serializer):
    pos_terminals = serializers.IntegerField()
    direct = serializers.IntegerField()
    website = serializers.IntegerField()
    web_pos = serializers.IntegerField()

class WalletBalanceSerializer(serializers.Serializer):
    wallet_balance = serializers.DecimalField(max_digits=20, decimal_places=2)
    savings_balance = serializers.DecimalField(max_digits=20, decimal_places=2)

class CustomerOverviewSerializer(serializers.Serializer):
    total_customers = serializers.IntegerField()
    total_pos_devices = serializers.IntegerField()
    total_savings = serializers.DecimalField(max_digits=20, decimal_places=2)

class TransactionStatsSerializer(serializers.Serializer):
    total_transfer_today = serializers.DecimalField(max_digits=20, decimal_places=2)
    total_inflow_today = serializers.DecimalField(max_digits=20, decimal_places=2)
    total_transactions_today = serializers.IntegerField()
    total_cashout_today = serializers.DecimalField(max_digits=20, decimal_places=2)
    total_commission_today = serializers.DecimalField(max_digits=20, decimal_places=2)
    average_commission_percent_today = serializers.FloatField()
    transaction_status_today = serializers.DictField(
        child=serializers.IntegerField()
    )

class FinancialMetricsSerializer(serializers.Serializer):
    gross_profit = serializers.DecimalField(max_digits=20, decimal_places=2)
    gross_profit_change = serializers.FloatField()
    total_expenses = serializers.DecimalField(max_digits=20, decimal_places=2)
    total_expenses_change = serializers.FloatField()