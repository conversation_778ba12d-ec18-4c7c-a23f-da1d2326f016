from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from datetime import datetime, timedelta, date
from django.utils import timezone
from django.conf import settings
import calendar
import string
import secrets
import math
import pytz

def get_ip_address(request):
    # Get IP ADDRESS
    address = request.META.get('HTTP_X_FORWARDED_FOR')
    if address:
        ip_addr = address.split(',')[-1].strip()
    else:
        ip_addr = request.META.get('REMOTE_ADDR')
    
    return ip_addr


class PayrollPaginator:
    @staticmethod
    def paginate(request, queryset, page):
        # request_get_data = request.GET

        # queryset_size = 30
        # if int(page) > 1:
        #     queryset_size = 20

        paginator = Paginator(queryset, per_page=20)

        try:
            paginated_data = paginator.page(page)
        except PageNotAnInteger:
            paginated_data = paginator.page(1)
        except EmptyPage:
            paginated_data = []

        return paginated_data
    
class PayrollHistoryPaginator:
    @staticmethod
    def paginate(request, queryset, page):
        # request_get_data = request.GET

        # queryset_size = 30
        # if int(page) > 1:
        #     queryset_size = 20

        paginator = Paginator(queryset, per_page=1)

        try:
            paginated_data = paginator.page(page)
        except PageNotAnInteger:
            paginated_data = paginator.page(1)
        except EmptyPage:
            paginated_data = []

        return paginated_data
    
class WalletHistoryPaginator:
    @staticmethod
    def paginate(request, queryset, page):
        # request_get_data = request.GET

        # queryset_size = 30
        # if int(page) > 1:
        #     queryset_size = 20

        paginator = Paginator(queryset, per_page=7)

        try:
            paginated_data = paginator.page(page)
        except PageNotAnInteger:
            paginated_data = paginator.page(1)
        except EmptyPage:
            paginated_data = []

        return paginated_data

def get_percentage_diff(previous, current):
    try:
        percentage = abs(previous - current)/max(previous, current) * 100
    except ZeroDivisionError:
        percentage = float('inf')
    except TypeError:
        percentage = 0

    try:

        if current > previous:
            change = "up"
        elif previous > current:
            change = "down"
        else:
            change = "no change"

        return {
            "percentage": percentage if percentage != float('inf') else 0,
            "change": change
        }
    except:
        return {
            "percentage": 0,
            "change": 0
        }
    
def date_utility(datetime):
    start_of_all_employees = datetime(2023, 5, 1)
    current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    previous_day = current_time - timedelta(days=1)

    date_today = current_time

    if date_today.month > 1:
        previous_month_start = datetime(date_today.year, date_today.month-1, 1)
    else:
        previous_month_start = datetime(date_today.year, 12, 1)
    current_month_start = datetime(date_today.year, date_today.month, 1)
    previous_month_end = current_month_start + timedelta(days=-1)

    current_date = date.today()
    month_start = datetime(current_date.year, current_date.month, 1)
    year_start = datetime(current_date.year, 1, 1)
    year_end = datetime(current_date.year, 12, 31)

    week_start = date_today - timedelta(days=current_date.weekday())

    previous_year_start = datetime(date_today.year-1, 1, 1)
    previous_year_end = datetime(date_today.year-1, 12, 31)

    first_day, last_day = calendar.monthrange(date_today.year-1, date_today.month)
    previous_year_current_month_start = datetime(date_today.year-1, current_date.month, 1)
    previous_year_current_month_end = datetime(date_today.year-1, current_date.month, last_day)
    previous_year_current_following_month = current_date.month + 1
    date_from = current_time - timedelta(days=1)
    month_ago = current_time - timedelta(days=30)
    datetime_today_6am = datetime(date_today.year, date_today.month, date_today.day, 6, 0)
    previous_day_six_am = datetime_today_6am - timedelta(days=1)


    data = {
        "start_of_all_employees": start_of_all_employees,
        "start_of_all_transactions": start_of_all_employees,
        "previous_day": previous_day,
        "today": date_today + timedelta(days=1),
        "previous_month_end": previous_month_end,
        "previous_month_start": previous_month_start,
        "init_start": start_of_all_employees,
        "month_start": month_start,
        "year_start": year_start,
        "year_end": year_end,
        "week_start": week_start.date(),
        "previous_year_start": previous_year_start,
        "previous_year_end": previous_year_end,
        "previous_year_current_month_start": previous_year_current_month_start,
        "previous_year_current_month_end": previous_year_current_month_end,
        "previous_year_current_following_month": previous_year_current_following_month,
        "date_from": date_from,
        "month_ago": month_ago,
        "date_today": date_today,
        "previous_day_six_am": previous_day_six_am,
        "datetime_today_6am": datetime_today_6am
    }

    return data
    
def filter_by_date_two(request, user, timezone):
    date_filter_gte = request.GET.get("date_filter_gte")
    date_filter_lte = request.GET.get("date_filter_lte")
    start_of_all_employees = user.objects.order_by("date_joined").first().date_joined
    current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    if date_filter_gte and date_filter_lte is not None:
        start_date = timezone.strptime(date_filter_gte, "%Y-%m-%d")
        end_date = timezone.strptime(date_filter_lte, "%Y-%m-%d")
    else:
        start_date = start_of_all_employees
        end_date = current_time

    data = {
        "start_date": start_date,
        "end_date": end_date + timedelta(days=1)
        }

    return data

def generate_bulk_id():
    from payroll_app.models import PayrollTable

    alphabet = string.ascii_lowercase + string.digits
    loop_condition = True
    while loop_condition:
        bulk_id = "".join(secrets.choice(alphabet) for i in range(15))
        if (
            any(c.islower() for c in bulk_id)
            and any(c.islower() for c in bulk_id)
            and sum(c.isdigit() for c in bulk_id) >= 12
        ):
            loop_condition = False

    if PayrollTable.objects.filter(bulk_id=bulk_id).exists():
        generate_bulk_id()

    return bulk_id

def round_amount(amount):
    return math.floor(amount * 10 ** 2) / 10 ** 2
    

def generate_one_click():
    from payroll_app.models import OneClickTransaction

    alphabet = string.ascii_lowercase + string.digits
    loop_condition = True
    while loop_condition:
        one_click_id = "".join(secrets.choice(alphabet) for i in range(15))
        if (
            any(c.islower() for c in one_click_id)
            and any(c.islower() for c in one_click_id)
            and sum(c.isdigit() for c in one_click_id) >= 12
        ):
            loop_condition = False

    if OneClickTransaction.objects.filter(transaction_id=one_click_id).exists():
        generate_one_click()

    return one_click_id


def check_employee_onboarding_percentage(employee_instance):
    from payroll_app.models import CompanyEmployeeAccountDetails
    employee_percentage = 0
    if employee_instance:
        if employee_instance.employee:
            if employee_instance.employee_first_name:
                employee_percentage += 1
            if employee_instance.employee_last_name:
                employee_percentage += 1
            if employee_instance.employee_phone_number:
                employee_percentage += 1
            if employee_instance.employee_alternate_email:
                employee_percentage += 1
            if employee_instance.employee_birth_date:
                employee_percentage += 1
            if employee_instance.employee_gender:
                employee_percentage += 1
            if employee_instance.employee_state:
                employee_percentage += 1
            if employee_instance.employee_city:
                employee_percentage += 1
            if employee_instance.employee_town:
                employee_percentage += 1
            if employee_instance.employee_bus_stop:
                employee_percentage += 1
            if employee_instance.employee_street_name:
                employee_percentage += 1
            if employee_instance.employee_house_no:
                employee_percentage += 1
            if employee_instance.employee_postal_code:
                employee_percentage += 1
            if employee_instance.employee_first_next_of_kin_firstname:
                employee_percentage += 1
            if employee_instance.employee_first_next_of_kin_lastname:
                employee_percentage += 1
            if employee_instance.employee_first_next_of_kin_phone_number:
                employee_percentage += 1
            if employee_instance.employee_first_next_of_kin_relationship:
                employee_percentage += 1
            if employee_instance.employee_first_next_of_kin_address:
                employee_percentage += 1
            if employee_instance.employee_second_next_of_kin_firstname:
                employee_percentage += 1
            if employee_instance.employee_second_next_of_kin_lastname:
                employee_percentage += 1
            if employee_instance.employee_second_next_of_kin_phone_number:
                employee_percentage += 1
            if employee_instance.employee_second_next_of_kin_relationship:
                employee_percentage += 1
            if employee_instance.employee_second_next_of_kin_address:
                employee_percentage += 1
            if employee_instance.employee_department:
                employee_percentage += 1
            if employee_instance.employee_contract_type:
                employee_percentage += 1

            get_account_ins = CompanyEmployeeAccountDetails.objects.filter(employee=employee_instance.employee, company=employee_instance.company)
            if get_account_ins:
                employee_percentage += 1
            try:
                get_percentage = round_amount((employee_percentage / 26) * 100)
            except ZeroDivisionError:
                get_percentage = 0
            return get_percentage
        else:
            return employee_percentage
    else:
        return 0
# def custom_sort_group_history(item):
#     months_order = ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december']
#     month_index = months_order.index(item['payroll_month'].lower())
#     year = int(item['payroll_year'])
#     return (year, month_index)

def generate_readable_employee_id(employee_number):
    return f"PBX-{str(employee_number).zfill(8)}"