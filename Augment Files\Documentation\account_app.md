# Account App Documentation

## Overview

The Account app manages financial accounts, transactions, and wallet operations for the Paybox360 platform. It provides the core banking functionality that supports fund transfers, wallet management, and financial record-keeping across the system.

## Core Features

### 1. Wallet Management

- Company and user wallet creation
- Wallet funding and withdrawals
- Balance tracking
- Transaction history

### 2. Transaction Processing

- Internal transfers between wallets
- External transfers to bank accounts
- Transaction verification
- Fee calculation

### 3. Bank Account Management

- Bank account verification
- Account linking
- Bank detail management

### 4. Financial Reporting

- Transaction reports
- Balance statements
- Financial summaries

### 5. Payment Gateway Integration

- VFD Bank API integration
- Liberty Pay API integration
- Mono API for account verification
- Paystack for payment processing

## How It Works

1. **Account Creation**: Company/user wallets are created during onboarding
2. **Funding**: Wallets are funded through various payment channels
3. **Transaction Processing**: System processes internal and external transfers
4. **Verification**: Transactions are verified and recorded
5. **Reporting**: Financial activities are tracked and reported
6. **Reconciliation**: System reconciles transactions with external providers

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `AccountSystem` | Main account record | `account_number`, `balance` | User, Company |
| `Wallet` | Virtual wallet | `wallet_type`, `balance` | AccountSystem |
| `Transaction` | Financial transactions | `amount`, `status`, `transaction_type` | AccountSystem |
| `BankDetail` | Bank account info | `account_number`, `bank_name` | User |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `vfd_funds_transfer` | External transfers | `models.py` (Transaction) | Called by requisition disbursement |
| `credit_wallet` | Add funds to wallet | `models.py` (Wallet) | Used in funding operations |
| `debit_wallet` | Remove funds from wallet | `models.py` (Wallet) | Used in disbursement operations |
| `verify_account_number` | Bank verification | `helpers/core_banking.py` | Used in account linking |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/acct/fund-wallet/` | Add funds to wallet | POST | `FundWalletAPIView` |
| `/acct/transfer/` | Process transfers | POST | `TransferAPIView` |
| `/acct/transactions/` | List transactions | GET | `TransactionListAPIView` |
| `/acct/verify-account/` | Verify bank account | POST | `VerifyAccountAPIView` |

### Integration Points

- **VFD Bank API**: External bank transfers
- **Liberty Pay API**: Payment processing
- **Mono API**: Account verification
- **Paystack**: Payment gateway

## Key Considerations

### 1. Transaction Security

- **Responsible App**: Account app and Core app
- **Key Functions**:
  - Transaction PIN verification in `TransferAPIView`
  - Secure API communication in `vfd_funds_transfer`
  - Transaction logging in `Transaction.save()`

### 2. Wallet Balance Management

- **Responsible App**: Account app
- **Key Functions**:
  - Balance checks in `debit_wallet` prevent overdrafts
  - Transaction atomicity ensures consistent balances
  - Reconciliation processes verify balance accuracy

### 3. Bank Account Verification

- **Responsible App**: Account app
- **Key Functions**:
  - `verify_account_number` validates bank details
  - Integration with Mono API for real-time verification
  - Account information storage with encryption

### 4. Fee Calculation

- **Responsible App**: Account app
- **Key Functions**:
  - `calculate_transaction_fee` determines applicable fees
  - Fee configuration based on transaction type and amount
  - Fee splitting for different stakeholders

### 5. Transaction Reconciliation

- **Responsible App**: Account app
- **Key Functions**:
  - Periodic reconciliation tasks check transaction status
  - Failed transaction handling and retries
  - Dispute resolution processes

### 6. Wallet Types Management

- **Responsible App**: Account app
- **Key Functions**:
  - Different wallet types (Main, Commission, etc.)
  - Wallet-specific rules for transactions
  - Balance isolation between wallet types