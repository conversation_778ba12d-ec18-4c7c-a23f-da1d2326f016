from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from leave_management.resources import LeavePolicyResource, LeaveRecordResource, LeaveRequestResource, LeaveTypeResource

from .models import LeaveType, LeavePolicy, LeaveRequest, LeaveRecord

class LeaveTypeResourceAdmin(ImportExportModelAdmin):
    resource_class = LeaveTypeResource
    search_fields = []
    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data

class LeavePolicyResourceAdmin(ImportExportModelAdmin):
    resource_class = LeavePolicyResource
    search_fields = []
    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data

class LeaveRequestResourceAdmin(ImportExportModelAdmin):
    resource_class = LeaveRequestResource
    search_fields = []
    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data

class LeaveRecordResourceAdmin(ImportExportModelAdmin):
    resource_class = LeaveRecordResource
    search_fields = []
    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data

admin.site.register(LeaveType, LeaveTypeResourceAdmin)
admin.site.register(LeavePolicy, LeavePolicyResourceAdmin)
admin.site.register(LeaveRequest, LeaveRequestResourceAdmin)
admin.site.register(LeaveRecord, LeaveRecordResourceAdmin)