from django.contrib import admin

from import_export.admin import ImportExportModelAdmin
from performance_management.models import ActivityLog, CompanyGoals, CompanyKeyResult, StatusUpdate
from performance_management.resources import *

# Register your models here.

class CompanyGoalsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyGoalsResource
    search_fields = ["company__company_name", "name", "description"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class CompanyKeyResultResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyKeyResultResource
    search_fields = ["company_goal__name", "name", "description"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class KPICheckInResourceAdmin(ImportExportModelAdmin):
    resource_class = KPICheckInResource
    search_fields = ["company_goal__name", "name", "description"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class TasksResourceAdmin(ImportExportModelAdmin):
    resource_class = TasksResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class CheckInFrequencyResourceAdmin(ImportExportModelAdmin):
    resource_class = CheckInFrequencyResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class KPICategoryResourceAdmin(ImportExportModelAdmin):
    resource_class = KPICategoryResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class ReviewResourceAdmin(ImportExportModelAdmin):
    resource_class = ReviewResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class StatusUpdateResourceAdmin(ImportExportModelAdmin):
    resource_class = StatusUpdateResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class ActivityLogResourceAdmin(ImportExportModelAdmin):
    resource_class = ActivityLogResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

admin.site.register(CompanyGoals, CompanyGoalsResourceAdmin)
admin.site.register(CompanyKeyResult, CompanyKeyResultResourceAdmin)
admin.site.register(KPICheckIn, KPICheckInResourceAdmin)
admin.site.register(Tasks, TasksResourceAdmin)
admin.site.register(CheckInFrequency, CheckInFrequencyResourceAdmin)
admin.site.register(KPICategory, KPICategoryResourceAdmin)
admin.site.register(Review, ReviewResourceAdmin)
admin.site.register(StatusUpdate, StatusUpdateResourceAdmin)
admin.site.register(ActivityLog, ActivityLogResourceAdmin)