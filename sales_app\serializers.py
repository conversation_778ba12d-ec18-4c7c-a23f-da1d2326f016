import calendar

from django.db.models import Sum
from rest_framework import serializers

from account.models import Transaction
from core.models import User
from helpers.enums import ChannelTypes
from helpers.reusable_functions import (
    format_date_and_time,
    is_valid_string,
    is_valid_uuid,
    validate_phone_number,
)
from requisition.models import Company, Team, TeamMember
from sales_app import models
from sales_app.helper.enums import (
    MeansOfPaymentChoices,
    ReturnChoices,
    ReturnRefundTypes,
    TransactionStatusChoices,
)
from sales_app.utils import (
    validate_amount,
    validate_batch_id,
    validate_customer,
    validate_number,
)
from stock_inventory.models import (
    Branch,
    Category,
    Product,
)


# Create your serializer(s) here.
class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Customer
        fields = [
            "id",
            "created_at",
            "company",
            "branch",
            "name",
            "email",
            "phone",
            "address",
            "city",
            "state",
            "country",
            "status",
        ]

    def validate(self, attrs):
        if attrs.get("email") is None and attrs.get("phone") is None:
            raise serializers.ValidationError(
                {"error": "customer details must contain 'email' or 'phone'."}
            )
        if attrs.get("phone") and is_valid_string(attrs.get("phone")):
            validate_phone_number(attrs.get("phone"))
        return super().validate(attrs)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = instance.branch.name
        representation["locations"] = CustomerLocationSerializer(
            instance.customerlocation_set.all(),
            many=True,
        ).data
        return representation


class CustomerLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.CustomerLocation
        fields = "__all__"
        read_only_fields = ["created_by"]


class AddLocationSerializer(serializers.Serializer):
    customer = serializers.PrimaryKeyRelatedField(
        queryset=models.Customer.objects.all()
    )
    location = serializers.CharField(max_length=2550)
    default = serializers.BooleanField(required=False, default=False)


class CustomerUpdateSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    customer_id = serializers.PrimaryKeyRelatedField(
        queryset=models.Customer.objects.all()
    )
    name = serializers.CharField(max_length=255, required=False)
    email = serializers.EmailField(max_length=255, required=False)
    phone = serializers.CharField(max_length=25, required=False)
    address = serializers.CharField(max_length=255, required=False)
    city = serializers.CharField(max_length=255, required=False)
    state = serializers.CharField(max_length=255, required=False)
    country = serializers.CharField(max_length=255, required=False)

    def validate(self, attrs):
        if len(attrs) <= 2:
            raise serializers.ValidationError(
                {"errors": "no field(s) was passed to be updated."}
            )
        if attrs.get("phone") and is_valid_string(attrs.get("phone")):
            validate_phone_number(attrs.get("phone"))
        attrs["customer"] = attrs.get("customer_id")
        attrs.pop("company")
        attrs.pop("customer_id")
        return super().validate(attrs)


class CustomerFileUpload(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    file = serializers.FileField()

    def validate(self, attrs):
        attrs = super().validate(attrs)
        file = attrs.get("file").name
        ext = file.split(".")[-1]
        if ext != "xlsx":
            raise serializers.ValidationError({"message": "file format not supported."})
        return super().validate(attrs)


class CustomCustomerSerializer(serializers.Serializer):
    name = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    email = serializers.EmailField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    phone = serializers.CharField(
        max_length=25,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    address = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )


class CustomerDetailsSerializer(serializers.Serializer):
    created_at = serializers.CharField(required=False)
    customer = serializers.UUIDField(required=False)
    customer_name = serializers.CharField(required=False)
    name = serializers.CharField(required=False)
    email = serializers.EmailField()
    phone = serializers.CharField(required=False)
    branch = serializers.CharField(required=False)
    transactions = serializers.DecimalField(
        max_digits=13, decimal_places=2, required=False
    )
    purchase_amount = serializers.DecimalField(
        max_digits=13, decimal_places=2, required=False
    )
    status = serializers.CharField(required=False)


class CustomerTransactionSerializer(serializers.Serializer):
    created_at = serializers.DateTimeField()
    invoice = serializers.CharField()
    payment = serializers.CharField()
    total = serializers.DecimalField(max_digits=13, decimal_places=2, required=False)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["return_refund"] = "NO"
        return representation


class SalesTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.SalesTransaction
        fields = "__all__"
        read_only_fields = [
            "created_by",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = instance.branch.name
        representation["invoice"] = (
            instance.invoice.reference if instance.invoice is not None else ""
        )
        representation["created_by"] = instance.created_by.full_name
        representation["customer"] = (
            instance.customer.name if instance.customer is not None else ""
        )
        representation["sales"] = SalesTransactionItemsSerializer(
            instance.salestransactionitem_set.all(), many=True
        ).data
        return representation


class SalesItemSerializer(serializers.Serializer):
    category_id = serializers.PrimaryKeyRelatedField(queryset=Category.objects.all())
    item_id = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all())
    quantity = serializers.IntegerField(validators=[validate_number])
    amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[validate_amount],
    )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["item_description"] = instance.item.name
        return representation

    def validate(self, attrs):
        # DO NOT REMOVE !!!
        # used as a key formatter for the serialized data.
        attrs["category"] = attrs.get("category_id")
        attrs["item"] = attrs.get("item_id")
        return super().validate(attrs)


class RegisterSalesSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    sales = SalesItemSerializer(many=True)
    device = serializers.ChoiceField(choices=ChannelTypes.choices)
    sales_tag = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    customer = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    discount_value = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
        allow_null=True,
        validators=[validate_amount],
    )
    delivery_fee = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
        allow_null=True,
        validators=[validate_amount],
    )
    save = serializers.BooleanField(required=False)

    def validate(self, attrs):
        if attrs.get("sales") == []:
            raise serializers.ValidationError({"message": "sales cannot be empty."})
        if "customer" in attrs and not is_valid_string(attrs.get("customer")):
            attrs.pop("customer")
        # handled null values sent from mobile/terminal client(s).
        if "discount_value" in attrs and attrs.get("discount_value") == None:
            attrs.pop("discount_value")
        if "delivery_fee" in attrs and attrs.get("delivery_fee") == None:
            attrs.pop("delivery_fee")
        if is_valid_string(attrs.get("customer")):
            attrs["customer"] = validate_customer(attrs.get("customer"))
        return super().validate(attrs)


class SplitPaymentSerializer(serializers.Serializer):
    cash = serializers.BooleanField(default=False)
    cash_amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        default=0.0,
    ) 
    others = serializers.BooleanField(default=False)
    others_amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        default=0.0,
    ) 
    transfer = serializers.BooleanField(default=False)
    transfer_amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        default=0.0,
    ) 
    other_transfer = serializers.BooleanField(default=False)
    other_transfer_amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        default=0.0,
    ) 


class ConfirmSalesSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    batch_id = serializers.CharField()
    means_of_payment = serializers.ChoiceField(choices=MeansOfPaymentChoices.choices)
    customer = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    split_method = SplitPaymentSerializer(required=False)

    def validate(self, attrs):
        if attrs.get("means_of_payment") == MeansOfPaymentChoices.SPLIT:
            if attrs.get("split_method") is None:
                raise serializers.ValidationError(
                    {"message": "provide a valid split method."}
                )
        if attrs.get("customer") and is_valid_string(attrs.get("customer")):
            if not is_valid_uuid(attrs.get("customer")):
                raise serializers.ValidationError(
                    {"message": "provide a valid customer ID."}
                )
            attrs["customer"] = validate_customer(attrs.get("customer"))
        return super().validate(attrs)


class UpdateSalesTransaction(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    batch_id = serializers.CharField(max_length=255)
    action_type = serializers.ChoiceField(choices=TransactionStatusChoices.choices)


class SalesTableSerializer(serializers.Serializer):
    company = serializers.CharField()
    created_at = serializers.DateTimeField()
    branch = serializers.CharField()
    cashier = serializers.CharField(required=False)
    amount = serializers.FloatField()
    invoice = serializers.CharField(required=False)
    payment_method = serializers.CharField()
    transaction_id = serializers.CharField()
    status = serializers.CharField()


class SalesBranchSerializer(serializers.ModelSerializer):
    class Meta:
        model = Branch
        fields = [
            "company",
            "name",
            "address",
            "vat",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        branch_sales = models.SalesTransaction.objects.filter(
            branch=instance,
            status=TransactionStatusChoices.COMPLETED,
        )
        branch_refunds = models.ReturnRefund.objects.filter(branch=instance)
        sales_value = (
            branch_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.00
        )
        refund_value = (
            branch_refunds.aggregate(total_amount=Sum("amount"))["total_amount"] or 0.00
        )
        branch_teams = Team.objects.filter(branch=instance, is_active=True)
        branch_members = (
            TeamMember.objects.filter(
                team__in=branch_teams,
                is_active=True,
            )
            .order_by("member__id")
            .distinct("member")
        )
        members = [
            {
                "name": member.member.full_name if member.member is not None else "",
                "email": member.email if member is not None else "",
                "role": member.role if member is not None else "",
            }
            for member in branch_members
        ]
        representation["total_sales_value"] = sales_value
        representation["total_sales_count"] = branch_sales.count()
        representation["refund_value"] = refund_value
        representation["refund_count"] = branch_refunds.count()
        representation["members"] = members
        return representation


class TopProductsSerializer(serializers.Serializer):
    product = serializers.CharField()
    percentage = serializers.FloatField()
    amount = serializers.DecimalField(max_digits=13, decimal_places=2, required=False)


class BranchComparisonSerializer(serializers.Serializer):
    month = serializers.CharField(required=False)
    company_sales = serializers.DecimalField(
        max_digits=13, decimal_places=2, required=False
    )
    branch_name = serializers.CharField(required=False)
    sales_value = serializers.DecimalField(
        max_digits=13, decimal_places=2, required=False
    )
    average_sales = serializers.DecimalField(
        max_digits=13, decimal_places=2, required=False
    )
    gross_profit = serializers.DecimalField(
        max_digits=13, decimal_places=2, required=False
    )
    total_products_sold = serializers.IntegerField(required=False)
    # percentage = serializers.IntegerField(required=False)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["month"] = calendar.month_name[instance.get("month")]
        return representation


class PaymentLinkRequestSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    batch_id = serializers.CharField(max_length=25)
    amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[validate_amount],
    )
    email = serializers.EmailField()
    is_new_customer = serializers.BooleanField(default=False)
    name = serializers.CharField(max_length=255, required=False)
    phone = serializers.CharField(max_length=25, required=False)
    address = serializers.CharField(max_length=255, required=False)

    def validate(self, attrs):
        attrs["batch_id"] = validate_batch_id(attrs.get("batch_id"))
        return super().validate(attrs)


class ReturnRefundItemSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.ReturnRefundItem
        fields = "__all__"
        read_only_fields = [
            "return_refund",
            "amount",
        ]


class RefundApproveDeclineSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    invoice_reference = serializers.CharField(max_length=25)
    decline_approval_reason = serializers.CharField(
        max_length=25,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    status = serializers.ChoiceField(choices=TransactionStatusChoices.choices)

    def validate(self, attrs):
        if attrs.get("status") not in [
            TransactionStatusChoices.APPROVED,
            TransactionStatusChoices.DENIED,
        ]:
            raise serializers.ValidationError(
                {"message": "invalid choice for request status."}
            )
        return super().validate(attrs)


class RefundItemsSerializer(serializers.ModelSerializer):
    items = ReturnRefundItemSerializer(many=True)

    class Meta:
        model = models.ReturnRefund
        fields = "__all__"
        read_only_fields = [
            "created_by",
        ]

    def validate(self, attrs):
        if attrs.get("choice") == ReturnRefundTypes.REFUND:
            if not attrs.get("payment_method"):
                raise serializers.ValidationError(
                    {"error": "provide a payment method."}
                )
        if attrs.get("reason") == ReturnChoices.OTHERS:
            if not attrs.get("additional_info"):
                raise serializers.ValidationError({"error": "provide additional info."})
        if attrs.get("items") == []:
            raise serializers.ValidationError({"error": "items cannot be empty."})
        return super().validate(attrs)


class SwapItemsSerializer(serializers.ModelSerializer):
    old_items = ReturnRefundItemSerializer(many=True)
    new_items = SalesItemSerializer(many=True)

    class Meta:
        model = models.ReturnRefund
        fields = "__all__"
        read_only_fields = [
            "created_by",
        ]

    def validate(self, attrs):
        if attrs.get("reason") == ReturnChoices.OTHERS:
            if not attrs.get("additional_info"):
                raise serializers.ValidationError({"error": "provide additional info."})
        if attrs.get("old_items") == []:
            raise serializers.ValidationError(
                {"errors": "Sales old items cannot be empty."}
            )
        if attrs.get("new_items") == []:
            raise serializers.ValidationError(
                {"errors": "Sales new items cannot be empty."}
            )
        return super().validate(attrs)


class ReturnRefundSerializer(serializers.ModelSerializer):
    invoice = serializers.CharField(source="invoice.reference")
    attendee = serializers.CharField(source="created_by.full_name")
    customer_name = serializers.CharField(source="customer.name")

    class Meta:
        model = models.ReturnRefund
        fields = [
            "created_at",
            "invoice",
            "customer_name",
            "attendee",
            "choice",
            "amount",
            "status",
        ]


class RefundsProductSerializer(serializers.Serializer):
    item = serializers.CharField()
    item_name = serializers.CharField()
    count = serializers.IntegerField()
    amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )


class CustomerSalesTransactionSerializer(CustomCustomerSerializer):
    created_at = serializers.DateTimeField()
    invoice_id = serializers.CharField(source="invoice.id")
    invoice_reference = serializers.CharField(source="invoice.reference")
    batch_id = serializers.CharField()
    amount = serializers.DecimalField(
        max_digits=13, decimal_places=2, source="total_cost"
    )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["name"] = (
            instance.customer.name if instance.customer is not None else None
        )
        representation["email"] = (
            instance.customer.email if instance.customer is not None else None
        )
        representation["phone"] = (
            instance.customer.phone if instance.customer is not None else None
        )
        representation["customer_id"] = (
            instance.customer.id if instance.customer is not None else None
        )
        return representation


class SalesTransactionItemsSerializer(serializers.Serializer):
    cashier = serializers.CharField(source="sales_transaction.created_by.full_name")
    created_at = serializers.DateTimeField()
    category_id = serializers.UUIDField(source="category.id")
    category = serializers.CharField(source="category.name")
    item_id = serializers.UUIDField(source="item.id")
    item_description = serializers.CharField(source="item.name")
    batch_id = serializers.CharField(source="sales_transaction.batch_id")
    status = serializers.CharField(source="sales_transaction.status")
    payment_method = serializers.CharField(source="sales_transaction.means_of_payment")
    amount = serializers.DecimalField(
        max_digits=20, decimal_places=2, source="total_value"
    )
    quantity = serializers.IntegerField()
    discount = serializers.DecimalField(
        max_digits=20,
        decimal_places=2,
        source="sales_transaction.discount_value",
    )
    vat = serializers.DecimalField(
        max_digits=20,
        decimal_places=2,
        source="sales_transaction.vat_amount",
    )
    delivery_fee = serializers.DecimalField(
        max_digits=20,
        decimal_places=2,
        source="sales_transaction.delivery_fee",
    )
    sub_total = serializers.DecimalField(
        max_digits=20,
        decimal_places=2,
        source="sales_transaction.total_sales_amount",
    )
    total = serializers.DecimalField(
        max_digits=20,
        decimal_places=2,
        source="sales_transaction.total_cost",
    )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["reference"] = (
            instance.sales_transaction.invoice.reference if 
            instance.sales_transaction.invoice is not None else ""
        )
        representation["customer"] = (
            instance.sales_transaction.customer.name if
            instance.sales_transaction.customer is not None else ""
        )
        return representation


class BranchInstantAccountSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    batch_id = serializers.CharField(max_length=255)

    def validate(self, attrs):
        attrs["batch_id"] = validate_batch_id(attrs.get("batch_id"))
        return super().validate(attrs)


class RegisterRRNSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    batch_id = serializers.CharField(max_length=255)
    rrn = serializers.CharField(max_length=255)

    def validate(self, attrs):
        attrs["batch_id"] = validate_batch_id(attrs.get("batch_id"))
        return super().validate(attrs)


class CashBookSerializer(serializers.ModelSerializer):
    sales_transaction = serializers.CharField(source="sales_transaction.batch_id")

    class Meta:
        model = models.CashBook
        fields = [
            "created_at",
            "company",
            "branch",
            "sales_transaction",
            "transaction_type",
            "amount",
            "payment_channel",
            "reference",
            "status",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = instance.branch.name
        return representation


class OfflineTransferSerializer(serializers.Serializer):
    account_number = serializers.CharField(max_length=10)
    amount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[validate_amount],
    )
    unique_code = serializers.CharField(max_length=8)
    confirmation_code = serializers.CharField(max_length=8)


class OfflineSalesSerializer(RegisterSalesSerializer):
    created_by = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())
    offline_identifier = serializers.CharField(max_length=255)
    means_of_payment = serializers.ChoiceField(choices=MeansOfPaymentChoices.choices)
    date_and_time = serializers.CharField(max_length=25)
    transaction_detail = OfflineTransferSerializer(
        required=False,
        allow_null=True,
    )

    def validate(self, attrs):
        attrs = super().validate(attrs)
        attrs["date_and_time"] = format_date_and_time(attrs.get("date_and_time"))
        return attrs


class ManageSavedSalesSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    sales = SalesItemSerializer(many=True)
    batch_id = serializers.CharField(max_length=25)
    customer = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    discount_value = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
        allow_null=True,
        validators=[validate_amount],
    )
    delivery_fee = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
        allow_null=True,
        validators=[validate_amount],
    )
    save = serializers.BooleanField(default=False)
    
    def validate(self, attrs):
        if attrs.get("sales") == []:
            raise serializers.ValidationError({"message": "sales cannot be empty."})
        if attrs.get("customer") and not is_valid_string(attrs.get("customer")):
            attrs.pop("customer")
        if "discount_value" in attrs and attrs.get("discount_value") == None:
            attrs.pop("discount_value")
        if "delivery_fee" in attrs and attrs.get("delivery_fee") == None:
            attrs.pop("delivery_fee")
        if attrs.get("customer") and is_valid_string(attrs.get("customer")):
            if not is_valid_uuid(attrs.get("customer")):
                raise serializers.ValidationError(
                    {"message": "provide a valid customer ID."}
                )
            attrs["customer"] = validate_customer(attrs.get("customer"))
        return super().validate(attrs)


class OfflineVirtualAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.OfflineVirtualAccount
        fields = "__all__"
        
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company_name"] = instance.company.company_name
        representation["branch_name"] = instance.branch.name
        return representation


class WalletWithdrawalSerializer(serializers.Serializer):
    WITHDRAWAL_CHOICES = (
        ("MAIN", "MAIN"),
        ("EXTERNAL", "EXTERNAL")
    )

    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())
    amount = serializers.IntegerField(validators=[validate_amount])
    transaction_pin = serializers.CharField(max_length=4, min_length=4)
    withdrawal_type = serializers.ChoiceField(choices=WITHDRAWAL_CHOICES)
    bank_name = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    bank_code = serializers.CharField(
        max_length=8,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    account_name = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    account_number = serializers.CharField(
        max_length=10,
        min_length=10,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    is_saved = serializers.BooleanField(default=False)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if attrs["withdrawal_type"] == "EXTERNAL":
            if "bank_name" not in attrs:
                raise serializers.ValidationError(
                    {"message": "provide a valid bank name"}
                )
            if "bank_code" not in attrs:
                raise serializers.ValidationError(
                    {"message": "provide a valid bank code"}
                )
            if "account_name" not in attrs:
                raise serializers.ValidationError(
                    {"message": "provide a valid account name"}
                )
            if "account_number" not in attrs:
                raise serializers.ValidationError(
                    {"message": "provide a valid account number"}
                )
        return attrs


class WalletBeneficiarySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.WalletWithdrawal
        fields = [
            "bank_name",
            "bank_code",
            "account_name",
            "account_number",
        ]


class TopFourBranchesSerializer(serializers.Serializer):
    branch = serializers.UUIDField()
    branch_name = serializers.CharField(max_length=255)
    amount = serializers.DecimalField(max_digits=13, decimal_places=2)
    percentage = serializers.DecimalField(max_digits=5, decimal_places=2)


class TopFourCustomersSerializer(serializers.Serializer):
    customer = serializers.UUIDField()
    customer_name = serializers.CharField(max_length=255)
    orders = serializers.IntegerField()
    revenue = serializers.DecimalField(max_digits=13, decimal_places=2)
    last_purchased = serializers.DateTimeField()


class StockAndSalesSerializer(serializers.Serializer):
    month = serializers.CharField(max_length=25)
    stocks_value = serializers.DecimalField(max_digits=20, decimal_places=2)
    sales_value = serializers.DecimalField(max_digits=20, decimal_places=2)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["month"] = calendar.month_name[instance.get("month")]
        return representation


class CompanyCashBookSerializer(serializers.Serializer):
    branch_name = serializers.CharField(max_length=255)
    cash_balance = serializers.DecimalField(max_digits=20, decimal_places=2)
    last_updated = serializers.DateTimeField()


class TopTenProductsSerializer(serializers.Serializer):
    count = serializers.IntegerField()
    product = serializers.CharField()
    product_id = serializers.CharField()
    branch = serializers.CharField()
    total_quantity = serializers.IntegerField()
    price = serializers.DecimalField(max_digits=13, decimal_places=2)
    sales_value = serializers.DecimalField(max_digits=20, decimal_places=2)
    profit = serializers.DecimalField(max_digits=20, decimal_places=2)


class ActiveClerksSerializer(serializers.Serializer):
    clerk = serializers.CharField()
    clerk_name = serializers.CharField()
    branch_name = serializers.CharField()
    total_sales = serializers.IntegerField()
    amount_sold = serializers.DecimalField(max_digits=20, decimal_places=2)
    last_transaction = serializers.DateTimeField()


class SalesWalletHistorySerializer(serializers.ModelSerializer):
    amount = serializers.DecimalField(max_digits=13, decimal_places=2)
    transaction_id = serializers.CharField(source="id")

    class Meta:
        model = Transaction
        fields = [
            "date_created",
            "transaction_id",
            "company_name",
            "beneficiary_account_name",
            "beneficiary_account_number",
            "transaction_type",
            "balance_before",
            "amount",
            "balance_after",
            "status",
            "narration",
        ]


class SalesOrderSerializer(serializers.Serializer):
    created_at = serializers.DateTimeField()
    order_stage = serializers.CharField()
    position = serializers.IntegerField()
    sales_tag = serializers.CharField()
    batch_id = serializers.CharField()
    total_sales_amount = serializers.DecimalField(max_digits=13, decimal_places=2)


class SalesOrderItemDetailsSerializer(serializers.Serializer):
    category_id = serializers.CharField(source="item.category.id")
    item_id = serializers.CharField(source="item.id")
    item_description = serializers.CharField(source="item.name")
    quantity = serializers.IntegerField()
    amount = serializers.DecimalField(max_digits=20, decimal_places=2)


class SalesOrderDetailsSerializer(serializers.ModelSerializer):
    current_stage = serializers.CharField(source="current_stage.name")

    class Meta:
        model = models.SalesTransaction
        fields = [
            "created_at",
            "batch_id",
            "sales_tag",
            "current_stage",
            "paid",
            "total_cost",
            "device",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.customer is not None:
            representation["customer_name"] = instance.customer.name
            representation["customer_email"] = instance.customer.email
            representation["customer_phone"] = instance.customer.phone
        else:
            representation["customer_name"] = ""
            representation["customer_email"] = ""
            representation["customer_phone"] = ""
        representation["products"] = SalesOrderItemDetailsSerializer(
            instance.salestransactionitem_set.all(), many=True
        ).data
        return representation
