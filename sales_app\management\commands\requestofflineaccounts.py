import json
import requests

from django.core.management.base import BaseCommand, CommandParser

from account.helpers.core_banking import CoreBankingService
from stock_inventory.models import Branch


class Command(BaseCommand):
    help = "REQUEST OFFLINE VIRTUAL ACCOUNTS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("branch_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        branch_id = kwargs["branch_id"]
        branch = Branch.objects.filter(id=branch_id)
        if branch.exists():
            url = "https://banking.libertypayng.com/api/v1/wema/offline_accounts/"
            token = CoreBankingService().login()
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            payload = json.dumps({
                "sub_company_unique_id": str(branch.first().id)
            })
            response = requests.request("POST", url, headers=headers, data=payload)
            self.stdout.write(
                self.style.SUCCESS(str(response.text))
            )
            self.stdout.write(
                self.style.SUCCESS("REQUEST SUCCESSFUL.")
            )
        else:
            self.stdout.write(
                self.style.ERROR("INVALID BRANCH ID WAS PROVIDED.")
            )

