# import uuid
# from typing import Dict
# from performance_sales_metrics_dashboard.serializers import PipelineSerializer

from django.contrib.auth import get_user_model
from django.db.models import UUIDField
from django.urls import reverse

from performance_sales_metrics_dashboard import signals
from performance_sales_metrics_dashboard.enums import Lead_status
from performance_sales_metrics_dashboard.models import (
    ProductVerticals,
    SalesLead,
    SalesOfficer,
    Pipeline,
    Lead, Category)
from rest_framework import status
from rest_framework.test import APITestCase
from uuid import UUID

from performance_sales_metrics_dashboard.serializers import ReadLeadSerializer
from performance_sales_metrics_dashboard.signals import create_default_stages

# Create your tests here.
User = get_user_model()


class PipelineViewsTest(APITestCase):
    def setUp(self):
        self.user_instance_1 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Alpha",
        )
        self.client.force_authenticate(user=self.user_instance_1)

        self.user_instance_2 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_2)

        self.product_verticals = ProductVerticals.objects.create(name="Test Product")

        self.sales_lead_instance_1 = SalesLead.objects.create(
            user=self.user_instance_1,
            name=self.user_instance_1.get_full_name(),
            email=self.user_instance_1.email,
        )

        self.sales_officer_instance_1 = SalesOfficer.objects.create(
            user=self.user_instance_2,
            sales_lead=self.sales_lead_instance_1,
            name=self.user_instance_2.get_full_name(),
            email=self.user_instance_2.email,
            product_module=self.product_verticals,
            referral_code="123-krutqhsz",
        )

    def post_url_response(self, data):
        url = reverse("create_pipeline")
        response = self.client.post(url, data, format="json")
        return response

    def get_url_response(self, data):
        url = reverse("get_a_pipeline")
        response = self.client.post(url, data, format="json")
        return response

    def get_all_url_response(self):
        url = reverse("get_multiple_pipelines")
        response = self.client.post(url, format="json")
        return response

    def test_create_pipeline_with_sales_officer_id(self):
        data = {
            "name": f"{self.sales_officer_instance_1.name} pipeline",
            "description": "Pipeline Description",
            "type": "CUSTOM",
        }

        response = self.post_url_response(data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual(response.data["message"], "Pipeline created successfully.")
        print("Message Details, Check!")

    def test_create_pipeline_with_CUSTOM_type(self):
        data = {
            "name": f"{self.sales_officer_instance_1.name} pipeline",
            "description": "Pipeline Description",
            "type": "CUSTOM",
        }

        response = self.post_url_response(data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual(response.data["message"], "Pipeline created successfully.")
        print("Message Details, Check!")

    def test_create_pipeline_with_DEFAULT_type_creating_default_stages(self):
        data = {
            "name": f"{self.sales_officer_instance_1.name} pipeline",
            "description": "Pipeline Description",
            "type": "DEFAULT",
        }

        response = self.post_url_response(data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual(response.data["message"], "Pipeline created successfully.")
        print("Message Details, Check!")

    def test_create_default_stages_signal_connection(self):
        signals.pre_save.connect(create_default_stages)

        result = signals.pre_save.disconnect(create_default_stages)
        self.assertTrue(result)

    def test_cannot_create_pipeline_with_wrong_information_for_type(self):
        data = {
            "name": f"{self.sales_officer_instance_1.name} pipeline",
            "description": "Pipeline Description",
            "type": "BYPASS",
        }

        response = self.post_url_response(data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertIn("error_details", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual(response.data["message"], "The data entered is incorrect, please try again.")
        print("Message Details, Check!")

    def test_cannot_create_pipeline_with_wrong_information_null_value_for_name(self):
        data: dict[str, str | bool | UUID | UUIDField] = {
            "name": "",
            "description": "Pipeline Description",
            "type": "CUSTOM",
        }

        response = self.post_url_response(data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual(
            response.data["message"], "The data entered is incorrect, please try again."
        )
        print("Message Details, Check!")

    # def test_get_an_instance_of_a_pipeline(self):
    #     # self.sales_officer_instance_1 = SalesOfficer.objects.create(
    #     #     user=self.user_instance_2,
    #     #     sales_lead=self.sales_lead_instance_1,
    #     #     name=self.user_instance_2.get_full_name(),
    #     #     email=self.user_instance_2.email,
    #     #     product_module=self.product_verticals,
    #     #     referral_code="123-krutqhsz",
    #     # )
    #
    #     pipeline = self.test_create_pipeline_with_sales_officer_id()
    #
    #     data = {}
    #
    #     response = self.get_all_url_response()
    #
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)
    #     print("Status Code, Check!")
    #
    #     self.assertIn("Number of results", response.data)
    #     self.assertIn("Results", response.data)
    #     print("Response Contains Message, Check!")
    #
    #     self.assertEqual(response.data["Number of results"], 1)
    #     print("Message Details, Check!")


class GetPipelineAPIViewTests(APITestCase):

    def get_url_response(self, data):
        url = reverse("get_a_pipeline")
        response = self.client.get(url, data, format="json")
        return response

    def get_all_url_response(self):
        url = reverse("get_multiple_pipelines")
        data = {}
        response = self.client.get(url, data, format="json")
        return response

    def setUp(self):
        # Create test users
        self.user_instance_1 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Alpha",
        )
        self.client.force_authenticate(user=self.user_instance_1)

        self.user_instance_2 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_2)

        self.product_verticals = ProductVerticals.objects.create(name="Test Product")

        self.sales_lead_instance_1 = SalesLead.objects.create(
            user=self.user_instance_1,
            name=self.user_instance_1.get_full_name(),
            email=self.user_instance_1.email,
        )

        self.sales_officer_instance_1 = SalesOfficer.objects.create(
            user=self.user_instance_2,
            sales_lead=self.sales_lead_instance_1,
            name=self.user_instance_2.get_full_name(),
            email=self.user_instance_2.email,
            product_module=self.product_verticals,
            referral_code="123-krutqhsz",
        )

        pipeline_1_data = {
            "name": "Pipeline 1",
            "description": "Pipeline Description",
            "type": "CUSTOM",
        }
        pipeline_2_data = {
            "name": "Pipeline 2",
            "description": "Pipeline Description",
            "type": "CUSTOM",
        }

        self.pipeline_1 = Pipeline.create(
            validated_data=pipeline_1_data,
            sales_officer=self.sales_officer_instance_1
        )

        self.pipeline_2 = Pipeline.create(
            validated_data=pipeline_2_data,
            sales_officer=self.sales_officer_instance_1
        )

    def test_get_pipeline_by_id(self):
        self.assertIsInstance(self.pipeline_1, Pipeline)

        url = reverse("get_a_pipeline")
        data = {
            "pipeline_id": self.pipeline_1.id
        }

        response = self.get_url_response(data)

        self.assertEqual(self.pipeline_1.name, "Pipeline 1")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'Pipeline exists')

    def test_get_pipeline_does_not_exist(self):
        url = reverse("get_a_pipeline")
        data = {
            "pipeline_id": "15"
        }

        response = self.client.get(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'Pipeline does not exist.')

    def test_get_all_pipelines(self):
        response = self.get_all_url_response()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['paginated_results']['count'], 2)

        # serializer_mimic = [
        #     {
        #         'id': str(self.pipeline_1.id),
        #         "name": "Pipeline 1",
        #         "description": "Pipeline Description",
        #         "type": "CUSTOM",
        #     },
        #     {
        #         'id': str(self.pipeline_2.id),
        #         "name": "Pipeline 2",
        #         "description": "Pipeline Description",
        #         "type": "CUSTOM",
        #     }
        # ]
        #
        # serialized_pipelines = PipelineSerializer(data=(self.pipeline_1, self.pipeline_2,), many=True)
        # validate_serialized_pipelines = serialized_pipelines.is_valid()
        # print(validate_serialized_pipelines)

        # self.assertEqual(response.data['Results'], serializer_mimic)

    #
    # def test_get_all_pipelines_for_sales_officer(self):
    #     # Authenticate the test user
    #     self.client.force_authenticate(user=self.user)
    #
    #     # Test the retrieval of all pipelines for the sales officer
    #     response = self.client.get(self.url)
    #
    #     # Check that the status is 200 OK
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)
    #
    #     # Validate the number of results and returned data
    #     expected_data = PipelineSerializer([self.pipeline_1, self.pipeline_2], many=True).data
    #     self.assertEqual(response.data['Number of results'], 2)
    #     self.assertEqual(response.data['Results'], expected_data)
    #
    # def test_sales_officer_does_not_exist(self):
    #     # Authenticate another user who is not a sales officer
    #     self.client.force_authenticate(user=self.another_user)
    #
    #     # Test when SalesOfficer does not exist for this user
    #     response = self.client.get(self.url)
    #     self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    #     self.assertEqual(response.data['message'], 'Sales Officer does not exist.')
    #
    # def test_unauthenticated_user(self):
    #     # Test without authentication
    #     response = self.client.get(self.url)
    #     self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class LeadViewsTest(APITestCase):
    def setUp(self):
        self.user_instance_1 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Alpha",
        )
        self.client.force_authenticate(user=self.user_instance_1)

        self.user_instance_2 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_2)

        self.product_verticals = ProductVerticals.objects.create(name="Test Product")

        self.sales_lead_instance_1 = SalesLead.objects.create(
            user=self.user_instance_1,
            name=self.user_instance_1.get_full_name(),
            email=self.user_instance_1.email,
        )
        # self.sales_lead_instance_1.product_verticals.set(self.product_verticals.id),

        self.sales_officer_instance_1 = SalesOfficer.objects.create(
            user=self.user_instance_2,
            sales_lead=self.sales_lead_instance_1,
            name=self.user_instance_2.get_full_name(),
            email=self.user_instance_2.email,
            product_module=self.product_verticals,
            referral_code="123-krutqhsz",
        )

    def url_response(self, data, url):
        response = self.client.post(url, data, format="json")
        return response

    # TODO: Make It Work
    # def test_cannot_create_lead_without_sales_officer_id(self):
    #     url = reverse("create_lead")
    #
    #     data = {
    #         "contact_name": "John Doe",
    #         "contact_phone_number": "1234567890",
    #         "company_name": "ACME Corp",
    #         "company_email": "<EMAIL>",
    #     }
    #
    #     response = self.url_response(data, url)
    #
    #     self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    #     print("Status Code, Check!")
    #     self.assertIn("message", response.data)
    #     print("Response Contains Message, Check!")
    #     self.assertEqual(response.data["message"], "Sales Officer does not exist.")
    #     print("Message Details, Check!")

    def test_create_lead_with_valid_sales_officer(self):
        url = reverse("create_lead")

        data = {
            "contact_name": "John Doe",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "1234567890",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "30000",
        }

        response = self.url_response(data, url)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual(response.data["message"], "Lead created successfully.")
        print("Message Details, Check!")

    def test_create_lead_with_invalid_sales_officer(self):
        self.user_instance_3 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Gamma",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_3)

        url = reverse("create_lead")

        data = {
            # "sales_officer": self.sales_officer_instance_1.id,
            "contact_name": "John Doe",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "1234567890",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "30000",
        }

        response = self.client.post(url, data=data, format='json')

        # self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertFalse(Lead.objects.filter(contact_name='Jane Doe').exists())

    def test_create_lead_invalid_email_data(self):
        url = reverse("create_lead")

        data = {
            "contact_name": "John Doe",
            "contact_email": "invalidemail",
            "contact_phone_number": "1234567890",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "30000",
        }

        response = self.client.post(url, data=data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'The data entered is incorrect, please try again.')
        self.assertFalse(Lead.objects.filter(contact_email='invalidemail').exists())
        self.assertFalse(Lead.objects.filter(contact_name='John Doe').exists())

    def test_create_lead_invalid_contact_name_data(self):
        url = reverse("create_lead")

        data = {
            "contact_name": "",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "1234567890",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "30000",
        }

        response = self.client.post(url, data=data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'The data entered is incorrect, please try again.')
        self.assertFalse(Lead.objects.filter(contact_email='<EMAIL>').exists())

    def test_create_lead_invalid_contact_phone_number_data(self):
        url = reverse("create_lead")

        data = {
            "contact_name": "John Doe",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "30000",
        }

        response = self.client.post(url, data=data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['message'], 'Lead created successfully.')
        self.assertTrue(Lead.objects.filter(contact_email='<EMAIL>').exists())
        # self.assertFalse(Lead.objects.filter(contact_email='<EMAIL>').exists())

    def test_create_lead_invalid_company_name_data(self):
        url = reverse("create_lead")

        data = {
            "contact_name": "Joh Doe",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "1234567890",
            "company_name": "",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "30000",
        }

        response = self.client.post(url, data=data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'The data entered is incorrect, please try again.')
        self.assertFalse(Lead.objects.filter(contact_email='<EMAIL>').exists())

    def test_can_create_lead_with_null_company_phone_number_data(self):
        url = reverse("create_lead")

        data = {
            "contact_name": "John Doe",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "4567890976",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "30000",
        }

        response = self.client.post(url, data=data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['message'], 'Lead created successfully.')
        self.assertTrue(Lead.objects.filter(contact_email='<EMAIL>').exists())
        # self.assertFalse(Lead.objects.filter(contact_email='<EMAIL>').exists())

    def test_can_create_lead_with_null_company_address_data(self):
        url = reverse("create_lead")

        data = {
            "contact_name": "John Doe",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "4567890976",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "34567890",
            "company_address": "",
            "deal_value": "30000",
        }

        response = self.client.post(url, data=data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['message'], 'Lead created successfully.')
        self.assertTrue(Lead.objects.filter(contact_email='<EMAIL>').exists())
        # self.assertFalse(Lead.objects.filter(contact_email='<EMAIL>').exists())

    def test_create_lead_invalid_company_deal_value_data(self):
        url = reverse("create_lead")

        data = {
            "contact_name": "John Doe",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "4567890976",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "",
        }

        response = self.client.post(url, data=data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'The data entered is incorrect, please try again.')
        self.assertFalse(Lead.objects.filter(contact_email='<EMAIL>').exists())


class GetLeadAPIViewTests(APITestCase):

    def get_url_response(self, data):
        url = reverse('get_a_lead')
        # url = '/psmd/get_lead/'

        response = self.client.get(url, data, format="json")
        return response

    def get_all_url_response(self):
        url = reverse('get_multiple_leads')
        # url = '/psmd/get_multi-leads/'
        data = {}
        response = self.client.get(url, data)  # , format="json")
        return response

    def setUp(self):
        # Create test users
        self.user_instance_1 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Alpha",
        )
        self.client.force_authenticate(user=self.user_instance_1)

        self.user_instance_2 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_2)

        self.product_verticals = ProductVerticals.objects.create(name="Test Product")

        self.sales_lead_instance_1 = SalesLead.objects.create(
            user=self.user_instance_1,
            name=self.user_instance_1.get_full_name(),
            email=self.user_instance_1.email,
        )

        self.sales_officer_instance_1 = SalesOfficer.objects.create(
            user=self.user_instance_2,
            sales_lead=self.sales_lead_instance_1,
            name=self.user_instance_2.get_full_name(),
            email=self.user_instance_2.email,
            product_module=self.product_verticals,
            referral_code="123-krutqhsz",
        )

        data_1 = {
            "contact_name": "John Doe",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "1234567890",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "30000",
        }
        self.lead_1 = Lead.create_lead(sales_officer=self.sales_officer_instance_1, validated_data=data_1)

        data_2 = {
            "contact_name": "Jane Doe",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "1234567890",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "40000",
        }
        self.lead_2 = Lead.create_lead(sales_officer=self.sales_officer_instance_1, validated_data=data_2)

        data_3 = {
            "contact_name": "John John",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "1234567890",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "80000",

        }
        self.lead_3 = Lead.create_lead(sales_officer=self.sales_officer_instance_1, validated_data=data_3)

        data_4 = {
            "contact_name": "Jane Jane",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "1234567890",
            "company_name": "Security Corp",
            "company_email": "<EMAIL>",
            "company_phone_number": "************",
            "company_address": "1, Address, address neighbourhood",
            "deal_value": "50000",
        }
        self.lead_4 = Lead.create_lead(sales_officer=self.sales_officer_instance_1, validated_data=data_4)
        self.lead_4.status = Lead_status.ACTIVE
        self.lead_4.save()

    def test_get_lead_by_id(self):
        self.assertIsInstance(self.lead_1, Lead)
        self.assertIsNotNone(self.lead_1.contact_name)
        self.assertEqual(self.lead_1.contact_name, "John Doe")
        self.assertEqual(self.lead_1.status, "PENDING")
        print("Lead Instance id =========> ", self.lead_1.id)

        data = {
            "lead_id": self.lead_1.id,
        }

        response = self.get_url_response(data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], "Lead Found")

    def test_get_lead_by_pending_status(self):
        data = {
            "lead_status": Lead_status.PENDING,
        }

        response = self.get_url_response(data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['paginated_results'].get('count'), 3)

        # serialized_leads = ReadLeadSerializer(data=(self.lead_1, self.lead_2, self.lead_3, ), many=True)
        # serialized_leads = ReadLeadSerializer(data=[self.lead_1, self.lead_2, self.lead_3], many=True)
        # validate_serialized_leads = serialized_leads.is_valid()
        # self.assertEqual(response.data['Results'], serialized_leads.data)

    def test_get_lead_by_active_status(self):
        data = {
            "lead_status": Lead_status.ACTIVE,
        }

        response = self.get_url_response(data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['paginated_results']['count'], 1)

        serialized_leads = ReadLeadSerializer(data=(self.lead_4,), many=True)
        validate_serialized_leads = serialized_leads.is_valid()
        self.assertEqual(response.data['paginated_results'].get('results'), serialized_leads.data)

    def test_get_all_leads(self):
        data = {}
        response = self.get_url_response(data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['paginated_results']['count'], 4)

        # serialized_leads = ReadLeadSerializer(data=(self.lead_1, self.lead_2, self.lead_3, self.lead_4,), many=True)
        # serialized_leads = ReadLeadSerializer(data=[self.lead_1, self.lead_2, self.lead_3, self.lead_4], many=True)
        # validate_serialized_leads = serialized_leads.is_valid()
        # self.assertEqual(response.data['Results'], serialized_leads.data)

    def test_get_lead_does_not_exist(self):
        # Authenticate the test user
        data = {
            "lead_id": "5"
        }

        response = self.get_url_response(data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'Lead does not exist.')


class StageViewsTest(APITestCase):
    def setUp(self):
        self.user_instance_1 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Alpha",
        )
        self.client.force_authenticate(user=self.user_instance_1)

        self.user_instance_2 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_2)

        self.product_verticals = ProductVerticals.objects.create(name="Test Product")

        self.sales_lead_instance_1 = SalesLead.objects.create(
            user=self.user_instance_1,
            name=self.user_instance_1.get_full_name(),
            email=self.user_instance_1.email,
        )

        self.sales_officer_instance_1 = SalesOfficer.objects.create(
            user=self.user_instance_2,
            sales_lead=self.sales_lead_instance_1,
            name=self.user_instance_2.get_full_name(),
            email=self.user_instance_2.email,
            product_module=self.product_verticals,
            referral_code="123-krutqhsz",
        )

        self.pipeline_instance = Pipeline.objects.create(
            sales_officer=self.sales_officer_instance_1,
            name=f"{self.sales_officer_instance_1.name} Pipeline",
            type="CUSTOM",
            is_default=False,
        )

        category_data_1 = {"name": "Interested", }
        self.category_instance_1 = Category.create(validated_data=category_data_1)

        category_data_2 = {"name": "Proposal", }
        self.category_instance_2 = Category.create(validated_data=category_data_2)

        category_data_3 = {"name": "Demo", }
        self.category_instance_3 = Category.create(validated_data=category_data_3)

        category_data_4 = {"name": "Invoice", }
        self.category_instance_4 = Category.create(validated_data=category_data_4)

        category_data_5 = {"name": "Won Deal", }
        self.category_instance_5 = Category.create(validated_data=category_data_5)

        category_data_6 = {"name": "Onboarding", }
        self.category_instance_6 = Category.create(validated_data=category_data_6)

    def url_response(self, data, url):
        response = self.client.post(url, data, format="json")
        return response

    def test_create_stage_with_pipeline_id(self):
        url = reverse("create_stage")

        data = {
            "pipeline": self.pipeline_instance.id,
            "name": "Stage 1",
            "category": self.category_instance_1.id,
        }

        response = self.url_response(data, url)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual(response.data["message"], "Stage created successfully.")
        print("Message Details, Check!")

    def test_cannot_create_stage_with_null_pipeline_id(self):
        url = reverse("create_stage")

        data = {
            "pipleine": "",
            "name": "Stage 1",
            "category": self.category_instance_1.id,
        }

        response = self.url_response(data, url)
        # response = self.client.post(url, data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual("Pipeline id cannot be null", response.data["message"])
        print("Message Details, Check!")

    def test_cannot_create_stage_without_pipeline_id(self):
        url = reverse("create_stage")

        data = {
            # "pipeline": "",
            "name": "Stage 1",
            "category": self.category_instance_2.id,
        }

        response = self.url_response(data, url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual("Pipeline id cannot be null", response.data["message"])
        print("Message Details, Check!")

    # TODO: Make It Work
    def test_cannot_create_stage_with_null_category(self):
        url = reverse("create_stage")

        data = {
            "pipeline": self.pipeline_instance.id,
            "name": "Stage 9",
            # "category": "",
        }

        response = self.url_response(data, url)
        # response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual(
            "Category id cannot be null", response.data["message"]
        )
        print("Message Details, Check!")


class ActivityViewsTest(APITestCase):

    def setUp(self):
        self.user_instance_1 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Alpha",
        )
        self.client.force_authenticate(user=self.user_instance_1)

        self.user_instance_2 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_2)

        self.product_verticals = ProductVerticals.objects.create(name="Test Product")

        self.sales_lead_instance_1 = SalesLead.objects.create(
            user=self.user_instance_1,
            name=self.user_instance_1.get_full_name(),
            email=self.user_instance_1.email,
        )

        self.sales_officer_instance_1 = SalesOfficer.objects.create(
            user=self.user_instance_2,
            sales_lead=self.sales_lead_instance_1,
            name=self.user_instance_2.get_full_name(),
            email=self.user_instance_2.email,
            product_module=self.product_verticals,
            referral_code="123-krutqhsz",
        )

        self.pipeline_instance = Pipeline.objects.create(
            sales_officer=self.sales_officer_instance_1,
            name=f"{self.sales_officer_instance_1.name} Pipeline",
            type="CUSTOM",
            is_default=False,
        )
        self.lead_instance = Lead.objects.create(
            sales_officer_in_charge=self.sales_officer_instance_1,
            contact_name="John Doe",
            contact_email="<EMAIL>",
            contact_phone_number="1234567890",
            company_name="Security Corp",
            company_email="<EMAIL>",
            company_phone_number="************",
            company_address="1, Address, address neighbourhood",
            deal_value="30000",
        )

    def url_response(self, data, url):
        response = self.client.post(url, data, format="json")
        return response

    def test_create_activities_with_valid_data(self):
        url = reverse("create_activity")

        data = {
            "lead": self.lead_instance.id,
            "title": "Activity 1",
            "description": "A lot of things to do.",
        }

        response = self.url_response(data, url)

        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        self.assertEqual("Activity created successfully.", response.data["message"])
        print("Message Details, Check!")

    def test_cannot_create_activities_with_invalid_lead_data(self):
        url = reverse("create_activity")

        data = {
            # "lead": "",
            "title": "Activity 1",
            "description": "A lot of things to do.",
        }

        response = self.url_response(data, url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        print("Status Code, Check!")

        self.assertIn(response.data["message"], "Lead does not exist.")
        print("Response Contains Message, Check!")

        # self.assertIn(response.data["message"], "The data entered is incorrect, please try again.")
        # print("Response Contains Message, Check!")

    def test_cannot_create_activities_with_invalid_title_data(self):
        url = reverse("create_activity")

        data = {
            "lead": self.lead_instance.id,
            # "title": "Activity 1",
            "description": "A lot of things to do.",
        }

        response = self.url_response(data, url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        print("Status Code, Check!")

        self.assertIn(response.data["message"], "The data entered is incorrect, please try again.")
        print("Response Contains Message, Check!")

    def test_cannot_create_activities_with_invalid_description_data(self):
        url = reverse("create_activity")

        data = {
            "lead": self.lead_instance.id,
            "title": "Activity 1",
            # "description": "A lot of things to do.",
        }

        response = self.url_response(data, url)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        print("Status Code, Check!")

        self.assertIn(response.data["message"], "Activity created successfully.")
        print("Response Contains Message, Check!")


class TaskViewsTest(APITestCase):

    def setUp(self):
        self.user_instance_1 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Alpha",
        )
        self.client.force_authenticate(user=self.user_instance_1)

        self.user_instance_2 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_2)

        self.product_verticals = ProductVerticals.objects.create(name="Test Product")

        self.sales_lead_instance_1 = SalesLead.objects.create(
            user=self.user_instance_1,
            name=self.user_instance_1.get_full_name(),
            email=self.user_instance_1.email,
        )

        self.sales_officer_instance_1 = SalesOfficer.objects.create(
            user=self.user_instance_2,
            sales_lead=self.sales_lead_instance_1,
            name=self.user_instance_2.get_full_name(),
            email=self.user_instance_2.email,
            product_module=self.product_verticals,
            referral_code="123-krutqhsz",
        )

        self.pipeline_instance = Pipeline.objects.create(
            sales_officer=self.sales_officer_instance_1,
            name=f"{self.sales_officer_instance_1.name} Pipeline",
            type="CUSTOM",
            is_default=False,
        )
        self.lead_instance = Lead.objects.create(
            sales_officer_in_charge=self.sales_officer_instance_1,
            contact_name="John Doe",
            contact_email="<EMAIL>",
            contact_phone_number="1234567890",
            company_name="Security Corp",
            company_email="<EMAIL>",
            company_phone_number="************",
            company_address="1, Address, address neighbourhood",
            deal_value="30000",
        )

    def url_response(self, data, url):
        response = self.client.post(url, data, format="json")
        return response

    # def test_create_4_tasks_sales_officer_id(self):
    #     url = reverse('create_lead')
    #
    #     data = {
    #         "sales_officer": self.sales_officer_instance_1.id,
    #         "contact_name": "John Doe",
    #         "contact_email": "<EMAIL>",
    #         "contact_phone_number": "1234567890",
    #         "company_name": "Security Corp",
    #         "company_email": "<EMAIL>",
    #         "company_phone_number": "************",
    #         "company_address": "1, Address, address neighbourhood",
    #         "deal_value": "30000"
    #     }
    #
    #     response = self.client.post(url, data, format='json')
    #
    #     self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    #     print("Status Code, Check!")
    #
    #     self.assertIn("message", response.data)
    #     print("Response Contains Message, Check!")
    #
    #     self.assertEqual(response.data["message"], "Lead created successfully.")
    #     print("Message Details, Check!")
    pass


class NoteViewsTest(APITestCase):

    def setUp(self):
        self.user_instance_1 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Alpha",
        )
        self.client.force_authenticate(user=self.user_instance_1)

        self.user_instance_2 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_2)

        self.product_verticals = ProductVerticals.objects.create(name="Test Product")

        self.sales_lead_instance_1 = SalesLead.objects.create(
            user=self.user_instance_1,
            name=self.user_instance_1.get_full_name(),
            email=self.user_instance_1.email,
        )

        self.sales_officer_instance_1 = SalesOfficer.objects.create(
            user=self.user_instance_2,
            sales_lead=self.sales_lead_instance_1,
            name=self.user_instance_2.get_full_name(),
            email=self.user_instance_2.email,
            product_module=self.product_verticals,
            referral_code="123-krutqhsz",
        )

        self.pipeline_instance = Pipeline.objects.create(
            sales_officer=self.sales_officer_instance_1,
            name=f"{self.sales_officer_instance_1.name} Pipeline",
            type="CUSTOM",
            is_default=False,
        )
        self.lead_instance = Lead.objects.create(
            sales_officer_in_charge=self.sales_officer_instance_1,
            contact_name="John Doe",
            contact_email="<EMAIL>",
            contact_phone_number="1234567890",
            company_name="Security Corp",
            company_email="<EMAIL>",
            company_phone_number="************",
            company_address="1, Address, address neighbourhood",
            deal_value="30000",
        )

    def url_response(self, data, url):
        response = self.client.post(url, data, format="json")
        return response

    # def test_create_notes_with_sales_officer_id(self):
    #     url = reverse('create_lead')
    #
    #     data = {
    #         "sales_officer": self.sales_officer_instance_1.id,
    #         "contact_name": "John Doe",
    #         "contact_email": "<EMAIL>",
    #         "contact_phone_number": "1234567890",
    #         "company_name": "Security Corp",
    #         "company_email": "<EMAIL>",
    #         "company_phone_number": "************",
    #         "company_address": "1, Address, address neighbourhood",
    #         "deal_value": "30000"
    #     }
    #
    #     response = self.client.post(url, data, format='json')
    #
    #     self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    #     print("Status Code, Check!")
    #
    #     self.assertIn("message", response.data)
    #     print("Response Contains Message, Check!")
    #
    #     self.assertEqual(response.data["message"], "Lead created successfully.")
    #     print("Message Details, Check!")
    #
    #
    # def test_create_6_with_sales_officer_id(self):
    #     url = reverse('create_lead')
    #
    #     data = {
    #         "sales_officer": self.sales_officer_instance_1.id,
    #         "contact_name": "John Doe",
    #         "contact_email": "<EMAIL>",
    #         "contact_phone_number": "1234567890",
    #         "company_name": "Security Corp",
    #         "company_email": "<EMAIL>",
    #         "company_phone_number": "************",
    #         "company_address": "1, Address, address neighbourhood",
    #         "deal_value": "30000"
    #     }
    #
    #     response = self.client.post(url, data, format='json')
    #
    #     self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    #     print("Status Code, Check!")
    #
    #     self.assertIn("message", response.data)
    #     print("Response Contains Message, Check!")
    #
    #     self.assertEqual(response.data["message"], "Lead created successfully.")
    #     print("Message Details, Check!")

    # class LeadTestCase(TestCase):
    #     def set_up(self):
    #         pass
    #
    #     @mock.patch('performance_sales_metrics_dashboard.models.Lead.create_lead_using_csv_data_upload')
    #     def test_csv_can_be_read_and_used_to_create_lead_instances(self, mock_read_csv):
    #         mock_read_csv.return_value = [
    #             {'contact_name': 'Darcy Espinos', 'contact_email': '<EMAIL>',
    #              'contact_phone_number': '************', 'company_name': 'Lazzy', 'company_email': '<EMAIL>',
    #              'company_phone_number': '************', 'company_address': '13th Floor', 'industry': 'Apparel',
    #              'sales_officer': '55434d95-6e8d-48b3-8d73-07e4896e2128'},
    #
    #             {'contact_name': 'Raye Shellard', 'contact_email': '<EMAIL>',
    #              'contact_phone_number': '************', 'company_name': 'Skibox',
    #              'company_email': '<EMAIL>', 'company_phone_number': '************',
    #              'company_address': 'Apt 820', 'industry': 'n/a', 'sales_officer': '55434d95-6e8d-48b3-8d73-07e4896e2128'
    #              },
    #
    #             {'contact_name': 'Dicky Sicily', 'contact_email': '<EMAIL>',
    #              'contact_phone_number': '************', 'company_name': 'Brainlounge',
    #              'company_email': '<EMAIL>', 'company_phone_number': '************',
    #              'company_address': 'Apt 1784', 'industry': 'Package Goods/Cosmetics',
    #              'sales_officer': '55434d95-6e8d-48b3-8d73-07e4896e2128'}
    #         ]
    #
    #         lead_data = Lead.create_lead_using_csv_data_upload(file_name=mock_read_csv)
    #
    #         for data in lead_data:
    #             print(data)
    pass


class CategoryViewsTest(APITestCase):

    def setUp(self):
        self.user_instance_1 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Alpha",
        )
        self.client.force_authenticate(user=self.user_instance_1)

        self.sales_lead_instance_1 = SalesLead.objects.create(
            user=self.user_instance_1,
            name=self.user_instance_1.get_full_name(),
            email=self.user_instance_1.email,
        )

    def url_response(self, data, url):
        response = self.client.post(url, data, format="json")
        return response

    # def test_category_can_be_created_with_valid_sales_lead_data(self):
    #     url = reverse("create_category")
    #
    #     data = {
    #         "name": "New Lead"
    #     }
    #
    #     response = self.url_response(data, url)
    #
    #     self.assertEqual(status.HTTP_201_CREATED, response.status_code)
    #     print("Status Code, Check!")
    #
    #     self.assertIn("message", response.data)
    #     print("Response Contains Message, Check!")
    #
    #     # self.assertEqual("Lead does not exist.", response.data["message"])
    #     self.assertEqual("Category created successfully.", response.data["message"])
    #     print("Message Details, Check!")

    def test_category_cannot_be_created_with_invalid_sales_lead_data(self):
        self.user_instance_2 = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="Beta",
        )
        self.client.force_authenticate(user=self.user_instance_2)

        url = reverse("create_category")

        data = {
            "name": "New Lead"
        }

        response = self.url_response(data, url)

        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertFalse(Category.objects.filter(name='New Lead').exists())

    def test_cannot_create_category_without_name(self):
        url = reverse("create_category")

        data = {
            "name": ""
        }

        response = self.url_response(data, url)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        print("Status Code, Check!")

        self.assertIn("message", response.data)
        print("Response Contains Message, Check!")

        # self.assertEqual("Lead does not exist.", response.data["message"])
        self.assertEqual("The data entered is incorrect, please try again.", response.data["message"])
        print("Message Details, Check!")

