from django.utils import timezone
from django.http import HttpResponseForbidden

from subscription_and_invoicing.service import SubscriptionManager
from .models import SubscriptionModule, Company


class SubscriptionMiddleware:
    """
    Middleware to manage subscriptions and restrict access based on the subscription status.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # This code is executed for each request before the view (and later middleware) is called.

        # Get the company ID from the request, assuming it's passed as part of the URL or request data
        company_id = request.GET.get('company_id') or request.POST.get('company_id')
        if company_id:
            try:
                company = Company.objects.get(pk=company_id)

                # Check and update the subscription status of the company
                self.update_company_subscription_status(company)

                # If the company's subscriptions are not active, restrict access
                if not self.has_active_subscription(company):
                    return HttpResponseForbidden("Company does not have an active subscription.")

            except Company.DoesNotExist:
                return HttpResponseForbidden("Invalid company ID provided.")

        # Pass the request to the next middleware or view
        response = self.get_response(request)

        # This code is executed for each request/response after the view is called.
        return response

    def update_company_subscription_status(self, company):
        """
        Update the subscription status for all subscriptions associated with a company.
        """
        # Fetch all invoices linked to this company

        # invoices = company.invoice_set.filter(payment_status__in=["paid", "paid_excess"])
        from subscription_and_invoicing.models import Invoice as NewInvoice

        invoices = NewInvoice.objects.filter(payment_status__in=["paid", "paid_excess"], company=company)
        for invoice in invoices:
            subscription_modules = SubscriptionModule.objects.filter(invoice=invoice)
            for subscription_module in subscription_modules:
                # Calculate if the subscription is still active based on its expiry date
                expiry_date = subscription_module.calculate_expiry_date()
                subscription_module.is_active = expiry_date > timezone.now()
                subscription_module.save()

            # Set the invoice as active if it has any active subscription modules
            invoice.is_active = subscription_modules.filter(is_active=True).exists()
            invoice.save()

    def has_active_subscription(self, company):
        """
        Check if the company has any active subscriptions.
        """
        # Check if any active subscription module is linked to the company
        return SubscriptionModule.objects.filter(company=company, is_active=True).exists()



class SubscriptionAccessMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if hasattr(request, 'user') and request.user.is_authenticated:
            module_code = self.get_module_from_api_path(request.path)
            if module_code and not SubscriptionManager.check_module_access(
                request.user, module_code
            ):
                return HttpResponseForbidden({
                    'error': f'Subscription required for {module_code} module'
                }, status=403)
        
        return self.get_response(request)

    def get_module_from_api_path(self, path):
        """
        Map API paths to module codes based on Django URL configuration
        """
        # Module mapping
        module_map = {
            '/api/v1/stock/': 'INVENTORY',
            '/api/v1/sales/': 'SALES',
            '/api/v1/invoicing/': 'SALES',
            '/instant_web/': 'SALES',
            '/req/': 'SPEND',
            '/payroll/': 'HR',
            '/management/': 'HR',
            '/leave-management/': 'HR'
        }
        
        for prefix, code in module_map.items():
            if path.startswith(prefix):
                return code
        return None