import calendar
import datetime as dt
import random
import uuid
from datetime import datetime, time, timedelta

from django.contrib.auth import get_user_model
from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.db import models
from django.db.models import Q, Sum
from django.utils import timezone

from core.models import BaseModel, ConstantTable, User
from core.tasks import send_email
from payroll_app.apis.enable_payroll_settings import (
    enable_payroll_settings,
    enable_payroll_settings_check,
)
from payroll_app.apis.func import (
    check_employee_onboarding_percentage,
    generate_bulk_id,
    round_amount,
)
from payroll_app.apis.tax_component import consolidated_tax
from payroll_app.tasks import automate_send_pfa, send_payroll_approval_notification_email
from requisition.models import Company

User = get_user_model()

SALARY_GRADE = [
    ("ED", "ED"),
    ("MD", "MD"),
    ("CTO", "CTO"),
    ("MANAGER", "MANAGER"),
    ("SOFTWARE_ENGINEER_1", "SOFTWARE_ENGINEER_1"),
    ("SOFTWARE_ENGINEER_2", "SOFTWARE_ENGINEER_2"),
    ("SOFTWARE_ENGINEER_3", "SOFTWARE_ENGINEER_3"),
    ("NOT_SPECIFIED", "NOT_SPECIFIED"),
]

STATUS = [
    ("APPROVAL", "APPROVAL"),
    ("DISBURSE", "DISBURSE"),
    ("DISBURSED", "DISBURSED"),
    ("OMITTED", "OMITTED"),
]

PAYROLL_TYPE = [
    ("RECORD", "RECORD"),
    ("WORK_HOURS", "WORK_HOURS"),
    ("TIME_SHEET", "TIME_SHEET"),
    ("DEFAULT", "DEFAULT"),
]

PAY_SCHEDULE = [
    ("WEEKLY", "WEEKLY"),
    ("BI-WEEKLY", "BI-WEEKLY"),
    ("MONTHLY", "MONTHLY"),
]

DEDUCTION_TYPE = [
    ("STATUTORY", "STATUTORY"),
    ("OTHERS", "OTHERS"),
]

ALL_SALARY_COMPONENT_CALCULATION_TYPE = [
    ("PERCENTAGE_NET", "PERCENTAGE_NET"),
    ("FIXED_AMOUNT", "FIXED_AMOUNT"),
]
SALARY_COMPONENT_CALCULATION_TYPE = [
    ("PERCENTAGE_NET", "PERCENTAGE_NET"),
    ("FIXED_AMOUNT", "FIXED_AMOUNT"),
    ("CUSTOM_PERCENTAGE", "CUSTOM_PERCENTAGE"),
]

DEDUCTION_CALCULATION_TYPE = [
    ("FIXED_AMOUNT", "FIXED_AMOUNT"),
    ("CUSTOM_PERCENTAGE", "CUSTOM_PERCENTAGE"),
    ("PERCENTAGE_NET", "PERCENTAGE_NET"),
    # ("GROSS_INCOME", "GROSS_INCOME"),
]
COMPONENT_FREQUENCY = [
    ("MONTHLY", "MONTHLY"),
    ("ANNUALLY", "ANNUALLY"),
    ("ONE_OFF", "ONE_OFF"),
    ("BI-MONTHLY", "BI-MONTHLY"),
]
DEPENDENCY_OPERATOR = [
    ("OR", "OR"),
    ("AND", "AND"),
    ("OF", "OF"),
    ("NULL", "NULL"),
]
OPERATOR_TYPE = [
    ("GREATER_THAN", "GREATER_THAN"),
    ("LESSER_THAN", "LESSER_THAN"),
    ("GREATER_THAN_OR_EQUALS", "GREATER_THAN_OR_EQUALS"),
    ("LESSER_THAN_OR_EQUALS", "LESSER_THAN_OR_EQUALS"),
    ("EQUALS", "EQUALS"),
    ("NOT_EQUALS", "NOT_EQUALS"),
    ("NULL", "NULL"),
]

PAY_RUN = [
    ("TIME_SHEET", "TIME_SHEET"),
    ("BASIC", "BASIC"),
]

EMPLOYEE_TYPE = [
    ("CONTRACT", "CONTRACT"),
    ("PART_TIME", "PART_TIME"),
    ("FULL_TIME", "FULL_TIME"),
]

MARITAL_STATUS = [
    ("SINGLE", "SINGLE"),
    ("MARRIED", "MARRIED"),
    ("DIVORCED", "DIVORCED"),
]

EMPLOYEE_RELIGION = [
    ("CHRISTIAN", "CHRISTIAN"),
    ("MUSLIM", "MUSLIM"),
]

EMPLOYEE_BLOOD_GROUP = [
    ("A+", "A+"),
    ("A-", "A-"),
    ("B+", "B+"),
    ("B-", "B-"),
    ("AB+", "AB+"),
    ("AB-", "AB-"),
    ("O+", "O+"),
    ("O-", "O-"),
]

EMPLOYEE_GENOTYPE = [
    ("AA", "AA"),
    ("AS", "AS"),
    ("AC", "AC"),
    ("SS", "SS"),
    ("SC", "SC"),
]

# Create your models here.
class PayrollTable(models.Model):
    payroll_admin = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="payroll_admin",
    )
    payroll_user = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="payroll_user",
    )
    company_owner = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="payroll_owner",
    )
    employee = models.ForeignKey(
        "payroll_app.CompanyEmployeeList",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="payroll_employee",
    )
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, related_name="company_payroll"
    )
    phone_number = models.CharField(max_length=200, null=True, blank=True)
    gross_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    life_insurance_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    net_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    bulk_id = models.CharField(null=True, blank=True, max_length=255)
    first_name = models.CharField(null=True, blank=True, max_length=255)
    last_name = models.CharField(null=True, blank=True, max_length=255)
    email = models.EmailField(null=True, blank=True)
    gender = models.CharField(null=True, blank=True, max_length=255)
    salary_grade = models.CharField(
        max_length=200, choices=SALARY_GRADE, default="NOT_SPECIFIED"
    )
    pension_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employer_pension_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_voluntary_pension_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    tax_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    other_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    other_deductions = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    payable_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    basic_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    housing_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    transport_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    hmo_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    instant_wage_deduction = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    bank_name = models.CharField(null=True, blank=True, max_length=255)
    account_number = models.CharField(null=True, blank=True, max_length=255)
    account_name = models.CharField(null=True, blank=True, max_length=255)
    bank_code = models.CharField(null=True, blank=True, max_length=255)
    payroll_deleted = models.BooleanField(default=False)
    status = models.CharField(max_length=200, choices=STATUS, default="APPROVAL")
    payroll_type = models.CharField(
        max_length=200, choices=PAYROLL_TYPE, default="DEFAULT"
    )
    payroll_disburse = models.BooleanField(default=False)
    payroll_status = models.CharField(null=True, blank=True, max_length=255)
    payroll_date = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PAYROLL TABLE"
        verbose_name_plural = "PAYROLL TABLES"

    @property
    def overall_amount(self):
        # override gross
        return f"**********"

    @property
    def settlement_amount(self):
        # override net amount
        return f"**********"

    @property
    def receivable_amount(self):
        # override payable amount
        return f"**********"

    @classmethod
    def multiple_payroll_count(cls, company_uuid):
        multiple_payroll_count = (
            cls.objects.filter(
                company__id=company_uuid,
                status__in=["APPROVAL", "DISBURSE", "OMITTED"],
                payroll_deleted=False,
            )
            .values_list("bulk_id", flat=True)
            .distinct()
            .order_by("-payroll_date")
            .count()
            or 0
        )
        return multiple_payroll_count

    @classmethod
    def create_approval_payroll(
        cls,
        user,
        company_uuid,
        payroll_date,
        is_recurring,
        narration,
        payroll_month,
        payroll_year,
        employees,
        multiple_payroll,
    ):
        """
        create payroll
        """
        CONST = ConstantTable.get_constant_instance()
        if multiple_payroll:
            if cls.multiple_payroll_count(company_uuid) >= CONST.multiple_payroll_limit:
                return {
                    "status": False,
                    "message": f"payroll limit is {CONST.multiple_payroll_limit} please try again after attending to existing payroll(s)",
                }
        month_map = {
            "january": 1,
            "february": 2,
            "march": 3,
            "april": 4,
            "may": 5,
            "june": 6,
            "july": 7,
            "august": 8,
            "september": 9,
            "october": 10,
            "november": 11,
            "december": 12,
        }
        # current_payroll_date = f"{payroll_year}-{month_map[payroll_month]}-03"
        current_payroll_date_str = f"{payroll_year}-{month_map[payroll_month]}-03"

        # Convert the string to a naive datetime object
        current_payroll_date = datetime.strptime(current_payroll_date_str, "%Y-%m-%d")

        # If your system uses timezone support, make it timezone aware
        current_payroll_date = timezone.make_aware(current_payroll_date)

        payroll = cls.objects.filter(
            Q(
                company__id=company_uuid,
                payroll_deleted=False,
                status__in=["APPROVAL", "DISBURSE", "OMITTED"],
            )
        ).first()
        if not multiple_payroll and payroll:
            return {"status": False, "message": "payroll already running"}
        else:

            filtered_payroll_data = CompanyEmployeeList.objects.filter(
                company__id=company_uuid, is_active=True
            )
            filtered_payroll_data.update(
                employee_instant_daily_wage=False, employee_instant_wage_status="OFF"
            )
            if len(employees) >= 1:
                payroll_data = filtered_payroll_data.filter(id__in=employees)
            else:
                payroll_data = filtered_payroll_data

            bulk_id = generate_bulk_id()
            company_ins = Company.objects.get(id=company_uuid)
            payroll_settings_ins = CompanyPayrollSettings.objects.filter(
                company=company_ins
            ).first()
            custom_tax_band = CompanyTaxBand.objects.filter(
                company=company_ins, is_active=True
            ).first()

            if payroll_settings_ins:
                if payroll_settings_ins.standard_tax:
                    company_tax = "STANDARD_TAX"
                else:
                    if custom_tax_band:
                        company_tax = "CUSTOM_TAX"
                    else:
                        company_tax = "OFF"
            else:
                if custom_tax_band:
                    company_tax = "CUSTOM_TAX"
                else:
                    company_tax = "OFF"

            if payroll_data:
                ## check if all employee profile is complete
                # failed_employee = []
                # for employee in payroll_data:
                #     get_percentage = check_employee_onboarding_percentage(employee)
                #     if get_percentage < 100:
                #         failed_employee.append({
                #             "email": employee.employee_email,
                #             "percentage": get_percentage
                #         })
                # if len(failed_employee) > 0:
                #     return {
                #         "status": False,
                #         "data": failed_employee,
                #         "message": "employee(s) data yet to be completed!"
                #     }

                company_component = (
                    SalaryComponentSettings.objects.filter(
                        company__id=company_uuid, is_active=True
                    ).count()
                    or 0
                )
                if company_component <= 0:
                    latest_payroll_data = payroll_data.filter(
                        employee_payable_amount__gt=0
                    )
                    ### if salary component does not exist

                    for user_data in latest_payroll_data:
                        cls.objects.create(
                            company_owner=company_ins.user,
                            payroll_admin=user,
                            employee=user_data,
                            payroll_user=user_data.employee,
                            phone_number=user_data.employee_phone_number,
                            gross_amount=user_data.employee_gross_amount,
                            housing_amount=user_data.housing_amount,
                            basic_amount=user_data.basic_amount,
                            transport_amount=user_data.transport_amount,
                            tax_amount=user_data.employee_tax_amount,
                            life_insurance_amount=user_data.employee_life_insurance_amount,
                            net_amount=user_data.employee_net_amount,
                            salary_grade=user_data.employee_salary_grade,
                            pension_amount=user_data.employee_pension_amount,
                            employer_pension_amount=user_data.employer_pension_amount,
                            employee_voluntary_pension_amount=user_data.employee_voluntary_pension_amount,
                            bulk_id=bulk_id,
                            first_name=user_data.employee_first_name,
                            last_name=user_data.employee_last_name,
                            email=user_data.employee_email,
                            gender=user_data.employee_gender,
                            # tax_amount=annual_tax_amount,
                            other_amount=user_data.employee_other_amount,
                            other_deductions=user_data.employee_other_deductions,
                            payable_amount=user_data.employee_payable_amount,
                            company=user_data.company,
                            instant_wage_deduction=user_data.employee_instant_used_wage_amount,
                            hmo_amount=user_data.employee_hmo_amount,
                            bank_name=user_data.employee_bank_name,
                            account_number=user_data.employee_account_number,
                            bank_code=user_data.employee_bank_code,
                            account_name=user_data.employee_account_name,
                            payroll_date=current_payroll_date,
                        )
                    # print(payroll_date, is_recurring, narration, payroll_month, payroll_year, "YES\n\n\n")
                    CompanyDetailsData.objects.create(
                        bulk_id=bulk_id,
                        company=company_ins,
                        payroll_date=payroll_date,
                        is_recurring=is_recurring,
                        narration=narration,
                        payroll_month=payroll_month,
                        payroll_year=payroll_year,
                    )
                    return {"status": True, "message": "payroll run successfully", "bulk_id": bulk_id}
                else:
                    ### if salary component exist
                    payroll_check_status, pay_settings_check = (
                        enable_payroll_settings_check(
                            company=company_ins,
                            employee_data=payroll_data,
                            company_tax=company_tax,
                        )
                    )
                    if not payroll_check_status:
                        return {"status": False, "message": pay_settings_check}

                    if len(employees) > 0:
                        this_payroll_data = payroll_data
                    else:
                        this_payroll_data = filtered_payroll_data

                    for user_data in this_payroll_data:
                        pay_bool, pay_value = enable_payroll_settings(
                            company=company_ins,
                            employee=user_data,
                            company_tax=company_tax,
                            net_calculation_type=pay_settings_check,
                        )

                        if pay_bool:
                            cls.objects.create(
                                company_owner=company_ins.user,
                                payroll_admin=user,
                                payroll_user=user_data.employee,
                                employee=user_data,
                                phone_number=user_data.employee_phone_number,
                                gross_amount=pay_value.get("employee_gross_amount"),
                                housing_amount=pay_value.get("housing_amount"),
                                basic_amount=pay_value.get("basic_amount"),
                                transport_amount=pay_value.get("transport_amount"),
                                tax_amount=pay_value.get("employee_tax_amount"),
                                life_insurance_amount=user_data.employee_life_insurance_amount,
                                net_amount=pay_value.get("employee_net_amount"),
                                salary_grade=user_data.employee_salary_grade,
                                pension_amount=pay_value.get("pension_amount"),
                                employer_pension_amount=pay_value.get(
                                    "employer_pension_amount"
                                ),
                                employee_voluntary_pension_amount=pay_value.get(
                                    "employee_voluntary_pension_amount"
                                ),
                                bulk_id=bulk_id,
                                first_name=user_data.employee_first_name,
                                last_name=user_data.employee_last_name,
                                email=user_data.employee_email,
                                gender=user_data.employee_gender,
                                # tax_amount=annual_tax_amount,
                                other_amount=user_data.employee_other_amount,
                                other_deductions=user_data.employee_other_deductions,
                                payable_amount=pay_value.get("employee_payable_amount"),
                                company=user_data.company,
                                instant_wage_deduction=user_data.employee_instant_used_wage_amount,
                                hmo_amount=user_data.employee_hmo_amount,
                                bank_name=user_data.employee_bank_name,
                                account_number=user_data.employee_account_number,
                                bank_code=user_data.employee_bank_code,
                                account_name=user_data.employee_account_name,
                                payroll_date=current_payroll_date,
                            )
                    CompanyDetailsData.objects.create(
                        bulk_id=bulk_id,
                        company=company_ins,
                        payroll_date=payroll_date,
                        is_recurring=is_recurring,
                        narration=narration,
                        payroll_month=payroll_month,
                        payroll_year=payroll_year,
                    )
                    return {"status": True, "message": "payroll run successfully", "bulk_id": bulk_id}
            else:
                return {"status": False, "message": "no employee to run payroll"}

    @classmethod
    def add_employee_existing_payroll(cls, user, company_uuid, employees):
        """
        add employee(s) to existing payroll
        """
        payroll = cls.objects.filter(
            Q(
                company__id=company_uuid,
                payroll_deleted=False,
                status__in=["APPROVAL", "DISBURSE", "OMITTED"],
            )
        ).first()
        if not payroll:
            return {"status": False, "message": "no running payroll"}
        else:

            filtered_payroll_data = CompanyEmployeeList.objects.filter(
                company__id=company_uuid, is_active=True
            )
            filtered_payroll_data.update(
                employee_instant_daily_wage=False, employee_instant_wage_status="OFF"
            )

            payroll_data = filtered_payroll_data.filter(id__in=employees)

            bulk_id = payroll.bulk_id

            company_ins = Company.objects.get(id=company_uuid)

            payroll_settings_ins = CompanyPayrollSettings.objects.filter(
                company=company_ins
            ).first()
            custom_tax_band = CompanyTaxBand.objects.filter(
                company=company_ins, is_active=True
            ).first()

            if payroll_settings_ins:
                if payroll_settings_ins.standard_tax:
                    company_tax = "STANDARD_TAX"
                else:
                    if custom_tax_band:
                        company_tax = "CUSTOM_TAX"
                    else:
                        company_tax = "OFF"
            else:
                if custom_tax_band:
                    company_tax = "CUSTOM_TAX"
                else:
                    company_tax = "OFF"

            if payroll_data:

                company_component = (
                    SalaryComponentSettings.objects.filter(
                        company__id=company_uuid, is_active=True
                    ).count()
                    or 0
                )
                if company_component <= 0:
                    latest_payroll_data = payroll_data.filter(
                        employee_payable_amount__gt=0
                    )
                    if latest_payroll_data:
                        ### if salary component does not exist
                        successful_employees = []
                        unsuccessful_employees = []
                        for user_data in latest_payroll_data:
                            if cls.objects.filter(
                                email=user_data.employee_email,
                                payroll_deleted=False,
                                bulk_id=bulk_id,
                            ).exists():
                                unsuccessful_employees.append(user_data.employee_email)
                            else:
                                cls.objects.create(
                                    company_owner=company_ins.user,
                                    payroll_admin=user,
                                    payroll_user=user_data.employee,
                                    employee=user_data,
                                    phone_number=user_data.employee_phone_number,
                                    gross_amount=user_data.employee_gross_amount,
                                    housing_amount=user_data.housing_amount,
                                    basic_amount=user_data.basic_amount,
                                    transport_amount=user_data.transport_amount,
                                    tax_amount=user_data.employee_tax_amount,
                                    life_insurance_amount=user_data.employee_life_insurance_amount,
                                    net_amount=user_data.employee_net_amount,
                                    salary_grade=user_data.employee_salary_grade,
                                    pension_amount=user_data.employee_pension_amount,
                                    employer_pension_amount=user_data.employer_pension_amount,
                                    employee_voluntary_pension_amount=user_data.employee_voluntary_pension_amount,
                                    bulk_id=bulk_id,
                                    first_name=user_data.employee_first_name,
                                    last_name=user_data.employee_last_name,
                                    email=user_data.employee_email,
                                    gender=user_data.employee_gender,
                                    # tax_amount=annual_tax_amount,
                                    other_amount=user_data.employee_other_amount,
                                    other_deductions=user_data.employee_other_deductions,
                                    payable_amount=user_data.employee_payable_amount,
                                    company=user_data.company,
                                    instant_wage_deduction=user_data.employee_instant_used_wage_amount,
                                    hmo_amount=user_data.employee_hmo_amount,
                                    bank_name=user_data.employee_bank_name,
                                    account_number=user_data.employee_account_number,
                                    bank_code=user_data.employee_bank_code,
                                    account_name=user_data.employee_account_name,
                                    payroll_date=payroll.payroll_date,
                                )
                                successful_employees.append(user_data.employee_email)

                        if len(successful_employees) > 0:
                            return {
                                "status": True,
                                "message": "payroll run successfully",
                                "data": {
                                    "successful_employees": successful_employees,
                                    "unsuccessful_employees": unsuccessful_employees,
                                },
                                "bulk_id": bulk_id
                            }
                        else:
                            return {
                                "status": False,
                                "message": "employees already exist on payroll",
                                "data": {
                                    "successful_employees": successful_employees,
                                    "unsuccessful_employees": unsuccessful_employees,
                                },
                                "bulk_id": bulk_id
                            }
                    else:
                        return {
                            "status": False,
                            "message": "payable amount must be greater than 0",
                            "bulk_id": bulk_id
                        }
                else:
                    ### if salary component exist
                    payroll_check_status, pay_settings_check = (
                        enable_payroll_settings_check(
                            company=company_ins,
                            employee_data=payroll_data,
                            company_tax=company_tax,
                        )
                    )
                    if not payroll_check_status:
                        return {"status": False, "message": pay_settings_check}

                    if payroll_check_status is True:
                        if latest_payroll_data:
                            successful_employees = []
                            unsuccessful_employees = []
                            for user_data in payroll_data:
                                if cls.objects.filter(
                                    email=user_data.employee_email,
                                    payroll_deleted=False,
                                    bulk_id=bulk_id,
                                ).exists():
                                    unsuccessful_employees.append(
                                        user_data.employee_email
                                    )
                                else:
                                    pay_bool, pay_value = enable_payroll_settings(
                                        company=company_ins,
                                        employee=user_data,
                                        company_tax=company_tax,
                                        net_calculation_type=pay_settings_check,
                                    )
                                    if pay_bool:
                                        cls.objects.create(
                                            company_owner=company_ins.user,
                                            payroll_admin=user,
                                            payroll_user=user_data.employee,
                                            employee=user_data,
                                            phone_number=user_data.employee_phone_number,
                                            gross_amount=pay_value.get(
                                                "employee_gross_amount"
                                            ),
                                            housing_amount=pay_value.get(
                                                "housing_amount"
                                            ),
                                            basic_amount=pay_value.get("basic_amount"),
                                            transport_amount=pay_value.get(
                                                "transport_amount"
                                            ),
                                            tax_amount=pay_value.get(
                                                "employee_tax_amount"
                                            ),
                                            life_insurance_amount=user_data.employee_life_insurance_amount,
                                            net_amount=pay_value.get(
                                                "employee_net_amount"
                                            ),
                                            salary_grade=user_data.employee_salary_grade,
                                            pension_amount=pay_value.get(
                                                "pension_amount"
                                            ),
                                            employer_pension_amount=pay_value.get(
                                                "employer_pension_amount"
                                            ),
                                            employee_voluntary_pension_amount=pay_value.get(
                                                "employee_voluntary_pension_amount"
                                            ),
                                            bulk_id=bulk_id,
                                            first_name=user_data.employee_first_name,
                                            last_name=user_data.employee_last_name,
                                            email=user_data.employee_email,
                                            gender=user_data.employee_gender,
                                            # tax_amount=annual_tax_amount,
                                            other_amount=user_data.employee_other_amount,
                                            other_deductions=user_data.employee_other_deductions,
                                            payable_amount=pay_value.get(
                                                "employee_payable_amount"
                                            ),
                                            company=user_data.company,
                                            instant_wage_deduction=user_data.employee_instant_used_wage_amount,
                                            hmo_amount=user_data.employee_hmo_amount,
                                            bank_name=user_data.employee_bank_name,
                                            account_number=user_data.employee_account_number,
                                            bank_code=user_data.employee_bank_code,
                                            account_name=user_data.employee_account_name,
                                            payroll_date=payroll.payroll_date,
                                        )

                                        successful_employees.append(
                                            user_data.employee_email
                                        )
                                    else:
                                        unsuccessful_employees.append(
                                            user_data.employee_email
                                        )
                            if len(successful_employees) > 0:
                                return {
                                    "status": True,
                                    "message": "payroll run successfully",
                                    "data": {
                                        "successful_employees": successful_employees,
                                        "unsuccessful_employees": unsuccessful_employees,
                                    },
                                    "bulk_id": bulk_id
                                }
                            else:
                                return {
                                    "status": False,
                                    "message": "employees already exist on payroll",
                                    "data": {
                                        "successful_employees": successful_employees,
                                        "unsuccessful_employees": unsuccessful_employees,
                                    },
                                }
                        else:
                            return {
                                "status": False,
                                "message": "payable must be grater than 0",
                            }
                    else:
                        return {
                            "status": False,
                            "message": "please check payroll settings and try again!",
                        }
            else:
                return {"status": False, "message": "no employee to run payroll"}

    @classmethod
    def create_record_payroll(
        cls, user, company_uuid, payroll_date, narration, payroll_month, payroll_year
    ):
        """
        create payroll
        """
        month_map = {
            "january": 1,
            "february": 2,
            "march": 3,
            "april": 4,
            "may": 5,
            "june": 6,
            "july": 7,
            "august": 8,
            "september": 9,
            "october": 10,
            "november": 11,
            "december": 12,
        }
        current_payroll_date = f"{payroll_year}-{month_map[payroll_month]}-03"
        payroll = cls.objects.filter(
            Q(
                company__id=company_uuid,
                payroll_deleted=False,
                status__in=["APPROVAL", "DISBURSE", "OMITTED"],
            )
        ).first()
        if payroll:
            return {"status": False, "message": "payroll already running"}
        else:
            payroll_data = CompanyEmployeeList.objects.filter(
                company__id=company_uuid, is_active=True, employee_payable_amount__gt=0
            )
            payroll_data.update(
                employee_instant_daily_wage=False, employee_instant_wage_status="OFF"
            )
            company_ins = Company.objects.get(id=company_uuid)
            if payroll_data:
                total_amount = 0
                total_count = 0
                bulk_id = generate_bulk_id()
                for user_data in payroll_data:
                    total_amount += user_data.employee_payable_amount
                    total_count += 1
                    cls.objects.create(
                        company_owner=company_ins.user,
                        payroll_admin=user,
                        payroll_user=user_data.employee,
                        employee=user_data,
                        phone_number=user_data.employee_phone_number,
                        gross_amount=user_data.employee_gross_amount,
                        net_amount=user_data.employee_net_amount,
                        salary_grade=user_data.employee_salary_grade,
                        pension_amount=user_data.employee_pension_amount,
                        employer_pension_amount=user_data.employer_pension_amount,
                        employee_voluntary_pension_amount=user_data.employee_voluntary_pension_amount,
                        bulk_id=bulk_id,
                        first_name=user_data.employee_first_name,
                        last_name=user_data.employee_last_name,
                        email=user_data.employee_email,
                        gender=user_data.employee_gender,
                        tax_amount=user_data.employee_tax_amount,
                        other_amount=user_data.employee_other_amount,
                        other_deductions=user_data.employee_other_deductions,
                        payable_amount=user_data.employee_payable_amount,
                        company=user_data.company,
                        hmo_amount=user_data.employee_hmo_amount,
                        bank_name=user_data.employee_bank_name,
                        account_number=user_data.employee_account_number,
                        bank_code=user_data.employee_bank_code,
                        account_name=user_data.employee_account_name,
                        payroll_type="RECORD",
                        status="DISBURSED",
                        payroll_date=current_payroll_date,
                        payroll_disburse=True,
                    )
                CompanyDetailsData.objects.create(
                    bulk_id=bulk_id,
                    company=company_ins,
                    payroll_date=payroll_date,
                    is_recurring=False,
                    narration=narration,
                    payroll_month=payroll_month,
                    payroll_year=payroll_year,
                )
                return {
                    "status": True,
                    "message": "success",
                    "data": {
                        "message": "Transaction completed successfully",
                        "amount_processed": total_amount,
                        "processed_transaction_count": total_count,
                    },
                    "date_completed": datetime.now(),
                    "bulk_id": bulk_id
                }
            else:
                return {"status": False, "message": "no employee to run payroll"}

    @classmethod
    def run_approval_payroll(cls, user, company_uuid, bulk_id=None):
        """
        Run payroll approval
        """
        payroll_settings_ins = CompanyPayrollSettings.objects.filter(company__id=company_uuid).first()
        if payroll_settings_ins:
            notify_employee = payroll_settings_ins.payroll_notification
        else:
            notify_employee = False

        if bulk_id:
            payroll = cls.objects.filter(
                company__id=company_uuid,
                bulk_id=bulk_id,
                payroll_deleted=False,
                status="APPROVAL",
            )
        else:
            payroll = cls.objects.filter(
                company__id=company_uuid, payroll_deleted=False, status="APPROVAL"
            )

        if payroll:

            for user_data in payroll:
                payroll_date = user_data.payroll_date
                payroll_month = payroll_date.strftime("%B")
                payroll_year = payroll_date.strftime("%Y")
                payment_date = f"{payroll_month} {payroll_year}"

                user_data.status = "DISBURSE"
                user_data.payroll_admin = user
                user_data.save()
                employee_ins = CompanyEmployeeList.objects.filter(
                    employee__email=user_data.payroll_user.email,
                    company=user_data.company,
                ).last()
                if employee_ins:
                    if notify_employee is True:
                        send_payroll_approval_notification_email.delay(
                            recipient=employee_ins.employee_email,
                            subject="Payroll Approval",
                            template_dir="salary_approval.html",
                            name=employee_ins.employee_first_name,
                            month=payment_date,
                            company_name=employee_ins.company.company_name,
                        )
            return {"status": True, "message": "Payroll has been approved successfully", "bulk_id": bulk_id}
        else:
            return {"status": False, "message": "no payroll for approval"}

    @classmethod
    def delete_payroll(cls, user, bulk_id) -> bool:
        if user:
            all_payroll_data = cls.objects.filter(bulk_id=bulk_id, payroll_admin=user)
            if all_payroll_data:
                for user_data in all_payroll_data:
                    if user_data.payroll_deleted is True:
                        return False
                    else:
                        user_data.payroll_deleted = True
                        user_data.save()
                return True
            else:
                return False
        else:
            return False


class CompanyEmployeeList(models.Model):

    INSTANT_WAGE_STATUS = (
        ("OFF", "OFF"),
        ("PENDING", "PENDING"),
        ("REJECTED", "REJECTED"),
        ("SUCCESSFUL", "SUCCESSFUL"),
    )

    EMPLOYEE_STATUS = (
        ("ACTIVE", "ACTIVE"),
        ("SUSPENDED", "SUSPENDED"),
        ("NOT_JOINED", "NOT_JOINED"),
        ("DELETED", "DELETED"),
    )

    WORK_TYPE = (
        ("REMOTE", "REMOTE"),
        ("HYBRID", "HYBRID"),
        ("ONSITE", "ONSITE"),
    )

    STAFF_TYPE = (
        ("STAFF", "STAFF"),
        ("AGENT", "AGENT"),
    )

    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="company_employer_list",
    )
    employee = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_list",
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_data",
        null=True,
        blank=True,
    )
    employee_gross_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_life_insurance_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_entertainment_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_utility_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_leave_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_net_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_salary_grade = models.CharField(
        max_length=200, choices=SALARY_GRADE, default="NOT_SPECIFIED"
    )
    employee_pension_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employer_contribution_pension_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_tax_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_other_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_other_deductions = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_payable_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_hmo_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_bank_name = models.CharField(null=True, blank=True, max_length=255)
    employee_account_number = models.CharField(null=True, blank=True, max_length=255)
    employee_email = models.EmailField(null=True, blank=True)
    employee_alternate_email = models.EmailField(null=True, blank=True)
    employee_staff_id = models.CharField(
        null=True, blank=True, max_length=255, editable=False
    )
    employee_staff_id_reporting_line = models.CharField(
        null=True, blank=True, max_length=255
    )
    paybox_id = models.CharField(null=True, blank=True, max_length=255, editable=False)
    employee_job_title = models.CharField(null=True, blank=True, max_length=255)
    employee_state = models.CharField(null=True, blank=True, max_length=255)
    employee_city = models.CharField(null=True, blank=True, max_length=255)
    employee_town = models.CharField(null=True, blank=True, max_length=255)
    employee_bus_stop = models.CharField(null=True, blank=True, max_length=255)
    employee_street_name = models.CharField(null=True, blank=True, max_length=255)
    employee_house_no = models.CharField(null=True, blank=True, max_length=255)
    employee_postal_code = models.CharField(null=True, blank=True, max_length=255)
    employee_gender = models.CharField(null=True, blank=True, max_length=255)
    employee_first_name = models.CharField(null=True, blank=True, max_length=255)
    employee_last_name = models.CharField(null=True, blank=True, max_length=255)
    employee_other_name = models.CharField(null=True, blank=True, max_length=255)
    employee_marital_status = models.CharField(null=True, blank=True, max_length=255, choices=MARITAL_STATUS)
    employee_religion = models.CharField(null=True, blank=True, max_length=255, choices=EMPLOYEE_RELIGION)
    employee_blood_group = models.CharField(null=True, blank=True, max_length=255, choices=EMPLOYEE_BLOOD_GROUP)
    employee_genotype = models.CharField(null=True, blank=True, max_length=255, choices=EMPLOYEE_GENOTYPE)
    employee_phone_number = models.CharField(null=True, blank=True, max_length=255)
    employee_address = models.CharField(null=True, blank=True, max_length=255)
    employee_first_next_of_kin_firstname = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_first_next_of_kin_lastname = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_first_next_of_kin_phone_number = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_first_next_of_kin_relationship = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_first_next_of_kin_address = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_firstname = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_lastname = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_phone_number = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_relationship = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_address = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_position = models.CharField(null=True, blank=True, max_length=255)
    employee_birth_date = models.DateField(null=True, blank=True)
    employee_bank_code = models.CharField(null=True, blank=True, max_length=255)
    employee_account_name = models.CharField(null=True, blank=True, max_length=255)
    employee_bvn_number = models.CharField(null=True, blank=True, max_length=255)
    employee_swift_code = models.CharField(null=True, blank=True, max_length=255)
    employee_profile_picture = models.TextField(blank=True, null=True)
    employee_department = models.ForeignKey(
        "payroll_app.CompanyDepartmentSettings",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_department",
    )
    employee_pay_grade = models.ForeignKey(
        "payroll_app.CompanyPayGradeSettings",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_pay_grade",
    )
    employee_contract_type = models.CharField(null=True, blank=True, max_length=255)
    employee_status = models.CharField(
        max_length=200, choices=EMPLOYEE_STATUS, default="NOT_JOINED"
    )
    employee_instant_wage_status = models.CharField(
        max_length=200,
        choices=INSTANT_WAGE_STATUS,
        null=True,
        blank=True,
        default="OFF",
    )
    employee_instant_daily_wage = models.BooleanField(default=False)
    employee_life_insurance_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_instant_daily_wage_date = models.DateField(null=True, blank=True)
    employee_instant_daily_collected_wage_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_instant_daily_remaining_wage_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    set_instant_wage_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_instant_daily_wage_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_instant_used_wage_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_instant_net_available_wage = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_instant_wage_completed = models.BooleanField(default=False)
    custom_daily_wage = models.BooleanField(default=False)
    custom_daily_wage_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    custom_instant_wage_percentage = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )

    basic_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    housing_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    transport_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employee_voluntary_pension_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    employer_pension_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )

    employee_role = models.ForeignKey(
        "payroll_app.ManagePermissionsRole",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_role",
    )
    employee_department_role = models.ForeignKey(
        "payroll_app.ManageDepartmentRole",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_department_role",
    )

    can_disburse = models.BooleanField(default=False)
    can_approve = models.BooleanField(default=False)
    can_add_member = models.BooleanField(default=False)
    can_edit_member = models.BooleanField(default=False)
    can_delete_member = models.BooleanField(default=False)
    can_delete_payroll = models.BooleanField(default=False)
    can_run_payroll = models.BooleanField(default=False)
    manager_permission = models.BooleanField(default=False)

    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_hr",
    )
    employee_start_date = models.DateField(null=True, blank=True)
    is_work_anniversary_enabled = models.BooleanField(default=True)
    is_birthday_enabled = models.BooleanField(default=True)

    pension_fund_admin = models.ForeignKey(
        "payroll_app.PensionFundAdminSettings",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_pfa",
    )
    pension_pin = models.CharField(null=True, blank=True, max_length=255)

    work_type = models.CharField(max_length=200, choices=WORK_TYPE, default="REMOTE")

    employee_branch = models.ForeignKey(
        "clock_app.Location", on_delete=models.CASCADE, null=True, blank=True
    )

    employee_type = models.CharField(
        max_length=200, choices=STAFF_TYPE, null=True, blank=True, default="STAFF"
    )

    pay_schedule = models.CharField(
        null=True, blank=True, max_length=255, choices=PAY_SCHEDULE, default="MONTHLY"
    )
    pay_run = models.CharField(
        null=True, blank=True, max_length=255, choices=PAY_RUN, default="BASIC"
    )
    staff_type = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=EMPLOYEE_TYPE,
        default="FULL_TIME",
    )

    is_active = models.BooleanField(default=False)
    is_suspended = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    is_invited = models.BooleanField(default=False)

    # employee guarantor data
    employee_guarantor_state = models.CharField(null=True, blank=True, max_length=255)
    employee_guarantor_city = models.CharField(null=True, blank=True, max_length=255)
    employee_guarantor_town = models.CharField(null=True, blank=True, max_length=255)
    employee_guarantor_bus_stop = models.CharField(null=True, blank=True, max_length=255)
    employee_guarantor_street_name = models.CharField(null=True, blank=True, max_length=255)
    employee_guarantor_house_no = models.CharField(null=True, blank=True, max_length=255)
    employee_guarantor_phone_number = models.CharField(null=True, blank=True, max_length=255)

    # verification data
    is_phone_number_verified = models.BooleanField(default=False)
    is_guarantor_phone_number_verified = models.BooleanField(default=False)
    employee_utility_bill = models.TextField(blank=True, null=True)
    employee_utility_bill_message = models.CharField(max_length=500, blank=True, null=True)
    employee_utility_bill_address_match_score = models.CharField(max_length=10, blank=True, null=True)
    guarantor_utility_bill = models.TextField(blank=True, null=True)
    guarantor_utility_bill_message = models.CharField(max_length=500, blank=True, null=True)
    guarantor_utility_bill_address_match_score = models.CharField(max_length=10, blank=True, null=True)
    guarantor_invite_id = models.CharField(max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.employee_email)

    class Meta:
        verbose_name = "COMPANY EMPLOYEE"
        verbose_name_plural = "COMPANY EMPLOYEES"

    @property
    def user_role(self):
        if self.employee_role:
            return "ADMIN"
        elif self.manager_permission:
            return "MANAGER"
        else:
            return "EMPLOYEE"
        
    @property
    def overall_amount(self):
        # override employee_gross_amount
        return f"**********"
    

    @property
    def settlement_amount(self):
        # override employee_net_amount
        return f"**********"
    

    @property
    def receivable_amount(self):
        # override employee_payable_amount
        return f"**********"
    

    @property
    def manager_role(self):
        if self.manager_permission:
            return "MANAGER"
        else:
            return "EMPLOYEE"

    @property
    def full_name(self):
        if self.employee_first_name:
            first_name = self.employee_first_name
        else:
            first_name = ""
        if self.employee_last_name:
            last_name = self.employee_last_name
        else:
            last_name = ""
        return f"{first_name} {last_name}"

    @classmethod
    def employee_exist(cls, email):

        user = cls.objects.filter(employee_email=email)
        if user:
            return user
        else:
            return None

    def save(self, *args, **kwargs):
        if self.pk:
            if self.is_deleted is True:
                self.is_active = False
                self.is_suspended = False
                self.employee_status = "DELETED"
            elif self.is_suspended is True:
                self.is_active = False
                self.employee_status = "SUSPENDED"
            elif self.is_active is True:
                self.employee_status = "ACTIVE"
                self.is_suspended = False

        super(CompanyEmployeeList, self).save(*args, **kwargs)

    @classmethod
    def create_employee(cls, company_ins, all_employee, company_uuid, user):

        invited_users = []
        inactive_users = []
        existing_users = []
        other_message = None
        company_id = company_ins.first()

        this_company_name = company_ins.first().company_name

        pay_schedule = None
        pay_run = None
        employee_type = None

        company_settings = CompanyPayrollSettings.objects.filter(
            company=company_id
        ).last()
        if company_settings:
            pay_schedule = company_settings.pay_schedule
            pay_run = company_settings.pay_run
            employee_type = company_settings.employee_type

        company_name = this_company_name.replace(" ", "%20")
        for employee in all_employee:
            employee_email = employee["employee_email"]
            employee_instance = User.objects.filter(email=employee_email).first()
            employee_det = CompanyEmployeeList.objects.filter(
                company__id=company_uuid,
                employee_email=employee_email,
                is_deleted=False,
            ).first()

            employee_department = employee.get("employee_department")
            employee_salary_grade = employee.get("employee_salary_grade")
            employee_gender = employee.get("employee_gender")
            employee_bank_name = employee.get("employee_bank_name")
            employee_bank_code = employee.get("employee_bank_code")
            employee_account_number = employee.get("employee_account_number")
            employee_account_name = employee.get("employee_account_name")

            employee_pension_amount = employee.get("employee_pension_amount")
            employee_tax_amount = employee.get("employee_tax_amount")
            employee_other_amount = employee.get("employee_other_amount")
            employee_other_deductions = employee.get("employee_other_deductions")
            employee_hmo_amount = employee.get("employee_hmo_amount")

            employee_gross_amount = employee["employee_gross_amount"]
            employee_net_amount = employee["employee_net_amount"]
            employee_first_name = employee["employee_first_name"]
            employee_last_name = employee["employee_last_name"]
            employee_payable_amount = employee["employee_payable_amount"]

            unformatted_phone_number = employee.get("employee_phone_number")
            if unformatted_phone_number:
                employee_phone_number = User.format_number_from_back_add_234(
                    unformatted_phone_number
                )
            else:
                employee_phone_number = unformatted_phone_number

            if employee_det:
                existing_users.append(employee_det.employee_email)
            else:
                if employee_instance:
                    # if employee_instance.is_active is True:
                    # owner = company_ins.filter(user__email=employee_email).first()
                    # if owner:
                    #     other_message = "cannot add company as employee"
                    # else:
                    employee_list = CompanyEmployeeList.objects.create(
                        employer=company_id.user,
                        employee=employee_instance,
                        added_by=user,
                        company=company_id,
                        employee_gross_amount=employee_gross_amount,
                        employee_net_amount=employee_net_amount,
                        employee_email=employee_email,
                        employee_payable_amount=employee_payable_amount,
                        employee_status="ACTIVE",
                        is_active=True,
                        employee_first_name=employee_first_name.title(),
                        employee_last_name=employee_last_name.title(),
                        # employee_phone_number=employee_phone_number,
                    )
                    if employee_department:
                        employee_list.employee_department = employee_department
                    if employee_salary_grade:
                        employee_list.employee_salary_grade = employee_salary_grade
                    if employee_gender:
                        employee_list.employee_gender = employee_gender
                    if employee_bank_name:
                        employee_list.employee_bank_name = employee_bank_name
                    if employee_bank_code:
                        employee_list.employee_bank_code = employee_bank_code
                    if employee_account_number:
                        employee_list.employee_account_number = employee_account_number
                    if employee_account_name:
                        employee_list.employee_account_name = employee_account_name

                    if employee_pension_amount:
                        employee_list.employee_pension_amount = employee_pension_amount
                    if employee_tax_amount:
                        employee_list.employee_tax_amount = employee_tax_amount
                    if employee_other_amount:
                        employee_list.employee_other_amount = employee_other_amount
                    if employee_other_deductions:
                        employee_list.employee_other_deductions = (
                            employee_other_deductions
                        )
                    if employee_hmo_amount:
                        employee_list.employee_hmo_amount = employee_hmo_amount
                    if employee_phone_number:
                        employee_list.employee_phone_number = employee_phone_number

                    employee_list.pay_schedule = pay_schedule
                    employee_list.pay_run = pay_run
                    employee_list.staff_type = employee_type
                    employee_list.save()

                    send_email.delay(
                        recipient=employee_email,
                        subject="Payroll Invite",
                        template_dir="team_invite.html",
                        team_name="PAYROLL",
                        company_name=this_company_name,
                        call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_id.id}/{company_name}/salary-dashboard",
                    )
                    invited_users.append(employee_email)
                    # else:
                    #     employee_list = CompanyEmployeeList.objects.create(
                    #         employer=company_id.user,
                    #         employee=employee_instance,
                    #         added_by=user,
                    #         company=company_id,
                    #         employee_gross_amount=employee_gross_amount,
                    #         employee_net_amount=employee_net_amount,
                    #         # employee_salary_grade=employee_salary_grade,
                    #         # employee_pension_amount=employee_pension_amount,
                    #         # employee_tax_amount=employee_tax_amount,
                    #         # employee_other_amount=employee_other_amount,
                    #         # employee_other_deductions=employee_other_deductions,
                    #         # employee_hmo_amount=employee_hmo_amount,
                    #         # employee_gender=employee_gender,
                    #         # employee_bank_code=employee_bank_code,
                    #         # employee_bank_name=employee_bank_name,
                    #         # employee_account_number=employee_account_number,
                    #         # employee_account_name=employee_account_name,
                    #         employee_email=employee_email,
                    #         employee_payable_amount=employee_payable_amount,
                    #         employee_status="SUSPENDED",
                    #         is_suspended=True,
                    #         employee_first_name=employee_first_name,
                    #         employee_last_name=employee_last_name,
                    #         employee_phone_number=employee_phone_number,
                    #     )
                    #     inactive_users.append(employee_email)
                    #     send_email.delay(recipient=employee_email, subject="Payroll Invite",
                    #                      template_dir="team_invite.html",
                    #                      team_name="PAYROLL", company_name=this_company_name,
                    #                      call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_id.id}/{company_name}/salary-dashboard")
                else:
                    employee_list = CompanyEmployeeList.objects.create(
                        employer=company_id.user,
                        added_by=user,
                        company=company_id,
                        employee_status="NOT_JOINED",
                        employee_gross_amount=employee_gross_amount,
                        employee_net_amount=employee_net_amount,
                        employee_email=employee_email,
                        employee_payable_amount=employee_payable_amount,
                        employee_first_name=employee_first_name,
                        employee_last_name=employee_last_name,
                        # employee_phone_number=employee_phone_number,
                    )

                    if employee_department:
                        employee_list.employee_department = employee_department
                    if employee_salary_grade:
                        employee_list.employee_salary_grade = employee_salary_grade
                    if employee_gender:
                        employee_list.employee_gender = employee_gender
                    if employee_bank_name:
                        employee_list.employee_bank_name = employee_bank_name
                    if employee_bank_code:
                        employee_list.employee_bank_code = employee_bank_code
                    if employee_account_number:
                        employee_list.employee_account_number = employee_account_number
                    if employee_account_name:
                        employee_list.employee_account_name = employee_account_name

                    if employee_pension_amount:
                        employee_list.employee_pension_amount = employee_pension_amount
                    if employee_tax_amount:
                        employee_list.employee_tax_amount = employee_tax_amount
                    if employee_other_amount:
                        employee_list.employee_other_amount = employee_other_amount
                    if employee_other_deductions:
                        employee_list.employee_other_deductions = (
                            employee_other_deductions
                        )
                    if employee_hmo_amount:
                        employee_list.employee_hmo_amount = employee_hmo_amount
                    if employee_phone_number:
                        employee_list.employee_phone_number = employee_phone_number

                    employee_list.pay_schedule = pay_schedule
                    employee_list.pay_run = pay_run
                    employee_list.staff_type = employee_type
                    employee_list.save()

                    invited_users.append(employee_email)
                    send_email.delay(
                        recipient=employee_email,
                        subject="Payroll Invite",
                        template_dir="team_invite.html",
                        team_name="PAYROLL",
                        company_name=this_company_name,
                        call_back_url="https://www.home.paybox360.com/sign-up/get-started",
                        # call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_id.id}/{company_name}/salary-dashboard",
                    )
        return {
            "status": True,
            "message": "successful",
            "invited_users": invited_users,
            "inactive_users": inactive_users,
            "existing_users": existing_users,
            "other_message": other_message,
        }

    @classmethod
    def create_employee_invite(cls, company_ins, all_employee, user):

        invited_users = []
        inactive_users = []
        company_id = company_ins.id

        pay_schedule = None
        pay_run = None
        employee_type = None
        company_settings = CompanyPayrollSettings.objects.filter(
            company=company_ins
        ).last()
        if company_settings:
            pay_schedule = company_settings.pay_schedule
            pay_run = company_settings.pay_run
            employee_type = company_settings.employee_type

        for employee in all_employee:
            employee_email = employee["email"]
            employee_det = CompanyEmployeeList.objects.filter(
                company__id=company_id, employee_email=employee_email, is_deleted=False
            ).last()
            if employee_det:
                inactive_users.append(employee_email)
            else:
                this_employee = CompanyEmployeeList.objects.create(
                    employer=company_ins.user,
                    company=company_ins,
                    added_by=user,
                    employee_email=employee_email,
                    employee_status="NOT_JOINED",
                )
                this_employee.pay_schedule = pay_schedule
                this_employee.pay_run = pay_run
                this_employee.staff_type = employee_type
                this_employee.save()

                employee_ins = CompanyEmployeeOnboardingForm.objects.filter(
                    employee_email=employee_email, employee_company=company_ins
                ).first()
                if employee_ins:
                    pass
                else:
                    CompanyEmployeeOnboardingForm.objects.create(
                        added_by=user,
                        employee_company=company_ins,
                        employee_email=employee_email,
                    )
                    invited_users.append(employee_email)
        return {
            "status": True,
            "message": "successful",
            "invited_users": invited_users,
            "un_invited_users": inactive_users,
        }

    @classmethod
    def invite_employee(cls, company_ins, email):
        this_company_name = company_ins.company_name
        company_name = this_company_name.replace(" ", "%20")
        send_email.delay(
            recipient=email,
            subject="Payroll Invite",
            template_dir="team_invite.html",
            team_name="PAYROLL",
            company_name=this_company_name,
            call_back_url="https://www.home.paybox360.com/sign-up/get-started",
            # call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_ins.id}/{company_name}/salary-dashboard",
        )
        return {"status": True, "message": "successful", "invited_users": email}

    @classmethod
    def agency_banking_onboarding(cls, **kwargs):
        from payroll_app.services import EmployeeOnboarding

        phone_number = kwargs.get("phone_number")
        username = kwargs.get("username")
        first_name = kwargs.get("first_name")
        last_name = kwargs.get("last_name")
        email = kwargs.get("email")
        state = kwargs.get("state")
        lga = kwargs.get("lga")
        nearest_landmark = kwargs.get("nearest_landmark")
        street = kwargs.get("street")
        gender = kwargs.get("gender")
        device_type = kwargs.get("device_type")

        agency_banking_resp = EmployeeOnboarding.send_agency_onboarding_data(
            phone_number=phone_number,
            username=username,
            first_name=first_name,
            last_name=last_name,
            email=email,
            state=state,
            lga=lga,
            nearest_landmark=nearest_landmark,
            street=street,
            gender=gender,
            device_type=device_type,
        )
        if agency_banking_resp.get("status") == "201":
            agency_data = {"success": True, "data": agency_banking_resp}
        else:
            agency_data = {"success": False, "data": agency_banking_resp}
        return agency_data
    
    @classmethod
    def agency_banking_phone_onboarding(cls, **kwargs):
        from payroll_app.services import EmployeeOnboarding

        phone_number = kwargs.get("phone_number")
        username = kwargs.get("username")
        first_name = kwargs.get("first_name")
        last_name = kwargs.get("last_name")
        email = kwargs.get("email")
        state = kwargs.get("state")
        lga = kwargs.get("lga")
        nearest_landmark = kwargs.get("nearest_landmark")
        street = kwargs.get("street")
        gender = kwargs.get("gender")
        device_type = kwargs.get("device_type")

        agency_banking_resp = EmployeeOnboarding.send_agency_onboarding_phone_data(
            phone_number=phone_number,
            username=username,
            first_name=first_name,
            last_name=last_name,
            email=email,
            state=state,
            lga=lga,
            nearest_landmark=nearest_landmark,
            street=street,
            gender=gender,
            device_type=device_type,
        )
        if agency_banking_resp.get("status") == "201":
            agency_data = {"success": True, "data": agency_banking_resp}
        else:
            agency_data = {"success": False, "data": agency_banking_resp}
        return agency_data

    @classmethod
    def staff_id_exist(cls, staff_id, company):
        this_staff_id = staff_id.lower()
        if cls.objects.filter(
            employee_staff_id=this_staff_id, company=company
        ).exists():
            return True
        else:
            return False

    @classmethod
    def auto_generate_staff_id(cls, date, company):
        this_settings = CompanyPayrollSettings.objects.filter(company=company).first()
        if this_settings.staff_id_prefix:
            prefix = this_settings.staff_id_prefix
        else:
            prefix = "PAY"

        # Get the current date in YYYYMM format
        current_date = date.strftime("%Y%m")

        unique_number = random.randint(********, ********)  # Random 8-digit number

        # Format the unique number with leading zeros
        unique_number_str = str(unique_number).zfill(8)

        # Generate the full employee ID
        staff_id = f"{prefix}-{current_date}-{unique_number_str}"

        return staff_id

    @classmethod
    def add_generate_staff_id(cls, date, company):
        staff_id = cls.auto_generate_staff_id(date, company)
        while cls.staff_id_exist(staff_id, company):
            staff_id = cls.auto_generate_staff_id(date, company)
        return staff_id

    @classmethod
    def get_payroll_charges(cls, employee_count):
        """
        Sets the payroll charges based on the number of employees.
        """
        if employee_count < 50:
            charge = 2000
        elif 50 <= employee_count < 100:
            charge = 1500
        else:
            charge = 1000

        return charge

class CompanyDetailsData(models.Model):
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, related_name="company_details"
    )
    is_recurring = models.BooleanField(default=False)
    payroll_initiated = models.DateField(null=True, blank=True)
    bulk_id = models.CharField(max_length=200, null=True, blank=True)
    payroll_date_completed = models.DateTimeField(null=True, blank=True)
    narration = models.CharField(max_length=200, null=True, blank=True)
    payroll_month = models.CharField(max_length=200, null=True, blank=True)
    payroll_year = models.CharField(max_length=200, null=True, blank=True)
    pension_xcel_file = models.JSONField(null=True, blank=True, default=dict)
    payroll_date = models.DateField(null=True, blank=True)
    disbursed_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="disbursed_payroll",
    )
    deleted_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="deleted_payroll",
    )
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "COMPANY PAYROLL DETAIL"
        verbose_name_plural = "COMPANY PAYROLL DETAILS"


class InstantWagePayroll(models.Model):
    instant_wage_user = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="instant_wage_user",
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="instant_wage_employer",
    )
    receiver = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="instant_wage_receiver",
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="instant_wage_company",
        null=True,
        blank=True,
    )
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    transaction_reference = models.CharField(max_length=255, blank=True, null=True)
    fund_reference = models.CharField(max_length=255, blank=True, null=True)
    paid = models.BooleanField(default=False)
    refund_paid_date = models.DateTimeField(null=True, blank=True)
    employee_due_payment = models.BooleanField(default=True)
    paid_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    company_paid = models.BooleanField(default=False)
    date_collected = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "INSTANT WAGE"
        verbose_name_plural = "INSTANT WAGES"


class CompanyAnnouncement(models.Model):

    ANNOUNCEMENT_TYPE = [
        ("ANNOUNCEMENT", "ANNOUNCEMENT"),
        ("EVENT", "EVENT"),
    ]
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="announcement_company",
        null=True,
        blank=True,
    )
    announcement_type = models.CharField(
        max_length=200, choices=ANNOUNCEMENT_TYPE, default="ANNOUNCEMENT"
    )
    announcement_title = models.CharField(null=True, blank=True, max_length=200)
    announcement_body = models.TextField(null=True, blank=True)
    announcement_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "COMPANY ANNOUNCEMENT"
        verbose_name_plural = "COMPANY ANNOUNCEMENTS"


class InstantWagePayrollRefund(models.Model):
    employee = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="instant_wage_refund_user",
    )
    receiver = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="instant_wage_refund_receiver",
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="instant_refund_company",
        null=True,
        blank=True,
    )
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    balance = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    transaction_reference = models.CharField(max_length=255, blank=True, null=True)
    fund_reference = models.CharField(max_length=255, blank=True, null=True)
    refund_completed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "INSTANT WAGE REFUND"
        verbose_name_plural = "INSTANT WAGE REFUNDS"


class Beneficiary(models.Model):
    user = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="beneficiary_owner",
    )
    account_number = models.CharField(null=True, blank=True, max_length=255)
    account_name = models.CharField(null=True, blank=True, max_length=255)
    bank_code = models.CharField(null=True, blank=True, max_length=255)
    narration = models.CharField(null=True, blank=True, max_length=255)
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    bank_name = models.CharField(null=True, blank=True, max_length=255)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "BENEFICIARY"
        verbose_name_plural = "BENEFICIARIES"

    @classmethod
    def create_single_multiple_beneficiary(cls, user, all_beneficiary):
        successful_beneficiary = []
        unsuccessful_beneficiary = []
        for beneficiary in all_beneficiary:
            account_number = beneficiary["account_number"]
            account_name = beneficiary["account_name"]
            bank_code = beneficiary["bank_code"]
            narration = beneficiary["narration"]
            amount = beneficiary["amount"]
            bank_name = beneficiary["bank_name"]

            beneficiary_instance = Beneficiary.objects.filter(
                account_number=account_number, bank_code=bank_code, is_deleted=False
            ).last()
            if beneficiary_instance:
                unsuccessful_beneficiary.append(
                    {
                        "account_number": account_number,
                        "account_name": account_name,
                        "bank_code": bank_code,
                        "bank_name": bank_name,
                        "narration": narration,
                        "amount": amount,
                    }
                )
            else:
                create_beneficiary_instance = Beneficiary.objects.create(
                    user=user,
                    account_number=account_number,
                    account_name=account_name,
                    bank_code=bank_code,
                    narration=narration,
                    bank_name=bank_name,
                    amount=amount,
                )
                successful_beneficiary.append(
                    {
                        "account_number": account_number,
                        "account_name": account_name,
                        "bank_code": bank_code,
                        "bank_name": bank_name,
                        "narration": narration,
                        "amount": amount,
                    }
                )

        return {
            "message": "successful",
            "successful_beneficiary": successful_beneficiary,
            "unsuccessful_beneficiary": unsuccessful_beneficiary,
        }


class OneClickTransaction(models.Model):
    SUCCESSFUL = "SUCCESSFUL"
    PENDING = "PENDING"
    FAILED = "FAILED"

    STATUS_CHOICES = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (PENDING, "PENDING"),
        (FAILED, "FAILED"),
    ]

    user = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="one_click_user",
    )
    account_number = models.CharField(null=True, blank=True, max_length=255)
    account_name = models.CharField(null=True, blank=True, max_length=255)
    bank_code = models.CharField(null=True, blank=True, max_length=255)
    narration = models.CharField(null=True, blank=True, max_length=255)
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    bank_name = models.CharField(null=True, blank=True, max_length=255)
    transaction_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    status_code = models.CharField(max_length=255, blank=True, null=True)
    disbursed = models.BooleanField(default=False)
    status = models.CharField(
        max_length=150, choices=STATUS_CHOICES, default="PENDING", null=True, blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "ONE CLICK TRANSACTION"
        verbose_name_plural = "ONE CLICK TRANSACTIONS"


class CompanyPayrollSettings(BaseModel):

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_payroll_settings",
        null=True,
        blank=True,
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employer_payroll_settings",
    )
    company_name = models.CharField(null=True, blank=True, max_length=255)
    company_address = models.CharField(null=True, blank=True, max_length=255)
    company_official_email = models.EmailField(null=True, blank=True)
    company_logo = models.TextField(blank=True, null=True)
    pay_schedule = models.CharField(
        null=True, blank=True, max_length=255, choices=PAY_SCHEDULE, default="MONTHLY"
    )
    pay_run = models.CharField(
        null=True, blank=True, max_length=255, choices=PAY_RUN, default="BASIC"
    )
    employee_type = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=EMPLOYEE_TYPE,
        default="FULL_TIME",
    )
    is_active = models.BooleanField(default=False)
    authorize_instant_wage = models.BooleanField(default=False)
    automate_payroll = models.BooleanField(default=False)
    standard_tax = models.BooleanField(default=False)
    pension = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    resumption_time = models.TimeField(null=True, blank=True, default=dt.time(8, 00))
    closing_time = models.TimeField(null=True, blank=True, default=dt.time(17, 30))
    has_shifts = models.BooleanField(default=False)
    break_duration = models.DurationField(
        null=True, blank=True, default=timedelta(minutes=30)
    )
    work_duration = models.DurationField(null=True, blank=True)
    overtime_duration = models.DurationField(
        null=True, blank=True, default=timedelta(minutes=60)
    )
    lateness_addition = models.DurationField(
        null=True, blank=True, default=timedelta(minutes=20)
    )
    hybrid_monday = models.BooleanField(default=False)
    hybrid_tuesday = models.BooleanField(default=False)
    hybrid_wednesday = models.BooleanField(default=False)
    hybrid_thursday = models.BooleanField(default=False)
    hybrid_friday = models.BooleanField(default=False)
    hybrid_saturday = models.BooleanField(default=False)
    hybrid_sunday = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    clock_in_anywhere = models.BooleanField(default=True)
    send_daily_report = models.BooleanField(default=False)
    send_weekly_report = models.BooleanField(default=False)
    hr_email = models.EmailField(null=True, blank=True)
    admin_email = models.EmailField(null=True, blank=True)
    country = models.CharField(null=True, blank=True, max_length=255)
    state = models.CharField(null=True, blank=True, max_length=255)
    business_name_number = models.CharField(null=True, blank=True, max_length=255)
    industry = models.CharField(null=True, blank=True, max_length=255)
    contact_name = models.CharField(null=True, blank=True, max_length=255)
    contact_phone_number = models.CharField(null=True, blank=True, max_length=255)
    contact_role = models.CharField(null=True, blank=True, max_length=255)
    employee_contribution_pension_amount = models.FloatField(
        null=True,
        blank=True,
        default=8.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
    )
    employer_contribution_pension_amount = models.FloatField(
        null=True,
        blank=True,
        default=10.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
    )
    employer_pension_code = models.CharField(null=True, blank=True, max_length=255)
    company_email = models.EmailField(null=True, blank=True)
    staff_id_prefix = models.CharField(null=True, blank=True, max_length=10)
    charge = models.FloatField(
        null=True, blank=True, default=2000.0, validators=[MinValueValidator(0.0)]
    )
    payroll_notification = models.BooleanField(default=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY PAYROLL SETTING"
        verbose_name_plural = "COMPANY PAYROLL SETTINGS"

    @property
    def start_resumption_time(self):
        # Check if duration is None or represents zero duration
        if (
            self.lateness_addition is None
            or self.lateness_addition == timezone.timedelta()
        ):
            return self.resumption_time if self.resumption_time else time(8, 00)
        else:
            if self.resumption_time:
                # Calculate and return the resumption time based on resumption time and lateness addition
                start_datetime = timezone.now().replace(
                    hour=self.resumption_time.hour, minute=self.resumption_time.minute
                )
                end_datetime = start_datetime + self.lateness_addition
                return end_datetime.time()
            else:
                return time(8, 00)

    @property
    def calculate_total_seconds(self):
        # Get the current timezone-aware datetime
        now = timezone.now()
        if self.closing_time and self.resumption_time:
            if self.closing_time >= self.resumption_time:
                # Combine time fields with today's date and the current timezone
                start_datetime = timezone.make_aware(
                    timezone.datetime.combine(now.date(), self.resumption_time),
                    timezone.get_current_timezone(),
                )
                end_datetime = timezone.make_aware(
                    timezone.datetime.combine(now.date(), self.closing_time),
                    timezone.get_current_timezone(),
                )
                # Calculate the time difference
                time_diff = end_datetime - start_datetime
                # Convert time difference to seconds
                total_seconds = time_diff.total_seconds()  # 3600 seconds in an hour
                return total_seconds
            else:
                return 0
        else:
            return 0

    @classmethod
    def create_company_payroll_settings(cls, company):
        payroll_setting_data = cls.objects.filter(company=company).first()
        if payroll_setting_data:
            basic_data = SalaryComponentSettings.objects.filter(
                company=company, salary_name="Basic"
            ).first()
            if basic_data:
                pass
            else:
                SalaryComponentSettings.objects.create(
                    company=company,
                    employer=company.user,
                    salary_name="Basic",
                    default_component=True,
                    is_active=False,
                )
            transport_data = SalaryComponentSettings.objects.filter(
                company=company, salary_name="Transport"
            ).first()
            if transport_data:
                pass
            else:
                SalaryComponentSettings.objects.create(
                    company=company,
                    employer=company.user,
                    salary_name="Transport",
                    default_component=True,
                    is_active=False,
                )
            housing_data = SalaryComponentSettings.objects.filter(
                company=company, salary_name="Housing"
            ).first()
            if housing_data:
                pass
            else:
                SalaryComponentSettings.objects.create(
                    company=company,
                    employer=company.user,
                    salary_name="Housing",
                    default_component=True,
                    is_active=False,
                )
        else:
            cls.objects.create(
                company=company,
                employer=company.user,
                company_name=company.company_name,
                is_active=True,
            )
            basic_data = SalaryComponentSettings.objects.filter(
                company=company, salary_name="Basic"
            ).first()
            if basic_data:
                pass
            else:
                SalaryComponentSettings.objects.create(
                    company=company,
                    employer=company.user,
                    salary_name="Basic",
                    default_component=True,
                    is_active=False,
                )
            transport_data = SalaryComponentSettings.objects.filter(
                company=company, salary_name="Transport"
            ).first()
            if transport_data:
                pass
            else:
                SalaryComponentSettings.objects.create(
                    company=company,
                    employer=company.user,
                    salary_name="Transport",
                    default_component=True,
                    is_active=False,
                )
            housing_data = SalaryComponentSettings.objects.filter(
                company=company, salary_name="Housing"
            ).first()
            if housing_data:
                pass
            else:
                SalaryComponentSettings.objects.create(
                    company=company,
                    employer=company.user,
                    salary_name="Housing",
                    default_component=True,
                    is_active=False,
                )

    @classmethod
    def pfa_switch(cls, company_id):
        company_ins = cls.objects.filter(company__id=company_id).last()
        if company_ins:
            return company_ins.automate_payroll
        else:
            return False


class CompanyDepartmentSettings(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_department_settings",
        null=True,
        blank=True,
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employer_department_settings",
    )
    department_name = models.CharField(null=True, blank=True, max_length=255)
    department_head_name = models.CharField(null=True, blank=True, max_length=255)
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="department_added_by",
    )
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY DEPARTMENT"
        verbose_name_plural = "COMPANY DEPARTMENTS"

    def __str__(self):
        return str(self.department_name)


class CompanyPayGroupSettings(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_pay_group_settings",
        null=True,
        blank=True,
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employer_pay_group_settings",
    )

    pay_group_name = models.CharField(null=True, blank=True, max_length=255)
    pay_group_description = models.CharField(null=True, blank=True, max_length=255)
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="pay_group_added_by",
    )
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY PAY GROUP SETTING"
        verbose_name_plural = "COMPANY PAY GROUP SETTINGS"

    def __str__(self):
        return str(self.pay_group_name)


class CompanyPayGradeSettings(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_pay_grade_settings",
        null=True,
        blank=True,
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employer_pay_grade_settings",
    )

    pay_group = models.ForeignKey(
        CompanyPayGroupSettings,
        on_delete=models.CASCADE,
        related_name="pay_group_pay_grade",
        null=True,
        blank=True,
    )
    pay_grade_name = models.CharField(null=True, blank=True, max_length=255)
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    pay_schedule = models.CharField(
        null=True, blank=True, max_length=255, choices=PAY_SCHEDULE, default="MONTHLY"
    )
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="pay_grade_added_by",
    )
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY PAY GRADE SETTING"
        verbose_name_plural = "COMPANY PAY GRADE SETTINGS"

    def __str__(self):
        return str(self.pay_grade_name)

    @classmethod
    def fetch_pay_grade(cls, pay_grade_id, company_id):
        pay_grade_ins = cls.objects.filter(
            id=pay_grade_id, company__id=company_id, is_deleted=False
        ).first()
        if pay_grade_ins:
            return pay_grade_ins
        else:
            return None

class OtherDeductionSettings(BaseModel):

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_deduction_settings",
        null=True,
        blank=True,
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employer_deduction_settings",
    )
    deduction_name = models.CharField(null=True, blank=True, max_length=255)
    deduction_type = models.CharField(
        null=True, blank=True, max_length=255, choices=DEDUCTION_TYPE, default="OTHERS"
    )
    calculation_type = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=DEDUCTION_CALCULATION_TYPE,
        default="FIXED_AMOUNT",
    )
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    custom_name = models.ManyToManyField(
        "payroll_app.SalaryComponentSettings",
        blank=True,
        related_name="custom_deduction_component",
    )
    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)
    frequency = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=COMPONENT_FREQUENCY,
        default="MONTHLY",
    )
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="deduction_added_by",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.deduction_name)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY DEDUCTION SETTING"
        verbose_name_plural = "COMPANY DEDUCTION SETTINGS"


class OtherDependencySettings(BaseModel):
    deduction = models.ForeignKey(
        OtherDeductionSettings,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="other_dependency_settings",
    )
    calculation_type = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=DEDUCTION_CALCULATION_TYPE,
        default="FIXED_AMOUNT",
    )
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    custom_name = models.ManyToManyField(
        "payroll_app.SalaryComponentSettings",
        blank=True,
        related_name="custom_dependency_component",
    )
    dependency_operator = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=DEPENDENCY_OPERATOR,
        default="NULL",
    )
    operator_type = models.CharField(
        null=True, blank=True, max_length=255, choices=OPERATOR_TYPE, default="NULL"
    )
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="dependency_added_by",
    )
    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.deduction.deduction_name)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "OTHER DEPENDENCY"
        verbose_name_plural = "OTHER DEPENDENCIES"


class SalaryComponentSettings(BaseModel):

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_salary_component_settings",
        null=True,
        blank=True,
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employer_salary_component_settings",
    )
    salary_name = models.CharField(null=True, blank=True, max_length=255)
    calculation_type = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=ALL_SALARY_COMPONENT_CALCULATION_TYPE,
        default="FIXED_AMOUNT",
    )
    frequency = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=COMPONENT_FREQUENCY,
        default="MONTHLY",
    )
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="salary_component_added_by",
    )
    default_component = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY SALARY COMPONENT SETTING"
        verbose_name_plural = "COMPANY SALARY COMPONENT SETTINGS"

    def __str__(self):
        return str(self.salary_name)


class CustomComponentSettings(BaseModel):

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="custom_component_settings",
        null=True,
        blank=True,
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employer_custom_component_settings",
    )
    custom_component_name = models.CharField(null=True, blank=True, max_length=255)
    calculation_type = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=SALARY_COMPONENT_CALCULATION_TYPE,
        default="FIXED_AMOUNT",
    )
    frequency = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=COMPONENT_FREQUENCY,
        default="MONTHLY",
    )
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    custom_name = models.ManyToManyField(
        "payroll_app.SalaryComponentSettings",
        blank=True,
        related_name="custom_component",
    )
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    part_of_employee_structure = models.BooleanField(default=False)
    calculate_pay_schedule_basis = models.BooleanField(default=False)
    show_on_payslip = models.BooleanField(default=False)
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="custom_component_added_by",
    )
    edited_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="custom_component_edited_by",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY CUSTOM COMPONENT SETTING"
        verbose_name_plural = "COMPANY CUSTOM COMPONENT SETTINGS"

    def __str__(self):
        return str(self.custom_name)


class BenefitComponentSettings(BaseModel):

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="benefit_component_settings",
        null=True,
        blank=True,
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employer_benefit_component_settings",
    )
    benefit_name = models.CharField(null=True, blank=True, max_length=255)
    calculation_type = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=SALARY_COMPONENT_CALCULATION_TYPE,
        default="FIXED_AMOUNT",
    )
    frequency = models.CharField(
        null=True,
        blank=True,
        max_length=255,
        choices=COMPONENT_FREQUENCY,
        default="MONTHLY",
    )
    amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    custom_name = models.ManyToManyField(
        "payroll_app.SalaryComponentSettings",
        blank=True,
        related_name="custom_benefit_component",
    )
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="benefit_component_added_by",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY BENEFIT SETTING"
        verbose_name_plural = "COMPANY BENEFIT SETTINGS"

    def __str__(self):
        return str(self.benefit_name)


class CompanyEmployeeOnboardingForm(BaseModel):
    ONBOARDING_STATUS = [
        ("NULL", "NULL"),
        ("DELETED", "DELETED"),
        ("AWAITING_APPROVAL", "AWAITING_APPROVAL"),
        ("APPROVED", "APPROVED"),
    ]
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="invite_admin_form",
    )
    employee_email = models.EmailField(null=True, blank=True)
    employee_company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="onboarding_employee_form",
        null=True,
        blank=True,
    )
    invite_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    employee_first_name = models.CharField(null=True, blank=True, max_length=255)
    employee_last_name = models.CharField(null=True, blank=True, max_length=255)
    employee_phone_number = models.CharField(null=True, blank=True, max_length=255)
    employee_alternate_email = models.CharField(null=True, blank=True, max_length=255)
    employee_birth_date = models.DateField(null=True, blank=True, max_length=255)
    employee_gender = models.CharField(null=True, blank=True, max_length=255)
    employee_staff_id = models.CharField(null=True, blank=True, max_length=255)
    employee_staff_id_reporting_line = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_state = models.CharField(null=True, blank=True, max_length=255)
    employee_city = models.CharField(null=True, blank=True, max_length=255)
    employee_town = models.CharField(null=True, blank=True, max_length=255)
    employee_bus_stop = models.CharField(null=True, blank=True, max_length=255)
    employee_street_name = models.CharField(null=True, blank=True, max_length=255)
    employee_house_no = models.CharField(null=True, blank=True, max_length=255)
    employee_postal_code = models.CharField(null=True, blank=True, max_length=255)
    employee_first_next_of_kin_firstname = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_first_next_of_kin_lastname = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_first_next_of_kin_phone_number = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_first_next_of_kin_relationship = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_first_next_of_kin_address = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_firstname = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_lastname = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_phone_number = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_relationship = models.CharField(
        null=True, blank=True, max_length=255
    )
    employee_second_next_of_kin_address = models.CharField(
        null=True, blank=True, max_length=255
    )
    is_completed = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    approval_status = models.CharField(
        null=True, blank=True, max_length=255, choices=ONBOARDING_STATUS, default="NULL"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY EMPLOYEE ONBOARDING FORM"
        verbose_name_plural = "COMPANY EMPLOYEE ONBOARDING FORMS"

    def __str__(self):
        return str(self.employee_email)


class CompanyEmployeeAccountDetails(BaseModel):
    employee = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_account_owner",
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="employee_account_details",
        null=True,
        blank=True,
    )
    bank_name = models.CharField(null=True, blank=True, max_length=255)
    account_name = models.CharField(null=True, blank=True, max_length=255)
    account_number = models.CharField(null=True, blank=True, max_length=255)
    bvn_number = models.CharField(null=True, blank=True, max_length=255)
    bank_code = models.CharField(null=True, blank=True, max_length=255)
    swift_code = models.CharField(null=True, blank=True, max_length=255)
    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY EMPLOYEE ACCOUNT DETAIL"
        verbose_name_plural = "COMPANY EMPLOYEE ACCOUNT DETAILS"

    def __str__(self):
        return str(self.employee)


class CompanyEmployeeEducationDetails(BaseModel):
    employee = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_education",
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_employee_education",
        null=True,
        blank=True,
    )
    school_name = models.CharField(null=True, blank=True, max_length=255)
    course_name = models.CharField(null=True, blank=True, max_length=255)
    degree = models.CharField(null=True, blank=True, max_length=255)
    grade = models.CharField(null=True, blank=True, max_length=255)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    additional_comment = models.TextField(null=True, blank=True)
    proof_of_qualification = models.CharField(null=True, blank=True, max_length=255)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY EMPLOYEE EDUCATION DETAIL"
        verbose_name_plural = "COMPANY EMPLOYEE EDUCATION DETAILS"

    def __str__(self):
        return str(self.employee)


class CompanyEmployeeExperienceDetails(BaseModel):
    employee = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_experience",
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_employee_experience",
        null=True,
        blank=True,
    )
    employer_name = models.CharField(null=True, blank=True, max_length=255)
    position = models.CharField(null=True, blank=True, max_length=255)
    employer_address = models.CharField(null=True, blank=True, max_length=255)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    employer_phone_number = models.TextField(null=True, blank=True)
    employer_email = models.EmailField(null=True, blank=True)
    reason_for_leaving = models.CharField(null=True, blank=True, max_length=255)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY EMPLOYEE EXPERIENCE DETAIL"
        verbose_name_plural = "COMPANY EMPLOYEE EXPERIENCE DETAILS"

    def __str__(self):
        return str(self.employee)


class CompanyEmployeeCertifications(BaseModel):
    employee = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_certifications",
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_employee_certifications",
        null=True,
        blank=True,
    )
    institution_name = models.CharField(null=True, blank=True, max_length=255)
    course_name = models.CharField(null=True, blank=True, max_length=255)
    license_name = models.CharField(null=True, blank=True, max_length=255)
    issuing_body = models.CharField(null=True, blank=True, max_length=255)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    proof_of_qualification = models.TextField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY EMPLOYEE CERTIFICATION"
        verbose_name_plural = "COMPANY EMPLOYEE CERTIFICATIONS"

    def __str__(self):
        return str(self.employee)


class CompanyTaxBand(models.Model):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_tax_band",
        null=True,
        blank=True,
    )
    start_band = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    end_band = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    tax_rate = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="tax_band_added_by",
    )
    deduction_amount = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    is_deleted = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "COMPANY TAX BAND"
        verbose_name_plural = "COMPANY TAX BANDS"

    def __str__(self):
        return f"{self.start_band}-{self.end_band}"


class PensionFundAdminSettings(models.Model):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_pfa_settings",
        null=True,
        blank=True,
    )
    employer = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employer_pfa_settings",
    )
    pfa_name = models.CharField(null=True, blank=True, max_length=255)
    pfa_account_number = models.CharField(null=True, blank=True, max_length=255)
    pfa_account_name = models.CharField(null=True, blank=True, max_length=255)
    pfa_bank_name = models.CharField(null=True, blank=True, max_length=255)
    pfa_bank_code = models.CharField(null=True, blank=True, max_length=255)
    pfa_sort_code = models.CharField(null=True, blank=True, max_length=255)
    company_code = models.CharField(null=True, blank=True, max_length=255)
    pfa_email = models.EmailField(null=True, blank=True)
    added_by = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="pfa_added_by",
    )
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PENSION FUND ADMINISTRATOR"
        verbose_name_plural = "PENSION FUND ADMINISTRATORS"

    def __str__(self):
        return str(self.pfa_name)

    @classmethod
    def company_pfa_data(cls, company_id, bulk_id):
        count = 0
        amount = 0
        base_qs = PayrollTable.objects.filter(
            company__id=company_id,
            status="DISBURSE",
            bulk_id=bulk_id,
            payroll_deleted=False,
            employee__pension_fund_admin__isnull=False,
        )
        unique_pfa = base_qs.values("employee__pension_fund_admin").distinct()

        # get the constant table
        const_table = ConstantTable.get_constant_instance()
        skip_pension_id = const_table.skip_pension_id
        skip_company_pension_id = const_table.skip_company_pension_id

        for pfa in unique_pfa:
            pfa_id = pfa["employee__pension_fund_admin"]

            ### Skip Liberty Assured Leadway Administrator

            if(
                skip_company_pension_id is not None
                and skip_pension_id is not None
            ):
                if (company_id == skip_company_pension_id and pfa_id == skip_pension_id):
                    continue
            
            get_pfa = PensionFundAdminSettings.objects.filter(id=pfa_id).first()
            all_employees = base_qs.filter(
                employee__pension_fund_admin=get_pfa,
                employee__pension_pin__isnull=False,
                pension_amount__gt=0,
            )
            total_amount = (
                all_employees.aggregate(Sum("pension_amount"))["pension_amount__sum"]
                or 0
            )
            this_employee_count = all_employees.count() or 0
            amount += round_amount(total_amount)
            if total_amount > 0:
                count += 1

        return count, amount


class ManagePermissionsRole(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_role_settings",
        null=True,
        blank=True,
    )
    role_name = models.CharField(null=True, blank=True, max_length=255)
    role_description = models.CharField(null=True, blank=True, max_length=255)
    can_disburse = models.BooleanField(default=False)
    can_approve = models.BooleanField(default=False)
    can_add_member = models.BooleanField(default=False)
    can_edit_member = models.BooleanField(default=False)
    can_delete_member = models.BooleanField(default=False)
    can_delete_payroll = models.BooleanField(default=False)
    can_run_payroll = models.BooleanField(default=False)
    can_edit_payroll_settings = models.BooleanField(default=False)
    can_create_leave_type = models.BooleanField(default=False)
    can_edit_leave_type = models.BooleanField(default=False)
    can_delete_leave_type = models.BooleanField(default=False)
    can_create_leave_policy = models.BooleanField(default=False)
    can_edit_leave_policy = models.BooleanField(default=False)
    can_delete_leave_policy = models.BooleanField(default=False)
    can_approve_leave = models.BooleanField(default=False)
    can_create_role = models.BooleanField(default=False)
    can_edit_role = models.BooleanField(default=False)
    can_delete_role = models.BooleanField(default=False)
    can_create_department = models.BooleanField(default=False)
    can_edit_department = models.BooleanField(default=False)
    can_delete_department = models.BooleanField(default=False)
    can_create_department_role = models.BooleanField(default=False)
    can_edit_department_role = models.BooleanField(default=False)
    can_delete_department_role = models.BooleanField(default=False)
    can_deploy_department_role = models.BooleanField(default=False)
    can_delete_employee_role = models.BooleanField(default=False)
    can_deploy_employee_role = models.BooleanField(default=False)
    can_deploy_department = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "EMPLOYEE ROLE AND PERMISSION"
        verbose_name_plural = "EMPLOYEE ROLES AND PERMISSIONS"

    def __str__(self):
        if self.company:
            return f"{self.role_name}-{self.company.company_name}"
        else:
            return f"{self.role_name}"

    @classmethod
    def role_exist(cls, company_id, role_name):
        this_role = cls.objects.filter(
            company__id=company_id, is_deleted=False, role_name__iexact=role_name
        ).first()
        if this_role:
            return True
        else:
            return False

    @classmethod
    def create_role(cls, **kwargs):
        company = kwargs.get("company")
        role_name = kwargs.get("role_name")
        can_disburse = kwargs.get("can_disburse")
        can_approve = kwargs.get("can_approve")
        can_add_member = kwargs.get("can_add_member")
        can_edit_member = kwargs.get("can_edit_member")
        can_delete_member = kwargs.get("can_delete_member")
        can_delete_payroll = kwargs.get("can_delete_payroll")
        can_run_payroll = kwargs.get("can_run_payroll")
        can_edit_payroll_settings = kwargs.get("can_edit_payroll_settings")
        can_create_leave_type = kwargs.get("can_create_leave_type")
        can_edit_leave_type = kwargs.get("can_edit_leave_type")
        can_delete_leave_type = kwargs.get("can_delete_leave_type")
        can_create_leave_policy = kwargs.get("can_create_leave_policy")
        can_edit_leave_policy = kwargs.get("can_edit_leave_policy")
        can_delete_leave_policy = kwargs.get("can_delete_leave_policy")
        can_approve_leave = kwargs.get("can_approve_leave")
        role_description = kwargs.get("role_description")
        can_create_role = kwargs.get("can_create_role")
        can_edit_role = kwargs.get("can_edit_role")
        can_delete_role = kwargs.get("can_delete_role")
        can_create_department = kwargs.get("can_create_department")
        can_edit_department = kwargs.get("can_edit_department")
        can_delete_department = kwargs.get("can_delete_department")
        can_create_department_role = kwargs.get("can_create_department_role")
        can_edit_department_role = kwargs.get("can_edit_department_role")
        can_delete_department_role = kwargs.get("can_delete_department_role")
        can_deploy_department_role = kwargs.get("can_deploy_department_role")
        can_deploy_department = kwargs.get("can_deploy_department")

        get_role = cls.objects.create(
            company=company,
            role_name=role_name,
            can_disburse=can_disburse,
            can_approve=can_approve,
            can_add_member=can_add_member,
            can_edit_member=can_edit_member,
            can_delete_member=can_delete_member,
            can_delete_payroll=can_delete_payroll,
            can_run_payroll=can_run_payroll,
            can_edit_payroll_settings=can_edit_payroll_settings,
            can_create_leave_type=can_create_leave_type,
            can_edit_leave_type=can_edit_leave_type,
            can_delete_leave_type=can_delete_leave_type,
            can_create_leave_policy=can_create_leave_policy,
            can_edit_leave_policy=can_edit_leave_policy,
            can_delete_leave_policy=can_delete_leave_policy,
            can_approve_leave=can_approve_leave,
            can_create_role=can_create_role,
            can_edit_role=can_edit_role,
            can_delete_role=can_delete_role,
            can_create_department=can_create_department,
            can_edit_department=can_edit_department,
            can_delete_department=can_delete_department,
            can_create_department_role=can_create_department_role,
            can_edit_department_role=can_edit_department_role,
            can_delete_department_role=can_delete_department_role,
            can_deploy_department_role=can_deploy_department_role,
            can_deploy_department=can_deploy_department,
        )
        if role_description:
            get_role.role_description = role_description
            get_role.save()
        return get_role


class ManageDepartmentRole(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_department_role_settings",
        null=True,
        blank=True,
    )
    role_name = models.CharField(null=True, blank=True, max_length=255)
    role_description = models.CharField(null=True, blank=True, max_length=255)
    is_deleted = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "DEPARTMENT ROLE"
        verbose_name_plural = "DEPARTMENT ROLES"

    def __str__(self):
        if self.company:
            return f"{self.role_name}-{self.company.company_name}"
        else:
            return f"{self.role_name}"

    @classmethod
    def role_exist(cls, company_id, role_name):
        this_role = cls.objects.filter(
            company__id=company_id, is_deleted=False, role_name__iexact=role_name
        ).first()
        if this_role:
            return True
        else:
            return False

    @classmethod
    def create_role(cls, **kwargs):
        company = kwargs.get("company")
        role_name = kwargs.get("role_name")
        role_description = kwargs.get("role_description")

        get_role = cls.objects.create(
            company=company,
            role_name=role_name,
        )
        if role_description:
            get_role.role_description = role_description
            get_role.save()
        return get_role


class SavingPayrollEditData(BaseModel):
    editor = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="savings_editor",
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="savings_payroll_company",
        null=True,
        blank=True,
    )
    data = models.TextField(null=True, blank=True)
    interacted = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SAVINGS PAYROLL DATUM"
        verbose_name_plural = "SAVINGS PAYROLL DATA"

class SendPensionData(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="send_pension_company",
        null=True,
        blank=True,
    )
    pfa = models.ForeignKey(
        PensionFundAdminSettings,
        on_delete=models.CASCADE,
        related_name="send_pension_pfa",
        null=True,
        blank=True,
    )
    pension_list = models.JSONField(blank=False, null=True)
    pension_paid = models.BooleanField(default=False)
    pension_email_sent = models.BooleanField(default=False)
    transaction_id = models.CharField(max_length=255, null=True, blank=True)
    payroll_date = models.DateField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SEND PENSION DATUM"
        verbose_name_plural = "SEND PENSION DATA"

    @classmethod
    def update_send_pension_data(cls, transaction_ins=None):
        if transaction_ins:
            send_pension_data = cls.objects.filter(id=transaction_ins.pension_data_id).first()
            if send_pension_data:
                if send_pension_data.pension_paid is False:
                    send_pension_data.transaction_id = transaction_ins.id
                    send_pension_data.pension_paid = True
                    send_pension_data.save()
                    bank_name = transaction_ins.bank_name
                    transaction_date = transaction_ins.date_created
                    amount = transaction_ins.amount
                    beneficiary_account_number = transaction_ins.beneficiary_account_number
                    beneficiary_account_name = transaction_ins.beneficiary_account_name
                    transaction_ref = f"{transaction_ins.transaction_ref}"
                    session_id = transaction_ins.session_id if transaction_ins.session_id else "xxxxxxxxxxx"
                    
                    automate_send_pfa.delay(
                                    payroll_date=send_pension_data.payroll_date, 
                                    pfa_id=send_pension_data.pfa.id if send_pension_data.pfa else None,
                                    pension_list=send_pension_data.pension_list,
                                    company_id=send_pension_data.company.id,
                                    beneficiary_account_number=beneficiary_account_number,
                                    transaction_type=f"TRANSFER to {bank_name}",
                                    payment_method="BANK_TRANSFER",
                                    transaction_date=transaction_date,
                                    amount=amount,
                                    beneficiary_account_name=beneficiary_account_name,
                                    transaction_ref=transaction_ref,
                                    pension_data_id=transaction_ins.pension_data_id,
                                    session_id=session_id
                                )
                    send_pension_data.pension_email_sent=True
                    send_pension_data.save()
                    
class EmployeeCharge(BaseModel):
    min_employees = models.PositiveIntegerField(null=True, blank=True)
    max_employees = models.PositiveIntegerField(null=True, blank=True)
    charge = models.FloatField(
        null=True, blank=True, default=0.0, validators=[MinValueValidator(0.0)]
    )
    
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "EMPLOYEE CHARGE"
        verbose_name_plural = "EMPLOYEE CHARGES"

    def __str__(self):
        if self.max_employees:
            return f"{self.min_employees} - {self.max_employees}: {self.charge}"
        return f"{self.min_employees} and above: {self.charge}"
    
    @classmethod
    def get_payroll_charges(cls, employee_count):
        """
        Sets the payroll charges based on the number of employees.
        """

        charge = cls.objects.filter(
            min_employees__lte=employee_count,
            max_employees__gte=employee_count
        ).first()

        if not charge:
            charge = cls.objects.filter(
                min_employees__lte=employee_count,
                max_employees__isnull=True
            ).first()

        return charge.charge if charge else 2000
    
class VerificationData(BaseModel):
    guarantor_invite_id = models.CharField(max_length=50, blank=True, null=True)
    employee = models.ForeignKey(
        "payroll_app.CompanyEmployeeList",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="verification_employee",
    )
    company = models.ForeignKey(
        Company, 
        on_delete=models.CASCADE, 
        null=True,
        blank=True,
        related_name="verification_data_company",
    )
    phone_number = models.CharField(max_length=55, null=True, blank=True)
    
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "VERIFICATION DATUM"
        verbose_name_plural = "VERIFICATION DATA"

    @classmethod
    def create_verification_dump(cls, employee, guarantor_invite_id):
        cls.objects.create(
            employee=employee, 
            guarantor_invite_id=guarantor_invite_id,
            phone_number=employee.employee_guarantor_phone_number,
            company=employee.company if employee.company else None)