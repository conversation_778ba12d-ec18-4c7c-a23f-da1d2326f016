from performance_sales_metrics_dashboard.utils.gmail_integration import GmailOAuthHandler
from performance_sales_metrics_dashboard.utils.sent_emails import save_sent_emails
from celery import shared_task
oauth_handler = GmailOAuthHandler()

@shared_task
def send_email_with_gmail(
    gmail_address,
    subject,
    message,
    senders_name,
    cc,
    bcc,
    recipient_email,
    date,
    team_member,
):
    try:
        outbox = oauth_handler.send_google_mails(
                        gmail_address,
                        recipient_email,
                        subject,
                        cc,
                        bcc,
                        message,
                    )
        if outbox["status"] == 235:
            saved_emails = save_sent_emails(
                is_sent=True,
                sales_officer=team_member, 
                date=date, 
                subject=subject, 
                sender=gmail_address, 
                senders_name=senders_name,
                cc=cc,
                bcc=bcc,
                receiver=recipient_email, 
                content=message,
            )
            if saved_emails:
                print("Emails saved successfully.", saved_emails)
            print("GOOGLE MAIL SENT SUCCESSFULLY!")
    except Exception as e:
        print("EMAIL SENDING FAILED BECAUSE AN ERROR OCCURRED!\n", f"ERROR: {str(e)}")
    return outbox