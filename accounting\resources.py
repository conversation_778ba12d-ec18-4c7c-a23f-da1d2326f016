from accounting.models import Account, AccountType, JournalEntry, JournalLine
from import_export import resources

class AccountTypeResource(resources.ModelResource):
    class Meta:
        model = AccountType

class AccountResource(resources.ModelResource):
    class Meta:
        model = Account

class JournalEntryResource(resources.ModelResource):
    class Meta:
        model = JournalEntry

class JournalLineResource(resources.ModelResource):
    class Meta:
        model = JournalLine