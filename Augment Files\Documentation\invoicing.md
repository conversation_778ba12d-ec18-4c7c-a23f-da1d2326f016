# Invoicing App Documentation

## Overview

The Invoicing app manages the creation, delivery, and tracking of invoices for businesses on the Paybox360 platform. It provides tools for generating professional invoices, tracking payments, and managing customer billing.

## Core Features

### 1. Invoice Generation

- Invoice creation from orders or manual entry
- Invoice numbering and sequencing
- Tax calculation and application
- Discount management

### 2. Invoice Delivery

- Email delivery of invoices
- PDF generation
- Invoice links for online viewing
- Bulk invoice sending

### 3. Payment Tracking

- Payment status monitoring
- Partial payment handling
- Payment reminders
- Payment receipt generation

### 4. Invoice Management

- Invoice search and filtering
- Invoice history
- Invoice cancellation and credit notes
- Recurring invoice setup

### 5. Customer Billing

- Customer billing profiles
- Payment terms management
- Customer statement generation
- Aging reports

## How It Works

1. **Invoice Creation**: Invoices are generated from sales orders or manual entry
2. **Delivery**: Invoices are delivered to customers via email or other channels
3. **Payment Processing**: Customer payments are recorded against invoices
4. **Status Tracking**: Invoice status is updated throughout the lifecycle
5. **Reporting**: Invoice and payment data is compiled into reports

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `Invoice` | Invoice records | `invoice_number`, `due_date`, `status` | Customer, Company |
| `InvoiceItem` | Invoice line items | `description`, `quantity`, `unit_price` | Invoice |
| `Payment` | Payment records | `amount`, `payment_date`, `payment_method` | Invoice |
| `InvoiceTemplate` | Design templates | `name`, `design`, `is_default` | Company |
| `RecurringInvoice` | Recurring billing | `frequency`, `next_date`, `end_date` | Customer, Company |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `generate_invoice` | Creates invoice records | `services.py` | Called from order processing |
| `calculate_invoice_totals` | Computes amounts | `models.py` (Invoice) | Used during invoice creation |
| `send_invoice_email` | Delivers invoices | `services.py` | Used in invoice delivery |
| `process_invoice_payment` | Records payments | `services.py` | Called during payment processing |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/invoicing/invoices/` | Manage invoices | POST/GET | `InvoiceViewSet` |
| `/invoicing/payments/` | Record payments | POST/GET | `PaymentViewSet` |
| `/invoicing/templates/` | Manage templates | POST/GET | `InvoiceTemplateViewSet` |
| `/invoicing/recurring/` | Setup recurring invoices | POST/GET | `RecurringInvoiceViewSet` |
| `/invoicing/statements/` | Generate statements | GET | `CustomerStatementView` |

### Integration Points

- **Sales App**: For order-to-invoice conversion
- **Account App**: For payment processing
- **Customer Management**: For customer information
- **Email Services**: For invoice delivery

## Key Considerations

### 1. Invoice Numbering

- **Responsible App**: Invoicing app
- **Key Functions**:
  - Sequential number generation
  - Custom numbering formats
  - Number uniqueness enforcement

### 2. Tax Calculation

- **Responsible App**: Invoicing app
- **Key Functions**:
  - Multiple tax rate support
  - Tax exemption handling
  - Tax reporting compliance

### 3. Payment Reconciliation

- **Responsible App**: Invoicing app
- **Key Functions**:
  - Partial payment allocation
  - Overpayment handling
  - Payment matching algorithms

### 4. Invoice Delivery Reliability

- **Responsible App**: Invoicing app
- **Key Functions**:
  - Email delivery tracking
  - Alternative delivery methods
  - Delivery retry mechanisms

### 5. Recurring Billing Management

- **Responsible App**: Invoicing app
- **Key Functions**:
  - Scheduling in RecurringInvoice model
  - Automatic generation process
  - Notification of recurring invoices