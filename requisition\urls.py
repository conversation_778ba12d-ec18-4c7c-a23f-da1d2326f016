from django.urls import path

from requisition import views


# Create your url pattern(s) here.
company = [
    path("cdg-company/", views.CreateListAndDelCompany.as_view()),
    path("get-companies/", views.ListFilterCompanies.as_view()),
    path("company-verification/", views.VerifyCorporateCompanyInfo.as_view()),
    path(
        "verify-reg-number/", views.VerifyCorporateCompanyRegistrationNumber.as_view()
    ),
    path("industries/", views.GetIndustries.as_view()),
    path("industries/create", views.IndustryCreateAPIView.as_view()),
    path("get-all-companies/", views.GetAllFilterCompanies.as_view()),
    path("set-default-company/", views.SetDefaultCompanyAPIView.as_view()),
]

team = [
    path("cdg-team/", views.CreateDelTeam.as_view()),
    path("edit-team/", views.EditTeam.as_view()),
    path("edit_team_mem/", views.EditTeamMemberPermissionAndOthers.as_view()),
    path("get-team_members/", views.ListFilterTeamMembers.as_view(), name="list-filter-team-members"),
    path("get-teams/", views.ListFilterTeam.as_view(), name="list-filter-team"),
    path("roles", views.TeamMemberRoleAPIView.as_view()),
    path("members", views.CompanyMembersAPIView.as_view()),
    path("all_team_members/", views.GetTeamMembersListFilterTeam.as_view()),
    path("all_team_members_paginated/", views.GetTeamMembersListFilterTeamPaginated.as_view()),
    path("add_team_members/", views.AddTeamMembersTeam.as_view()),
    path("fetch_all_member_invite/", views.GetAllMembersInvite.as_view()),
    path("team_member_approval/", views.TeamInviteApproval.as_view()),
    path("delete-team/<str:team_id>", views.TeamDestroyAPIView.as_view(), name="delete-team"),
]

requisitions = [
    path(
        "create_requisition/",
        views.CreateRequisition.as_view(),
        name="create_requisition",
    ),
    path("requisitions/", views.ListFilterRequisition.as_view(), name="requisitions"),
    path("disbursement/", views.Disbursement.as_view(), name="disbursement"),
    path("bulk-disbursement/", views.BulkDisbursementAPIView.as_view(), name="bulk-disbursement"),
    path(
        "decline_requisition/",
        views.DeclineRequsition.as_view(),
        name="decline_requisition",
    ),
    path(
        "req_dashboard/", views.GetRequisitionDashboard.as_view(), name="req_dashboard"
    ),
    path("vendor_details/", views.VendorDetails.as_view(), name="vendor_details"),
    path(
        "all_vendor_list/", views.AllVendorDetails.as_view(), name="all_vendor_details"
    ),
    path("req_reminder/", views.ReqReminder.as_view(), name="req_reminder"),
    # NEW
    path("overall_reqs/", views.ListRequisition.as_view(), name="requisitions"),
    path(
        "unique_req_data/", views.GetRequisitionInfo.as_view(), name="unique_req_data"
    ),
    path(
        "all_company_requisition/",
        views.ListFilterAllCompanyRequisitions.as_view(),
        name="all_company_requisition",
    ),
    path("sms_disbursement/", views.SmsDisbursement.as_view(), name="sms_disbursement"),
    path(
        "sms_decline_requisition/",
        views.SmsDeclineRequsition.as_view(),
        name="sms_decline_requisition",
    ),

    path('banks/', views.BankNameResolveAPIView.as_view(), name='bank-name-enquiry'),
    path('procurement-purchase-invoices/', views.PurchaseInvoiceAPIList.as_view(), name='purchase_invoice_list'),
    path('procurement-purchase-invoices/<str:id>/', views.PurchaseInvoiceAPIDetail.as_view(), name='purchase_invoice_detail'),

    path('procurement-purchase-invoices-image-preview/<str:id>/', views.ProcurementPurchaseInoviceImagePreview.as_view(), name="image_preview"),
    path('procurement-purchase-invoices-confirm-total/<str:id>/', views.ProcurementInvoiceConfirmAmountPaidAPIView.as_view(), name="confirm_amount"),
    path('invoice-select-wallet/<str:invoice_no>/', views.CompanyPaymentWalletAPIView.as_view(), name="company_payment"),
    path('pay-with-paybox/enter-amount/<str:wallet_id>/<str:invoice_no>/', views.EnterAmountAPIView.as_view(), name="enter_amount"),
    path('enter-pin/make-payment/<str:wallet_id>/<str:invoice_no>/', views.MakepaymentAPIView.as_view(), name="make_payment"),

]

procurement = [
    path("procurement/credit-note", views.ProcurementCreditNoteAPIView.as_view(), name="procurement-credit-note"),
    path("procurement/credit-note/<str:pk>", views.ProcurementCreditNoteAPIView.as_view(), name="procurement-credit-note"),
    path("procurement/confirm-delivery", views.ConfirmDeliveryAPIView.as_view(), name="procurement-confirm-delivery"),
    path("procurement-chat", views.PurchaseOrderCommentAPIView.as_view(), name="procurement-chat"),
    path("procurement-chat/<str:pk>", views.PurchaseOrderCommentAPIView.as_view(), name="procurement-chat-delete"),
    path("procurement/purchase-order", views.PurchaseOrderAPIView.as_view(), name="procurement-purchase-order"),
    path("procurement/purchase-order/<str:pk>", views.PurchaseOrderAPIView.as_view(), name="procurement-purchase-order-detail"),
    path("procurement/bulk-requisition", views.BulkProcurementAPIView.as_view(), name="procurement-bulk-requisition"),
    path("approve-procurement", views.ApprovePurchaseIndentAPIView.as_view(), name="procurement-approve"),
    path("procurement/bulkupload/add", views.PurchaseIndentBulkUploadAPIView.as_view(), name="procurement-bulk-upload"),
    path("procurement/", views.ProcurementRequisitionAPIView.as_view(), name="procurement-requisition"),
    path("procurement/<str:pk>", views.ProcurementRequisitionAPIView.as_view(), name="procurement-requisition-detail"),
    path("assets/", views.AssetAPIView.as_view(), name="asset"),
    path("assets/<str:pk>", views.AssetAPIView.as_view(), name="asset-detail"),
    path("asset-expense/", views.AssetExpenseAPIView.as_view(), name="asset-expense"),
    path("asset-expense/<str:pk>/", views.AssetExpenseAPIView.as_view(), name="asset-expense-detail"),
    path("upload-asset-image/", views.AssetImageUploadAPIView.as_view(), name="upload-asset-image"),
    path("asset-dashboard", views.AssetDashboardAPIView.as_view(), name="asset-dashboard"),
    path("procurement-dashboard", views.ProcurementDashboardAPIView.as_view(), name="procurement-dashboard"),
]

suppliers = [
    path("suppliers-add", views.CompanySupplierAPIView.as_view(), name="add-supplier"),
    path("suppliers-onboarding", views.SupplierOnboardingAPIView.as_view(), name="supplier-onboarding"),
    path("approve-supplier", views.SupplierApprovalAPIView.as_view(), name="approve-supplier"),
    path("suppliers/<str:company_id>", views.CompanySupplierListAPIView.as_view(), name="list-supplier"),
    path("suppliers/<str:company_id>/<str:pk>", views.CompanySupplierListAPIView.as_view(), name="supplier-detail"),
]

suppliersapi = [
    path("supplier-dashboard/request-otp", views.RequestProcurementOTPAPIView.as_view(), name="supplier-request-otp"),
    path("supplier-dashboard/fulfill-order", views.SupplierOrderFulfilmentAPIView.as_view(), name="supplier-fulfilment"),
    path("supplier-dashboard/update", views.UpdateSupplierProfileAPIView.as_view(), name="update-supplier-detail"),
    path("supplier-dashboard/login", views.SupplierLoginAPIView.as_view(), name="supplier-login"),
    path("supplier-dashboard/approve-returns", views.SupplierApproveDeclineReturnsAPIView.as_view(), name="supplier-return-approve"),
    path("supplier-dashboard/approve-purchase", views.SupplierApproveDeclinePurchaseOrderAPIView.as_view(), name="supplier-po-approval"),
    path("supplier-dashboard/change-password", views.SupplierChangePasswordAPIView.as_view(), name="supplier-change-password"),
    path("supplier-dashboard/reset-password", views.SupplierResetPasswordAPIView.as_view(), name="supplier-reset-password"),
]

comment = [
    path("comment/", views.CreateComment.as_view(), name="comment"),
]

categories = [
    path("categories/", views.GetCategories.as_view(), name="categories"),
]

budget = [
    path("edit-budget/categories/", views.EditBudgetCategory.as_view()),
    path("track-budget/expense/", views.TrackBudget.as_view()),
    path("budget/", views.CreateBudget.as_view(), name="budget"),
    path("get-paid-budgets/", views.ListFilterPaidBudget.as_view()),
    path("create-budget/categories/", views.CreateBudgetCategories.as_view()),
    path("get-budgets/", views.ListFilterBudget.as_view()),
    path("budget/link-team/", views.LinkTeamToBudget.as_view()),
    path("budget/allocate/", views.AllocateBudget.as_view()),
]
expense = [
    path("expense_dashboard/", views.ExpenseDashboard.as_view()),
    path("expense_list/", views.ExpenseList.as_view()),
    path("team_expense_list/", views.TeamExpenseList.as_view()),
    path("record_expense/", views.RecordExpense.as_view()),
    path("receipt_reader/", views.ReadExpenseReceipt.as_view()),
]

pin = [
    path("create-pin/", views.CreateRequisitionTransactionPin.as_view()),
    path("update-agency-pin/", views.UpdateAgencyTransactionPin.as_view()),
    path("agn-trnx-exist/", views.AgencyTransactionPinExist.as_view()),
    path("dis-trnx-exist/", views.OnDisbursementVerifyReqPin.as_view()),
    path("reset_transaction_pin/", views.ResetTransactionPinView.as_view()),
]

fund_wallet = [
    path("fund_wallet/", views.FundWalletDetails.as_view()),
    path("withdraw_wallet/", views.WithdrawWallet.as_view()),
    path("withdraw_wallet_external/", views.WithdrawToExternal.as_view()),
]
un_utilized_allocations = [
    path("un-utilized/", views.GetUnUtilizedAllocations.as_view()),
    path("annul_running_balance/", views.AnnulRunningBalanceToCompanyWallet.as_view()),
    path("monowebhook/", views.MonoPaymentHook.as_view()),
    path("mono_key_exchange/", views.GetMonokeyExchangCode.as_view()),
]


on_board_company_via_jot_form = [
    path("jotform_company_onboarding_webhook/", views.CompanyOnboardingViaJotFormWebhookApiView.as_view()),
]
paybox_marketing = [
    path("ussd_campaign/", views.PayboxActivityLogsViaUssdApiview.as_view()),
    path("campaign_jotform_data/", views.JotFormForPaybox360UssdAdApiView.as_view()),
    path("whatsapp_conversation_record_update/", views.UpdateUserWhatsappConversationApiView.as_view())
]


urlpatterns = [
    *pin,
    *company,
    *comment,
    *team,
    *requisitions,
    *categories,
    *budget,
    *expense,
    *fund_wallet,
    *un_utilized_allocations,
    *procurement,
    # *purchase_order,
    *suppliers,
    *suppliersapi,
    *on_board_company_via_jot_form,
    *paybox_marketing,
]
