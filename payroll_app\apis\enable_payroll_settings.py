from payroll_app.apis.func import round_amount
from payroll_app.apis.tax_component import consolidated_tax, consolidated_tax_calculator, custom_consolidated_tax, custom_consolidated_tax_calculator, get_bht_amount, get_bht_calculator
from django.db.models import ( Sum )
from decimal import Decimal

def other_dependency_calculation(deduction_id, net_amount, amount):
    from payroll_app.models import SalaryComponentSettings, OtherDependencySettings
    dependencies_amount = amount

    get_other_dependency = OtherDependencySettings.objects.filter(deduction__id=deduction_id, is_active=True)
    for other_dependency in get_other_dependency:
        all_custom_dependency_id = [component for component in other_dependency.custom_name.all().values_list('id', flat=True)]
        custom_amount = 0
        for component_id in all_custom_dependency_id:
            custom_comp = SalaryComponentSettings.objects.filter(id=component_id, is_active=True).first()
            if custom_comp:
                if custom_comp.calculation_type == "FIXED_AMOUNT":
                    this_amount = custom_comp.amount
                    if custom_comp.frequency == "MONTHLY":
                        yearly_amount = this_amount * 12
                        custom_amount += yearly_amount
                    elif custom_comp.frequency == "BI-MONTHLY":
                        yearly_amount = this_amount * 6
                        custom_amount += yearly_amount
                    else:
                        custom_amount += this_amount
                elif custom_comp.calculation_type == "PERCENTAGE_NET":
                    try:
                        this_percent = custom_comp.amount / 100
                    except ZeroDivisionError:
                        this_percent = 0
                    PERCENTAGE_NET = net_amount * this_percent
                    if custom_comp.frequency == "MONTHLY":
                        yearly_amount = PERCENTAGE_NET * 12
                        custom_amount += yearly_amount
                    elif custom_comp.frequency == "BI-MONTHLY":
                        yearly_amount = PERCENTAGE_NET * 6
                        custom_amount += yearly_amount
                    else:
                        custom_amount += PERCENTAGE_NET
                else:
                    custom_amount += PERCENTAGE_NET
            else:
                custom_amount += 0

        if other_dependency.dependency_operator == "OR":
            if other_dependency.operator_type == "GREATER_THAN":
                if custom_amount > amount:
                    dependencies_amount = custom_amount
                else:
                    dependencies_amount = amount
            elif other_dependency.operator_type == "LESS_THAN":
                if custom_amount < amount:
                    dependencies_amount = custom_amount
                else:
                    dependencies_amount = amount
            elif other_dependency.operator_type == "GREATER_THAN_OR_EQUALS":
                if custom_amount >= amount:
                    dependencies_amount = custom_amount
                else:
                    dependencies_amount = amount
            elif other_dependency.operator_type == "LESSER_THAN_OR_EQUALS":
                if custom_amount <= amount:
                    dependencies_amount = custom_amount
                else:
                    dependencies_amount = amount
            elif other_dependency.operator_type == "EQUALS":
                if custom_amount == amount:
                    dependencies_amount = custom_amount
                else:
                    dependencies_amount = amount
            elif other_dependency.operator_type == "NOT_EQUALS":
                if custom_amount != amount:
                    dependencies_amount = custom_amount
                else:
                    dependencies_amount = amount
            else:
                pass

        elif other_dependency.dependency_operator == "AND":
            if other_dependency.operator_type == "GREATER_THAN":
                if custom_amount > amount:
                    dependencies_amount += custom_amount
                else:
                    dependencies_amount += amount
            elif other_dependency.operator_type == "LESS_THAN":
                if custom_amount < amount:
                    dependencies_amount += custom_amount
                else:
                    dependencies_amount += amount
            elif other_dependency.operator_type == "GREATER_THAN_OR_EQUALS":
                if custom_amount >= amount:
                    dependencies_amount += custom_amount
                else:
                    dependencies_amount += amount
            elif other_dependency.operator_type == "LESSER_THAN_OR_EQUALS":
                if custom_amount <= amount:
                    dependencies_amount += custom_amount
                else:
                    dependencies_amount += amount
            elif other_dependency.operator_type == "EQUALS":
                if custom_amount == amount:
                    dependencies_amount += custom_amount
                else:
                    dependencies_amount += amount
            elif other_dependency.operator_type == "NOT_EQUALS":
                if custom_amount != amount:
                    dependencies_amount += custom_amount
                else:
                    dependencies_amount += amount
            else:
                pass

        else:
            pass

    return dependencies_amount

def employee_salary_component(salary_data, employee_data):
    
    net_amount = employee_data.employee_net_amount

    total_amount = 0
    if len(salary_data) <= 0:
        return total_amount
    else:
        for salary in salary_data:
            if salary.get("fixed"):
                amount = salary.get("amount")
                if salary.get("frequency") == "MONTHLY":
                    yearly_amount =  amount * 12
                    total_amount += yearly_amount
                elif salary.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  amount * 6
                    total_amount += yearly_amount
                else:
                    total_amount += amount
            else:
                try: 
                    amount = float(salary.get("percentage")) / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                if salary.get("frequency") == "MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 12
                    total_amount += yearly_amount
                elif salary.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 6
                    total_amount += yearly_amount
                else:
                    total_amount += PERCENTAGE_NET
        return total_amount
    
def employee_salary_component_calculator(salary_data, net_amount):
    
    total_amount = 0
    if len(salary_data) <= 0:
        return total_amount
    else:
        for salary in salary_data:
            if salary.get("fixed"):
                amount = salary.get("amount")
                if salary.get("frequency") == "MONTHLY":
                    yearly_amount =  amount * 12
                    total_amount += yearly_amount
                elif salary.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  amount * 6
                    total_amount += yearly_amount
                else:
                    total_amount += amount
            else:
                try: 
                    amount = float(salary.get("percentage")) / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                if salary.get("frequency") == "MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 12
                    total_amount += yearly_amount
                elif salary.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 6
                    total_amount += yearly_amount
                else:
                    total_amount += PERCENTAGE_NET
        return total_amount
    
def employee_salary_component_calculator_display(salary_data, net_amount):
    
    this_data = []
    total_amount = 0
    if len(salary_data) <= 0:
        return this_data
    else:
        for salary in salary_data:
            if salary.get("fixed"):
                amount = salary.get("amount")
                if salary.get("frequency") == "MONTHLY":
                    yearly_amount =  amount * 12
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": salary.get("name"),
                            "calculation_type": salary.get("calculation_type"),
                            "annual_amount": yearly_amount,
                        }
                    )
                elif salary.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  amount * 6
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": salary.get("name"),
                            "calculation_type": salary.get("calculation_type"),
                            "annual_amount": yearly_amount,
                        }
                    )
                else:
                    total_amount += amount
                    this_data.append(
                        {
                            "name": salary.get("name"),
                            "calculation_type": salary.get("calculation_type"),
                            "annual_amount": amount,
                        }
                    )

            else:
                try: 
                    amount = float(salary.get("percentage")) / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                if salary.get("frequency") == "MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 12
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": salary.get("name"),
                            "calculation_type": salary.get("calculation_type"),
                            "annual_amount": yearly_amount,
                        }
                    )
                elif salary.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 6
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": salary.get("name"),
                            "calculation_type": salary.get("calculation_type"),
                            "annual_amount": yearly_amount,
                        }
                    )
                else:
                    total_amount += PERCENTAGE_NET
                    this_data.append(
                        {
                            "name": salary.get("name"),
                            "calculation_type": salary.get("calculation_type"),
                            "annual_amount": PERCENTAGE_NET,
                        }
                    )
        return this_data
    
def employee_salary_component_amount(salary_data, employee_data):
    
    net_amount = employee_data.employee_net_amount

    total_amount = 0
    if len(salary_data) <= 0:
        return total_amount
    else:
        for salary in salary_data:
            if salary.get("fixed"):
                amount = salary.get("amount")
                total_amount += amount/12
            else:
                try: 
                    amount = float(salary.get("percentage")) / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                number = Decimal(PERCENTAGE_NET)
                total_amount += round(PERCENTAGE_NET, 2)
        return total_amount
    
def employee_salary_report(salary, net_amount):
    total_amount = 0
    if salary is None:
        return total_amount
    else:
        if salary.get("calculation_type") == "FIXED_AMOUNT":
            amount = salary.get("amount")
            if salary.get("frequency") == "MONTHLY":
                yearly_amount =  amount * 12
                total_amount += yearly_amount
            elif salary.get("frequency") == "BI-MONTHLY":
                yearly_amount =  amount * 6
                total_amount += yearly_amount
            else:
                total_amount += amount
        else:
            try: 
                amount = float(salary.get("percentage")) / 100
            except ZeroDivisionError:
                amount = 0
            PERCENTAGE_NET = net_amount * amount
            if salary.get("frequency") == "MONTHLY":
                yearly_amount =  PERCENTAGE_NET * 12
                total_amount += yearly_amount
            elif salary.get("frequency") == "BI-MONTHLY":
                yearly_amount =  PERCENTAGE_NET * 6
                total_amount += yearly_amount
            else:
                total_amount += PERCENTAGE_NET
        return total_amount

def employee_benefit_component(benefit_data, net_amount):
    from payroll_app.models import SalaryComponentSettings
    total_amount = 0
    if len(benefit_data) <= 0:
        return total_amount
    else:
        for benefit in benefit_data:
            if benefit.get("calculation_type") == "FIXED_AMOUNT":
                amount = benefit.get("amount")
                if benefit.get("frequency") == "MONTHLY":
                    yearly_amount =  amount * 12
                    total_amount += yearly_amount
                elif benefit.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  amount * 6
                    total_amount += yearly_amount
                else:
                    total_amount += amount
            elif benefit.get("calculation_type") == "PERCENTAGE_NET":
                try:
                    amount = benefit.get("percentage") / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                if benefit.get("frequency") == "MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 12
                    total_amount += yearly_amount
                elif benefit.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 6
                    total_amount += yearly_amount
                else:
                    total_amount += PERCENTAGE_NET
            else:
                amount = benefit.get("percentage")
                all_component_id = benefit.get("custom_component_id")
                custom_amount = 0
                # amount = benefit.get("percentage")
                try:
                    percentage_amount = amount / 100
                except ZeroDivisionError:
                    percentage_amount = 0
                
                
                for component_id in all_component_id:
                    custom_comp = SalaryComponentSettings.objects.filter(id=component_id).first()
                    if custom_comp:
                        if custom_comp.calculation_type == "FIXED_AMOUNT":
                            this_amount = custom_comp.amount
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = this_amount * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = this_amount * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += this_amount
                        elif custom_comp.calculation_type == "PERCENTAGE_NET":
                            try:
                                this_percent = custom_comp.amount / 100
                            except ZeroDivisionError:
                                this_percent = 0
                            PERCENTAGE_NET = net_amount * this_percent
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += PERCENTAGE_NET
                        else:
                            custom_amount += PERCENTAGE_NET
                    else:
                        custom_amount += 0

                total_custom_amount = custom_amount * percentage_amount
                # total_amount += total_custom_amount
                if benefit.get("frequency") == "MONTHLY":
                    yearly_amount =  total_custom_amount * 12
                    total_amount += yearly_amount
                elif benefit.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  total_custom_amount * 6
                    total_amount += yearly_amount
                else:
                    yearly_amount =  total_custom_amount
                    total_amount += yearly_amount
        return total_amount
    
def employee_benefit_component_calculator(benefit_data, net_amount):
    from payroll_app.models import SalaryComponentSettings
    total_amount = 0
    if len(benefit_data) <= 0:
        return total_amount
    else:
        for benefit in benefit_data:
            if benefit.get("calculation_type") == "FIXED_AMOUNT":
                amount = benefit.get("amount")
                if benefit.get("frequency") == "MONTHLY":
                    yearly_amount =  amount * 12
                    total_amount += yearly_amount
                elif benefit.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  amount * 6
                    total_amount += yearly_amount
                else:
                    total_amount += amount
            elif benefit.get("calculation_type") == "PERCENTAGE_NET":
                try:
                    amount = benefit.get("percentage") / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                if benefit.get("frequency") == "MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 12
                    total_amount += yearly_amount
                elif benefit.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 6
                    total_amount += yearly_amount
                else:
                    total_amount += PERCENTAGE_NET
            else:
                amount = benefit.get("percentage")
                all_component_id = benefit.get("custom_component_id")
                custom_amount = 0
                # amount = benefit.get("percentage")
                try:
                    percentage_amount = amount / 100
                except ZeroDivisionError:
                    percentage_amount = 0
                
                
                for component_id in all_component_id:
                    custom_comp = SalaryComponentSettings.objects.filter(id=component_id).first()
                    if custom_comp:
                        if custom_comp.calculation_type == "FIXED_AMOUNT":
                            this_amount = custom_comp.amount
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = this_amount * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = this_amount * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += this_amount
                        elif custom_comp.calculation_type == "PERCENTAGE_NET":
                            try:
                                this_percent = custom_comp.amount / 100
                            except ZeroDivisionError:
                                this_percent = 0
                            PERCENTAGE_NET = net_amount * this_percent
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += PERCENTAGE_NET
                        else:
                            custom_amount += PERCENTAGE_NET
                    else:
                        custom_amount += 0

                total_custom_amount = custom_amount * percentage_amount
                # total_amount += total_custom_amount
                if benefit.get("frequency") == "MONTHLY":
                    yearly_amount =  total_custom_amount * 12
                    total_amount += yearly_amount
                elif benefit.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  total_custom_amount * 6
                    total_amount += yearly_amount
                else:
                    yearly_amount =  total_custom_amount
                    total_amount += yearly_amount
        return total_amount
    
def employee_benefit_component_calculator_display(benefit_data, net_amount):
    from payroll_app.models import SalaryComponentSettings
    this_data = []
    total_amount = 0
    if len(benefit_data) <= 0:
        return this_data
    else:
        for benefit in benefit_data:
            if benefit.get("calculation_type") == "FIXED_AMOUNT":
                amount = benefit.get("amount")
                if benefit.get("frequency") == "MONTHLY":
                    yearly_amount =  amount * 12
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": benefit.get("name"),
                            "calculation_type": benefit.get("calculation_type"),
                            "frequency": benefit.get("frequency"),
                            "annual_amount": yearly_amount,
                        }
                    )
                elif benefit.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  amount * 6
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": benefit.get("name"),
                            "calculation_type": benefit.get("calculation_type"),
                            "frequency": benefit.get("frequency"),
                            "annual_amount": yearly_amount,
                        }
                    )
                else:
                    total_amount += amount
                    this_data.append(
                        {
                            "name": benefit.get("name"),
                            "calculation_type": benefit.get("calculation_type"),
                            "frequency": benefit.get("frequency"),
                            "annual_amount": amount,
                        }
                    )
            elif benefit.get("calculation_type") == "PERCENTAGE_NET":
                try:
                    amount = benefit.get("percentage") / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                if benefit.get("frequency") == "MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 12
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": benefit.get("name"),
                            "calculation_type": benefit.get("calculation_type"),
                            "frequency": benefit.get("frequency"),
                            "annual_amount": yearly_amount,
                        }
                    )
                elif benefit.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 6
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": benefit.get("name"),
                            "calculation_type": benefit.get("calculation_type"),
                            "frequency": benefit.get("frequency"),
                            "annual_amount": yearly_amount,
                        }
                    )
                else:
                    total_amount += PERCENTAGE_NET
                    this_data.append(
                        {
                            "name": benefit.get("name"),
                            "calculation_type": benefit.get("calculation_type"),
                            "frequency": benefit.get("frequency"),
                            "annual_amount": PERCENTAGE_NET,
                        }
                    )
            else:
                amount = benefit.get("percentage")
                all_component_id = benefit.get("custom_component_id")
                custom_amount = 0
                # amount = benefit.get("percentage")
                try:
                    percentage_amount = amount / 100
                except ZeroDivisionError:
                    percentage_amount = 0
                
                
                for component_id in all_component_id:
                    custom_comp = SalaryComponentSettings.objects.filter(id=component_id).first()
                    if custom_comp:
                        if custom_comp.calculation_type == "FIXED_AMOUNT":
                            this_amount = custom_comp.amount
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = this_amount * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = this_amount * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += this_amount
                        elif custom_comp.calculation_type == "PERCENTAGE_NET":
                            try:
                                this_percent = custom_comp.amount / 100
                            except ZeroDivisionError:
                                this_percent = 0
                            PERCENTAGE_NET = net_amount * this_percent
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += PERCENTAGE_NET
                        else:
                            custom_amount += PERCENTAGE_NET
                    else:
                        custom_amount += 0

                total_custom_amount = custom_amount * percentage_amount
                # total_amount += total_custom_amount
                if benefit.get("frequency") == "MONTHLY":
                    yearly_amount =  total_custom_amount * 12
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": benefit.get("name"),
                            "calculation_type": benefit.get("calculation_type"),
                            "frequency": benefit.get("frequency"),
                            "annual_amount": yearly_amount,
                        }
                    )
                elif benefit.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  total_custom_amount * 6
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": benefit.get("name"),
                            "calculation_type": benefit.get("calculation_type"),
                            "frequency": benefit.get("frequency"),
                            "annual_amount": yearly_amount,
                        }
                    )
                else:
                    yearly_amount =  total_custom_amount
                    total_amount += yearly_amount
                    this_data.append(
                        {
                            "name": benefit.get("name"),
                            "calculation_type": benefit.get("calculation_type"),
                            "frequency": benefit.get("frequency"),
                            "annual_amount": yearly_amount,
                        }
                    )
        return this_data
 
def employee_benefit_report(benefit, net_amount):
    
    from payroll_app.models import SalaryComponentSettings
    total_amount = 0
    custom_amount = 0
    if benefit is None:
        return total_amount
    else:
        if benefit.get("calculation_type") == "FIXED_AMOUNT":
            amount = benefit.get("amount")
            if benefit.get("frequency") == "MONTHLY":
                yearly_amount =  amount * 12
                total_amount += yearly_amount
            elif benefit.get("frequency") == "BI-MONTHLY":
                yearly_amount =  amount * 6
                total_amount += yearly_amount
            else:
                total_amount += amount
        elif benefit.get("calculation_type") == "PERCENTAGE_NET":
            try:
                amount = benefit.get("percentage") / 100
            except ZeroDivisionError:
                amount = 0
            PERCENTAGE_NET = net_amount * amount
            if benefit.get("frequency") == "MONTHLY":
                yearly_amount =  PERCENTAGE_NET * 12
                total_amount += yearly_amount
            elif benefit.get("frequency") == "BI-MONTHLY":
                yearly_amount =  PERCENTAGE_NET * 6
                total_amount += yearly_amount
            else:
                total_amount += PERCENTAGE_NET
        else:
            amount = benefit.get("percentage")
            all_component_id = benefit.get("custom_component_id")
            try:
                percentage_amount = amount / 100
            except ZeroDivisionError:
                percentage_amount = 0        
            for component_id in all_component_id:
                custom_comp = SalaryComponentSettings.objects.filter(id=component_id).first()
                if custom_comp:
                    if custom_comp.calculation_type == "FIXED_AMOUNT":
                        this_amount = custom_comp.amount
                        if custom_comp.frequency == "MONTHLY":
                            yearly_amount = this_amount * 12
                            custom_amount += yearly_amount
                        elif custom_comp.frequency == "BI-MONTHLY":
                            yearly_amount = this_amount * 6
                            custom_amount += yearly_amount
                        else:
                            custom_amount += this_amount
                    elif custom_comp.calculation_type == "PERCENTAGE_NET":
                        try:
                            this_percent = custom_comp.amount / 100
                        except ZeroDivisionError:
                            this_percent = 0
                        PERCENTAGE_NET = net_amount * this_percent
                        if custom_comp.frequency == "MONTHLY":
                            yearly_amount = PERCENTAGE_NET * 12
                            custom_amount += yearly_amount
                        elif custom_comp.frequency == "BI-MONTHLY":
                            yearly_amount = PERCENTAGE_NET * 6
                            custom_amount += yearly_amount
                        else:
                            custom_amount += PERCENTAGE_NET
                    else:
                        custom_amount += PERCENTAGE_NET
                else:
                    custom_amount += 0
            total_custom_amount = custom_amount * percentage_amount
            total_amount = total_custom_amount
            if benefit.get("frequency") == "MONTHLY":
                yearly_amount =  total_custom_amount * 12
                total_amount = yearly_amount
            elif benefit.get("frequency") == "BI-MONTHLY":
                yearly_amount =  total_custom_amount * 6
                total_amount = yearly_amount
            else:
                yearly_amount =  total_custom_amount
                total_amount =  yearly_amount
        return total_amount
    
def employee_deduction_component(deduction_data, net_amount):
    from payroll_app.models import SalaryComponentSettings, OtherDependencySettings
    total_amount = 0
    if len(deduction_data) <= 0:
        return total_amount
    else:
        for deduction in deduction_data:
            if deduction.get("calculation_type") == "FIXED_AMOUNT":
                amount = deduction.get("amount")
                deduction_id = deduction.get("deduction_id")
                if deduction.get("frequency") == "MONTHLY":
                    yearly_amount =  amount * 12                  
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount       
                elif deduction.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  amount * 6
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                else:
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, amount)
                    total_amount += deducted_amount

            elif deduction.get("calculation_type") == "PERCENTAGE_NET":
                try:
                    amount = deduction.get("percentage") / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                deduction_id = deduction.get("deduction_id")
                if deduction.get("frequency") == "MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 12                               
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                    
                elif deduction.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 6                                
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                else:                               
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, amount)
                    total_amount += deducted_amount
            else:
                deduction_id = deduction.get("deduction_id")
                all_component_id = deduction.get("custom_component_id")
                custom_amount = 0
                amount = deduction.get("percentage")
                try:
                    percentage_amount = amount / 100
                except ZeroDivisionError:
                    percentage_amount = 0
                
                for component_id in all_component_id:
                    custom_comp = SalaryComponentSettings.objects.filter(id=component_id).first()
                    if custom_comp:
                        if custom_comp.calculation_type == "FIXED_AMOUNT":
                            this_amount = custom_comp.amount
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = this_amount * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = this_amount * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += this_amount
                        elif custom_comp.calculation_type == "PERCENTAGE_NET":
                            try:
                                this_percent = custom_comp.amount / 100
                            except ZeroDivisionError:
                                this_percent = 0
                            PERCENTAGE_NET = net_amount * this_percent
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += PERCENTAGE_NET
                        else:
                            custom_amount += PERCENTAGE_NET
                    else:
                        custom_amount += 0

                total_custom_amount = custom_amount * percentage_amount
                deduction_id = deduction.get("deduction_id")                                
                
                # total_amount += deducted_amount
                if deduction.get("frequency") == "MONTHLY":
                    yearly_amount =  total_custom_amount * 12                                
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                elif deduction.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  total_custom_amount * 6                             
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                else:
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, total_custom_amount)
                    total_amount += deducted_amount

        return total_amount
    
def employee_deduction_component_calculator(deduction_data, net_amount):
    from payroll_app.models import SalaryComponentSettings, OtherDependencySettings
    total_amount = 0
    if len(deduction_data) <= 0:
        return total_amount
    else:
        for deduction in deduction_data:
            if deduction.get("calculation_type") == "FIXED_AMOUNT":
                amount = deduction.get("amount")
                deduction_id = deduction.get("deduction_id")
                if deduction.get("frequency") == "MONTHLY":
                    yearly_amount =  amount * 12                  
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount       
                elif deduction.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  amount * 6
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                else:
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, amount)
                    total_amount += deducted_amount

            elif deduction.get("calculation_type") == "PERCENTAGE_NET":
                try:
                    amount = deduction.get("percentage") / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                deduction_id = deduction.get("deduction_id")
                if deduction.get("frequency") == "MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 12                               
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                    
                elif deduction.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 6                                
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                else:                               
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, amount)
                    total_amount += deducted_amount
            else:
                deduction_id = deduction.get("deduction_id")
                all_component_id = deduction.get("custom_component_id")
                custom_amount = 0
                amount = deduction.get("percentage")
                try:
                    percentage_amount = amount / 100
                except ZeroDivisionError:
                    percentage_amount = 0
                
                for component_id in all_component_id:
                    custom_comp = SalaryComponentSettings.objects.filter(id=component_id).first()
                    if custom_comp:
                        if custom_comp.calculation_type == "FIXED_AMOUNT":
                            this_amount = custom_comp.amount
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = this_amount * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = this_amount * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += this_amount
                        elif custom_comp.calculation_type == "PERCENTAGE_NET":
                            try:
                                this_percent = custom_comp.amount / 100
                            except ZeroDivisionError:
                                this_percent = 0
                            PERCENTAGE_NET = net_amount * this_percent
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += PERCENTAGE_NET
                        else:
                            custom_amount += PERCENTAGE_NET
                    else:
                        custom_amount += 0

                total_custom_amount = custom_amount * percentage_amount
                deduction_id = deduction.get("deduction_id")                                
                
                # total_amount += deducted_amount
                if deduction.get("frequency") == "MONTHLY":
                    yearly_amount =  total_custom_amount * 12                                
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                elif deduction.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  total_custom_amount * 6                             
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                else:
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, total_custom_amount)
                    total_amount += deducted_amount

        return total_amount
    
def employee_deduction_component_calculator_display(deduction_data, net_amount):
    from payroll_app.models import SalaryComponentSettings, OtherDependencySettings
    this_data = []
    total_amount = 0
    if len(deduction_data) <= 0:
        return this_data
    else:
        for deduction in deduction_data:
            if deduction.get("calculation_type") == "FIXED_AMOUNT":
                amount = deduction.get("amount")
                deduction_id = deduction.get("deduction_id")
                if deduction.get("frequency") == "MONTHLY":
                    yearly_amount =  amount * 12                  
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount   
                    this_data.append(
                        {
                            "name": deduction.get("name"),
                            "calculation_type": deduction.get("calculation_type"),
                            "frequency": deduction.get("frequency"),
                            "annual_amount": deducted_amount,
                        }  
                    ) 
                elif deduction.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  amount * 6
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                    this_data.append(
                        {
                            "name": deduction.get("name"),
                            "calculation_type": deduction.get("calculation_type"),
                            "frequency": deduction.get("frequency"),
                            "annual_amount": deducted_amount,
                        }  
                    ) 
                else:
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, amount)
                    total_amount += deducted_amount
                    this_data.append(
                        {
                            "name": deduction.get("name"),
                            "calculation_type": deduction.get("calculation_type"),
                            "frequency": deduction.get("frequency"),
                            "annual_amount": deducted_amount,
                        }  
                    ) 

            elif deduction.get("calculation_type") == "PERCENTAGE_NET":
                try:
                    amount = deduction.get("percentage") / 100
                except ZeroDivisionError:
                    amount = 0
                PERCENTAGE_NET = net_amount * amount
                deduction_id = deduction.get("deduction_id")
                if deduction.get("frequency") == "MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 12                               
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                    this_data.append(
                        {
                            "name": deduction.get("name"),
                            "calculation_type": deduction.get("calculation_type"),
                            "frequency": deduction.get("frequency"),
                            "annual_amount": deducted_amount,
                        }  
                    ) 
                    
                elif deduction.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  PERCENTAGE_NET * 6                                
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                    this_data.append(
                        {
                            "name": deduction.get("name"),
                            "calculation_type": deduction.get("calculation_type"),
                            "frequency": deduction.get("frequency"),
                            "annual_amount": deducted_amount,
                        }  
                    ) 
                else:                               
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, amount)
                    total_amount += deducted_amount
                    this_data.append(
                        {
                            "name": deduction.get("name"),
                            "calculation_type": deduction.get("calculation_type"),
                            "frequency": deduction.get("frequency"),
                            "annual_amount": deducted_amount,
                        }  
                    ) 
            else:
                deduction_id = deduction.get("deduction_id")
                all_component_id = deduction.get("custom_component_id")
                custom_amount = 0
                amount = deduction.get("percentage")
                try:
                    percentage_amount = amount / 100
                except ZeroDivisionError:
                    percentage_amount = 0
                
                for component_id in all_component_id:
                    custom_comp = SalaryComponentSettings.objects.filter(id=component_id).first()
                    if custom_comp:
                        if custom_comp.calculation_type == "FIXED_AMOUNT":
                            this_amount = custom_comp.amount
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = this_amount * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = this_amount * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += this_amount
                        elif custom_comp.calculation_type == "PERCENTAGE_NET":
                            try:
                                this_percent = custom_comp.amount / 100
                            except ZeroDivisionError:
                                this_percent = 0
                            PERCENTAGE_NET = net_amount * this_percent
                            if custom_comp.frequency == "MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 12
                                custom_amount += yearly_amount
                            elif custom_comp.frequency == "BI-MONTHLY":
                                yearly_amount = PERCENTAGE_NET * 6
                                custom_amount += yearly_amount
                            else:
                                custom_amount += PERCENTAGE_NET
                        else:
                            custom_amount += PERCENTAGE_NET
                    else:
                        custom_amount += 0

                total_custom_amount = custom_amount * percentage_amount
                deduction_id = deduction.get("deduction_id")                                
                
                # total_amount += deducted_amount
                if deduction.get("frequency") == "MONTHLY":
                    yearly_amount =  total_custom_amount * 12                                
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                    this_data.append(
                        {
                            "name": deduction.get("name"),
                            "calculation_type": deduction.get("calculation_type"),
                            "frequency": deduction.get("frequency"),
                            "annual_amount": deducted_amount,
                        }  
                    ) 
                elif deduction.get("frequency") == "BI-MONTHLY":
                    yearly_amount =  total_custom_amount * 6                             
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                    total_amount += deducted_amount
                    this_data.append(
                        {
                            "name": deduction.get("name"),
                            "calculation_type": deduction.get("calculation_type"),
                            "frequency": deduction.get("frequency"),
                            "annual_amount": deducted_amount,
                        }  
                    ) 
                else:
                    deducted_amount = other_dependency_calculation(deduction_id, net_amount, total_custom_amount)
                    total_amount += deducted_amount
                    this_data.append(
                        {
                            "name": deduction.get("name"),
                            "calculation_type": deduction.get("calculation_type"),
                            "frequency": deduction.get("frequency"),
                            "annual_amount": deducted_amount,
                        }  
                    ) 

        return this_data
    
def employee_deduction_report(deduction, net_amount):
    from payroll_app.models import SalaryComponentSettings, OtherDependencySettings
    net_amount
    total_amount = 0
    if deduction is None:
        return total_amount
    else:
        if deduction.get("calculation_type") == "FIXED_AMOUNT":
            amount = deduction.get("amount")
            deduction_id = deduction.get("deduction_id")
            if deduction.get("frequency") == "MONTHLY":
                yearly_amount =  amount * 12                  
                deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                total_amount += deducted_amount       
            elif deduction.get("frequency") == "BI-MONTHLY":
                yearly_amount =  amount * 6
                deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                total_amount += deducted_amount
            else:
                deducted_amount = other_dependency_calculation(deduction_id, net_amount, amount)
                total_amount += deducted_amount

        elif deduction.get("calculation_type") == "PERCENTAGE_NET":
            try:
                amount = deduction.get("percentage") / 100
            except ZeroDivisionError:
                amount = 0
            PERCENTAGE_NET = net_amount * amount
            deduction_id = deduction.get("deduction_id")
            if deduction.get("frequency") == "MONTHLY":
                yearly_amount =  PERCENTAGE_NET * 12                               
                deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                total_amount += deducted_amount
                
            elif deduction.get("frequency") == "BI-MONTHLY":
                yearly_amount =  PERCENTAGE_NET * 6                                
                deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                total_amount += deducted_amount
            else:                               
                deducted_amount = other_dependency_calculation(deduction_id, net_amount, amount)
                total_amount += deducted_amount
        else:
            deduction_id = deduction.get("deduction_id")
            all_component_id = deduction.get("custom_component_id")
            custom_amount = 0
            amount = deduction.get("percentage")
            try:
                percentage_amount = amount / 100
            except ZeroDivisionError:
                percentage_amount = 0
            
            for component_id in all_component_id:
                custom_comp = SalaryComponentSettings.objects.filter(id=component_id).first()
                if custom_comp:
                    if custom_comp.calculation_type == "FIXED_AMOUNT":
                        this_amount = custom_comp.amount
                        if custom_comp.frequency == "MONTHLY":
                            yearly_amount = this_amount * 12
                            custom_amount += yearly_amount
                        elif custom_comp.frequency == "BI-MONTHLY":
                            yearly_amount = this_amount * 6
                            custom_amount += yearly_amount
                        else:
                            custom_amount += this_amount
                    elif custom_comp.calculation_type == "PERCENTAGE_NET":
                        try:
                            this_percent = custom_comp.amount / 100
                        except ZeroDivisionError:
                            this_percent = 0
                        PERCENTAGE_NET = net_amount * this_percent
                        if custom_comp.frequency == "MONTHLY":
                            yearly_amount = PERCENTAGE_NET * 12
                            custom_amount += yearly_amount
                        elif custom_comp.frequency == "BI-MONTHLY":
                            yearly_amount = PERCENTAGE_NET * 6
                            custom_amount += yearly_amount
                        else:
                            custom_amount += PERCENTAGE_NET
                    else:
                        custom_amount += PERCENTAGE_NET
                else:
                    custom_amount += 0

            total_custom_amount = custom_amount * percentage_amount
            deduction_id = deduction.get("deduction_id")                                
            
            # total_amount += deducted_amount
            if deduction.get("frequency") == "MONTHLY":
                yearly_amount =  total_custom_amount * 12                                
                deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                total_amount += deducted_amount
            elif deduction.get("frequency") == "BI-MONTHLY":
                yearly_amount =  total_custom_amount * 6                             
                deducted_amount = other_dependency_calculation(deduction_id, net_amount, yearly_amount)
                total_amount += deducted_amount
            else:
                deducted_amount = other_dependency_calculation(deduction_id, net_amount, total_custom_amount)
                total_amount += deducted_amount

        return total_amount

def enable_payroll_settings_check(company, employee_data, company_tax):
    from payroll_app.models import BenefitComponentSettings, OtherDeductionSettings, SalaryComponentSettings, CompanyPayrollSettings
    if not employee_data:
        return False, "no employee to apply payroll settings"
    
    salary_component = []
    benefit_component = []
    deduction_component = []
    #### salary component
    payroll_status = False
    salary_component_distinct = False
    # try:
    #     company_payroll_settings = CompanyPayrollSettings.objects.get(company=company)
    #     employee_contribution_pension_amount = company_payroll_settings.employee_contribution_pension_amount
    #     employer_contribution_pension_amount = company_payroll_settings.employer_contribution_pension_amount
    # except CompanyPayrollSettings.DoesNotExist:
    #     employee_contribution_pension_amount = 0
    #     employer_contribution_pension_amount = 0

    all_salary_component = SalaryComponentSettings.objects.filter(company__id=company.id, is_active=True)
    if not all_salary_component:
        return False, "no salary component available"

    if company_tax == "STANDARD_TAX":
        salary_component_count = all_salary_component.filter(salary_name__in=["Basic", "Housing", "Transport"]).count() or 0
        payroll_status = True if salary_component_count >= 1 else False
        distinct_salary_value_count= all_salary_component.values('calculation_type').distinct().count() or 0
        
        salary_component_distinct = True if distinct_salary_value_count == 1 else False
        if not payroll_status:
            return payroll_status, "At least one of Basic, Housing or Transport components is needed for this calculation"
        if not salary_component_distinct:
            return salary_component_distinct, "all salary components must have the same calculation_type"
    else:
        distinct_salary_value_count= all_salary_component.values('calculation_type').distinct().count() or 0
        salary_component_distinct = True if distinct_salary_value_count == 1 else False
        if not salary_component_distinct:
            return salary_component_distinct, "all salary components must have the same calculation_type"
    
    temp_net_amount = 0
    net_calculation_type = "FIXED_AMOUNT"
    
    if all_salary_component.first().calculation_type == "PERCENTAGE_NET":
        net_calculation_type = "PERCENTAGE_NET"
        salary_percentage_amount = all_salary_component.aggregate(Sum("amount"))["amount__sum"] or 0
        if round(salary_percentage_amount) <= 100:
            temp_net_amount = salary_percentage_amount
        else:
            return False, "total salary component percentage must be more than 100 percent"
    else:
        net_calculation_type = "FIXED_AMOUNT"
        temp_net_amount = all_salary_component.aggregate(Sum("amount"))["amount__sum"] or 0
        if temp_net_amount <= 0:
            return False, "total salary component fixed amount must be more than 0"
        else:
            pass
    
    
    # if all_salary_component:
    #     for components in all_salary_component:
    #         if components.calculation_type == "FIXED_AMOUNT":
    #             salary_component.append({
    #                     "fixed": True, 
    #                     "amount": components.amount,
    #                     "name": components.salary_name,
    #                     "frequency": components.frequency,
    #                     "calculation_type": "FIXED_AMOUNT"
    #                 }
    #             )
    #         elif components.calculation_type == "PERCENTAGE_NET":
    #             salary_component.append(
    #                 {
    #                     "fixed": False,
    #                     "percentage": components.amount,
    #                     "PERCENTAGE_NET": True,
    #                     "name": components.salary_name,
    #                     "frequency": components.frequency,
    #                     "calculation_type": "PERCENTAGE_NET"
    #                 }
    #             )
    
    #### benefit component
    # all_benefit_component = BenefitComponentSettings.objects.filter(company__id=company.id, is_active=True)
    # if all_benefit_component:
    #     for components in all_benefit_component:
    #         if components.calculation_type == "FIXED_AMOUNT":
    #             benefit_component.append(
    #                 {
    #                     "fixed": True, 
    #                     "amount": components.amount,
    #                     "name": components.benefit_name,
    #                     "frequency": components.frequency,
    #                     "calculation_type": "FIXED_AMOUNT"
    #                 }
    #             )
    #         elif components.calculation_type == "CUSTOM_PERCENTAGE":
    #             benefit_component.append(
    #                 {
    #                     "fixed": False,
    #                     "percentage": components.amount,
    #                     "custom_percentage": True,
    #                     "name": components.benefit_name,
    #                     "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
    #                     "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
    #                     "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
    #                     "frequency": components.frequency,
    #                     "calculation_type": "CUSTOM_PERCENTAGE"
    #                 }
    #             )
    #         elif components.calculation_type == "PERCENTAGE_NET":
    #             benefit_component.append(
    #                 {
    #                     "fixed": False,
    #                     "custom_percentage": False,
    #                     "name": components.benefit_name,
    #                     "percentage": components.amount,
    #                     "frequency": components.frequency,
    #                     "calculation_type": "PERCENTAGE_NET"
    #                 }
    #             )

    # #### deductions
    # all_deduction_component = OtherDeductionSettings.objects.filter(company__id=company.id, is_active=True)
    # if all_deduction_component:
    #     for components in all_deduction_component:
    #         if components.calculation_type == "FIXED_AMOUNT":
    #             deduction_component.append(
    #                 {
    #                     "fixed": True, 
    #                     "amount": components.amount,
    #                     "name": components.deduction_name,
    #                     "frequency": components.frequency,
    #                     "calculation_type": "FIXED_AMOUNT",
    #                     "deduction_id": components.id
    #                 }
    #             )
    #         elif components.calculation_type == "CUSTOM_PERCENTAGE":
    #             deduction_component.append(
    #                 {
    #                     "fixed": False,
    #                     "percentage": components.amount,
    #                     "custom_percentage": True,
    #                     "name": components.deduction_name,
    #                     "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
    #                     "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
    #                     "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
    #                     "frequency": components.frequency,
    #                     "calculation_type": "CUSTOM_PERCENTAGE",
    #                     "deduction_id": components.id
                    
    #                 }
    #             )
    #         elif components.calculation_type == "PERCENTAGE_NET":
    #             deduction_component.append(
    #                 { 
    #                     "fixed": False,
    #                     "custom_percentage": False,
    #                     "percentage": components.amount,
    #                     "name": components.deduction_name,
    #                     "frequency": components.frequency,
    #                     "calculation_type": "PERCENTAGE_NET",
    #                     "deduction_id": components.id
    #                 }

    #             )
    # this_error = 0
    # for employee in employee_data:
    #     if employee.employee_net_amount or 0 <= 0 and net_calculation_type == "PERCENTAGE_NET":
    #         continue
    #     if net_calculation_type == "PERCENTAGE_NET":
    #         salary_component_amount = employee.employee_net_amount or 0
    #     else:
    #         salary_component_amount = employee_salary_component(salary_data=salary_component, employee_data=employee)
        

    #     benefit_component_amount = employee_benefit_component(benefit_data=benefit_component, employee_data=employee, net_amount=salary_component_amount)
    #     deduction_component_amount = employee_deduction_component(deduction_data=deduction_component, employee_data=employee)
        
    #     total_net_amount = salary_component_amount
    #     try:
    #         monthly_gross = total_net_amount / 12
    #     except ZeroDivisionError:
    #         monthly_gross = 0

        
    #     # monthly_gross = total_net_amount
    #     total_monthly_gross = round(total_net_amount + benefit_component_amount, 2)
    #     total_annual_gross_amount = total_monthly_gross
    #     employee_net_amount = total_annual_gross_amount - deduction_component_amount
   

    #     if company_tax == "STANDARD_TAX":
    #         tax_data = consolidated_tax(employee, total_monthly_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount)
            
    #         if not tax_data: 
    #             annual_gross_after_tax = 0
    #         else:
    #             annual_gross_after_tax = tax_data.get("annual_gross_after_tax")
                
    #     elif company_tax == "CUSTOM_TAX":
    #         tax_data = custom_consolidated_tax(employee, total_monthly_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, deduction_component_amount)
    #         if not tax_data:
    #             annual_gross_after_tax = 0
    #         else:
    #             annual_gross_after_tax = tax_data.get("annual_gross_after_tax")
    #     else:
    #         annual_gross_after_tax = 0

    #     try:
    #         net_amount = annual_gross_after_tax / 12
    #     except ZeroDivisionError:
    #         net_amount = 0
            
    #     other_deductions = employee.employee_other_deductions or 0
    #     other_bonus = employee.employee_other_amount or 0
        
    #     total_payable_amount = (net_amount + other_bonus) - other_deductions
    #     if total_payable_amount < 0:
    #         this_error+=1

    # if this_error > 0:
    #     return False, "check payroll settings, some employees will have negative payable" 
    # else:
    return True, f"{net_calculation_type}"

def enable_payroll_settings_calculator_check(company, company_tax):
    from payroll_app.models import BenefitComponentSettings, OtherDeductionSettings, SalaryComponentSettings, CompanyPayrollSettings

    payroll_status = False
    salary_component_distinct = False
    all_salary_component = SalaryComponentSettings.objects.filter(company__id=company.id, is_active=True)
    if not all_salary_component:
        return False, "no salary component available"

    if company_tax == "STANDARD_TAX":
        salary_component_count = all_salary_component.filter(salary_name__in=["Basic", "Housing", "Transport"]).count() or 0
        payroll_status = True if salary_component_count >= 1 else False
        distinct_salary_value_count= all_salary_component.values('calculation_type').distinct().count() or 0
        
        salary_component_distinct = True if distinct_salary_value_count == 1 else False
        if not payroll_status:
            return payroll_status, "At least one of Basic, Housing or Transport components is needed for this calculation"
        if not salary_component_distinct:
            return salary_component_distinct, "all salary components must have the same calculation_type"
    else:
        distinct_salary_value_count= all_salary_component.values('calculation_type').distinct().count() or 0
        salary_component_distinct = True if distinct_salary_value_count == 1 else False
        if not salary_component_distinct:
            return salary_component_distinct, "all salary components must have the same calculation_type"
    
    temp_net_amount = 0
    net_calculation_type = "FIXED_AMOUNT"
    
    if all_salary_component.first().calculation_type == "PERCENTAGE_NET":
        net_calculation_type = "PERCENTAGE_NET"
        salary_percentage_amount = all_salary_component.aggregate(Sum("amount"))["amount__sum"] or 0
        if round(salary_percentage_amount) <= 100:
            temp_net_amount = salary_percentage_amount
        else:
            return False, "total salary component percentage must be more than 100 percent"
    else:
        net_calculation_type = "FIXED_AMOUNT"
        temp_net_amount = all_salary_component.aggregate(Sum("amount"))["amount__sum"] or 0
        if temp_net_amount <= 0:
            return False, "total salary component fixed amount must be more than 0"
        else:
            pass
    
    return True, f"{net_calculation_type}"
        
    
def enable_payroll_settings(company, employee, company_tax, net_calculation_type):
    from payroll_app.models import BenefitComponentSettings, OtherDeductionSettings, SalaryComponentSettings, CompanyPayrollSettings
    salary_component = []
    benefit_component = []
    deduction_component = []

    try:
        company_payroll_settings = CompanyPayrollSettings.objects.get(company=company)
        if company_payroll_settings.pension:
            employee_contribution_pension_amount = company_payroll_settings.employee_contribution_pension_amount
            employer_contribution_pension_amount = company_payroll_settings.employer_contribution_pension_amount
        else:
            employee_contribution_pension_amount = 0
            employer_contribution_pension_amount = 0
    except CompanyPayrollSettings.DoesNotExist:
        employee_contribution_pension_amount = 0
        employer_contribution_pension_amount = 0

    #### salary component
    all_salary_component = SalaryComponentSettings.objects.filter(company__id=company.id, is_active=True)
    if all_salary_component:
        for components in all_salary_component:
            if components.calculation_type == "FIXED_AMOUNT":
                salary_component.append({
                        "fixed": True, 
                        "amount": components.amount,
                        "name": components.salary_name,
                        "frequency": components.frequency,
                        "calculation_type": "FIXED_AMOUNT"
                    }
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                salary_component.append(
                    {
                        "fixed": False,
                        "percentage": components.amount,
                        "PERCENTAGE_NET": True,
                        "name": components.salary_name,
                        "frequency": components.frequency,
                        "calculation_type": "PERCENTAGE_NET"
                    }
                )
    
    #### benefit component
    all_benefit_component = BenefitComponentSettings.objects.filter(company__id=company.id, is_active=True)
    if all_benefit_component:
        for components in all_benefit_component:
            if components.calculation_type == "FIXED_AMOUNT":
                benefit_component.append(
                    {
                        "fixed": True, 
                        "amount": components.amount,
                        "name": components.benefit_name,
                        "frequency": components.frequency,
                        "calculation_type": "FIXED_AMOUNT"
                    }
                )
            elif components.calculation_type == "CUSTOM_PERCENTAGE":
                benefit_component.append(
                    {
                        "fixed": False,
                        "percentage": components.amount,
                        "custom_percentage": True,
                        "name": components.benefit_name,
                        "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                        "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                        "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
                        "frequency": components.frequency,
                        "calculation_type": "CUSTOM_PERCENTAGE"
                    }
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                benefit_component.append(
                    {
                        "fixed": False,
                        "custom_percentage": False,
                        "name": components.benefit_name,
                        "percentage": components.amount,
                        "frequency": components.frequency,
                        "calculation_type": "PERCENTAGE_NET"
                    }
                )

    #### deductions
    all_deduction_component = OtherDeductionSettings.objects.filter(company__id=company.id, is_active=True)
    if all_deduction_component:
        for components in all_deduction_component:
            if components.calculation_type == "FIXED_AMOUNT":
                deduction_component.append(
                    {
                        "fixed": True, 
                        "amount": components.amount,
                        "name": components.deduction_name,
                        "frequency": components.frequency,
                        "calculation_type": "FIXED_AMOUNT",
                        "deduction_id": components.id
                    }
                )
            elif components.calculation_type == "CUSTOM_PERCENTAGE":
                deduction_component.append(
                    {
                        "fixed": False,
                        "percentage": components.amount,
                        "custom_percentage": True,
                        "name": components.deduction_name,
                        "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                        "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                        "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
                        "frequency": components.frequency,
                        "calculation_type": "CUSTOM_PERCENTAGE",
                        "deduction_id": components.id
                    
                    }
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                deduction_component.append(
                    { 
                        "fixed": False,
                        "custom_percentage": False,
                        "percentage": components.amount,
                        "name": components.deduction_name,
                        "frequency": components.frequency,
                        "calculation_type": "PERCENTAGE_NET",
                        "deduction_id": components.id
                    }

                )
    
    this_employee_net_amount = employee.employee_net_amount or 0
    if  this_employee_net_amount <= 0 and net_calculation_type == "PERCENTAGE_NET":
        return False, {"employee_payable_amount": 0,
            "basic_amount": 0,
            "housing_amount": 0,
            "transport_amount": 0,
            "employee_tax_amount": 0,
            "employee_gross_amount": 0,
            "employee_net_amount": 0,
            "pension_amount": 0,
            "employer_pension_amount": 0,
            "employee_voluntary_pension_amount": 0,
            }
    if net_calculation_type == "PERCENTAGE_NET":
        salary_component_amount = employee.employee_net_amount or 0
    else:
        salary_component_amount = employee_salary_component(salary_data=salary_component, employee_data=employee)

    benefit_component_amount = employee_benefit_component(benefit_data=benefit_component, net_amount=salary_component_amount)
    deduction_component_amount = employee_deduction_component(deduction_data=deduction_component, net_amount=salary_component_amount)

    total_net_amount = salary_component_amount

    if net_calculation_type == "PERCENTAGE_NET":
        annual_gross = (total_net_amount * 12) + benefit_component_amount
    else:
        annual_gross = round(total_net_amount + benefit_component_amount, 2)
    
    basic_amount = 0
    housing_amount = 0
    transport_amount = 0
    monthly_tax = 0
    if company_tax == "STANDARD_TAX":
        tax_data = consolidated_tax(employee, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, net_calculation_type)
        basic_amount = tax_data.get("basic_amount")
        housing_amount = tax_data.get("housing_amount")
        transport_amount = tax_data.get("transport_amount")
        monthly_tax = tax_data.get("monthly_tax")
        pension_amount = tax_data.get("total_monthly_employee_pension_amount") 
        monthly_net = tax_data.get("monthly_net")
        pension_employer_amount = tax_data.get("monthly_employer_pension_amount")
        employee_voluntary_pension_amount = tax_data.get("employee_voluntary_pension_amount")
            
    elif company_tax == "CUSTOM_TAX":
        tax_data = custom_consolidated_tax(employee, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, deduction_component_amount, net_calculation_type)
        basic_amount = tax_data.get("basic_amount")
        housing_amount = tax_data.get("housing_amount")
        transport_amount = tax_data.get("transport_amount")
        monthly_tax = tax_data.get("monthly_tax")
        pension_amount = tax_data.get("total_monthly_employee_pension_amount")
        monthly_net = tax_data.get("monthly_net")
        pension_employer_amount = tax_data.get("monthly_employer_pension_amount")
        employee_voluntary_pension_amount = tax_data.get("employee_voluntary_pension_amount")
    else:
        monthly_tax = 0
        try:
            employee_pension_contribution =  employee_contribution_pension_amount / 100
        except ZeroDivisionError:
            employee_pension_contribution = 0
        try:
            employer_pension_contribution = employer_contribution_pension_amount / 100
        except ZeroDivisionError:    
            employer_pension_contribution = 0

        total_bht = get_bht_calculator(company, total_net_amount)
        bht_data = get_bht_amount(company, total_net_amount)
        basic_amount = bht_data.get("basic_amount")
        housing_amount = bht_data.get("housing_amount")
        transport_amount = bht_data.get("transport_amount")

        employee_pension_amount = employee_pension_contribution * total_bht
        employer_pension_amount = employer_pension_contribution * total_bht
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0
        try:
            monthly_employer_pension_amount = employer_pension_amount / 12
        except ZeroDivisionError:
            monthly_employer_pension_amount = 0

        
        pension_employer_amount = monthly_employer_pension_amount
        pension_amount = monthly_employee_pension_amount
        employee_voluntary_pension_amount = employee.employee_voluntary_pension_amount
        
        monthly_life_insurance = employee.employee_life_insurance_amount or 0
        life_insurance = monthly_life_insurance

        total_pension_deduction = pension_amount + employee_voluntary_pension_amount
        try:
            this_monthly_net = annual_gross / 12
        except ZeroDivisionError:
            this_monthly_net = 0

        monthly_net = this_monthly_net - total_pension_deduction - life_insurance

    # employee.employee_net_amount = monthly_net
    other_deductions = employee.employee_other_deductions or 0
    other_bonus = employee.employee_other_amount or 0
    total_payable_amount = (monthly_net + other_bonus) - other_deductions

    try:
        monthly_basic_amount = basic_amount / 12
    except ZeroDivisionError:
        monthly_basic_amount = 0
    try:
        monthly_housing_amount = housing_amount / 12
    except ZeroDivisionError:
        monthly_housing_amount = 0
    try:
        monthly_transport_amount = transport_amount / 12
    except ZeroDivisionError:
        monthly_transport_amount = 0
    
    if net_calculation_type == "PERCENTAGE_NET":
        employee_gross_amount = salary_component_amount
    else:
        try:
            employee_gross_amount = salary_component_amount / 12
        except ZeroDivisionError:   
            employee_gross_amount = 0

    if total_payable_amount > 0:
        return True, {"employee_payable_amount": round(total_payable_amount, 2),
                    "basic_amount": round(monthly_basic_amount, 2),
                    "housing_amount": round(monthly_housing_amount, 2),
                    "transport_amount": round(monthly_transport_amount, 2),
                    "employee_tax_amount": round(monthly_tax, 2),
                    "employee_gross_amount": round(employee_gross_amount, 2),
                    "employee_net_amount": round(monthly_net, 2),
                    "pension_amount": round(pension_amount, 2),
                    "employer_pension_amount": round(pension_employer_amount, 2),
                    "employee_voluntary_pension_amount": round(employee_voluntary_pension_amount, 2)
                    }
    else:
        return False, {"employee_payable_amount": round(total_payable_amount, 2),
                    "basic_amount": round(monthly_basic_amount, 2),
                    "housing_amount": round(monthly_housing_amount, 2),
                    "transport_amount": round(monthly_transport_amount, 2),
                    "employee_tax_amount": round(monthly_tax, 2),
                    "employee_gross_amount": round(employee_gross_amount, 2),
                    "employee_net_amount": round(monthly_net, 2),
                    "pension_amount": round(pension_amount, 2),
                    "employer_pension_amount": round(pension_employer_amount, 2),
                    "employee_voluntary_pension_amount": round(employee_voluntary_pension_amount, 2)
                    }
    
def enable_payroll_settings_calculator(company, company_tax, net_calculation_type, employee_net_amount, other_deductions, other_bonus, life_insurance, hmo, voluntary_pension):
    from payroll_app.models import BenefitComponentSettings, OtherDeductionSettings, SalaryComponentSettings, CompanyPayrollSettings
    salary_component = []
    benefit_component = []
    deduction_component = []

    try:
        company_payroll_settings = CompanyPayrollSettings.objects.get(company=company)
        if company_payroll_settings.pension:
            employee_contribution_pension_amount = company_payroll_settings.employee_contribution_pension_amount
            employer_contribution_pension_amount = company_payroll_settings.employer_contribution_pension_amount
        else:
            employee_contribution_pension_amount = 0
            employer_contribution_pension_amount = 0
    except CompanyPayrollSettings.DoesNotExist:
        employee_contribution_pension_amount = 0
        employer_contribution_pension_amount = 0

    #### salary component
    all_salary_component = SalaryComponentSettings.objects.filter(company__id=company.id, is_active=True)
    if all_salary_component:
        for components in all_salary_component:
            if components.calculation_type == "FIXED_AMOUNT":
                salary_component.append({
                        "fixed": True, 
                        "amount": components.amount,
                        "name": components.salary_name,
                        "frequency": components.frequency,
                        "calculation_type": "FIXED_AMOUNT"
                    }
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                salary_component.append(
                    {
                        "fixed": False,
                        "percentage": components.amount,
                        "PERCENTAGE_NET": True,
                        "name": components.salary_name,
                        "frequency": components.frequency,
                        "calculation_type": "PERCENTAGE_NET"
                    }
                )
    
    #### benefit component
    all_benefit_component = BenefitComponentSettings.objects.filter(company__id=company.id, is_active=True)
    if all_benefit_component:
        for components in all_benefit_component:
            if components.calculation_type == "FIXED_AMOUNT":
                benefit_component.append(
                    {
                        "fixed": True, 
                        "amount": components.amount,
                        "name": components.benefit_name,
                        "frequency": components.frequency,
                        "calculation_type": "FIXED_AMOUNT"
                    }
                )
            elif components.calculation_type == "CUSTOM_PERCENTAGE":
                benefit_component.append(
                    {
                        "fixed": False,
                        "percentage": components.amount,
                        "custom_percentage": True,
                        "name": components.benefit_name,
                        "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                        "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                        "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
                        "frequency": components.frequency,
                        "calculation_type": "CUSTOM_PERCENTAGE"
                    }
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                benefit_component.append(
                    {
                        "fixed": False,
                        "custom_percentage": False,
                        "name": components.benefit_name,
                        "percentage": components.amount,
                        "frequency": components.frequency,
                        "calculation_type": "PERCENTAGE_NET"
                    }
                )

    #### deductions
    all_deduction_component = OtherDeductionSettings.objects.filter(company__id=company.id, is_active=True)
    if all_deduction_component:
        for components in all_deduction_component:
            if components.calculation_type == "FIXED_AMOUNT":
                deduction_component.append(
                    {
                        "fixed": True, 
                        "amount": components.amount,
                        "name": components.deduction_name,
                        "frequency": components.frequency,
                        "calculation_type": "FIXED_AMOUNT",
                        "deduction_id": components.id
                    }
                )
            elif components.calculation_type == "CUSTOM_PERCENTAGE":
                deduction_component.append(
                    {
                        "fixed": False,
                        "percentage": components.amount,
                        "custom_percentage": True,
                        "name": components.deduction_name,
                        "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                        "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                        "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
                        "frequency": components.frequency,
                        "calculation_type": "CUSTOM_PERCENTAGE",
                        "deduction_id": components.id
                    
                    }
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                deduction_component.append(
                    { 
                        "fixed": False,
                        "custom_percentage": False,
                        "percentage": components.amount,
                        "name": components.deduction_name,
                        "frequency": components.frequency,
                        "calculation_type": "PERCENTAGE_NET",
                        "deduction_id": components.id
                    }

                )
    
    this_employee_net_amount = employee_net_amount
    if  this_employee_net_amount <= 0 and net_calculation_type == "PERCENTAGE_NET":
        return {
            "employee_payable_amount": 0,
            "basic_amount": 0,
            "annual_basic_amount": 0,
            "housing_amount": 0,
            "annual_housing_amount": 0,
            "transport_amount": 0,
            "annual_transport_amount": 0,
            "tax_amount": 0,
            "annual_tax_amount": 0,
            "monthly_gross_salary": 0,
            "annual_gross_salary": 0,
            "annual_gross_pay": 0,
            "employee_net_amount": 0,
            "pension_amount": 0,
            "pension_employer_amount": 0,
            "total_pension_amount": 0,
            "monthly_net": 0,
            "salary_components": [],
            "benefit_components": [],
            "deduction_components": []
        }
    if net_calculation_type == "PERCENTAGE_NET":
        salary_component_amount = employee_net_amount
    else:
        salary_component_amount = employee_salary_component_calculator(salary_data=salary_component, net_amount=employee_net_amount)

    benefit_component_amount = employee_benefit_component_calculator(benefit_data=benefit_component, net_amount=salary_component_amount)
    deduction_component_amount = employee_deduction_component_calculator(deduction_data=deduction_component, net_amount=salary_component_amount)

    total_net_amount = salary_component_amount 
    if net_calculation_type == "PERCENTAGE_NET":
        annual_gross = (total_net_amount * 12) + benefit_component_amount
    else:
        annual_gross = round(total_net_amount + benefit_component_amount, 2)

    basic_amount = 0
    housing_amount = 0
    transport_amount = 0
    monthly_tax = 0
    if company_tax == "STANDARD_TAX":
        tax_data = consolidated_tax_calculator(company, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, life_insurance, hmo, voluntary_pension, net_calculation_type)
        basic_amount = tax_data.get("basic_amount")
        housing_amount = tax_data.get("housing_amount")
        transport_amount = tax_data.get("transport_amount")
        monthly_tax = tax_data.get("monthly_tax")
        pension_amount = tax_data.get("total_monthly_employee_pension_amount") 
        monthly_net = tax_data.get("monthly_net")
        pension_employer_amount = tax_data.get("monthly_employer_pension_amount")
        total_pension_amount = pension_amount + pension_employer_amount + voluntary_pension
            
    elif company_tax == "CUSTOM_TAX":
        tax_data = custom_consolidated_tax_calculator(company, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, deduction_component_amount, life_insurance, hmo, voluntary_pension, net_calculation_type)
        basic_amount = tax_data.get("basic_amount")
        housing_amount = tax_data.get("housing_amount")
        transport_amount = tax_data.get("transport_amount")
        monthly_tax = tax_data.get("monthly_tax")
        pension_amount = tax_data.get("total_monthly_employee_pension_amount")
        monthly_net = tax_data.get("monthly_net")
        pension_employer_amount = tax_data.get("monthly_employer_pension_amount")
        total_pension_amount = pension_amount + pension_employer_amount + voluntary_pension
    else:
        monthly_tax = 0
        try:
            employee_pension_contribution =  employee_contribution_pension_amount / 100
        except ZeroDivisionError:
            employee_pension_contribution = 0
        try:
            employer_pension_contribution = employer_contribution_pension_amount / 100
        except ZeroDivisionError:    
            employer_pension_contribution = 0

        total_bht = get_bht_calculator(company, total_net_amount)
        bht_data = get_bht_amount(company, total_net_amount)
        basic_amount = bht_data.get("basic_amount")
        housing_amount = bht_data.get("housing_amount")
        transport_amount = bht_data.get("transport_amount")


        employee_pension_amount = employee_pension_contribution * total_bht
        employer_pension_amount = employer_pension_contribution * total_bht
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0
        try:
            monthly_employer_pension_amount = employer_pension_amount / 12
        except ZeroDivisionError:
            monthly_employer_pension_amount = 0

        pension_amount = monthly_employee_pension_amount
        pension_employer_amount = monthly_employer_pension_amount
        total_pension_amount = pension_amount + pension_employer_amount + voluntary_pension
        total_pension_deduction = pension_amount + voluntary_pension
        try:
            this_monthly_net = annual_gross / 12
        except ZeroDivisionError:
            this_monthly_net = 0


        monthly_life_insurance = life_insurance or 0
        life_insurance = monthly_life_insurance

        try:
            this_monthly_net = annual_gross / 12
        except ZeroDivisionError:
            this_monthly_net = 0

        monthly_net = this_monthly_net - total_pension_deduction - life_insurance

    # employee.employee_net_amount = monthly_net
    other_deductions = other_deductions or 0
    other_bonus = other_bonus or 0
    total_payable_amount = (monthly_net + other_bonus) - other_deductions

    try:
        monthly_basic_amount = basic_amount / 12
    except ZeroDivisionError:
        monthly_basic_amount = 0
    try:
        monthly_housing_amount = housing_amount / 12
    except ZeroDivisionError:
        monthly_housing_amount = 0
    try:
        monthly_transport_amount = transport_amount / 12
    except ZeroDivisionError:
        monthly_transport_amount = 0
    
    if net_calculation_type == "PERCENTAGE_NET":
        employee_gross_amount = salary_component_amount
    else:
        try:
            employee_gross_amount = salary_component_amount / 12
        except ZeroDivisionError:   
            employee_gross_amount = 0
        
    salary_component_display = employee_salary_component_calculator_display(salary_data=salary_component, net_amount=employee_net_amount)
    benefit_component_display = employee_benefit_component_calculator_display(benefit_data=benefit_component, net_amount=salary_component_amount)
    deduction_component_display = employee_deduction_component_calculator_display(deduction_data=deduction_component, net_amount=salary_component_amount)
    return {
        "employee_payable_amount": round(total_payable_amount , 2),
        "basic_amount": round(monthly_basic_amount, 2),
        "annual_basic_amount": round(monthly_basic_amount * 12, 2),
        "housing_amount": round(monthly_housing_amount, 2),
        "annual_housing_amount": round(monthly_housing_amount * 12, 2),
        "transport_amount": round(monthly_transport_amount, 2),
        "annual_transport_amount": round(monthly_transport_amount * 12, 2),
        "tax_amount": round(monthly_tax, 2),
        "annual_tax_amount": round(monthly_tax * 12, 2),
        "monthly_gross_salary": round(employee_gross_amount, 2),
        "annual_gross_salary": round(employee_gross_amount * 12, 2),
        "annual_gross_pay": round(annual_gross, 2),
        "employee_net_amount": round(total_net_amount, 2),
        "pension_amount": round(pension_amount, 2),
        "monthly_net": round(monthly_net, 2),
        "pension_employer_amount": pension_employer_amount,
        "total_pension_amount": total_pension_amount,
        "salary_components": salary_component_display,
        "benefit_components": benefit_component_display,
        "deduction_components": deduction_component_display,
    }

def enable_payroll_settings_report(company, employee, company_tax, net_calculation_type):

    from payroll_app.models import BenefitComponentSettings, OtherDeductionSettings, SalaryComponentSettings, CompanyPayrollSettings
    salary_component = []
    benefit_component = []
    deduction_component = []

    try:
        company_payroll_settings = CompanyPayrollSettings.objects.get(company=company)
        if company_payroll_settings.pension:
            employee_contribution_pension_amount = company_payroll_settings.employee_contribution_pension_amount
            employer_contribution_pension_amount = company_payroll_settings.employer_contribution_pension_amount
        else:
            employee_contribution_pension_amount = 0
            employer_contribution_pension_amount = 0
    except CompanyPayrollSettings.DoesNotExist:
        employee_contribution_pension_amount = 0
        employer_contribution_pension_amount = 0

    #### salary component
    all_salary_component = SalaryComponentSettings.objects.filter(company__id=company.id, is_active=True)
    if all_salary_component:
        for components in all_salary_component:
            if components.calculation_type == "FIXED_AMOUNT":
                salary_component.append({
                        "fixed": True, 
                        "amount": components.amount,
                        "name": components.salary_name,
                        "frequency": components.frequency,
                        "calculation_type": "FIXED_AMOUNT"
                    }
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                salary_component.append(
                    {
                        "fixed": False,
                        "percentage": components.amount,
                        "PERCENTAGE_NET": True,
                        "name": components.salary_name,
                        "frequency": components.frequency,
                        "calculation_type": "PERCENTAGE_NET"
                    }
                )
    
    #### benefit component
    all_benefit_component = BenefitComponentSettings.objects.filter(company__id=company.id, is_active=True)
    if all_benefit_component:
        for components in all_benefit_component:
            if components.calculation_type == "FIXED_AMOUNT":
                benefit_component.append(
                    {
                        "fixed": True, 
                        "amount": components.amount,
                        "name": components.benefit_name,
                        "frequency": components.frequency,
                        "calculation_type": "FIXED_AMOUNT"
                    }
                )
            elif components.calculation_type == "CUSTOM_PERCENTAGE":
                benefit_component.append(
                    {
                        "fixed": False,
                        "percentage": components.amount,
                        "custom_percentage": True,
                        "name": components.benefit_name,
                        "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                        "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                        "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
                        "frequency": components.frequency,
                        "calculation_type": "CUSTOM_PERCENTAGE"
                    }
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                benefit_component.append(
                    {
                        "fixed": False,
                        "custom_percentage": False,
                        "name": components.benefit_name,
                        "percentage": components.amount,
                        "frequency": components.frequency,
                        "calculation_type": "PERCENTAGE_NET"
                    }
                )

    #### deductions
    all_deduction_component = OtherDeductionSettings.objects.filter(company__id=company.id, is_active=True)
    if all_deduction_component:
        for components in all_deduction_component:
            if components.calculation_type == "FIXED_AMOUNT":
                deduction_component.append(
                    {
                        "fixed": True, 
                        "amount": components.amount,
                        "name": components.deduction_name,
                        "frequency": components.frequency,
                        "calculation_type": "FIXED_AMOUNT",
                        "deduction_id": components.id
                    }
                )
            elif components.calculation_type == "CUSTOM_PERCENTAGE":
                deduction_component.append(
                    {
                        "fixed": False,
                        "percentage": components.amount,
                        "custom_percentage": True,
                        "name": components.deduction_name,
                        "custom_component": [component for component in components.custom_name.all().values_list('salary_name', flat=True)],
                        "custom_component_id": [component for component in components.custom_name.all().values_list('id', flat=True)],
                        "custom_component_amount": [component for component in components.custom_name.all().values_list('amount', flat=True)],
                        "frequency": components.frequency,
                        "calculation_type": "CUSTOM_PERCENTAGE",
                        "deduction_id": components.id
                    
                    }
                )
            elif components.calculation_type == "PERCENTAGE_NET":
                deduction_component.append(
                    { 
                        "fixed": False,
                        "custom_percentage": False,
                        "percentage": components.amount,
                        "name": components.deduction_name,
                        "frequency": components.frequency,
                        "calculation_type": "PERCENTAGE_NET",
                        "deduction_id": components.id
                    }

                )
    
    this_employee_net_amount = employee.employee_net_amount or 0
    if this_employee_net_amount <= 0 and net_calculation_type == "PERCENTAGE_NET":
        return {
            "annual_tax": 0,
            "total_net_amount": 0,
            "one_percent_gross_income_or_200k": 0,
            "total_bht": 0,
            "non_taxable_relief": 0,
            "taxable_income": 0,
            "twenty_percent_gross_income": 0,
            "one_percent_gross_income": 0,
            "ten_percent_bht": 0,
            "monthly_gross": 0,
            "basic_amount": 0,
            "housing_amount": 0,
            "transport_amount": 0,
            "employee_tax_amount": 0,
            "pension_amount": 0,
            "life_insurance_amount": 0,
            "total_payable_amount": 0,
            "employee_net_amount": 0,
            "employee_gross_amount": 0,
            "employee_payable_amount": 0,
            "total_monthly_deductions": 0,
            "annual_gross": 0
        }
    if net_calculation_type == "PERCENTAGE_NET":
        salary_component_amount = employee.employee_net_amount or 0
    else:
        salary_component_amount = employee_salary_component(salary_data=salary_component, employee_data=employee)

    benefit_component_amount = employee_benefit_component(benefit_data=benefit_component, net_amount=salary_component_amount)
    deduction_component_amount = employee_deduction_component(deduction_data=deduction_component, net_amount=salary_component_amount)

    total_net_amount = salary_component_amount

    if net_calculation_type == "PERCENTAGE_NET":
        annual_gross = (total_net_amount * 12) + benefit_component_amount
    else:
        annual_gross = round(total_net_amount + benefit_component_amount, 2)
    
    basic_amount = 0
    housing_amount = 0
    transport_amount = 0
    monthly_tax = 0
    if company_tax == "STANDARD_TAX":
        tax_data = consolidated_tax(employee, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, net_calculation_type)
        basic_amount = tax_data.get("basic_amount")
        housing_amount = tax_data.get("housing_amount")
        transport_amount = tax_data.get("transport_amount")
        monthly_tax = tax_data.get("monthly_tax")
        pension_amount = tax_data.get("total_monthly_employee_pension_amount") 
        monthly_net = tax_data.get("monthly_net")
        annual_tax = tax_data.get("annual_tax")
        one_percent_gross_income_or_200k = tax_data.get("one_percent_gross_income_or_200k")
        total_bht = tax_data.get("total_bht")
        total_monthly_deductions = tax_data.get("total_monthly_deductions")

        non_taxable_relief = tax_data.get("non_taxable_relief")
        taxable_income = tax_data.get("taxable_income")
        twenty_percent_gross_income = tax_data.get("twenty_percent_gross_income")
        one_percent_gross_income = tax_data.get("one_percent_gross_income")
        ten_percent_bht = tax_data.get("ten_percent_bht")
        monthly_gross = tax_data.get("monthly_gross_salary")
            
    elif company_tax == "CUSTOM_TAX":
        tax_data = custom_consolidated_tax(employee, annual_gross, total_net_amount, employee_contribution_pension_amount, employer_contribution_pension_amount, deduction_component_amount, net_calculation_type)
        basic_amount = tax_data.get("basic_amount")
        housing_amount = tax_data.get("housing_amount")
        transport_amount = tax_data.get("transport_amount")
        monthly_tax = tax_data.get("monthly_tax")
        pension_amount = tax_data.get("total_monthly_employee_pension_amount")
        monthly_net = tax_data.get("monthly_net")
        annual_tax = tax_data.get("annual_tax")
        one_percent_gross_income_or_200k = tax_data.get("one_percent_gross_income_or_200k")
        total_bht = tax_data.get("total_bht")
        total_monthly_deductions = tax_data.get("total_monthly_deductions")

        non_taxable_relief = tax_data.get("non_taxable_relief")
        taxable_income = tax_data.get("taxable_income")
        twenty_percent_gross_income = tax_data.get("twenty_percent_gross_income")
        one_percent_gross_income = tax_data.get("one_percent_gross_income")
        ten_percent_bht = tax_data.get("ten_percent_bht")
        monthly_gross = tax_data.get("monthly_gross_salary")
    else:

        monthly_tax = 0
        try:
            employee_pension_contribution =  employee_contribution_pension_amount / 100
        except ZeroDivisionError:
            employee_pension_contribution = 0
        try:
            employer_pension_contribution = employer_contribution_pension_amount / 100
        except ZeroDivisionError:    
            employer_pension_contribution = 0

        total_bht = get_bht_calculator(company, total_net_amount)
        bht_data = get_bht_amount(company, total_net_amount)
        basic_amount = bht_data.get("basic_amount")
        housing_amount = bht_data.get("housing_amount")
        transport_amount = bht_data.get("transport_amount")

        employee_pension_amount = employee_pension_contribution * total_bht
        employer_pension_amount = employer_pension_contribution * total_bht
        try:
            monthly_employee_pension_amount = employee_pension_amount / 12
        except ZeroDivisionError:
            monthly_employee_pension_amount = 0
        try:
            monthly_employer_pension_amount = employer_pension_amount / 12
        except ZeroDivisionError:
            monthly_employer_pension_amount = 0

        
        pension_employer_amount = monthly_employer_pension_amount
        pension_amount = monthly_employee_pension_amount
        employee_voluntary_pension_amount = employee.employee_voluntary_pension_amount
        
        monthly_life_insurance = employee.employee_life_insurance_amount or 0
        life_insurance = monthly_life_insurance

        total_pension_deduction = pension_amount + employee_voluntary_pension_amount
        try:
            this_monthly_net = annual_gross / 12
        except ZeroDivisionError:
            this_monthly_net = 0

        annual_tax = 0
        one_percent_gross_income_or_200k = 0
        total_bht = total_bht
        total_monthly_deductions = monthly_tax + monthly_life_insurance + total_pension_deduction
        monthly_net = this_monthly_net - total_monthly_deductions

        non_taxable_relief = 0
        taxable_income = 0
        twenty_percent_gross_income = 0
        one_percent_gross_income = 0
        ten_percent_bht = pension_employer_amount
        
        if net_calculation_type == "PERCENTAGE_NET":
            monthly_gross = total_net_amount
        else:
            try:
                monthly_gross = total_net_amount / 12
            except ZeroDivisionError:
                monthly_gross = 0
                    
    life_insurance_amount = employee.employee_life_insurance_amount or 0
    # employee.employee_net_amount = monthly_net
    other_deductions = employee.employee_other_deductions or 0
    other_bonus = employee.employee_other_amount or 0
    total_payable_amount = (monthly_net + other_bonus) - other_deductions

    try:
        monthly_basic_amount = basic_amount / 12
    except ZeroDivisionError:
        monthly_basic_amount = 0
    try:
        monthly_housing_amount = housing_amount / 12
    except ZeroDivisionError:
        monthly_housing_amount = 0
    try:
        monthly_transport_amount = transport_amount / 12
    except ZeroDivisionError:
        monthly_transport_amount = 0
    
    if net_calculation_type == "PERCENTAGE_NET":
        employee_gross_amount = salary_component_amount
    else:
        try:
            employee_gross_amount = salary_component_amount / 12
        except ZeroDivisionError:   
            employee_gross_amount = 0

    return {
        "annual_tax": round(annual_tax, 2),
        "total_net_amount": total_net_amount,
        "one_percent_gross_income_or_200k": round(one_percent_gross_income_or_200k, 2),
        "total_bht": round(total_bht, 2),
        "non_taxable_relief": round(non_taxable_relief, 2),
        "taxable_income": round(taxable_income, 2),
        "twenty_percent_gross_income": round(twenty_percent_gross_income, 2),
        "one_percent_gross_income": round(one_percent_gross_income, 2),
        "ten_percent_bht": round(ten_percent_bht, 2),
        "monthly_gross": round(monthly_gross, 2),
        "basic_amount": round(monthly_basic_amount * 12, 2),
        "housing_amount": round(monthly_housing_amount * 12, 2),
        "transport_amount": round(monthly_transport_amount * 12, 2),
        "employee_tax_amount": round(monthly_tax, 2),
        "pension_amount": round(pension_amount, 2),
        "life_insurance_amount": life_insurance_amount * 12,
        "total_payable_amount": round(total_payable_amount, 2),
        "employee_net_amount": round(monthly_net, 2),
        "employee_gross_amount": round(employee_gross_amount, 2),
        "employee_payable_amount": round(total_payable_amount, 2),
        "total_monthly_deductions": total_monthly_deductions,
        "annual_gross": annual_gross

    }