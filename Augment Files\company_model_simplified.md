# Company Model - Simplified Overview

## What is the Company Model?

The Company model is a central part of the Paybox360 system that stores information about businesses using the platform. Think of it as a digital record of a company with all its important details and relationships to other parts of the system.

## Information Stored in the Company Model

### Basic Company Information
- **Company name**: The official name of the business
- **Industry**: What sector the company operates in (e.g., Healthcare, Technology)
- **Company size**: Number of employees in the company
- **Registration number**: Official company registration number
- **Incorporation date**: When the company was legally established

### Configuration Settings
- **Wallet type**: What kind of financial account the company uses ("MAIN" or "CORPORATE")
- **Channel**: How the company was registered (e.g., "WEB")
- **Supplier invite ID**: Unique code for inviting suppliers

### Status Information
- **Active status**: Whether the company account is currently active
- **Deleted status**: Whether the company has been removed from the system
- **Test account**: Whether this is a test company (not a real business)
- **Instant wage**: Whether the company can use instant wage payments
- **Auto-approve suppliers**: Whether supplier approvals happen automatically

### Relationships
- **Owner**: The user who owns/manages the company
- **Teams**: Groups within the company (e.g., Finance, HR, Marketing)

## What the Company Model Can Do

### Basic Functions

#### Display Company Name
- **What it does**: Shows the company name when needed
- **Where it's used**: In the admin dashboard, reports, and user interface

#### Save with Unique ID
- **What it does**: Ensures each company has a unique supplier invitation code
- **Where it's used**: When creating or updating company information

### Financial Functions

#### Calculate Total Budget
- **What it does**: Adds up all active budgets for the company
- **Where it's used**: In financial dashboards and reports

#### Track Monthly Expenses
- **What it does**: Calculates how much the company has spent in the current month
- **Where it's used**: In expense reports and financial dashboards

#### Provide Expense Details
- **What it does**: Gives detailed information about current month expenses
- **Where it's used**: In detailed financial reports

### Management Functions

#### Create New Company
- **What it does**: Sets up a new company in the system with all required information
- **Where it's used**: 
  - When a user registers a new company
  - During the onboarding process
  - When importing company data from forms

#### Find Company Information
- **What it does**: Retrieves company details when needed
- **Where it's used**: 
  - When displaying company information
  - When checking permissions
  - When processing transactions

## External Services Used with the Company Model

### Company Verification (YouVerify)
- **What it does**: Checks if a company's registration information is legitimate
- **Why it's needed**: Ensures only real, legally registered companies can use the platform

### Financial Account Setup (Liberty Pay)
- **What it does**: Creates financial accounts for companies
- **Why it's needed**: Allows companies to make and receive payments through the platform

### Document Validation (OpenAI/ChatGPT)
- **What it does**: Helps verify documents like utility bills
- **Why it's needed**: Ensures documents submitted during verification are valid

### Text Analysis (Spacy NLP)
- **What it does**: Analyzes text in documents
- **Why it's needed**: Helps extract and understand information from company documents

### Document Text Extraction (Pytesseract & PDF2Image)
- **What it does**: Pulls text from images and PDF documents
- **Why it's needed**: Allows the system to "read" uploaded documents

### Bank Account Verification (Mono API)
- **What it does**: Verifies bank account information
- **Why it's needed**: Ensures financial transactions go to the correct accounts

## In Simple Terms

The Company model is like a digital folder that contains all the important information about a business using Paybox360. It stores basic details like the company's name and size, tracks financial information like budgets and expenses, and connects to other parts of the system like teams and users.

It can perform various tasks such as calculating financial totals, creating new companies, and finding company information when needed. It also works with external services to verify company information, set up financial accounts, and validate documents.

This model is essential because it serves as the foundation for most operations in the Paybox360 system, ensuring that each company's data is properly organized and accessible when needed.
