from django.urls import path
# includepython manage.py runserver
# from django.contrib import admin

from subscription_and_invoicing import views
from subscription_and_invoicing.views import FetchAllModules, TieSubscriptionToUserCompanyView, \
    CompanySubscriptionsView, CompanySubscriptionCountView, SubscriptionPlanListView

# from drf_yasg.views import get_schema_view
# from drf_yasg import openapi
# from rest_framework import permissions

# from .views import CreateInvoiceAPIView, ReceivePaymentAPIView, UpdateInvoiceAPIView

# schema_view = get_schema_view(
#    openapi.Info(
#       title="Invoicing API",
#       default_version='v1',
#       description="API for invoicing application",
#    ),
#    public=True,
#    permission_classes=[permissions.AllowAny],
# )

subscription = [
    # path(r"^admin/", admin.site.urls),
    path('create-invoice/', views.CreateInvoiceAPIView.as_view(), name='create_invoice'),
    # path('create-invoice/', views.Tracteit.as_view, name='create_invoice'),
    path('invoices/stats/', views.InvoiceStatsAPIView.as_view(), name='invoice-stats'),
    path('invoices/', views.GetAllInvoiceAPIView.as_view(), name='invoices'),
    path('update-invoice/<int:invoice_id>/', views.UpdateInvoiceAPIView.as_view(), name='update_invoice'),
    path('subscription-dashboard/', views.SubscriptionDashboardAPIView.as_view(), name='subscription_dashboard'),
    path('create_user_and_company/', views.CreateUserAndCompany.as_view(), name='create-user-and-company'),
    path('companies_details/', views.CompanyDetailsAPIView.as_view(), name='company-details'),
    path('companies/statistics/', views.CompanyStatisticsAPIView.as_view(), name='company-statistics'),
    # path('cancel-invoice/<int:invoice_id>/', CancelInvoiceAPIView.as_view, name='cancel_invoice'),
    path('module/', FetchAllModules.as_view(), name='invoice-list'),
    path('subscriptions/tie/', TieSubscriptionToUserCompanyView.as_view(), name='tie-subscription'),
    path('company_subscription/', CompanySubscriptionsView.as_view(), name='company-subscription'),
    path('company_subscription_counts/', CompanySubscriptionCountView.as_view(), name='company-subscription-counts'),
    path('subscription/plan/', SubscriptionPlanListView.as_view(), name='subcription-plan'),
    # path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
]

urlpatterns = [
   *subscription,
]
