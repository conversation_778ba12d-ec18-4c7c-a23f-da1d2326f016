from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

from uuid import UUID

from stock_inventory.models import Branch

class Command(BaseCommand):
    help = "Assign a default user to branches without a 'created_by' user"

    def handle(self, *args, **options):

        try:
            # Fetch the default user
            User = get_user_model()
            default_user = User.objects.get(email="<EMAIL>")

            # Get branches without a created_by user
            branches_to_update = Branch.objects.filter(created_by__isnull=True) | Branch.objects.filter(created_by="")

            if not branches_to_update.exists():
                self.stdout.write(self.style.SUCCESS("No branches need updating."))
                return

            # Update branches
            updated_count = branches_to_update.update(created_by=default_user.id)
            self.stdout.write(self.style.SUCCESS(f"Successfully updated {updated_count} branches."))

        except User.DoesNotExist:
            self.stderr.write(self.style.ERROR(f"User with UUID {default_user_uuid} does not exist."))
