from datetime import datetime
from typing import Optional

from django.conf import settings
from django.db.models import F, Max, Sum
import pytz

from requisition.models import Company
from stock_inventory import models


User = settings.AUTH_USER_MODEL


# Create your utility function(s) here.
def branch_opening_stock(branch):
    """
    Get the opening stock for a specific branch based on the current date.
    Args:
        branch (Branch): The branch for which to retrieve the opening stock.
    Returns:
        int: The opening stock quantity for the given branch on the current date.
        additional info: includes date and creation time.
    """
    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    branch_inventory_status = models.InventoryStatus.objects.filter(
        branch=branch,
        created_at__date=TODAY.date(),
    ).aggregate(date_time=Max("created_at"), quantity=Sum("opening_quantity"))
    if branch_inventory_status.get("date_time") is not None:
        data = {
            "total_stocK": branch_inventory_status.get("quantity") or 0,
            "date": branch_inventory_status.get("date_time").date(),
            "time": branch_inventory_status.get("date_time").time(),
        }
    else:
        data = {
            "total_stock": branch_inventory_status.get("quantity") or 0,
            "date": TODAY.date(),
            "time": TODAY.time(),
        }
    return data


def branch_closing_stock(branch):
    """
    Get the closing stock for a specific branch based on the current inventory status.
    Args:
        branch (Branch): The branch for which to retrieve the closing stock.
    Returns:
        int: the closing stock quantity for the given branch on the given date.
        additional info: includes date and creation time.
    """
    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    branch_inventory_status = models.StockDetail.objects.filter(branch=branch)
    total_quantity = (
        branch_inventory_status.aggregate(closing_quantity=Sum("quantity"))[
            "closing_quantity"
        ]
        or 0
    )
    data = {
        "date": TODAY.date(),
        "time": TODAY.time(),
        "total_stocks": total_quantity,
    }
    return data


def deplete_branch_quantity_available(
    company,
    branch,
    category,
    item,
    quantity,
    sales_transaction=None,
    stock_out=None,
    stock_request=None,
    stock_transfer=None,
    on_hold: Optional[bool] = True,
    check: Optional[bool] = False,
):
    """
    Deplete the available stock quantity of a product for a specific branch.
    NOTE:
    - if hold, deplete the available stock and put on hold, else just deplete it.
    - if check, return True if goods are available in stock.
    """

    branch_stock = models.StockDetail.objects.filter(
        company=company,
        branch=branch,
        category=category,
        item=item,
    ).first()
    if branch_stock is None:
        return {
            "status": False,
            "deplete_quantity": quantity,
            "quantity_before": 0,
            "quantity_after": 0,
            "message": f"NO INVENTORY RECORD FOR {company.company_name} => {branch.name} => {category.name} => {item.name}.",
        }
    if check:
        if branch_stock.quantity >= quantity:
            return True
    quantity_before = branch_stock.quantity
    branch_stock.quantity = F("quantity") - quantity
    if on_hold:
        branch_stock.stock_on_hold = F("stock_on_hold") + quantity
        models.StockOnHoldHistory.objects.create(
            company=company,
            branch=branch,
            category=category,
            item=item,
            quantity=quantity,
            sales_transaction=sales_transaction,
            stock_out=stock_out,
            stock_request=stock_request,
            stock_transfer=stock_transfer,
        )
    branch_stock.save()
    branch_stock.refresh_from_db()
    quantity_after = branch_stock.quantity
    return {
        "status": True,
        "deplete_quantity": quantity,
        "quantity_before": quantity_before,
        "quantity_after": quantity_after,
        "message": f"SUCCESSFULLY DEPLETED INVENTORY RECORD FOR {company.company_name} => {branch.name} => {category.name} => {item.name}.",
    }
    # return {
    #     "status": False,
    #     "deplete_quantity": quantity,
    #     "quantity_before": branch_stock.quantity,
    #     "quantity_after": branch_stock.quantity,
    #     "message": f"INSUFFICIENT INVENTORY RECORD FOR {company.company_name} => {branch.name} => {category.name} => {item.name}.",
    # }


def deplete_branch_quantity_on_hold(
    company,
    branch,
    category,
    item,
    quantity,
):
    """
    Deplete the quantity on hold for a product in a specific branch.
    """
    branch_stock = models.StockDetail.objects.filter(
        company=company,
        branch=branch,
        category=category,
        item=item,
    ).first()
    if branch_stock is None:
        return None
    branch_stock.stock_on_hold = F("stock_on_hold") - quantity
    branch_stock.save()
    return True


def register_stock_history(
    company,
    branch,
    category,
    item,
    price,
    quantity_before,
    quantity,
    quantity_after,
    transaction_type,
    status,
    supplier=None,
    created_by=None,
    comment=None,
):
    """
    This method creates a new stock history entry with the provided information.
    """
    history = models.StockHistory.objects.create(
        company=company,
        branch=branch,
        category=category,
        item=item,
        price=price,
        quantity_before=quantity_before,
        quantity=quantity,
        quantity_after=quantity_after,
        transaction_type=transaction_type,
        status=status,
        supplier=supplier,
        created_by=created_by,
        # comment=f"{comment}-{datetime.now(tz=pytz.timezone(settings.TIME_ZONE))}",
        comment=comment,
        stock_value=(quantity * price),
    )
    return history


def increase_branch_quantity_available(
    company,
    branch,
    category,
    item,
    quantity,
    user,
):
    """
    Increase the available stock quantity of a product for a specific branch.
    """
    branch_stock = models.StockDetail.objects.filter(
        company=company,
        branch=branch,
        category=category,
        item=item,
    ).first()
    if branch_stock is None:
        branch_stock = models.StockDetail.objects.create(
            company=company,
            branch=branch,
            category=category,
            item=item,
            uploaded_by=user,
            quantity=quantity,
        )
        quantity_before = 0
        quantity_after = quantity
    else:
        quantity_before = branch_stock.quantity
        branch_stock.quantity += quantity
        branch_stock.save()
        quantity_after = branch_stock.quantity
    return quantity_before, quantity_after


def transfer_unique_items(
    user,
    company,
    from_branch,
    to_branch,
    unique_ids,
    stock_item=None,
    stock_variant=None,
):
    """
    Transfer unique items from one branch to another.
    Args:
        user (User): The user initiating the transfer.
        company (Company): The company associated with the transfer.
        from_branch (Branch): The branch from which the items are being transferred.
        to_branch (Branch): The branch to which the items are being transferred.
        unique_ids (list): List of unique IDs to be transferred.
        stock_item (Optional[StockDetail]): Optional parameter representing the stock item associated with the transfer.
        stock_variant (Optional[StockVariant]): Optional parameter representing the stock variant associated with the transfer.
    Returns:
        bool: True if the transfer is successful.
    NOTE:
        - If `stock_item` is provided, unique IDs will be transferred for that specific stock item.
        - If `stock_variant` is provided, unique IDs will be transferred for that specific stock variant.
        - Existing unique IDs in the 'from_branch' will be soft-deleted after successful transfer to 'to_branch'.
    """
    if stock_item is not None:
        new_stock_ids = [
            models.StockUniqueId(
                stock_item=stock_item,
                unique_id=unique_id,
                branch=to_branch,
                company=company,
                uploaded_by=user,
            )
            for unique_id in unique_ids
        ]
        models.StockUniqueId.objects.bulk_create(new_stock_ids)
        old_stock_ids = models.StockUniqueId.objects.filter(
            branch=from_branch,
            stock_item=stock_item,
            unique_id__in=unique_ids,
        )
        if old_stock_ids.exists():
            [old_stock_id.soft_delete() for old_stock_id in old_stock_ids]

    if stock_variant is not None:
        new_variant_ids = [
            models.StockUniqueId(
                stock_variant=stock_variant,
                unique_id=unique_id,
                branch=to_branch,
                company=company,
                uploaded=user,
            )
            for unique_id in unique_ids
        ]
        models.StockUniqueId.objects.bulk_create(new_variant_ids)
        old_variant_ids = models.StockUniqueId.objects.filter(
            branch=from_branch,
            stock_variant=stock_variant,
            unique_id__in=unique_ids,
        )
        if old_variant_ids.exists():
            [old_variant_id.soft_delete() for old_variant_id in old_variant_ids]
    return True


def destroy_company_product(company, item):
    """
    Deleting of a product or item will only be possible if the said product or item
    is yet to have a stock record or details.
    NOTE [LOGIC]:
    - get the specified product;
    - check for its stock record or details;
    - if no record matches allow the user to delete else do not permit action.
    """
    item_stock_detail = models.StockDetail.objects.filter(company=company, item=item)
    if item_stock_detail.exists():
        return {
            "status": False,
            "message": "cannot delete a product with available stock.",
        }
    else:
        item.delete()
        return {
            "status": True,
            "message": "successfully deleted the product.",
        }


def destroy_company_category(company, category):
    """
    Deleting of a category will only be possible if the said category is owned by the company
    and is yet to have a stock record or details.
    NOTE [LOGIC]:
    - get the specified category;
    - check for its stock record or details;
    - if no record matches allow the user to delete else do not permit action.
    """
    if category.company != company:
        return {
            "status": False,
            "message": "cannot delete a category not owned.",
        }
    category_stock_detail = models.StockDetail.objects.filter(
        company=company,
        category=category,
    )
    if category_stock_detail.exists():
        return {
            "status": False,
            "message": "cannot delete a category with available stocks.",
        }
    else:
        category.delete()
        return {
            "status": True,
            "message": "successfully deleted the category.",
        }


def destroy_company_subcategory(company, subcategory):
    """
    Deleting of a category will only be possible if the said category is owned by the company
    and is yet to have a stock record or details.
    NOTE [LOGIC]:
    - get the specified category;
    - check for its stock record or details;
    - if no record matches allow the user to delete else do not permit action.
    """
    if subcategory.company != company:
        return {
            "status": False,
            "message": "cannot delete a category not owned.",
        }
    category_stock_detail = models.StockDetail.objects.filter(
        company=company,
        category=subcategory,
    )
    if category_stock_detail.exists():
        return {
            "status": False,
            "message": "cannot delete a category with available stocks.",
        }
    else:
        subcategory.delete()
        return {
            "status": True,
            "message": "successfully deleted the category.",
        }


def get_completion(prompt, client_instance, system_msg=None, model="gpt-3.5-turbo"):
    messages = [{"role": "user", "content": prompt}]
    if system_msg:
        messages.append({"role": "system", "content": system_msg})

    response = client_instance.chat.completions.create(
        model=model,
        messages=messages,
        temperature=0.0,
    )
    return response


def send_prompt_to_chatgpt(
    prompt, system_msg=None, model="gpt-3.5-turbo", module=None, user=None
):
    from django.core.cache import cache
    from django.template.loader import render_to_string
    from django.utils import timezone
    from openai import OpenAI
    import random
    from django.conf import settings

    api_keys = str(settings.OPENAI_API_KEY).split(",")

    # Shuffle the API keys randomly
    random.shuffle(api_keys)

    # Try each API key until a successful request is made or all keys are exhausted
    for api_key in api_keys:
        client = OpenAI(api_key=api_key)

        try:
            response = get_completion(prompt, client, system_msg, model)

            # Extract relevant information from the response object
            response_data = response.choices[0].message.content
            usage = response.usage

            return response_data
        except Exception as e:
            print(f"An error occurred with API key {api_key}: {e}")
            continue  # Try the next API key if the current one fails

    raise Exception("All API keys failed to get a successful response from OpenAI.")
