# Subscription and Invoicing App Documentation

## Overview

The Subscription and Invoicing app manages subscription plans, billing cycles, and recurring payments for businesses on the Paybox360 platform. It provides tools for creating subscription products, managing customer subscriptions, and processing recurring invoices.

## Core Features

### 1. Subscription Plan Management

- Plan creation and configuration
- Pricing tier management
- Feature inclusion/exclusion
- Trial period settings

### 2. Subscription Lifecycle

- Subscription activation
- Renewal processing
- Cancellation handling
- Upgrade/downgrade management

### 3. Recurring Billing

- Automated invoice generation
- Payment processing
- Failed payment handling
- Proration calculations

### 4. Customer Subscription Management

- Subscription status tracking
- Payment method management
- Subscription history
- Self-service subscription portal

### 5. Reporting and Analytics

- Subscription metrics
- Revenue forecasting
- Churn analysis
- Subscription performance tracking

## How It Works

1. **Plan Setup**: Businesses create subscription plans with pricing and features
2. **Customer Subscription**: Customers subscribe to plans
3. **Billing Cycle**: System generates invoices based on billing frequency
4. **Payment Processing**: Recurring payments are processed automatically
5. **Renewal/Cancellation**: Subscriptions are renewed or cancelled based on settings
6. **Reporting**: Subscription data is analyzed for business insights

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `SubscriptionPlan` | Plan configuration | `name`, `price`, `billing_frequency` | Company |
| `PlanFeature` | Plan inclusions | `name`, `description`, `is_included` | SubscriptionPlan |
| `Subscription` | Customer subscriptions | `start_date`, `end_date`, `status` | Customer, SubscriptionPlan |
| `SubscriptionInvoice` | Billing records | `due_date`, `amount`, `status` | Subscription |
| `PaymentMethod` | Payment information | `method_type`, `details`, `is_default` | Customer |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `create_subscription` | Initiates subscriptions | `services.py` | Called during subscription sign-up |
| `generate_recurring_invoice` | Creates billing records | `services.py` | Called by billing cycle task |
| `process_subscription_payment` | Handles payments | `services.py` | Called during payment processing |
| `calculate_proration` | Computes partial billing | `utils.py` | Used during plan changes |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/subscription/plans/` | Manage plans | POST/GET | `SubscriptionPlanViewSet` |
| `/subscription/subscriptions/` | Manage subscriptions | POST/GET | `SubscriptionViewSet` |
| `/subscription/invoices/` | View invoices | GET | `SubscriptionInvoiceViewSet` |
| `/subscription/payment-methods/` | Manage payment methods | POST/GET | `PaymentMethodViewSet` |
| `/subscription/analytics/` | View subscription metrics | GET | `SubscriptionAnalyticsView` |

### Integration Points

- **Invoicing App**: For invoice generation
- **Account App**: For payment processing
- **Customer Management**: For customer information
- **Notification System**: For subscription alerts

## Key Considerations

### 1. Billing Cycle Management

- **Responsible App**: Subscription and Invoicing app
- **Key Functions**:
  - Billing date calculation
  - Frequency handling (monthly, annual, etc.)
  - Timezone considerations for billing

### 2. Payment Failure Handling

- **Responsible App**: Subscription and Invoicing app
- **Key Functions**:
  - Retry logic for failed payments
  - Dunning management
  - Grace period configuration

### 3. Plan Change Management

- **Responsible App**: Subscription and Invoicing app
- **Key Functions**:
  - Proration calculations in `calculate_proration`
  - Upgrade/downgrade processing
  - Feature access adjustments

### 4. Subscription Metrics

- **Responsible App**: Subscription and Invoicing app
- **Key Functions**:
  - MRR/ARR calculations
  - Churn rate analysis
  - Customer lifetime value computation

### 5. Trial Management

- **Responsible App**: Subscription and Invoicing app
- **Key Functions**:
  - Trial period configuration
  - Trial-to-paid conversion
  - Trial expiration handling