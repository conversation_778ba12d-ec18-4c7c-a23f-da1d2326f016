from datetime import <PERSON><PERSON><PERSON>
from celery import shared_task
from django.utils import timezone
from django.core.exceptions import PermissionDenied
from django.db import transaction
from django.conf import settings
from subscription_and_invoicing.models import AccessPath, CompanySubscription, Module, ModuleSubscription, \
    SubscriptionAudit, Invoice, SubscriptionPlan
from django.utils import timezone
from django.core.exceptions import PermissionDenied


from core.models import PaystackPayment
from subscription_and_invoicing.models import SubscriptionPlan, CompanySubscription, ModuleSubscription, \
    AccessPath
from django.core.exceptions import PermissionDenied
from decimal import Decimal
import json

class SubscriptionManager:
    def __init__(self, user):
        """
        Initialize the subscription manager for a specific user
        
        Args:
            user (User): The user creating/managing subscriptions
        """
        self.user = user
        self.company = user.default_company if user else None

    @transaction.atomic
    def create_subscription(self, modules=None, access_type=None, plan=None):
        """
        Create a new subscription based on access type and selected modules
        
        Args:
            modules (list, optional): List of modules to subscribe to
            access_type (str, optional): Type of access (trial, paid, bypass)
            plan (SubscriptionPlan, optional): Subscription plan for paid subscriptions
        
        Returns:
            CompanySubscription: The created subscription
        
        Raises:
            PermissionDenied: If no user or company is associated
            ValueError: If modules are not specified for paid subscriptions
        """
        if not self.user:
            raise PermissionDenied("No user provided")
        if not self.company:
            raise PermissionDenied("No company associated with user")

        now = timezone.now()
        
        # Handle different access types
        if access_type == AccessPath.BYPASS:
            end_date = None
            status = 'active'
            modules = Module.objects.all()
            
        elif access_type == AccessPath.TRIAL:
            end_date = now + timedelta(days=14)
            status = 'trial'
            modules = Module.objects.all()
            
        else:  # Paid subscription
            if not modules:
                raise ValueError("Modules must be specified for paid subscriptions")
            
            # Use the provided plan or default to the first module's plan
            if not plan:
                plan = SubscriptionPlan.objects.filter(duration_months=12).first()
            
            # Calculate end date based on the selected plan
            end_date = now + timedelta(days=plan.duration_months * 30)
            status = 'active'

        # Create the company subscription
        subscription = CompanySubscription.objects.create(
            company=self.company,
            access_type=access_type,
            status=status,
            created_by=self.user
        )

        # Create module subscriptions
        if modules:
            module_subs = []
            for module in modules:
                module_subs.append(ModuleSubscription(
                    company_subscription=subscription,
                    module=module,
                    plan=plan,  
                    start_date=now,
                    end_date=end_date,
                    is_active=True
                ))
            ModuleSubscription.objects.bulk_create(module_subs)

        # Create subscription audit log
        SubscriptionAudit.objects.create(
            company=self.company,
            subscription=subscription,
            action='create_subscription',
            action_by=self.user,
            details={
                'access_type': access_type,
                'modules': [m.id for m in modules] if modules else 'all',
                'end_date': end_date.isoformat() if end_date else None,
                'plan': plan.id if plan else None
            }
        )

        return subscription

    def renew_subscription(self, company_subscription, modules=None, plan=None):
        """
        Renew an existing subscription
        
        Args:
            company_subscription (CompanySubscription): Subscription to renew
            modules (list, optional): Modules to renew (defaults to existing modules)
            plan (SubscriptionPlan, optional): Plan to use for renewal
        
        Returns:
            CompanySubscription: The renewed subscription
        """
        now = timezone.now()
        
        # Use existing modules if not specified
        if not modules:
            modules = company_subscription.modules.all()
        
        # Use existing plan or default to 12-month plan
        if not plan:
            plan = SubscriptionPlan.objects.filter(duration_months=12).first()
        
        # Calculate new end date
        end_date = now + timedelta(days=plan.duration_months * 30)
        
        # Update existing module subscriptions or create new ones
        existing_module_subs = ModuleSubscription.objects.filter(
            company_subscription=company_subscription
        )
        
        # Mark existing subscriptions as inactive
        existing_module_subs.update(is_active=False)
        
        # Create new module subscriptions
        new_module_subs = []
        for module in modules:
            new_module_subs.append(ModuleSubscription(
                company_subscription=company_subscription,
                module=module,
                plan=plan,
                start_date=now,
                end_date=end_date,
                is_active=True
            ))
        ModuleSubscription.objects.bulk_create(new_module_subs)
        
        # Update company subscription status
        company_subscription.status = 'active'
        company_subscription.save()
        
        # Create renewal audit log
        SubscriptionAudit.objects.create(
            company=company_subscription.company,
            subscription=company_subscription,
            action='renew_subscription',
            action_by=self.user,
            details={
                'modules': [m.id for m in modules],
                'end_date': end_date.isoformat(),
                'plan': plan.id
            }
        )
        
        return company_subscription

    @staticmethod
    def check_module_access(user, module_code):
        """
        Check if a user has access to a specific module
        
        Args:
            user (User): User to check access for
            module_code (str): Module code to check
        
        Returns:
            dict or False: Module details if access is granted, False otherwise
        """
        if not user or not user.default_company:
            return False
        
        try:
            module = Module.objects.get(code=module_code)
            
            # Check company subscription
            company_subscription = CompanySubscription.objects.filter(
                company=user.default_company,
                status__in=['active', 'trial']
            ).first()
            
            if not company_subscription:
                return False
            
            # Check module subscription
            module_subscription = ModuleSubscription.objects.filter(
                company_subscription=company_subscription,
                module=module,
                is_active=True,
                end_date__gt=timezone.now()
            ).exists()
            
            if module_subscription:
                return {
                    'module_code': module.code,
                    'module_name': module.name,
                    'description': module.description
                }
            
            return False

        except Module.DoesNotExist:
            return False

    def process_subscription_expiration(self):
        """
        Process and update expired subscriptions
        """
        now = timezone.now()
        
        # Process expired trial subscriptions
        expired_trials = CompanySubscription.objects.filter(
            access_type=AccessPath.TRIAL,
            status='trial',
            created_at__lte=now - timedelta(days=14)
        )
        
        for subscription in expired_trials:
            subscription.status = 'expired'
            subscription.save()
            
            # Create audit log for expired trials
            SubscriptionAudit.objects.create(
                company=subscription.company,
                subscription=subscription,
                action='trial_expired',
                details={'expired_at': now.isoformat()}
            )
        
        # Process module subscriptions nearing or past expiration
        expired_module_subs = ModuleSubscription.objects.filter(
            is_active=True,
            end_date__lte=now
        )
        
        for module_sub in expired_module_subs:
            module_sub.is_active = False
            module_sub.save()
            
            # Check if all module subscriptions for the company are expired
            active_modules = ModuleSubscription.objects.filter(
                company_subscription=module_sub.company_subscription,
                is_active=True
            )
            
            if not active_modules.exists():
                module_sub.company_subscription.status = 'expired'
                module_sub.company_subscription.save()

# Decorator for view access control
def subscription_required(module_code):
    """
    Decorator to check module access based on subscription type
    """
    def decorator(view_func):
        def wrapped_func(request, *args, **kwargs):
            if not request.user.is_authenticated:
                raise PermissionDenied("Authentication required")
                
            try:
                subscription = CompanySubscription.objects.filter(
                    company=request.user.default_company,
                    status__in=['active', 'trial']
                ).latest('created_at')
                
                # Check module access based on subscription type
                if not subscription.has_module_access(module_code):
                    raise PermissionDenied("No access to this module")
                    
                return view_func(request, *args, **kwargs)
                
            except CompanySubscription.DoesNotExist:
                raise PermissionDenied("Valid subscription required")
                
        return wrapped_func
    return decorator








