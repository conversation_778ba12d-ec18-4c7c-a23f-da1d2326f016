import json
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.enums import TransactionType
from account.helpers.vfd import VfdBank
from account.models import (
    AccountSystem,
    Beneficiary,
    BulkTransfer,
    CoreBankingCallback,
    DebitCreditRecordOnAccount,
    Transaction,
    Wallet,
)
from account.serializers import (
    AccountSystemDetailsSerializer,
    BeneficiarySerializer,
    BulkTransferSerializer,
    CreateBeneficiarySerializer,
    ScheduledBulkTransferItemsSerializer,
    SendMoneySerializer,
    VfdRequestSerializer,
    WalletHistorySerializer,
    WalletTransactionHistorySerializer,
)
from core.auth.custom_auth import CustomUserAuthentication, IsStaff
from core.pagenator import CustomPagination
from core.permissions import IpWhiteListPermission
from helpers.custom_permissions import CanInitiateTransfer
from helpers.disbursment_helper import LibertyPay


class SendMoney(APIView):
    permission_classes = [IsAuthenticated, CanInitiateTransfer]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = SendMoneySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        # acct = AccountSystem.objects.get(id="1c2036fc-51ce-4dc6-8a1a-61ee9375f52f")
        # print(acct, "\n\n")
        # send_money = Transaction.vfd_funds_transfer(
        #     bank_code="999999",
        #     bank_name="VFD",
        #     account_name="Roland",
        #     account_number="**********",
        #     narration="Test transfer funds",
        #     amount=100,
        #     account=acct,
        # )
        # print(send_money_result)
        return Response({}, status=status.HTTP_200_OK)


class LibertyCrediFundWalletApiView(APIView):
    permission_classes = [IpWhiteListPermission]

    def post(self, request):
        data = request.data
        Wallet.fund_wallet_vfd_bank_call_back(response_result=data)

        return Response({"message": "Payment received"}, status=status.HTTP_200_OK)


class CheckTransactionStatusVfd(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = VfdRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        transaction_id = data.get("request_data")

        vfd_obj = VfdBank()
        transaction_status = vfd_obj.vfd_transaction_verification_handler(
            reference=transaction_id
        )

        return Response(
            {"message": "Success", "response_data": transaction_status},
            status=status.HTTP_200_OK,
        )


class CheckAccountDetailsVfd(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = VfdRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        acct_number = data.get("request_data")

        vfd_obj = VfdBank()
        account_query = vfd_obj.vfd_account_enquiry(account_number=acct_number)

        return Response(
            {"message": "Success", "response_data": account_query},
            status=status.HTTP_200_OK,
        )


class AdminUpdateAcct(APIView):
    permission_classes = [IsAuthenticated, IsStaff]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        nuban = request.query_params.get("nuban")
        nin = request.query_params.get("nin")
        dob = request.query_params.get("dob")
        bvn = None
        VFDBank = VfdBank()
        get_user_qs = AccountSystem.objects.filter(account_number=nuban)
        if get_user_qs.count() > 1:
            response = {
                "status": False,
                "message": "Found more than 1 account",
            }
        elif not get_user_qs.exists():
            response = {
                "status": False,
                "message": "No Account Found",
            }
        else:
            get_user = get_user_qs.first()
            user = get_user.user
            bvn = user.bvn_number
            if nin:
                response = VFDBank.update_bvn_nin(
                    account_number=nuban, bvn=bvn, nin=nin, dob=dob
                )
            elif bvn:
                response = VFDBank.update_bvn_nin(
                    account_number=nuban, bvn=bvn, dob=dob
                )
            else:
                response = {
                    "status": False,
                    "message": "No BVN or NIN Found on account",
                }
        response["bvn"] = bvn
        return Response(response, status=status.HTTP_200_OK)


class CoreBankingCallBacks(APIView):
    permission_classes = [IpWhiteListPermission]

    def post(self, request, *args, **kwargs):
        inflow = CoreBankingCallback.register_wema_inflow(request.data)
        return Response(data=inflow, status=status.HTTP_200_OK)


class AccountSystemDetails(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = AccountSystemDetailsSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "company",
        "branch",
        "account_type",
    )
    # search_fields = ("",)

    def get_queryset(self):
        request_user = self.request.user
        wallet_queryset = AccountSystem.objects.filter(user=request_user).order_by(
            "-created_at"
        )
        return wallet_queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        libertypay_account_balance_enquiry = LibertyPay().get_agent_balance(
            user_ids=[request.user.liberty_pay_id]
        )

        libertypay_account_balance = 0.0
        for item in libertypay_account_balance_enquiry:
            if isinstance(item, dict) and len(item) > 0:
                wallets = item.get("wallets", [])
                if len(wallets) > 0:
                    for wallet in wallets:
                        if wallet["wallet_type"] == "COLLECTION":
                            libertypay_account_balance = wallet.get(
                                "available_balance", 0.0
                            )
                else:
                    break

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "libertypay_account_balance": libertypay_account_balance,
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class WalletHistory(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = WalletHistorySerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "wallet",
        "requisition_type",
        "entry",
    )
    # search_fields = ("",)

    def get_queryset(self):
        request_user = self.request.user
        wallet_queryset = DebitCreditRecordOnAccount.objects.filter(
            user=request_user
        ).order_by("-date_created")
        return wallet_queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class WalletTransactionHistory(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = WalletTransactionHistorySerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    search_fields = (
        "transaction_ref",
        "beneficiary_account_number",
        "beneficiary_account_name",
    )

    def get_queryset(self):
        request = self.request
        acct_type = request.GET.get("account_type")
        company_id = request.GET.get("company")
        trans_status = request.GET.get("status")
        trans_type = request.GET.get("transaction_type")

        query = Q(user=request.user)
        if acct_type:
            query &= Q(payout_type=acct_type)
        if company_id:
            query &= Q(company_id=company_id)
        if trans_status:
            query &= Q(status=trans_status)
        if trans_type:
            query &= Q(transaction_type=trans_type)

        wallet_queryset = (
            Transaction.objects.filter(query)
            .exclude(
                Q(transaction_type=TransactionType.COMMISSION)
                | Q(transaction_type=TransactionType.DUPLICATE_FUND_WALLET)
                | Q(transaction_type=TransactionType.RETRIEVAL)
                | Q(transaction_type=TransactionType.SEND_MONEY_TO_FLOAT)
                | Q(transaction_type=TransactionType.STAMP_DUTY)
                | Q(transaction_type=TransactionType.FLOAT_RECONCILIATION)
            )
            .order_by("-date_created")
        )
        return wallet_queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class BeneficiaryListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = BeneficiarySerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    search_fields = (
        "beneficiary_name",
        "account_number",
        "bank_code",
    )

    def get_queryset(self):
        request = self.request

        return Beneficiary.objects.filter(
            user=request.user,
            is_removed=False,
            is_active=True,
            saved_as_beneficiary=True,
        ).values()

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        paginator.page_size = 100  # Set page size directly in the view
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class BulkCreateBeneficiaryAPIView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    serializer_class = CreateBeneficiarySerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except ValueError as err:
            data = {
                "status": False,
                "message": str(err),
                "result": None,
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data

        result = Beneficiary.get_create_beneficiaries(
            user=request.user, beneficiaries=validated_data["beneficiaries"]
        )

        data = {
            "status": True,
            "message": "success",
            "result": result,
        }
        return Response(data=data, status=status.HTTP_201_CREATED)


class BulkTransferAPIView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    serializer_class = BulkTransferSerializer

    def get(self, request):

        request_body = json.loads(request.body)

        beneficiaries = request_body.get("beneficiaries", [])

        beneficiary_ids = [b.get("id") for b in beneficiaries if "id" in b]

        if not beneficiary_ids:
            data = {
                "status": False,
                "message": "No beneficiaries provided",
                "result": None,
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # Fetch existing beneficiaries in a single query
        existing_beneficiaries = {
            str(b.id): b for b in Beneficiary.objects.filter(id__in=beneficiary_ids)
        }

        # Validate all beneficiaries exist & amounts > 100
        for b in beneficiaries:
            beneficiary_id = str(b.get("id"))
            amount = b.get("amount")

            if beneficiary_id not in existing_beneficiaries:
                data = {
                    "status": False,
                    "message": f"Beneficiary with ID {beneficiary_id} does not exist",
                    "result": None,
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if amount is None or not isinstance(amount, (int, float)) or amount <= 100:

                data = {
                    "status": False,
                    "message": f"Invalid amount for beneficiary {beneficiary_id}. Amount must be greater than 100",
                    "result": None,
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        data = {
            "status": True,
            "message": "success",
            "company_id": request_body.get("company_id"),
            **Beneficiary.get_total_deductable_and_actual_transfer_for_multiple_beneficiary(
                beneficiaries=beneficiaries
            ),
            # "total_deductable": total_deductable,
            # "actual_transfer": actual_transfer,
            # "result": summary,
        }
        return Response(data=data, status=status.HTTP_200_OK)

    def post(self, request):

        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except ValueError as err:
            data = {
                "status": False,
                "message": str(err),
                "result": None,
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data

        blk_tf = BulkTransfer.create_bulk_transfer_record(
            user=request.user, validated_data=validated_data
        )
        validated_data.pop("transaction_pin")
        validated_data.pop("beneficiaries")

        validated_data["beneficiaries"] = validated_data[
            "updated_beneficiaries_with_ids"
        ]
        validated_data.pop("updated_beneficiaries_with_ids")
        BulkTransfer.transfer_funds(transfer_id=blk_tf)
        data = {
            "status": True,
            "message": "success",
            "result": validated_data,
        }
        return Response(data=data, status=status.HTTP_200_OK)


class GetScheduledTransferItemsAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ScheduledBulkTransferItemsSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    # search_fields = (

    # )

    def get_queryset(self):
        request = self.request

        return BulkTransfer.objects.filter(user=request.user).order_by("-id")

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        paginator.page_size = 100  # Set page size directly in the view
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
