import calendar
from datetime import (
    date,
    datetime,
    timedelta,
    time,
)
from typing import Optional

from django.conf import settings
import pytz

from helpers.enums import FilterTypes
from helpers.reusable_functions import format_string_to_date_object


# Create your custom filter(s) here.
def get_previous_week_range(current_date: date, start_day: Optional[int] = 0) -> tuple:
    calendar.setfirstweekday(start_day)
    current_weekday = current_date.weekday()
    days_past = (current_weekday - start_day + 7) % 7
    start_of_previous_week = current_date - timedelta(days=days_past + 7)
    end_of_previous_week = start_of_previous_week + timedelta(days=6)
    return start_of_previous_week, end_of_previous_week


def get_current_week_range(current_date: date, start_day: Optional[int] = 0) -> tuple:
    calendar.setfirstweekday(start_day)
    current_weekday = current_date.weekday()
    days_past = (current_weekday - start_day + 7) % 7
    start_of_current_week = current_date - timedelta(days=days_past)
    end_of_current_week = start_of_current_week + timedelta(days=7)
    return start_of_current_week, end_of_current_week


def get_previous_month_range(current_date: date) -> tuple:
    first_day_of_current_month = current_date.replace(day=1)
    last_day_of_previous_month = first_day_of_current_month - timedelta(days=1)
    first_day_of_previous_month = last_day_of_previous_month.replace(day=1)
    return first_day_of_previous_month, last_day_of_previous_month


def get_current_month_range(current_date: date) -> tuple:
    start_date = current_date.replace(day=1)
    next_month = start_date + timedelta(
        days=calendar
        .monthrange(current_date.year, current_date.month)[1]
    )
    end_date = next_month - timedelta(days=1)
    return start_date, end_date


def get_last_quarter_range(current_date: date) -> tuple:
    current_quarter_start = (current_date.month - 1) // 3 * 3 + 1
    first_day_of_current_quarter = current_date.replace(
        month=current_quarter_start, day=1
    )
    last_day_of_last_quarter = first_day_of_current_quarter - timedelta(days=1)
    first_day_of_last_quarter = last_day_of_last_quarter.replace(
        month=(last_day_of_last_quarter - timedelta(days=90)).month, day=1
    )
    return first_day_of_last_quarter, last_day_of_last_quarter


def get_current_quarter_range(current_date: date) -> tuple:
    current_quarter_start = (current_date.month - 1) // 3 * 3 + 1
    first_day_of_current_quarter = current_date.replace(
        month=current_quarter_start, day=1
    )
    last_day_of_current_quarter = first_day_of_current_quarter + timedelta(days=89)
    return first_day_of_current_quarter, last_day_of_current_quarter


def get_last_year_range(current_date: date) -> tuple:
    start_of_current_year = current_date.replace(month=1, day=1)
    end_of_last_year = start_of_current_year - timedelta(days=1)
    start_of_last_year = end_of_last_year.replace(month=1, day=1)
    return start_of_last_year, end_of_last_year


def get_current_year_range(current_date: date) -> tuple:
    start_of_current_year = current_date.replace(month=1, day=1)
    end_of_the_current_year = current_date.replace(month=12, day=31)
    return start_of_current_year, end_of_the_current_year


class QuerysetCustomFilter:
    """
    Fetch queryset by date range.
    """

    @classmethod
    def get_date_range(
        cls,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        AVAILABLE_RESULT_TYPES = [
            FilterTypes.CUSTOM,
            FilterTypes.TODAY,
            FilterTypes.YESTERDAY,
            FilterTypes.LAST_WEEK,
            FilterTypes.CURRENT_WEEK,
            FilterTypes.LAST_MONTH,
            FilterTypes.THIS_MONTH,
            FilterTypes.LAST_QUARTER,
            FilterTypes.THIS_QUARTER,
            FilterTypes.LAST_YEAR,
            FilterTypes.THIS_YEAR,
        ]
        result_type = result_type.upper()
        if result_type.upper() not in AVAILABLE_RESULT_TYPES:
            return {"status": False, "details": "invalid result type."}
        if result_type == FilterTypes.CUSTOM:
            if start_date is None or end_date is None:
                return {
                    "status": False,
                    "details": "provide a valid start date and end date.",
                }
            else:
                start_date = format_string_to_date_object(start_date)
                end_date = format_string_to_date_object(end_date)
                if (start_date is None or end_date is None) or start_date > end_date:
                    return {
                        "status": False,
                        "details": "provide a valid start date and end date.",
                    }
        TODAY = date.today()
        if result_type == FilterTypes.TODAY:
            start_date = TODAY
            end_date = TODAY
        if result_type == FilterTypes.YESTERDAY:
            start_date = TODAY - timedelta(days=1)
            end_date = TODAY - timedelta(days=1)
        if result_type == FilterTypes.LAST_WEEK:
            start_date, end_date = get_previous_week_range(TODAY)
        if result_type == FilterTypes.CURRENT_WEEK:
            start_date, end_date = get_current_week_range(TODAY)
        if result_type == FilterTypes.LAST_MONTH:
            start_date, end_date = get_previous_month_range(TODAY)
        if result_type == FilterTypes.THIS_MONTH:
            start_date, end_date = get_current_month_range(TODAY)
        if result_type == FilterTypes.LAST_QUARTER:
            start_date, end_date = get_last_quarter_range(TODAY)
        if result_type == FilterTypes.THIS_QUARTER:
            start_date, end_date = get_current_quarter_range(TODAY)
        if result_type == FilterTypes.LAST_YEAR:
            start_date, end_date = get_last_year_range(TODAY)
        if result_type == FilterTypes.THIS_YEAR:
            start_date, end_date = get_current_year_range(TODAY)
        time_zone = pytz.timezone(settings.TIME_ZONE)
        start_object = time_zone.localize(datetime.combine(start_date, time.min))
        end_object = (
            time_zone.localize(datetime.combine(end_date, time.min)) + timedelta(days=1)
        )
        return start_object, end_object

    @classmethod
    def date_range_filter(
        cls,
        queryset,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        date_range = cls.get_date_range(
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(date_range, dict):
            return date_range
        start, end = date_range
        queryset_result = queryset.filter(
            created_at__range=[start, end]
        )
        return queryset_result
