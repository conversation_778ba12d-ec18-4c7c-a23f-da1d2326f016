import json

from django.contrib import admin
from django.db.models.query import QuerySet
from import_export.admin import ImportExportModelAdmin

from sales_app import models, resources
from sales_app.helper.enums import TransactionStatusChoices


# Register your model(s) here.
class ConstantVariableResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ConstantVariableResource
    search_fields = []
    list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CustomerResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = [
        "company",
        "branch",
    ]
    resource_class = resources.CustomerResource
    search_fields = [
        "company__company_name",
        "branch__name",
        "name",
        "email",
        "phone",
        "unique_identifier",
    ]
    list_filter = [
        "status",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CustomerLocationResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["customer"]
    resource_class = resources.CustomerLocationResource
    search_fields = [
        "customer__company__company_name",
        "customer__branch__name",
        "customer__name",
        "customer__email",
        "customer__phone",
        "customer__unique_identifier",
    ]
    list_filter = [
        "default",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CashBookResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = [
        "company",
        "branch",
        "sales_transaction",
    ]
    resource_class = resources.CashBookResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "branch__name",
        "sales_transaction__batch_id",
        "reference",
    ]
    list_filter = [
        "status",
        "transaction_type",
        "payment_channel",
        "cash_book_type",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesTransactionResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = [
        "company",
        "branch",
        "invoice",
    ]
    resource_class = resources.SalesTransactionResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "branch__name",
        "customer__email",
        "customer__phone",
        "batch_id",
        "offline_identifier",
        "payment_reference",
    ]
    list_filter = [
        "device",
        "means_of_payment",
        "status",
        "created_at",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesTransactionItemResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = [
        "company",
        "sales_transaction",
    ]
    resource_class = resources.SalesTransactionItemResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "category__name",
        "item__name",
        "sales_transaction__batch_id",
    ]
    list_filter = [
        "swapped",
        "refunded",
        "created_at",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ReturnRefundResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ReturnRefundResource
    search_fields = [
        "customer__email",
        "customer__phone",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ReturnRefundItemResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ReturnRefundItemResource
    search_fields = []
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class POSDeviceResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.POSDeviceResource
    autocomplete_fields = [
        "user",
        "company",
    ]

    search_fields = [
        "serial",
        "terminal_id",
    ]

    list_filter = [
        "is_active",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CardTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.POSDeviceResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "rrn",
    ]
    list_filter = [
        "created_at",
        "status",
        "is_settled",
    ]
    date_hierarchy = "created_at"

    @admin.action(description="LibertyPay Card Transaction: fund user wallet")
    def fund_sales_wallet(
        self,
        request,
        queryset: QuerySet[models.CardTransaction],
    ):
        from account.enums import (
            AccountType,
            TransactionType,
        )
        from account.models import (
            AccountSystem,
            Transaction,
            TransactionMetaData,
            Wallet,
        )
        for obj in queryset.reverse():
            sales_transaction = models.SalesTransaction.objects.filter(card_rrn=obj.rrn).last()
            amount_payable = float(obj.amount) - float(sales_transaction.applicable_charges)
            # Check for duplicate fund transaction.
            fund_transaction = Transaction.objects.filter(
                bank_deposit_ref=obj.rrn
            ).last()
            if fund_transaction is None:
                account_details = AccountSystem.objects.filter(
                    company=sales_transaction.company,
                    branch=sales_transaction.branch,
                    account_type=AccountType.SALES,
                ).last()
                if account_details is not None:
                    meta_data_instance = TransactionMetaData.objects.create(
                        user=sales_transaction.company.user,
                        fund_wallet_payload=json.dumps(obj.payload),
                    )
                    transaction_instance = Transaction.objects.create(
                        user=sales_transaction.company.user,
                        company_name=sales_transaction.company.company_name,
                        beneficiary_account_number=account_details.account_number,
                        beneficiary_account_name=account_details.account_name,
                        bank_code=obj.rrn,
                        source_account_name="LibertyPay",
                        source_account_number="**********",
                        user_full_name=sales_transaction.company.user.full_name,
                        user_email=sales_transaction.company.user.email,
                        transaction_type=TransactionType.CARD,
                        amount=amount_payable,
                        total_amount_received=obj.amount,
                        bank_deposit_ref=obj.rrn,
                        status=TransactionStatusChoices.PENDING,
                        narration="LibertyPay card settlement",
                        payout_type=account_details.account_type,
                        company_id=account_details.company.id,
                        date_credited=obj.created_at,
                    )
                    meta_data_instance.transaction = transaction_instance
                    meta_data_instance.save()
                    # Actual wallet funding
                    Wallet.fund_wallet(
                        account_id=account_details.id,
                        transaction_id=transaction_instance.id,
                    )
                    Transaction.objects.filter(bank_deposit_ref=obj.rrn).update(
                        date_created=obj.updated_at,
                        last_updated=obj.updated_at,
                    )
            else:
                obj.is_settled = True
                obj.save()

    actions = [
        fund_sales_wallet,
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class APILogResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.APILogResource

    search_fields = [
        "user__email",
        "sales_batch_id",
    ]
    list_filter = [
        "method",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesTransactionBackorderResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = [
        "company",
        "sales_transaction",
    ]
    resource_class = resources.SalesTransactionBackorderResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "category__name",
        "item__name",
        "sales_transaction__batch_id",
    ]
    list_filter = [
        "created_at",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OfflineVirtualAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.OfflineVirtualAccountResource
    search_fields = [
        "unique_code"
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WalletWithdrawalResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.WalletWithdrawalResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "account_number",
    ]
    list_filter = [
        "status",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.ConstantVariable, ConstantVariableResourceAdmin)
admin.site.register(models.Customer, CustomerResourceAdmin)
admin.site.register(models.CustomerLocation, CustomerLocationResourceAdmin)
admin.site.register(models.CashBook, CashBookResourceAdmin)
admin.site.register(models.SalesTransaction, SalesTransactionResourceAdmin)
admin.site.register(models.SalesTransactionItem, SalesTransactionItemResourceAdmin)
admin.site.register(models.ReturnRefund, ReturnRefundResourceAdmin)
admin.site.register(models.ReturnRefundItem, ReturnRefundItemResourceAdmin)
admin.site.register(models.POSDevice, POSDeviceResourceAdmin)
admin.site.register(models.CardTransaction, CardTransactionResourceAdmin)
admin.site.register(models.APILog, APILogResourceAdmin)
admin.site.register(models.SalesTransactionBackorder, SalesTransactionBackorderResourceAdmin)
admin.site.register(models.OfflineVirtualAccount, OfflineVirtualAccountResourceAdmin)
admin.site.register(models.WalletWithdrawal, WalletWithdrawalResourceAdmin)
