from django.urls import path
from performance_sales_metrics_dashboard.performance.views import SalesOfficerMetricsAPIView, SalesLeadMetricsAPIView, \
    MerchantMetricsAPIView, SystemUsageMetricsView
from performance_sales_metrics_dashboard.views import ActivityAPIView, AuthCodeView, CategoryAPIView, \
    CheckFreeBusyStatusView, CheckSalesOfficerAvailabilityView, \
    CreateCalendarView, CreateLeadOnlineView, DeleteEmailsView, DraftEmailsView, EmailAPIView, EventsView, \
    GetAllSalesOfficersView, GetAuthUrlView, \
    GetCalendarView, GetScheduledEmailsView, GetSentEmailsView, FetchTokenDataView, GetValidTokenView, GmailSetupView, \
    LeadAPIView, LeadDemoDetailsView, LeadStatusAPIView, Oauth2StringView, \
    PipelineAPIView, ReadEmailsView, \
    RefreshEmailView, RefreshTokenView, RetrieveEmailView, \
    SalesLeadOnboardingAPIView, SalesOfficerOnboardingAPIView, SalesOfficerOnboardingCompanyListCreateAPIView, \
    ScheduleBulkEmailView, ScheduleZoomMeetingToGoogleView, \
    SendEmailView, \
    StagesAPIView, TaskAPIView, QualifyLeadAPIView, MoveLeadToPipelineAPIView, MoveLeadToNextStageInPipelineAPIView, \
    LeadProgression, RecentAndUpcomingTasksView, GetStagesByPipeline, GetCategoryAPIView, GetLeadsNotInAPipeline, \
    SalesOfficerUploadLeadsViaFileAPIView, SalesOfficerUploadLeadsViaFileAPIView, LeadStatusAPIView, \
    GetLeadsByStage, GetLeadsInStagesByPipeline, \
    DeleteSalesOfficerAPIView, ProductVerticalsAPIView, \
    GetProductVerticalsAPIView, \
    AdminPipelineOverviewAPIView, GetMultipleLeadAPIView, \
    GetMultipleStagesAPIView, GetMultipleActivityAPIView, \
    GetMultipleTaskAPIView, GetMultiplePipelineAPIView, UserStatusConfirmationAPIView, WorkEmailSetupView, \
    ZoomBearerTokenView, \
    ZoomMeetView, ZoomAccessTokenView, \
    InboxView, GetLeadsInAPipeline, \
    GetAllLeadsInACategory, AdminStatus, GetLeadsInATeam, \
    GetExpiredLeads, NewsLetterSubscriptionAPIView  # ProspectAPIView, GetPipelineBySalesOfficer, SumLeadsInStage
    # ProspectAPIView, GetPipelineBySalesOfficer, SumLeadsInStage

urlpatterns = [
    # METRICS URLs
    path('user_status/', UserStatusConfirmationAPIView.as_view(), name='user_details'),
    path('admin_status/', AdminStatus.as_view(), name='determine_if_admin'),
    path('admin_dash-board/', AdminPipelineOverviewAPIView.as_view(), name='admin_dash_board'),

    path('calculate_sales_officer_metrics/', SalesOfficerMetricsAPIView.as_view(), name='sales_officer_metrics'),
    path('calculate_sales_lead_metrics/', SalesLeadMetricsAPIView.as_view(), name='sales_lead-metrics'),
    path('calculate_merchant_metrics/', MerchantMetricsAPIView.as_view(), name='merchant_metrics'),
    path('calculate_system_usage_metrics/', SystemUsageMetricsView.as_view(), name='system_usage_metrics'),

    path('sales_lead_onboard/', SalesLeadOnboardingAPIView.as_view(), name='sales_lead_onboard'),
    path('edit_sales_lead/<int:pk>/', SalesLeadOnboardingAPIView.as_view(), name='edit_sales_lead'),
    path('get_sales_lead/', SalesLeadOnboardingAPIView.as_view(), name='get_sales_lead'),
    path('get_multiple-sales_leads/', SalesLeadOnboardingAPIView.as_view(), name='get_multiple_sales_leads'),
    path('delete_sales_lead/', SalesLeadOnboardingAPIView.as_view(), name='delete_sales_lead'),

    path('sales_officer_onboard/', SalesOfficerOnboardingAPIView.as_view(), name='sales_officer_onboard'),
    path('edit_sales_officer/<int:pk>/', SalesOfficerOnboardingAPIView.as_view(), name='edit_sales_officer'),
    path('get_sales_officer/', SalesOfficerOnboardingAPIView.as_view(), name='get_sales_officer'),
    path('get_multiple-sales_officers/', SalesOfficerOnboardingAPIView.as_view(), name='get_multiple_sales_officers'),
    path('delete_sales_officer/', DeleteSalesOfficerAPIView.as_view(), name='delete_sales_officer'),

    path('sales_officer_onboard_company/', SalesOfficerOnboardingCompanyListCreateAPIView.as_view(),
         name='sales_officer_onboard_company'),

    path('create_lead/', LeadAPIView.as_view(), name='create_lead'),
    path('edit_lead/<int:pk>/', LeadAPIView.as_view(), name='edit_lead'),
    path('get_lead/', LeadAPIView.as_view(), name='get_a_lead'),
    path('get_leads_in_pipeline/', GetLeadsInAPipeline.as_view(), name='get_leads_in_a_pipeline'),
    path('get_leads_in_team/', GetLeadsInATeam.as_view(), name='get_leads_in_a_team'),
    path('get_leads_in_category/', GetAllLeadsInACategory.as_view(), name='get_leads_in_a_category'),
    path('get_leads_null_pipeline/', GetLeadsNotInAPipeline.as_view(), name='get_leads_not_in_a_pipeline'),
    path('get_expired_leads/', GetExpiredLeads.as_view(), name='get_expired_leads'),
    path('get_stage-leads/', GetLeadsByStage.as_view(), name='get_stage_leads'),
    path('get_leads_in_all_stages/', GetLeadsInStagesByPipeline.as_view(), name='get_leads_in_all_stages'),
    path('get_multi-leads/', GetMultipleLeadAPIView.as_view(), name='get_multiple_leads'),
    path('delete_lead/', LeadAPIView.as_view(), name='delete_lead'),
    path('lead_status_stats/', LeadStatusAPIView.as_view(), name='lead_status_stats'),
    path('lead_upload_so/', SalesOfficerUploadLeadsViaFileAPIView.as_view(), name='lead_upload_by_so'),
    path('lead_upload_sl/', SalesOfficerUploadLeadsViaFileAPIView.as_view(), name='lead_upload_by_sl'),

    path('create_pipeline/', PipelineAPIView.as_view(), name='create_pipeline'),
    path('edit_pipeline/<int:pk>/', PipelineAPIView.as_view(), name='edit_pipeline'),
    path('get_pipeline/', PipelineAPIView.as_view(), name='get_a_pipeline'),
    path('get_multi-pipelines/', GetMultiplePipelineAPIView.as_view(), name='get_multiple_pipelines'),
    path('delete_pipeline/', PipelineAPIView.as_view(), name='delete_pipeline'),

    path('create_stage/', StagesAPIView.as_view(), name='create_stage'),
    path('edit_stage/<int:pk>/', StagesAPIView.as_view(), name='edit_stage'),
    path('get_stage/', StagesAPIView.as_view(), name='get_a_stage'),
    path('pipeline_stages/', GetStagesByPipeline.as_view(), name='pipeline_stages'),
    path('get_multi-stages/', GetMultipleStagesAPIView.as_view(), name='get_multiple_stages'),
    path('delete_stage/', StagesAPIView.as_view(), name='delete_stage'),

    path('create_activity/', ActivityAPIView.as_view(), name='create_activity'),
    path('edit_activity/<int:pk>/', ActivityAPIView.as_view(), name='edit_activity'),
    path('get_activity/', ActivityAPIView.as_view(), name='get_activity'),
    path('get_multiple-activities/', GetMultipleActivityAPIView.as_view(), name='get_multiple_activity'),
    path('delete_activity/', ActivityAPIView.as_view(), name='delete_activity'),

    path('create_task/', TaskAPIView.as_view(), name='create_task'),
    path('edit_task/<int:pk>/', TaskAPIView.as_view(), name='edit_task'),
    path('get_task/', TaskAPIView.as_view(), name='get_a_task'),
    path('recent_activity/', RecentAndUpcomingTasksView.as_view(), name='activity_for_last_seven_days'),
    path('get_multi-tasks/', GetMultipleTaskAPIView.as_view(), name='get_multiple_tasks'),
    path('delete_task/', TaskAPIView.as_view(), name='delete_task'),

    path('qualify_lead/', QualifyLeadAPIView.as_view(), name='qualify_lead'),
    path('move_lead_to_pipeline/', MoveLeadToPipelineAPIView.as_view(), name='move_lead_to_pipeline'),
    path('move_lead_to_next_stage_in_pipeline/', MoveLeadToNextStageInPipelineAPIView.as_view(),
         name='move_lead_to_next_stage_in_pipeline'),
    path("track_lead_progress/", LeadProgression.as_view(), name="move_deal"),

    path('create_category/', CategoryAPIView.as_view(), name='create_category'),
    path('edit_category/<int:pk>/', CategoryAPIView.as_view(), name='edit_category'),
    path('get_category/', GetCategoryAPIView.as_view(), name='get_category'),
    path('get_multiple-categories/', GetCategoryAPIView.as_view(), name='get_multiple_categories'),
    path('delete_category/', CategoryAPIView.as_view(), name='delete_category'),

    # Email URLS
    path('mailgun_send_email/', EmailAPIView.as_view(), name='send_email_with_mailgun'),
    path('send_email/', SendEmailView.as_view(), name='send_email'),
    path('get_emails/', RetrieveEmailView.as_view(), name='get_emails'),
    path('get_inbox/', InboxView.as_view(), name='get_inbox'),
    path('refresh_emails/', RefreshEmailView.as_view(), name='refresh_emails'),
    path('send_bulk_email/', ScheduleBulkEmailView.as_view(), name='send_bulk_emails'),
    path('read_email/', ReadEmailsView.as_view(), name='read_email'),
    path('sent_emails/', GetSentEmailsView.as_view(), name='sent_email'),
    path('get_draft_emails/', DraftEmailsView.as_view(), name='get_draft_emails'),
    path('save_as_draft/', DraftEmailsView.as_view(), name='save_email_as_draft'),
    path('delete_email/', DeleteEmailsView.as_view(), name='sent_email'),
    path('get_deleted_emails/', DeleteEmailsView.as_view(), name='get_deleted_emails'),
    path('get_scheduled_emails/', GetScheduledEmailsView.as_view(), name='get_scheduled_emails'),
    path('work_email_details/', WorkEmailSetupView.as_view(), name='work_email_details'),
    path('gmail_details/', GmailSetupView.as_view(), name='gmail_details'),
    path('token_data/', FetchTokenDataView.as_view(), name='token_data'),
    path('valid_token/', GetValidTokenView.as_view(), name='valid_token'),
    path('get_auth_url/', GetAuthUrlView.as_view(), name='get_auth_url'),
    path('refresh_token/', RefreshTokenView.as_view(), name='refresh_token'),
    path('get_oauth2_string/', Oauth2StringView.as_view(), name='get_oauth2_string'),
    path('oauth_code/', AuthCodeView.as_view(), name='oauth_code'),

    # Zoom API URLs
    path('generate_bearer_token/', ZoomBearerTokenView.as_view(), name='generate_bearer_token'),
    path("zoom_availability/", CheckSalesOfficerAvailabilityView.as_view(), name="check_availability"),
    path('access_token/', ZoomAccessTokenView.as_view(), name='generate_zoom_access_token'),
    path('meetings/', ZoomMeetView.as_view(), name='retrieve_zoom_meeting'),
    path('meetings/create/', ZoomMeetView.as_view(), name='create_zoom_meeting'),
    path('book_demo/', CreateLeadOnlineView.as_view(), name='book_demo'),
    path('save_lead_demo_details/', LeadDemoDetailsView.as_view(), name='save_lead_demo_details'),
    path("get_all_sales_officers/", GetAllSalesOfficersView.as_view(), name="get_all_sales_officers"),
    # path('meeting/<int:meetingId>', ZoomMeetingView.as_view(), name='retrieve_meeting'),
    # path('meeting/create', ZoomMeetingView.as_view(), name='create_meeting'),

    path('create_product_vertical/', ProductVerticalsAPIView.as_view(), name='create_product_vertical'),
    path('edit_product_vertical/<int:pk>/', ProductVerticalsAPIView.as_view(), name='edit_product_vertical'),
    path('get_product_vertical/', GetProductVerticalsAPIView.as_view(), name='get_product_vertical'),
    path('get_multiple-product_verticals/', GetProductVerticalsAPIView.as_view(), name='get_multiple_product_verticals'),
    path('delete_product_vertical/', ProductVerticalsAPIView.as_view(), name='delete_product_vertical'),

    # Google Calendar URLs
    path('create_event/', EventsView.as_view(), name='create_event'),
    path('create_calendar/', CreateCalendarView.as_view(), name='create_calendar'),
    path('get_calendar/', GetCalendarView.as_view(), name='retrieve_calendar'),
    path('check_availability/', CheckFreeBusyStatusView.as_view(), name='check_availability_status'),
    path('save_zoom_meeting_to_google/', ScheduleZoomMeetingToGoogleView.as_view(), name='save_zoom_meeting_to_google'),


    path('create_subscriber/', NewsLetterSubscriptionAPIView.as_view(), name='create_subscriber'),
    path('get_subscriber/', NewsLetterSubscriptionAPIView.as_view(), name='get_subscriber')
    # path('delete_calendar/', CalendarView.as_view(), name='delete_calendar'),

    # Google Meet URLs
    # path('create_meeting_link/', GoogleMeetView.as_view(), name='create_google_meeting_link'),

    # path('testapi/', TestAPIView.as_view()),
]
