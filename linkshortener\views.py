from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.http import HttpResponseRedirect

from linkshortener.models import (
    UrlData,
)

class BaseView(APIView):

    def get(self, request):

        return Response({"message": "Not FOund"}, status=status.HTTP_200_OK)

class UrlRedirect(APIView):

    def get(self, request, slug):

        try:
            url = UrlData.objects.get(slug=slug)
            return HttpResponseRedirect(url.url)

        except UrlData.DoesNotExist:
            return Response({"message": "No such link"}, status=status.HTTP_404_NOT_FOUND)


def clean_flags(flags):

    from datetime import datetime

    def to_datetime(dt_str):

        # The date string
        date_string = '16-12-2023 07:56:16'

        # Converting to datetime object
        date_object = datetime.strptime(dt_str, '%d-%m-%Y %H:%M:%S')

        # Printing the datetime object
        print(date_object)
        date_string = date_object.strftime('%d-%m-%Y %H:%M')

        return date_string

    cleaned_flags = dict()

    for flag in flags:

        # print(to_datetime(flag))
        new_date = to_datetime(flag["time"])
        print(new_date)

        cleaned_flags[f'{new_date}'] = flag["flag"]


    new_flags = [{value:key} for key, value in cleaned_flags.items()]

    return new_flags