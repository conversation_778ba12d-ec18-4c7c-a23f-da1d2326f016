from typing import Optional, Dict
import logging
import requests
import json

logger = logging.getLogger(__name__)


class LibertyCrediMgr:
    """
    A class to handle Liberty Credit notifications for wallet charges and float account deductions
    """

    base_url = "https://libertycredi.com"
    # base_url = "https://dragon-humble-blatantly.ngrok-free.app"

    @classmethod
    def format_api_response(cls, payload, response, url):
        """
        Handles the response from API requests, returning a structured result.

        Args:
            payload (dict): The data sent with the request.
            response (requests.Response): The response object returned by the API.
            url (str): The URL that was called.

        Returns:
            dict: A dictionary containing the request result, including status, response,
                  and other related information.
        """
        try:
            response = {
                "url": url,
                "status_code": response.status_code,
                "method": response.request.method,
                "status": "success",
                "response": response.json(),
                "payload": payload,
            }
        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as e:
            response = {
                "url": url,
                "error": str(e),
                "method": response.request.method,
                "status_code": response.status_code,
                "status": "failed",
                "response": response.text,
                "payload": payload,
            }
        return response

    @classmethod
    def notify_float_deduction(
        cls,
        amount: float,
        account_id: Optional[str] = None,
        transaction_ref: Optional[str] = None,
        reason: Optional[str] = None,
    ) -> dict:
        """
        Notify Liberty Credit when a float account deduction occurs

        Args:
            account_id: The float account identifier
            amount: The amount deducted
            transaction_ref: Unique transaction reference
            reason: Reason for the deduction
            metadata: Additional transaction information

        Returns:
            bool: True if notification was successful, False otherwise
        """

        absolute_url = f"{cls.base_url}/acct/api/v1/float/deduction_notification/"
        # print(absolute_url)
        payload = {
            "account_id": account_id,
            "amount": amount,
            "transaction_ref": transaction_ref,
            "reason": reason,
        }

        headers = {"Content-Type": "application/json"}
        response = requests.request(
            "POST", 
            url=absolute_url, 
            headers=headers,
            json=payload 
        )

        request_result = cls.format_api_response(
            url=absolute_url, payload=payload, response=response
        )

        logger.info(f"{request_result}")
        return request_result
