# Requisition App Documentation

## Overview

The Requisition app is a spend management system that allows companies to manage, approve, and disburse funds for various business expenses. It provides a structured workflow for requesting funds, approving requests, and tracking expenses across teams within an organization.

## Core Features

### 1. Company Management

- Create and manage company profiles
- Verify company information
- Set up company wallets for fund disbursement
- Manage industry classifications

### 2. Team Management

- Create teams within a company
- Assign members to teams with specific roles (<PERSON><PERSON>, Disburser, Reviewer, etc.)
- Invite new team members via email

### 3. Requisition Workflow

- Create fund requests with detailed information
- Specify request categories, amounts, and reasons
- Upload supporting documents (invoices)
- Track request status (Pending, Approved, Declined)
- Receive notifications on request status changes

### 4. Disbursement

- Secure fund transfers to approved requisitions
- Transaction PIN protection for disbursements
- Bulk disbursement capabilities
- SMS notifications for disbursements

### 5. Budget Management

- Create and allocate budgets to teams
- Track budget utilization
- Set spending limits
- Monitor out-of-budget expenses

### 6. Expense Tracking

- Record and categorize expenses
- Upload and extract data from receipts
- Generate expense reports
- View expense dashboards

### 7. Procurement Management

- Create purchase indents
- Manage supplier relationships
- Track purchase orders
- Process invoices and returns

## How It Works

1. **Setup**: Company owner creates an account, sets up company profile, and creates teams
2. **Roles**: Team members are assigned roles that determine their permissions
3. **Funding**: Company wallet is funded for disbursements
4. **Budget Creation**: Company owner creates budgets with specific amounts and time periods
5. **Team Allocation**: Budgets are allocated to teams with spending limits and categories
6. **Requisition**: Team members create requisition requests for funds
7. **Approval**: Authorized members review and approve/decline requests
8. **Disbursement**: Approved requests receive funds via secure transfers
9. **Tracking**: All transactions are recorded for reporting and auditing

## How Procurement Works

1. **Purchase Indent**: Team members create purchase indents specifying products, quantities, and suppliers
2. **Approval Workflow**: Authorized members review and approve purchase indents based on budget availability
3. **Purchase Order**: Approved indents generate purchase orders sent to suppliers
4. **Invoice Processing**: Supplier invoices are uploaded, verified, and processed
5. **Payment**: Funds are disbursed to suppliers based on approved invoices
6. **Delivery Tracking**: Product deliveries are tracked and confirmed
7. **Returns Management**: Any product returns or issues are documented and processed
8. **Expense Recording**: All procurement expenses are recorded against team budgets

## User Roles

- **Company Owner**: Full access to all features
- **Admin**: Manage teams and approve requisitions
- **Disburser**: Approve and disburse funds
- **Reviewer**: Review and comment on requisitions
- **Regular Member**: Create requisitions and track their status

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `Company` | Stores company information | `company_name`, `is_verified` | User (owner), Teams |
| `Team` | Represents company departments | `team_name`, `is_active` | Company, TeamMembers |
| `TeamMember` | Links users to teams | `role`, `is_active` | Team, User |
| `Requisition` | Stores fund requests | `request_amount`, `status` | User, Company, Team |
| `Budget` | Defines spending limits | `budget_amount`, `start_date` | Company |
| `BudgetAllocation` | Links budgets to teams | `amount`, `is_active` | Budget, Team |
| `Expense` | Tracks spending | `expense_amount`, `category` | Budget, Requisition |
| `PurchaseIndent` | Procurement requests | `estimated_cost`, `status` | Team, Supplier |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `requisition_disbursement` | Processes fund transfers | `models.py` (Requisition) | Called in disbursement views |
| `percentage_spent` | Calculates budget usage | `models.py` (BudgetAllocation) | Used in budget reporting |
| `process_invoice_file_2` | Extracts data from invoices | `helpers/receipt_extract.py` | Called in receipt processing views |
| `purchase_ident_with_budget_check` | Validates against budget | `utils.py` | Used in procurement creation |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/requisition/create_requisition/` | Create fund request | POST | `CreateRequisition` |
| `/requisition/disbursement/` | Process disbursement | POST | `Disbursement` |
| `/requisition/expense_dashboard/` | View expense stats | GET | `ExpenseDashboard` |
| `/requisition/receipt_reader/` | Extract receipt data | POST | `ReadExpenseReceipt` |

### Integration Points

- **VFD Bank API**: Fund transfers to external accounts
- **WhisperSMS**: SMS notifications for approvals and disbursements
- **AWS S3**: Document storage for receipts and invoices
- **OpenAI/PaddleOCR**: Document data extraction

## Key Considerations

### 1. Ensure sufficient wallet balance before approving requisitions
- **Responsible App**: Account system and Requisition app
- **Key Functions**: 
  - Wallet balance checks are performed in disbursement operations
  - Found in `requisition/views.py` where `SmsDisbursement` class handles disbursements
  - The `DisbursementSerializer` validates sufficient funds before processing
  - Account balances are managed through the `AccountSystem` and `Wallet` models from `account.models`

### 2. Set appropriate spending limits for teams
- **Responsible App**: Requisition app
- **Key Functions**:
  - Budget allocation is handled by `BudgetAllocationSerializer` in `requisition/serializers.py`
  - The `TrackBudget` class in `requisition/views.py` monitors budget utilization
  - Budget creation and editing is managed through `BudgetSerializer` and `EditBudgetSerializer`
  - Team budget linking is handled by `LinkTeamToBudgetSerializer`

### 3. Transaction PIN security
- **Responsible App**: Core app and Requisition app
- **Key Functions**:
  - PIN verification occurs in multiple views like `SmsDisbursement` in `requisition/views.py`
  - The `User.check_sender_payroll_pin()` method validates PINs before critical operations
  - PIN creation is handled by `CreateRequisitionTransactionPinSerializer`
  - OTP verification for PIN reset uses the `VerifyOtpSerializer` from `core.serializers`

### 4. Procurement workflow management
- **Responsible App**: Requisition app
- **Key Functions**:
  - Purchase order management in `ProcurementPurchaseOrderSerializerOut` 
  - Supplier management through `AddSupplierSerializerIn` and related serializers
  - Invoice processing via `ProcurementPurchaseInvoiceSerializerIn`
  - Delivery confirmation handled by `ConfirmDeliverySerializerIn`

### 5. Document verification and processing
- **Responsible App**: Requisition app
- **Key Functions**:
  - Receipt data extraction in `ReadExpenseReceipt` view in `requisition/views.py`
  - The `process_invoice_file_2` function in `requisition/helpers/receipt_extract.py`
  - File uploads to AWS S3 via the `upload_file_aws_s3_bucket` task in `core.tasks`
  - Invoice image preview in `ProcurementPurchaseInoviceImagePreview` view

### 6. Notification system
- **Responsible App**: Core app
- **Key Functions**:
  - Email notifications through `send_email` task in `core.tasks`
  - SMS notifications for disbursements in `SmsDisbursement` view
  - Notification marking as read in `NotificationUpdateSerializer` from `core/views.py`

### 7. Role-based permissions
- **Responsible App**: Core app and Helpers
- **Key Functions**:
  - Custom permissions like `CanDisburse`, `CanEditBudget`, `CanEditTeam`, `CanInitiateTransfer` in `helpers/custom_permissions.py`
  - Authentication through `CustomUserAuthentication` in `core/auth/custom_auth.py`
  - User blacklisting via `IsUSerBlackListed` permission in `core/permissions.py`
