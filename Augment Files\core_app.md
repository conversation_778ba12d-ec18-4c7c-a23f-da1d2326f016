# Core App Documentation

## Overview

The Core app serves as the foundation of the Paybox360 platform, providing essential functionality used across all other apps. It handles user authentication, permissions, common utilities, and shared services that support the entire system.

## Core Features

### 1. User Management

- User registration and authentication
- Role-based access control
- User profile management
- Transaction PIN security

### 2. Notification System

- Email notifications via Mailgun
- SMS notifications via WhisperSMS
- In-app notifications

### 3. File Management

- File uploads to AWS S3
- Document processing and storage
- Image manipulation

### 4. Authentication & Security

- Custom authentication backends
- Permission management
- Token-based authentication
- User blacklisting

### 5. Background Tasks

- Celery task management
- Scheduled operations
- Asynchronous processing

### 6. Utility Functions

- Common helper functions
- Reusable code components
- Shared business logic

## How It Works

1. **User Registration**: Users register with email/phone and create accounts
2. **Authentication**: Custom authentication system validates credentials
3. **Permission Assignment**: Users receive role-based permissions
4. **Transaction Security**: PIN creation and verification for sensitive operations
5. **Service Integration**: Core services connect to third-party APIs
6. **Task Processing**: Background tasks handle asynchronous operations
7. **Notification Delivery**: System events trigger appropriate notifications

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `User` | Custom user model | `email`, `phone`, `is_active` | Profile, Companies |
| `Notification` | In-app notifications | `message`, `is_read`, `notification_type` | User |
| `BlacklistedToken` | Token security | `token`, `blacklisted_at` | User |
| `OTP` | One-time passwords | `code`, `expires_at`, `is_used` | User |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `send_email` | Sends email notifications | `tasks.py` | Used by all apps for notifications |
| `upload_file_aws_s3_bucket` | Uploads files to S3 | `tasks.py` | Used for document storage |
| `generate_otp` | Creates verification codes | `utils.py` | Used for security verification |
| `CustomUserAuthentication` | Handles authentication | `auth/custom_auth.py` | Used in API views |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/core/register/` | User registration | POST | `RegisterAPIView` |
| `/core/login/` | User authentication | POST | `LoginAPIView` |
| `/core/reset-password/` | Password reset | POST | `ResetPasswordAPIView` |
| `/core/notifications/` | Get notifications | GET | `NotificationAPIView` |

### Integration Points

- **Mailgun**: Email delivery service
- **WhisperSMS**: SMS notification service
- **AWS S3**: File storage service
- **Celery**: Background task processing

## Key Considerations

### 1. User Authentication Security

- **Key Functions**:
  - `CustomUserAuthentication` in `core/auth/custom_auth.py` handles token validation
  - `BlacklistedToken` model tracks invalidated tokens
  - `LoginSerializer` validates credentials and generates tokens

### 2. Transaction PIN Management

- **Key Functions**:
  - `create_transaction_pin` in User model creates secure PINs
  - `check_sender_payroll_pin` validates PINs for sensitive operations
  - PIN reset workflow uses OTP verification

### 3. File Upload Security

- **Key Functions**:
  - `upload_file_aws_s3_bucket` in `core/tasks.py` handles secure uploads
  - File validation occurs before processing
  - Temporary files are cleaned up after processing

### 4. Notification Delivery

- **Key Functions**:
  - `send_email` task handles email formatting and delivery
  - SMS notification helpers format and send text messages
  - In-app notification system tracks read/unread status

### 5. Permission Management

- **Key Functions**:
  - Custom permission classes in `core/permissions.py`
  - Role-based access control through user attributes
  - Permission checking middleware

### 6. Background Task Processing

- **Key Functions**:
  - Celery tasks defined in `core/tasks.py`
  - Task scheduling through Django Celery Beat
  - Task result tracking with Django Celery Results

### 7. Documentation Generation

- **Key Functions**:
  - `doc_generator.py` uses OpenAI to generate documentation
  - File filtering to exclude sensitive information
  - Chunking large files for processing
