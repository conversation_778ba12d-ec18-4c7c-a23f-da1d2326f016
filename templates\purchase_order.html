<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<html lang="en">

<head data-id="__react-email-head"></head>

<body data-id="__react-email-body" style="
      background-color: rgb(244, 244, 244);
      margin: auto;
      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
        Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif,
        Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
    ">
    <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellspacing="0"
        cellpadding="0" border="0" style="
        max-width: 37.5em;
        border-radius: 0.25rem;
        background-color: rgb(255, 255, 255);
        margin-left: auto;
        margin-right: auto;
        padding: 20px;
        width: 600px;
      ">
        <tbody>
            <tr style="width: 100%">
                <td>
                    <h1 data-id="react-email-heading" style="
                color: rgb(0, 0, 0);
                font-size: 24px;
                width: 100%;
                font-weight: 400;
                text-align: center;
                padding: 0px;
                margin-left: 0px;
                margin-right: 0px;
              ">
                        <img data-id="react-email-img" alt=""
                            src="https://res.cloudinary.com/eddiewurld/image/upload/v1731667190/Template_header_faxwty.png"
                            style="
                  display: block;
                  outline: none;
                  border: none;
                  text-decoration: none;
                " />
                    </h1>
                    <p data-id="react-email-text" style="
                font-size: 14px;
                line-height: 24px;
                margin: 16px 0;
                padding-top: 0.75rem;
                padding-bottom: 0.75rem;
                margin-top: auto;
                margin-bottom: auto;
                background-color: rgb(244, 244, 244);
              "></p>
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0"
                        cellspacing="0" role="presentation" style="
                margin-top: 40px;
                margin-left: 2.25rem;
                margin-right: 2.25rem;
              ">
                        <tbody>
                            <tr>
                                <td>
                                    <p data-id="react-email-text" style="
                        font-size: 14px;
                        line-height: 20.55px;
                        margin: 16px 0;
                        color: rgb(39, 39, 39);
                        width: 378px;
                      ">
                                        Hello $supplier_name,
                                    </p>

                                    <p data-id="react-email-text" style="
                        font-size: 14px;
                        line-height: 20.55px;
                        margin: 16px 0;
                        color: rgb(39, 39, 39);
                        width: 500px;
                      ">
                                        A purchase order was made for the following items as listed below.
                                        <br>
                                        You can contact $company_name, for further details about the request.
                                    </p>
                                    <p data-id="react-email-text" style="
                        font-size: 14px;
                        line-height: 20.55px;
                        margin: 16px 0;
                        color: rgb(39, 39, 39);
                        width: 378px;
                      ">
                                        Kindly go through it and send an Invoice to the company.
                                    </p>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!--INVOICE ITEMS TABLE  -->
                    <table align="center" width="100%" data-id="__react-email-container" role="presentation"
                        cellspacing="0" cellpadding="0" border="0" style="
                max-width: 37.5em;
                border-radius: 0.25rem;
                background-color: rgb(255, 255, 255);
                margin-left: auto;
                margin-right: auto;
                padding: 20px;
                padding-top: 0px;
                width: 600px;
              ">
                        <thead>
                            <tr>
                                <th style="
                      border-bottom: 0.1px solid #d3d3d3;
                      padding: 8px;
                      text-align: left;
                      font-size: 12px;
                      color: #242424;
                    ">
                                    Category
                                </th>
                                <th style="
                      border-bottom: 0.1px solid #d3d3d3;
                      padding: 8px;
                      text-align: left;
                      font-size: 12px;
                      color: #242424;
                    " ;>
                                    Product
                                </th>
                                <th style="
                      border-bottom: 0.1px solid #d3d3d3;
                      padding: 8px;
                      text-align: left;
                      font-size: 12px;
                      color: #242424;
                    " ;>
                                    Quantity
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            $items
                        </tbody>
                    </table>

                    <!--  -->
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0"
                        cellspacing="0" role="presentation" style="
                margin-top: 10px;
                margin-bottom: 10px;
                margin-left: 2.25rem;
                margin-right: 2.25rem;
              ">
                        <tbody>
                            <tr>
                                <td>
                                    <p data-id="react-email-text" style="
                        font-size: 14px;
                        line-height: 24px;
                        margin: 16px 0;
                        color: rgb(39, 39, 39);
                        margin-top: 32px;
                        margin-bottom: 32px;
                        width: 370px;
                      ">
                                        Have any question? Please reach out to our support team at
                                        <a href="#" style="
                          color: rgb(37, 99, 235);
                          text-decoration-line: none;
                        ">support@libertyPay</a>
                                    </p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
    <table align="center" width="100%" data-id="react-email-section" style="
        background-image: url(https://res.cloudinary.com/eddiewurld/image/upload/v1685529828/Frame_9_qpdmtu.png);
        background-size: cover;
        background-position: center;
        height: 200px;
        margin-bottom: 22px;
        width: 691px;
      " border="0" cellpadding="0" cellspacing="0" role="presentation">
        <tbody>
            <tr>
                <td>
                    <table align="center" width="100%" class="" data-id="react-email-row" role="presentation"
                        cellspacing="0" cellpadding="0" border="0">
                        <tbody style="width: 100%">
                            <tr style="width: 100%">
                                <td data-id="__react-email-column" style="padding-left: 3rem; width: 401px">
                                    <table align="center" width="100%" data-id="react-email-row" role="presentation"
                                        cellspacing="0" cellpadding="0" border="0">
                                        <tbody style="width: 100%">
                                            <tr style="width: 100%">
                                                <td data-id="__react-email-column">
                                                    <p data-id="react-email-text" style="
                                font-size: 12px;
                                line-height: 24px;
                                margin: 16px 0;
                                color: rgb(255, 255, 255);
                              ">
                                                        Follow us:
                                                    </p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <table align="center" width="100%" data-id="react-email-row" role="presentation"
                                        cellspacing="0" cellpadding="0" border="0">
                                        <tbody style="width: 100%">
                                            <tr style="width: 100%">
                                                <td data-id="__react-email-column" style="
                              display: flex;
                              flex-direction: row;
                              gap: 1.75rem;
                            ">
                                                    <a href="https://www.facebook.com/LibertyAssured/" style="
                                border-radius: 9999px;
                                border-width: 1px;
                                border-style: dashed;
                                border-color: rgb(255, 255, 255);
                              "><img data-id="react-email-img"
                                                            src="https://res.cloudinary.com/eddiewurld/image/upload/v1685529923/facebook_wjwihz.png"
                                                            width="22" height="22" style="
                                  display: block;
                                  outline: none;
                                  border: none;
                                  text-decoration: none;
                                  margin: 0.25rem;
                                " /></a><a href="https://www.instagram.com/libertyassured/?hl=en" style="
                                border-radius: 9999px;
                                border-width: 1px;
                                border-style: dashed;
                                border-color: rgb(255, 255, 255);
                              "><img data-id="react-email-img"
                                                            src="https://res.cloudinary.com/eddiewurld/image/upload/v1685023382/instagram2x_fcsxam.png"
                                                            width="22" height="22" style="
                                  display: block;
                                  outline: none;
                                  border: none;
                                  text-decoration: none;
                                  margin: 0.25rem;
                                " /></a><a href="https://www.linkedin.com/company/liberty-assured-limited/?originalSubdomain=ng"
                                                        style="
                                border-radius: 9999px;
                                border-width: 1px;
                                border-style: dashed;
                                border-color: rgb(255, 255, 255);
                              "><img data-id="react-email-img"
                                                            src="https://res.cloudinary.com/eddiewurld/image/upload/v1685023382/linkedin2x_lym8sl.png"
                                                            width="22" height="22" style="
                                  display: block;
                                  outline: none;
                                  border: none;
                                  text-decoration: none;
                                  margin: 0.25rem;
                                " /></a>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <table align="center" width="100%" data-id="react-email-row" role="presentation"
                                        cellspacing="0" cellpadding="0" border="0">
                                        <tbody style="width: 100%">
                                            <tr style="width: 100%">
                                                <td data-id="__react-email-column">
                                                    <p data-id="react-email-text" style="
                                font-size: 12px;
                                line-height: 24px;
                                margin: 16px 0;
                                color: rgb(255, 255, 255);
                              ">
                                                        © 2023, LibertyPay All rights reserved.
                                                    </p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                                <td data-id="__react-email-column">
                                    <table align="center" width="100%" data-id="react-email-row" role="presentation"
                                        cellspacing="0" cellpadding="0" border="0">
                                        <tbody style="width: 100%">
                                            <tr style="width: 100%">
                                                <td data-id="__react-email-column">
                                                    <a
                                                        href="https://play.google.com/store/apps/details?id=com.libertytechx.org.libertpaiy"><img
                                                            class="" data-id="react-email-img"
                                                            src="https://res.cloudinary.com/eddiewurld/image/upload/v1685529909/play-store_csxc6q.png"
                                                            width="124px" height="42px" style="
                                  display: block;
                                  outline: none;
                                  border: none;
                                  text-decoration: none;
                                " /></a>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                                <td data-id="__react-email-column">
                                    <table align="center" width="100%" data-id="react-email-row" role="presentation"
                                        cellspacing="0" cellpadding="0" border="0">
                                        <tbody style="width: 100%">
                                            <tr style="width: 100%">
                                                <td data-id="__react-email-column">
                                                    <a
                                                        href="https://apps.apple.com/ng/app/liberty-pay-agency/id1637666052"><img
                                                            class="" data-id="react-email-img"
                                                            src="https://res.cloudinary.com/eddiewurld/image/upload/v1685529909/app-store_xveogi.png"
                                                            width="124px" height="43px" style="
                                  display: block;
                                  outline: none;
                                  border: none;
                                  text-decoration: none;
                                " /></a>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</body>

</html>