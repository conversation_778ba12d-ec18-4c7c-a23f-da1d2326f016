from django.utils import timezone
from payroll_app.models import CompanyEmployeeList, CompanyPayGradeSettings
from rest_framework import serializers
from .models import LEAVE_FREQUENCY, LEAVE_PRIORITY, LeavePolicy, LeaveRecord, LeaveRequest, LeaveType, \
    GradeLevel, PayGroup, Company, Department, Employee

class LeaveTypeSerializer(serializers.ModelSerializer):
    def create(self, validated_data):
        request = self.context.get('request')
        
        company_id = request.query_params.get('company_id')
        company = Company.objects.filter(id=company_id).last()
        self.validated_data['company_id'] = company

        functional_group_id = request.query_params.get('functional_group_id')
        functional_group = PayGroup.objects.filter(id=functional_group_id).last()
        print(functional_group)
        self.validated_data['functional_group'] = functional_group

        grade_level_id = request.query_params.get('grade_level_id')
        grade_level = GradeLevel.objects.filter(id=grade_level_id).last()
        self.validated_data['grade_level'] = grade_level

        leave_type, created = LeaveType.objects.get_or_create(
            company = company,
            functional_group = functional_group,
            grade_level = grade_level,
            title = self.validated_data['title']
        )

        if not created:
            raise serializers.ValidationError(f'{leave_type.company}\'s {leave_type.grade_level}-{leave_type.functional_group}-{leave_type.title} already exists, Update Existing {leave_type.title}')
        
        leave_type.value = self.validated_data['value']
        leave_type.save()

        self.instance = leave_type
        return self.instance
    
    def update(self, instance, validated_data):
        return super().update(instance, validated_data)
    
    def save(self, **kwargs):
        return super().save(**kwargs)
    
    class Meta:
        model = LeaveType
        fields = ['id', 'company', 'functional_group', 'grade_level',
                   'title', 'value', 'date_created', 'date_updated']

class ListLeaveType(serializers.ModelSerializer):
    class Meta:
        model = LeaveType
        fields = ['id', 'company', 'title', 'created_by']

class UpdateLeaveTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeaveType
        fields = ['title',]

class CreateLeaveTypeSerializer(serializers.Serializer):
    title = serializers.CharField(required=True, allow_null=False)

class CreateLeavePolicySerializer(serializers.Serializer):
    leave_type_id = serializers.UUIDField(required=True)
    pay_grade_id = serializers.UUIDField(required=True)
    monthly_allocation = serializers.IntegerField(min_value=0)
    yearly_allocation = serializers.IntegerField(min_value=0)

    def validate(self, attrs):
        company_id = self.context.get("company_id")
        leave_type_id = attrs.get("leave_type_id")
        pay_grade_id = attrs.get("pay_grade_id")

        leave_type = LeaveType.fetch_leave_type(leave_type_id, company_id)
        if not leave_type:
            raise serializers.ValidationError({"message": "leave type not found"})
        
        pay_grade = CompanyPayGradeSettings.fetch_pay_grade(pay_grade_id, company_id)
        if not pay_grade:
            raise serializers.ValidationError({"message": "pay grade not found"})
        
        attrs["leave_type"] = leave_type
        attrs["grade_level"] = pay_grade
        return attrs

class ListLeavePolicy(serializers.ModelSerializer):
    class Meta:
        model = LeavePolicy
        fields = ['id', 'company', 'grade_level', 'leave_type', 'monthly_allocation', 'yearly_allocation', 'created_by']

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["grade_level_name"] = instance.grade_level.pay_grade_name if instance.grade_level else ""
        serialized_data["leave_type"] = instance.leave_type.title if instance.leave_type else ""
        
        return serialized_data
    
class UpdateLeavePolicySerializer(serializers.ModelSerializer):
    class Meta:
        model = LeavePolicy
        fields = ['grade_level', 'leave_type', 'monthly_allocation', 'yearly_allocation']
        
    def validate(self, attrs):
        company_id = self.context.get("company_id")
        leave_type_id = attrs.get("leave_type")
        pay_grade_id = attrs.get("grade_level")

        if leave_type_id:
            leave_type = LeaveType.fetch_leave_type(leave_type_id.id, company_id)
            if not leave_type:
                raise serializers.ValidationError({"message": "leave type not found"})
        if pay_grade_id:
            pay_grade = CompanyPayGradeSettings.fetch_pay_grade(pay_grade_id.id, company_id)
            if not pay_grade:
                raise serializers.ValidationError({"message": "pay grade not found"})
        return attrs
    
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["grade_level_name"] = instance.grade_level.pay_grade_name if instance.grade_level else ""
        serialized_data["leave_type_name"] = instance.leave_type.title if instance.leave_type else ""
        
        return serialized_data

class CreateLeaveRequestSerializer(serializers.Serializer):

    leave_type_id = serializers.UUIDField(required=True)
    reason = serializers.CharField(max_length=500, required=True)
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    leave_document = serializers.FileField(required=False, allow_null=False)
    priority = serializers.ChoiceField(required=True, choices=LEAVE_PRIORITY)

    def validate(self, attrs):
        company_id = self.context.get("company_id")
        user = self.context.get("user")
        leave_type_id = attrs.get("leave_type_id")
        start_date = attrs.get("start_date")   
        end_date = attrs.get("end_date")
        leave_type = LeaveType.fetch_leave_type(leave_type_id, company_id)
        if not leave_type:
            raise serializers.ValidationError({"message": "leave type not found"})
        
        leave_policy = LeavePolicy.objects.filter(leave_type=leave_type, is_deleted=False).first()
        if not leave_policy:
            raise serializers.ValidationError({"message": "leave policy not found"})
        
        get_employee = CompanyEmployeeList.objects.filter(employee=user, company__id=company_id).first()
        if leave_policy.grade_level:
            if get_employee.employee_pay_grade != leave_policy.grade_level:
                raise serializers.ValidationError({"message":"leave type does not apply to this employee"})


        if start_date < timezone.now().date():
            raise serializers.ValidationError({"message": "start_date must be greater or equal to today"})
        if end_date < start_date:
            raise serializers.ValidationError({"message": "end_date must be greater or equal to start date"})
        
        date_difference = end_date - start_date
        attrs["leave_days"] = date_difference.days
        attrs["leave_type"] = leave_type
        attrs["employee"] = get_employee
        return attrs
    
class ApproveLeaveRequestSerializer(serializers.Serializer):
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    other_information = serializers.CharField(max_length=500, required=True)

    def validate(self, attrs):
        start_date = attrs.get("start_date")   
        end_date = attrs.get("end_date")

        if start_date < timezone.now().date():
            raise serializers.ValidationError({"message": "start_date must be greater or equal to today"})
        if end_date < start_date:
            raise serializers.ValidationError({"message": "end_date must be greater or equal to start date"})

        date_difference = end_date - start_date
        attrs["approved_leave_days"] = date_difference.days

        return attrs
    
class RejectLeaveRequestSerializer(serializers.Serializer):
    reason = serializers.CharField(max_length=500, required=True)

class ListLeaveRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeaveRequest
        fields = [
            'id', 'employee', 'leave_type', 'start_date', 'end_date', 
            'leave_days', 'reason', 'status', 'leave_days',
        ]
    
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["employee"] = instance.employee.full_name if instance.employee else ""
        serialized_data["leave_type"] = instance.leave_type.title if instance.leave_type else ""
        
        return serialized_data
        
class LeaveRecordDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeaveRequest
        fields = ['id', 'employee', 'leave_type', 'start_date', 'end_date', 
                  'approved_start_date', 'approved_end_date', 'number_of_days',  'reason', 
                  'rejection_reason', 'other_information', 'status', 'leave_days', 'leave_priority', 
                  'leave_document', 'signed_by', 'rejected_by', 'is_taken',
                  'completed_date', 'leave_days_taken', 'approved_leave_days', 'leave_days_exceeded',
                  'leave_days_remaining', 'total_leave_days'
                  ]
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["employee"] = instance.employee.full_name if instance.employee else ""
        serialized_data["leave_type"] = instance.leave_type.title if instance.leave_type else ""
        serialized_data["signed_by"] = instance.signed_by.full_name if instance.signed_by.full_name else ""
        serialized_data["rejected_by"] = instance.rejected_by.full_name if instance.rejected_by.full_name else ""
        
        return serialized_data
        

class LeavePolicySerializer(serializers.ModelSerializer):
    def create(self, validated_data):
        request = self.context.get('request')
        company_id = request.query_params.get('company_id')
        company = Company.objects.filter(id=company_id).last()
        self.validated_data['company_id'] = company

        if self.validated_data['leave_types']:
            leave_types = self.validated_data.pop("leave_types")

        leave_policy, created = LeavePolicy.objects.get_or_create(
            company = company
        )        

        if not created:
            raise serializers.ValidationError(f'{company}\'s Leave Policy Exists, use update endpoint')

        for leave_type in leave_types:
            if leave_type.company != company:
                raise serializers.ValidationError(f'{leave_type} belongs to {leave_type.company} not {company}')


        leave_policy.title = self.validated_data['title']
        leave_policy.leave_types.set(leave_types)
        leave_policy.save()

        self.validated_data["leave_types"] = leave_types
        self.instance = leave_policy
        return self.instance
        return super().create(validated_data)
    
    def save(self, **kwargs):
        return super().save(**kwargs)
    
    class Meta:
        model = LeavePolicy
        fields = ['id', 'title', 'leave_types', 'date_created', 'date_updated']
        extra_kwargs = {'leave_types':{'required':False}}

class LeaveRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeaveRequest
        fields = ['id', 'employee', 'start_date', 'end_date', 'number_of_days', 
                  'reason', 'leave_type', 'status', 'minute', 'signed_by', 
                  'is_taken', 'date_created', 'date_updated']    
    
    def create(self, validated_data):
        request = self.context.get('request')

        company_id = request.query_params.get('company_id')
        company = Company.objects.filter(id=company_id).last()
        if not company:
            raise serializers.ValidationError(f"Invalid Company")
        self.validated_data['company'] = company

        department_id = request.query_params.get('department_id')
        department = Department.objects.filter(pk=department_id).last()
        if not department:
            raise serializers.ValidationError(f"Department doesn't belong to {company}")
        self.validated_data['department'] = department

        employee = Employee.objects.filter(employee=request.user, company=company).last()
        if not employee:
            raise serializers.ValidationError(f"Employee doesn't belong to {company}")
        self.validated_data['employee'] = employee

        leave_type_id = request.query_params.get('leave_type_id')
        leave_type = LeaveType.objects.filter(pk=leave_type_id, company=company).last()
        if not leave_type:
            raise serializers.ValidationError(f'Leave type selected doesn\'t belong to {company}')
        self.validated_data['leave_type'] = leave_type


        leave_request = LeaveRequest.objects.create(**validated_data)

        self.instance = leave_request
        return self.instance

    def update(self, instance, validated_data):

        request = self.context.get('request')
        company = instance.company

        department_id = request.query_params.get('department_id')
        if department_id:
            department = Department.objects.filter(pk=department_id, company=company).last()
            if not department:
                raise serializers.ValidationError(f"Department doesn't belong to {company}")
            self.validated_data['department'] = department
        else:
            pass
        
        employee = Employee.objects.filter(employee = self.context.get('employee').id, company=company).last
        if not employee:
            raise serializers.ValidationError(f"Employee doesn't belong to {company}")
        self.validated_data["employee"] = employee

        leave_type_id = request.query_params.get('leave_type_id')
        if leave_type_id:
            leave_type = LeaveType.objects.filter(pk=leave_type_id, company=company).last()
            if not leave_type:
                raise serializers.ValidationError(f'Leave type selected doesn\'t belong to {company}')
            self.validated_data['leave_type'] = leave_type
        else: 
            pass

        if self.validated_data.get('status')=='A' or 'D': 
            # if not request.user is hr:
                # raise serializers.ValidationError(f'You don't have permission to approve or disapprove leave for {company}')
      
            signed_by = Employee.objects.filter(employee=request.user, company=company).last()
            if not signed_by:
                raise serializers.ValidationError(f"Employee doesn't belong to {company}")
            self.validated_data["signed_by"] = signed_by

        if validated_data.get("is_taken") == True:
            # if not request.user is hr:
                # raise serializers.ValidationError(f'You don't have permission to create leave record leave for {company}')
            # if request.user == instance.signed_by:
            leave_record = LeaveRecord.objects.create(
                company = company,
                department = department,
                employee = employee,
                leave_request = instance,
            )
            # else:
            #     raise serializers.ValidationError(f'Leave type selected doesn\'t belong to {company}')
                
        print(validated_data)

        return super().update(instance, validated_data)

class LeaveRequestApprovalSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeaveRequest
        fields = ['id', 'employee', 'start_date', 'end_date', 'reason', 'leave_type', \
                  'status', 'minute', 'signed_by', 'is_taken', 'date_created', 'date_updated']    
    
    def create(self, validated_data):
        request = self.context.get('request')

        company_id = request.query_params.get('company_id')
        company = Company.objects.filter(id=company_id).last()
        if not company:
            raise serializers.ValidationError(f"Invalid Company")
        self.validated_data['company'] = company

        department_id = request.query_params.get('department_id')
        department = Department.objects.filter(pk=department_id).last()
        if not department:
            raise serializers.ValidationError(f"Department doesn't belong to {company}")
        self.validated_data['department'] = department

        employee = Employee.objects.filter(employee=request.user, company=company).last()
        if not employee:
            raise serializers.ValidationError(f"Employee doesn't belong to {company}")
        self.validated_data['employee'] = employee

        leave_type_id = request.query_params.get('leave_type_id')
        leave_type = LeaveType.objects.filter(pk=leave_type_id, company=company).last()
        if not leave_type:
            raise serializers.ValidationError(f'Leave type selected doesn\'t belong to {company}')
        self.validated_data['leave_type'] = leave_type

        leave_request = LeaveRequest.objects.create(**validated_data)

        self.instance = leave_request
        return self.instance

    def update(self, instance, validated_data):

        request = self.context.get('request')
        company = instance.company

        # department_id = request.query_params.get('department_id')
        # if department_id:
        #     department = Department.objects.filter(pk=department_id, company=company).last()
        #     if not department:
        #         raise serializers.ValidationError(f"Department doesn't belong to {company}")
        #     self.validated_data['department'] = department
        # else:
        #     pass
        
        # employee = Employee.objects.filter(employee = self.context.get('employee').id, company=company).last
        # if not employee:
        #     raise serializers.ValidationError(f"Employee doesn't belong to {company}")
        # self.validated_data["employee"] = employee

        # leave_type_id = request.query_params.get('leave_type_id')
        # if leave_type_id:
        #     leave_type = LeaveType.objects.filter(pk=leave_type_id, company=company).last()
        #     if not leave_type:
        #         raise serializers.ValidationError(f'Leave type selected doesn\'t belong to {company}')
        #     self.validated_data['leave_type'] = leave_type
        # else: 
        #     pass

        if self.validated_data.get('status')=='A' or 'D': 
            # if not request.user is hr:
                # raise serializers.ValidationError(f'You don't have permission to approve or disapprove leave for {company}')
      
            signed_by = Employee.objects.filter(employee=request.user, company=company).last()
            if not signed_by:
                raise serializers.ValidationError(f"Employee doesn't belong to {company}")
            self.validated_data["signed_by"] = signed_by

        if validated_data.get("is_taken") == True:
            # if not request.user is hr:
                # raise serializers.ValidationError(f'You don't have permission to create leave record leave for {company}')
            # if request.user == instance.signed_by:
            leave_record = LeaveRecord.objects.create(
                company = company,
                department = instance.department,
                employee = instance.employee,
                leave_request = instance,
                total_leave_days = instance.leave_type.value,

            )
            # else:
            #     raise serializers.ValidationError(f'Leave type selected doesn\'t belong to {company}')
                
        print(validated_data)

        return super().update(instance, validated_data)

    # def save(self, **kwargs):
    #     # validated_data = self.validated_data
    #     # employee = self.context['employee_id']
    #     # employee = Employee.objects.get(id=employee)
    #     # is_taken = validated_data.get("is_taken")
    #     # print("context=>", self.context)
    #     # leave_request = self.context["item"]
        
    #     if is_taken:
    #         leave_request.is_taken = is_taken
    #         leave_record = LeaveRecord.objects.create(
    #             employee = employee, 
    #             leave_request = leave_request)
    #     leave_request.save()
    #     self.instance = leave_request
    #     return self.instance

class LeaveRecordSerializer(serializers.ModelSerializer):
    #Leave_count
    def save(self, **kwargs):
        return super().save(**kwargs)
    class Meta:
        model = LeaveRecord
        fields = ['id', 'company', 'department', 'employee', 'leave_request', 'leave_days_taken',
                  'leave_days_remaining', 'leave_days_exceeded', 'total_leave_days', 'date_created',
                  'date_updated']
