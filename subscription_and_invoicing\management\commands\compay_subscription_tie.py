import os
import re
import csv
import logging
import uuid
import random
import string
from decimal import Decimal, InvalidOperation
from datetime import timed<PERSON>ta

from django.conf import settings
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils.timezone import make_aware

from performance_sales_metrics_dashboard.models import SalesOfficer
from requisition.models import Company
from subscription_and_invoicing.models import (
    SubscriptionPlan, 
    Module, 
    CompanySubscription, 
    ModuleSubscription, 
    AccessPath, 
    Invoice, 
    InvoiceModuleDetail,
    SubscriptionAudit,
    PromotionalOffer
)

User = get_user_model()
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Import comprehensive subscriptions from CSV with advanced validation'

    def add_arguments(self, parser):
        parser.add_argument(
            'csv_file', 
            nargs='?',  
            type=str, 
            default=os.path.join(
                settings.BASE_DIR, 
                'subscription_and_invoicing', 
                'subscriptions_list.csv'
            ),
            help='Path to the CSV file containing subscription data'
        )
        parser.add_argument(
            '--dry-run', 
            action='store_true', 
            help='Validate data without committing to database'
        )
        parser.add_argument(
            '--verbose', 
            action='store_true', 
            help='Provide detailed logging and reporting'
        )

    def handle(self, *args, **options):
        csv_file_path = options['csv_file']
        is_dry_run = options['dry_run']
        is_verbose = options['verbose']

        # Configure logging
        logging.basicConfig(
            level=logging.INFO if is_verbose else logging.WARNING,
            format='%(asctime)s - %(levelname)s: %(message)s'
        )

        # Pre-check required modules and setup
        try:
            self.validate_import_prerequisites()
        except Exception as setup_error:
            logger.error(f"Import prerequisites check failed: {setup_error}")
            return

        processed_records = {
            'total': 0,
            'processed': 0,
            'skipped': 0,
            'errors': []
        }

        logger.info(f"Starting subscription import from {csv_file_path}")
        logger.info(f"Dry Run Mode: {'Enabled' if is_dry_run else 'Disabled'}")

        try:
            with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                current_month = None
                
                for row_index, row in enumerate(reader, 1):
                    # Skip empty rows or month headers
                    if not row.get("Name of Business", "").strip():
                        # Check if this is a month header row
                        month = row.get("Month", "").strip()
                        if month:
                            current_month = month
                        continue
                    
                    processed_records['total'] += 1
                    
                    try:
                        with transaction.atomic():
                            result = self.process_subscription_row(
                                row, 
                                current_month=current_month,
                                dry_run=is_dry_run
                            )
                            
                            if result:
                                processed_records['processed'] += 1
                                if is_verbose:
                                    logger.info(f"Successfully processed row {row_index}")
                            else:
                                processed_records['skipped'] += 1
                                if is_verbose:
                                    logger.warning(f"Skipped row {row_index}")
                    
                    except Exception as e:
                        processed_records['skipped'] += 1
                        error_msg = f"Error processing row {row_index}: {str(e)}"
                        processed_records['errors'].append(error_msg)
                        logger.error(error_msg)
                        
                        # In dry run, we don't want to break entire import
                        if not is_dry_run:
                            raise

        except Exception as import_error:
            logger.error(f"Critical error during import: {str(import_error)}")
            self.stdout.write(self.style.ERROR(f'Error during import: {str(import_error)}'))
            raise

        # Final reporting
        self.stdout.write(self.style.SUCCESS('Subscription Import Summary:'))
        self.stdout.write(f"Total Records: {processed_records['total']}")
        self.stdout.write(f"Processed Records: {processed_records['processed']}")
        self.stdout.write(f"Skipped Records: {processed_records['skipped']}")
        
        if processed_records['errors']:
            self.stdout.write(self.style.WARNING('Errors Encountered:'))
            for error in processed_records['errors']:
                self.stdout.write(self.style.WARNING(error))

    def validate_import_prerequisites(self):
        """Validate essential prerequisites before import"""
        try:
            # Ensure all required modules exist
            modules = [
                {'code': 'INVENTORY', 'name': 'Stock and Inventory'},
                {'code': 'SALES', 'name': 'Sales'},
                {'code': 'HR', 'name': 'HR Management'},
                {'code': 'SPEND', 'name': 'Spend Management'},
            ]
            
            for module_data in modules:
                Module.objects.get_or_create(
                    code=module_data['code'], 
                    defaults={
                        'name': module_data['name'], 
                        'is_premium': True,
                        'requires_subscription': True
                    }
                )

            # Create a default promotional offer if none exists
            PromotionalOffer.objects.get_or_create(
                name='Standard Import Promo',
                defaults={
                    'description': 'Default promotional offer for imported subscriptions',
                    'discount_amount': Decimal('0'),
                    'start_date': timezone.now(),
                    'end_date': timezone.now() + timezone.timedelta(days=365),
                    'is_active': True
                }
            )

            # Ensure subscription plans exist
            SubscriptionPlan.objects.get_or_create(
                name="Semi-Annual Subscription",
                duration_months=6,
                defaults={
                    'description': "Subscription plan for 6 months",
                    'price': Decimal('150000.00'),
                    'is_active': True
                }
            )
            
            SubscriptionPlan.objects.get_or_create(
                name="Annual Subscription",
                duration_months=12,
                defaults={
                    'description': "Subscription plan for 12 months",
                    'price': Decimal('300000.00'),
                    'is_active': True
                }
            )

        except Exception as e:
            logger.error(f"Prerequisites validation failed: {e}")
            raise

    def normalize_company_name(self, name):
        """Normalize company name for consistent matching"""
        return re.sub(r'[^a-zA-Z0-9]', '', name).lower() if name else ''

    def parse_date(self, date_str, current_month=None, current_year=None):
        """Robust date parsing with multiple format support"""
        if not date_str or date_str.strip() == '':
            if current_month:
                # Default to the 1st of the current month if no date provided
                current_year = current_year or timezone.now().year
                months = {
                    'January': 1, 'February': 2, 'March': 3, 'April': 4,
                    'May': 5, 'June': 6, 'July': 7, 'August': 8,
                    'September': 9, 'October': 10, 'November': 11, 'December': 12,
                    'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                    'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
                }
                month_num = months.get(current_month, timezone.now().month)
                parsed_date = timezone.datetime(current_year, month_num, 1)
                return make_aware(parsed_date)
            return None

        date_str = date_str.strip()
        
        date_formats = [
            "%d/%m/%Y", "%m/%d/%Y", 
            "%Y-%m-%d", "%m-%d-%Y",
            "%d/%m/%y", "%m/%d/%y",
        ]
        
        for fmt in date_formats:
            try:
                parsed_date = timezone.datetime.strptime(date_str, fmt)
                
                # Handle two-digit year interpretation
                if '/' in date_str and len(date_str.split('/')[2]) == 2:
                    current_year = timezone.now().year
                    century = (current_year // 100) * 100
                    parsed_date = parsed_date.replace(year=century + parsed_date.year)
                
                return make_aware(parsed_date)
            except ValueError:
                continue
        
        logger.warning(f"Could not parse date: {date_str}")
        return None

    def clean_currency_value(self, value_str):
        """Clean and convert currency strings to Decimal"""
        if not value_str:
            return Decimal('0.00')
        
        cleaned_value = value_str.replace('₦', '').replace('N', '').replace(',', '').strip()
        
        try:
            return Decimal(cleaned_value)
        except InvalidOperation:
            logger.warning(f"Could not convert currency value: {value_str}")
            return Decimal('0.00')

    def get_subscription_details(self, package_str):
        """Determine subscription details from package description"""
        if not package_str:
            return {'duration_months': 12, 'pos_included': False}
        
        package_str = package_str.lower()
        
        duration_months = 12  # Default to annual
        if any(term in package_str for term in ['6', 'six', 'half', '6 month']):
            duration_months = 6
        
        pos_included = 'pos' in package_str
        
        return {
            'duration_months': duration_months,
            'pos_included': pos_included
        }

    def find_sales_officer(self, sales_agent_name):
        """Find or create sales officer based on name"""
        if not sales_agent_name:
            return None
        
        try:
            sales_officer, created = SalesOfficer.objects.get_or_create(
                name=sales_agent_name.strip(),
                defaults={'is_active': True}
            )
            if created:
                logger.info(f"Created new sales officer: {sales_agent_name}")
            return sales_officer
        except Exception as e:
            logger.warning(f"Could not process sales officer {sales_agent_name}: {e}")
            return None

    def process_subscription_row(self, row, current_month=None, dry_run=False):
        """Process individual subscription row with comprehensive validation"""
        # Skip completely empty rows
        company_name = row.get("Name of Business", "").strip()
        if not company_name:
            logger.warning("Skipping row: No company name")
            return False

        # Normalize and find company
        normalized_company_name = self.normalize_company_name(company_name)
        company = next(
            (c for c in Company.objects.all() if 
             self.normalize_company_name(c.company_name) == normalized_company_name),
            None
        )

        if not company:
            logger.warning(f"Company not found: {company_name}")
            return False

        # Find sales officer
        sales_officer = self.find_sales_officer(row.get('Sales Agent'))

        # Parse financial details
        subscription_fee = self.clean_currency_value(row.get('Subscription Fee Paid', '0'))
        pending_balance = self.clean_currency_value(row.get('Balance', '0'))

        # Determine package details
        package_str = row.get('Package Paid For', '').strip()
        subscription_details = self.get_subscription_details(package_str)
        duration_months = subscription_details['duration_months']
        pos_included = subscription_details['pos_included']

        # Parse dates
        current_year = None
        if current_month == '2025':
            current_month = 'January'  # Default month for 2025 section
            current_year = 2025
            
        start_date_str = row.get('Payment Date', '').strip()
        start_date = self.parse_date(start_date_str, current_month, current_year)

        if not start_date:
            logger.warning(f"Invalid date for {company_name}: {start_date_str}")
            return False

        # Calculate end date based on duration
        end_date = start_date + timezone.timedelta(days=30 * duration_months)

        # If this is a dry run, we stop here
        if dry_run:
            return True

        # Fetch default promotional offer
        promo_offer = PromotionalOffer.objects.filter(name='Standard Import Promo').first()

        # Get the appropriate subscription plan
        plan_name = "Semi-Annual Subscription" if duration_months == 6 else "Annual Subscription"
        try:
            plan = SubscriptionPlan.objects.get(name=plan_name)
        except SubscriptionPlan.DoesNotExist:
            logger.error(f"Subscription plan not found: {plan_name}")
            return False

        # Create or retrieve company subscription
        subscription, created = CompanySubscription.objects.get_or_create(
            company=company,
            defaults={
                'access_type': AccessPath.PAID,
                'status': 'active',
                'created_by': None,
                'pos_included': pos_included
            }
        )
        
        if not created and subscription.access_type != AccessPath.PAID:
            subscription.access_type = AccessPath.PAID
            subscription.status = 'active'
            subscription.pos_included = pos_included
            subscription.save()

        # Generate a unique batch ID for the invoice
        batch_id = Invoice.generate_batch_id() if hasattr(Invoice, 'generate_batch_id') else None

        # Create invoice
        invoice = Invoice.objects.create(
            company=company,
            company_subscription=subscription,
            total_amount=subscription_fee,
            amount_paid=subscription_fee,
            balance_due=pending_balance,
            start_date=start_date,
            expiry_date=end_date,
            payment_status="paid" if pending_balance == 0 else "part_payment",
            sales_officer=sales_officer,
            batch_id=batch_id,
            package_description=package_str,
            is_active=True,
            created_by=None
        )

        # Get modules
        inventory_module = Module.objects.get(code='INVENTORY')
        sales_module = Module.objects.get(code='SALES')
        
        # Default modules to subscribe to
        modules_to_subscribe = [
            {'module': inventory_module, 'is_primary': True},
            {'module': sales_module, 'is_primary': False}
        ]
        
        # Add optional modules based on package or other criteria
        if "all modules" in package_str.lower() or subscription_fee >= Decimal('250000'):
            hr_module = Module.objects.get(code='HR')
            spend_module = Module.objects.get(code='SPEND')
            modules_to_subscribe.append({'module': hr_module, 'is_primary': False})
            modules_to_subscribe.append({'module': spend_module, 'is_primary': False})

        # Create module subscriptions and invoice module details
        for module_info in modules_to_subscribe:
            module = module_info['module']
            is_primary = module_info['is_primary']
            
            # Create module subscription
            module_sub, created = ModuleSubscription.objects.get_or_create(
                company_subscription=subscription,
                module=module,
                defaults={
                    'plan': plan,
                    'is_auto_renewal': False,
                    'is_active': True,
                    'start_date': start_date,
                    'end_date': end_date,
                    'pending_balance': Decimal('0'),
                    'promotional_offer': promo_offer
                }
            )
            
            # If not newly created, update it
            if not created:
                module_sub.plan = plan
                module_sub.is_active = True
                module_sub.start_date = start_date
                module_sub.end_date = end_date
                module_sub.promotional_offer = promo_offer
                module_sub.save()
            
            # Calculate module price based on primacy
            module_price = plan.price if is_primary else Decimal('0.00')
            
            # Create invoice module detail
            InvoiceModuleDetail.objects.create(
                invoice=invoice,
                module=module,
                quantity=duration_months,
                unit_price=module_price / duration_months if duration_months > 0 else Decimal('0.00'),
                discount=0,
                total_price=module_price
            )

        # Create subscription audit
        SubscriptionAudit.objects.create(
            company=company,
            subscription=subscription,
            action="Subscription Created from CSV Import",
            action_by=None,
            details={
                "amount": str(subscription_fee),
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d'),
                "sales_agent": row.get('Sales Agent', ''),
                "package": package_str,
                "pos_included": pos_included,
                "pending_balance": str(pending_balance)
            }
        )

        logger.info(f"Successfully processed subscription for {company_name}")
        return True