import http.client
import json
import urllib.parse
from decouple import config
import base64
from django.core.cache import cache
import pprint
from core.models import SalesAgents, VfdMeeting
from celery import shared_task
from core.tasks import send_email
from performance_sales_metrics_dashboard.utils.zoom_integration import ZoomAPIntegration
    

def assign_meeting_to_sales_agent(leads_email):
    
    vfd_meeting_instance = VfdMeeting.objects.filter(work_email=leads_email, is_assigned = False).last()
    if vfd_meeting_instance == None:
        return "DOES NOT EXIST"

    agents = SalesAgents.objects.all()

    if not agents:
        print("No agent available!")
    
    agents_meetings_count = {}

    for agent in agents:
        agents_meetings_count[agent.phone_number] = VfdMeeting.objects.filter(is_assigned=True, paybox_agent_phone_number=agent.phone_number).count()
    
    min_meetings = min(agents_meetings_count.values()) 
    
    min_meetings_agent = [agent for agent, count in agents_meetings_count.items() if count == min_meetings][0]

    min_agent_instance = SalesAgents.objects.get(phone_number=min_meetings_agent)

    vfd_meeting_instance.is_assigned = True
    vfd_meeting_instance.paybox_agent_phone_number = min_meetings_agent
    vfd_meeting_instance.paybox_agent_name = f"{min_agent_instance.name}"
    vfd_meeting_instance.save()

    return {
            "agent_email": min_agent_instance.email,
            "agent_name": min_agent_instance.name,
        }