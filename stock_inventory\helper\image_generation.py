from django.conf import settings
from core.tasks import upload_file_aws_s3_bucket
from stock_inventory import models
import requests, json
from django.core.files.base import ContentFile


class ImageGeneration:

    headers = {
        "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
        "Content-Type": "application/json"
    }

    
    @classmethod
    def generate_images(cls, prompt):
        url = "https://api.openai.com/v1/images/generations"

        data = {
                "model": "dall-e-2",
                "prompt": prompt,
                "n": 5,
                "size": "512x512"
            }

        req = requests.post(url, headers=cls.headers, data=json.dumps(data))

        if req.status_code == 200:
            response_data = req.json()
            return [image['url'] for image in response_data.get('data', [])]
        else:
            print(f"Error: {req.status_code}, {req.text}")
            return []
            
    @classmethod
    def image_generation(cls):
        products = models.Product.objects.filter(
            product_image_1__isnull=True,
            product_image_2__isnull=True,
            product_image_3__isnull=True,
            product_image_4__isnull=True,
            product_image_5__isnull=True
        )



        for product in products:
            category_name = product.category.name if product.category else ''
            # print(f"category_name: {category_name}")
            product_description = product.product_description if product.product_description else product.name
            # print(f"product_description: {product_description}")
            images = cls.generate_images(f"You are a product artist, and have a product from category {category_name}, the product name is {product.name} and description might be {product_description}. Generate an appropriate realistic image")
            # images = cls.generate_images(
            #     f"{product.category+' '+product.product_description if product.product_description else product.category+' '+product.name}"
            # )

            if images:                
                uploaded_urls = []

                for idx, image_url in enumerate(images):
                    response = requests.get(image_url)

                    if response.status_code == 200:
                        file_name = f'product_{product.id}_image_{idx + 1}.jpg'
                                                 
                        content_file = ContentFile(response.content, name=file_name)

                        uploaded_url = upload_file_aws_s3_bucket(
                            model_instance_id = product.id, 
                            file = content_file,
                            model_name = "Product")
                        
                        uploaded_urls.append(uploaded_url)
                    else:
                        print(f"Failed to download image {idx + 1}: {response.status_code}")

                product.product_image_1 = uploaded_urls[0] if len(uploaded_urls) > 0 else None
                product.product_image_2 = uploaded_urls[1] if len(uploaded_urls) > 1 else None
                product.product_image_3 = uploaded_urls[2] if len(uploaded_urls) > 2 else None
                product.product_image_4 = uploaded_urls[3] if len(uploaded_urls) > 3 else None
                product.product_image_5 = uploaded_urls[4] if len(uploaded_urls) > 4 else None

                product.save()
                print(f"Updated product {product.id} with new images.")
            
            else:
                print(f"Failed to download image")
        
        return "Image update process completed."
