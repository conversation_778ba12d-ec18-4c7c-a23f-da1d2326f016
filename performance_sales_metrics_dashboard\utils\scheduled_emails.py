from requisition.helpers.enums import User<PERSON><PERSON>
from requisition.models import TeamMember
from ..models import Emails

def save_to_scheduled(
        is_scheduled, 
        sales_officer, 
        date, 
        subject,
        sender, 
        senders_name,
        cc,
        bcc,
        receiver, 
        content, 
    ):
    try:
        date_scheduled = Emails.objects.get(date=date)
        Emails.objects.get(date=date_scheduled.date)
            # continue
    except Emails.DoesNotExist:
        sales_officer_instance = TeamMember.objects.get(
                id=sales_officer, role=UserRole.SALES_OFFICER
            )
        Emails.save_as_scheduled(
            is_scheduled=is_scheduled,
            sales_officer=sales_officer_instance, 
            date=date, 
            subject=subject, 
            sender=sender,
            senders_name=senders_name,
            cc=cc,
            bcc=bcc,
            recipients=receiver, 
            content=content, 
        )
    except Exception as e:
        print("An error occurred in the save_to_scheduled function.", str(e))
