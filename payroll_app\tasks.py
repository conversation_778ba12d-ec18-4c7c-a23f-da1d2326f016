import os
from datetime import datetime, timedelta
from string import Template
from pathlib import Path
import requests
from celery import shared_task
import calendar
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db.models import Avg, Count, Q, Sum, Value
from django.utils import timezone

from account.models import AccountSystem, Transaction, Wallet
from core.models import ConstantTable
from core.tasks import send_email_with_attachment
from payroll_app.apis.func import generate_readable_employee_id, round_amount
from payroll_app.apis.pfa_schedule import manual_pension_data, manual_pension_data_no_payment, update_single_pfa_pension_data
from requisition.models import Company

from django.db.models.functions import Coalesce
import pandas as pd
User = get_user_model()
logger = settings.LOGGER


@shared_task
def send_money_pay_account_task(
    user,
    payroll_type,
    account_ins,
    payroll_pin,
    payroll_id,
    account_number,
    account_name,
    bank_name,
    bank_code,
    amount,
    narration,
    bulk_id,
    charge_fee,
    instant_amount,
    receiver_id,
):
    from payroll_app.models import CompanyEmployeeList, PayrollTable
    from accounting.utils import categorize_transaction
    from payroll_app.models import CompanyPayrollSettings

    all_send_money = []
    payed = PayrollTable.objects.filter(id=payroll_id, bulk_id=bulk_id, status="DISBURSE", payroll_deleted=False).first()
    if payed:

        payroll_settings_ins = CompanyPayrollSettings.objects.filter(company=payed.company).first()
        if payroll_settings_ins:
            notify_employee = payroll_settings_ins.payroll_notification
        else:
            notify_employee = False

        if not payed.payroll_disburse:
            user_ins = User.objects.get(id=user)
            employee = User.objects.get(id=receiver_id)

            payroll_date = payed.payroll_date
            payroll_month = payroll_date.strftime("%B")
            payroll_year = payroll_date.strftime("%Y")
            payment_date = f"{payroll_month} {payroll_year}"

            account_inst = AccountSystem.objects.get(id=account_ins)

            # new send money
            send_money = Transaction.vfd_funds_transfer(
                bank_code=bank_code,
                bank_name=bank_name,
                account_name=account_name,
                account_number=account_number,
                narration=narration,
                amount=amount,
                user=user_ins,
                account=account_inst,
                charge_fee=charge_fee
            )
            if isinstance(send_money, str):
                transaction_instance = Transaction.objects.filter(transaction_ref=send_money).first()
                if transaction_instance:
                    try:
                        categorize_transaction(
                            transaction_instance,
                            source="hr",
                            transaction_type="payroll_expenses"
                        )
                    except Exception as e:
                        logger.info(f"PAYROLL TO EXTERNAL ACCOUNT -> {str(e)}")
            
            all_send_money.append(send_money)
            if isinstance(send_money, str):
                payed.payroll_status = "00"
                payed.payroll_disburse = True
                payed.status = "DISBURSED"
                payed.save()

                employee_ins = CompanyEmployeeList.objects.filter(
                    employee__email=payed.payroll_user.email, company=payed.company
                ).last()
                if employee_ins:
                    if notify_employee:
                        send_payroll_payment_notification_email.delay(
                            recipient=employee_ins.employee_email,
                            subject="Payroll Payment",
                            template_dir="salary_notification.html",
                            name=employee_ins.employee_first_name,
                            amount=amount,
                            month=payment_date,
                            company_name=employee_ins.company.company_name,
                        )

                ## send instant wage money to Instant Wage Company
                if instant_amount > 0:
                    send_money = Wallet.send_money_via_buddy_instant_wage_company(
                        instant_amount,
                        user_ins,
                        payroll_type,
                        account_inst,
                        employee,
                    )

            else:
                payed.payroll_status = "04"
                payed.save()

    return f"SEND MONEY TO ACCOUNT DONE {all_send_money}"


@shared_task
def send_money_pay_buddy_task(
    user,
    payroll_type,
    account_ins,
    payroll_pin,
    payroll_id,
    amount,
    narration,
    bulk_id,
    charge_fee,
    receiver_id,
    instant_amount,
):
    from payroll_app.models import CompanyEmployeeList, PayrollTable
    from payroll_app.models import CompanyPayrollSettings

    all_send_money = []
    payed = PayrollTable.objects.filter(id=payroll_id, bulk_id=bulk_id, status="DISBURSE", payroll_deleted=False).first()
    if payed:

        payroll_settings_ins = CompanyPayrollSettings.objects.filter(company=payed.company).first()
        if payroll_settings_ins:
            notify_employee = payroll_settings_ins.payroll_notification
        else:
            notify_employee = False
            
        if not payed.payroll_disburse:
            user_ins = User.objects.get(id=user)
            receiver_ins = User.objects.get(id=receiver_id)
            account_inst = AccountSystem.objects.get(id=account_ins)
            send_money = Wallet.send_money_via_buddy(
                narration,
                amount,
                user_ins,
                payroll_type,
                payroll_pin,
                account_inst,
                receiver_ins,
                charge_fee
            )
            all_send_money.append(send_money)

            payroll_date = payed.payroll_date
            payroll_month = payroll_date.strftime("%B")
            payroll_year = payroll_date.strftime("%Y")
            payment_date = f"{payroll_month} {payroll_year}"
            if "status_code" in send_money:
                if send_money["status_code"] == "00":
                    payed.payroll_status = send_money["status_code"]
                    payed.payroll_disburse = True
                    payed.status = "DISBURSED"
                    payed.save()

                    employee_ins = CompanyEmployeeList.objects.filter(
                        employee__email=payed.payroll_user.email, company=payed.company
                    ).last()
                    if employee_ins:
                        if notify_employee:
                            send_payroll_payment_notification_email.delay(
                                recipient=employee_ins.employee_email,
                                subject="Payroll Payment",
                                template_dir="salary_notification.html",
                                name=employee_ins.employee_first_name,
                                amount=amount,
                                month=payment_date,
                                company_name=employee_ins.company.company_name,
                            )

                    employee = receiver_ins
                    ## send instant wage money to Instant Wage Company
                    if instant_amount > 0:
                        send_money = Wallet.send_money_via_buddy_instant_wage_company(
                            instant_amount,
                            user_ins,
                            payroll_type,
                            account_inst,
                            employee,
                        )
                else:
                    payed.payroll_status = send_money["status_code"]
                    payed.save()
            else:
                pass

    return f"SEND MONEY TO BUDDY DONE {all_send_money}"


@shared_task
def payout_task(
    user,
    payout_type,
    account_ins,
    disburse_pin,
    account_number,
    account_name,
    bank_name,
    bank_code,
    amount,
    narration,
    charge_fee,
):

    user_ins = User.objects.get(id=user)
    account_inst = AccountSystem.objects.get(id=account_ins)

    # new send money
    send_money = Transaction.vfd_funds_transfer(
        bank_code=bank_code,
        bank_name=bank_name,
        account_name=account_name,
        account_number=account_number,
        narration=narration,
        amount=amount,
        user=user_ins,
        account=account_inst,
        charge_fee=charge_fee
    )

    return f"PAYOUT BUDDY-ACCOUNT DONE {send_money}"


@shared_task
def run_payroll():
    from payroll_app.models import CompanyDetailsData, PayrollTable

    all_company = CompanyDetailsData.objects.all().filter(is_recurring=True)
    run_company = []
    for company in all_company:
        company_admin = Company.objects.get(id=company.id)
        today = datetime.today()
        if today == company.payroll_date:
            run_payroll = PayrollTable.create_approval_payroll(
                user=company_admin.user, company_uuid=company.id
            )
            run_company.append({"company_id": company.id, "payroll_data": run_payroll})

    return f"RUN PAYROLL DONE, PAYROLL DATA => {run_company}"


@shared_task
def instant_wage_daily():
    from payroll_app.models import CompanyEmployeeList, PayrollTable

    all_instant_wage_data = []
    # instant_wage_account_id = settings.INSTANT_WAGE_ACCOUNT_ID
    # instant_wage_user_id = settings.INSTANT_WAGE_USER_ID
    today_date = timezone.now().date()
    # instant_wage_user_ins = User.objects.get(id="9b9cb825-7136-470e-8c91-041f76be1856")
    # instant_wage_account_inst = AccountSystem.objects.get(id="7996751e-8f7a-4f5f-8560-43c1450a19c0")
    # instant_wage_user_ins = User.objects.get(id=instant_wage_user_id)
    # instant_wage_account_inst = AccountSystem.objects.get(id=instant_wage_account_id)
    all_instant_wage_user = CompanyEmployeeList.objects.filter(
        employee_instant_daily_wage=True,
        employee_instant_wage_completed=False,
        is_active=True,
        employee_instant_wage_status="SUCCESSFUL",
        is_deleted=False,
        is_suspended=False,
    )
    # print(all_instant_wage_user, "ALL USERS")
    for wage_user in all_instant_wage_user:
        if wage_user.employee_instant_daily_wage_date != today_date:

            receiver_ins = wage_user.employee
            receiver_company = wage_user.company
            receiver_employer_ins = wage_user.employer

            if wage_user.custom_daily_wage:
                check_amount = wage_user.custom_daily_wage_amount
                # print(check_amount, "check amount")
            else:
                check_amount = wage_user.employee_instant_daily_wage_amount
                # print(check_amount, "check amount")

            if check_amount > wage_user.employee_instant_daily_remaining_wage_amount:
                amount = wage_user.employee_instant_daily_remaining_wage_amount
                # print(amount, "REAL AMOUNT")
                instant_wage_completed = True
            else:
                amount = check_amount
                # print(amount, "REAL AMOUNT")
                instant_wage_completed = False

            if amount > 0:
                # print(amount, "REAL AMOUNT > 0")

                # payroll_type = "INSTANT_WAGE"

                instant_wage_data = {
                    "receiver": receiver_ins.email,
                    "receiver_company": receiver_company.company_name,
                    "receiver_employer": receiver_employer_ins.email,
                    "amount": amount,
                }

                all_instant_wage_data.append(instant_wage_data)

                # net available wage = (daily wage * date) - collected wage

                collected_wage_amount = (
                    wage_user.employee_instant_daily_collected_wage_amount
                    + round_amount(amount)
                )
                remaining_wage = (
                    wage_user.employee_instant_daily_remaining_wage_amount
                    - round_amount(amount)
                )
                net_available_wage = (
                    collected_wage_amount - wage_user.employee_instant_used_wage_amount
                )
                # net_available_wage = collected_wage_amount -  remaining_wage

                wage_user.employee_instant_daily_collected_wage_amount = (
                    collected_wage_amount
                )
                wage_user.employee_instant_daily_remaining_wage_amount = remaining_wage
                wage_user.employee_instant_net_available_wage = net_available_wage
                wage_user.employee_instant_wage_completed = instant_wage_completed
                wage_user.save()

                # send_money = Wallet.send_money_via_instant_wage(amount, instant_wage_user_ins,
                #                                                 instant_wage_account_inst,
                #                                                 receiver_employer_ins,
                #                                                 receiver_company,
                #                                                 payroll_type,
                #                                                 receiver_ins
                #                                         )
                # instant_wage_data.update(send_money)
                # all_instant_wage_data.append(instant_wage_data)

                # if "status_code" in send_money:
                #     if send_money["status_code"] == "00":
                #         collect_amount = round_amount(wage_user.employee_instant_used_wage_amount + amount)
                #         remaining_wage = round_amount(wage_user.employee_instant_daily_remaining_wage_amount - amount)
                #         # used_wage = (remaining_wage - collect_amount)
                #         # wage_user.employee_instant_daily_collected_wage_amount = collect_amount
                #         wage_user.employee_instant_daily_remaining_wage_amount = remaining_wage
                #         wage_user.employee_instant_wage_completed = instant_wage_completed
                #         wage_user.employee_instant_used_wage_amount = collect_amount
                #         wage_user.save()
                #     else:
                #         pass
                # else:
                #     pass
            else:
                pass
        else:
            pass
    return f"INSTANT WAGE PAYROLL DATA => {all_instant_wage_data}"


@shared_task
def instant_wage_logic_company():
    from payroll_app.models import CompanyDetailsData, PayrollTable

    # receive payroll for the month true or false checker to access instant wage
    # if true difference btw last pay is < 35 days means eligibility
    const_table = ConstantTable.get_constant_instance()
    company_valid_day_number = const_table.company_validity_days
    company_validity = datetime.now() - timedelta(days=company_valid_day_number)
    company_valid_days = company_validity.date()
    company_active_days = const_table.company_active_days
    company_payroll_count = const_table.company_payroll_count
    today_date = timezone.now()
    all_company_data = CompanyDetailsData.objects.all()
    all_company_instance = Company.objects.all()
    for company_instance in all_company_instance:
        all_pay_roll = PayrollTable.objects.filter(
            company=company_instance, status="DISBURSED",
            payroll_deleted=False
        )
        if all_pay_roll:
            get_last_payroll = all_pay_roll.last()
            last_payroll_date = get_last_payroll.date_created
            date_difference = today_date - last_payroll_date
            # check if the company has been active in the last 35 days
            if date_difference.days < company_active_days:
                # check if the company done payroll at least 3 months
                get_pay_count = 0
                get_bulk_id = all_company_data.filter(
                    company=company_instance, bulk_id=get_last_payroll.bulk_id, payroll_date__gte=company_valid_days, is_deleted=False
                )
                for bulk in get_bulk_id:
                    bulk_id = bulk.bulk_id
                    pay_count = all_pay_roll.filter(bulk_id=bulk_id).first()
                    if pay_count:
                        get_pay_count += 1
                    else:
                        get_pay_count += 0
                if get_pay_count >= company_payroll_count:
                    company_instance.instant_wage = True
                    company_instance.save()
                else:
                    company_instance.instant_wage = False
                    company_instance.save()
            else:
                company_instance.instant_wage = False
                company_instance.save()
        else:
            company_instance.instant_wage = False
            company_instance.save()

    return "INSTANT WAGE COMPLETE CHECKER FOR COMPANIES"


@shared_task
def instant_wage_logic_employee():
    from payroll_app.models import CompanyDetailsData, CompanyEmployeeList, PayrollTable

    # eligibility for constant wage constant wage
    # at least 3 payroll (field) same with employer
    # dont have access to instant wage for this month
    const_pay_count = ConstantTable.get_constant_instance().employee_payroll_count
    this_company_data = CompanyDetailsData.objects.filter(is_deleted=False)
    company_employees_off = CompanyEmployeeList.objects.all().update(
        employee_instant_daily_wage=False,
        employee_instant_wage_status="OFF",
        employee_instant_daily_collected_wage_amount=0,
        employee_instant_daily_remaining_wage_amount=0,
        set_instant_wage_amount=0,
        employee_instant_daily_wage_amount=0,
        employee_instant_net_available_wage=0,
        employee_instant_wage_completed=False,
        custom_daily_wage=False,
        custom_daily_wage_amount=0,
        custom_instant_wage_percentage=0,
    )
    all_company_employees = CompanyEmployeeList.objects.filter(
        is_active=True, staff_type="FULL_TIME"
    )
    month_map = {
        "january": 1,
        "february": 2,
        "march": 3,
        "april": 4,
        "may": 5,
        "june": 6,
        "july": 7,
        "august": 8,
        "september": 9,
        "october": 10,
        "november": 11,
        "december": 12,
    }
    for company_employee in all_company_employees:
        employee = company_employee.employee
        # print(employee, "Employee")
        company = company_employee.company
        # print(company, "Company")
        company_instant_wage = company_employee.company.instant_wage
        current_employee = all_company_employees.filter(
            employee=employee, company=company
        ).first()
        employee_payable = current_employee.employee_payable_amount
        employee_used_wage = current_employee.employee_instant_used_wage_amount
        if employee_payable <= 0:
            # if employee net pay amount is less than zero, not eligible
            pass
        elif employee_used_wage > 0 or employee_used_wage < 0:
            # if employee instant used wage is greater or les than zero, owing instant wage
            pass
        else:
            all_payroll = PayrollTable.objects.filter(
                company=company,
                payroll_user=employee,
                status="DISBURSED",
                payroll_deleted=False,
            )
            if all_payroll:
                # print(all_payroll, "all_payroll")
                if company_instant_wage:
                    company_data = this_company_data.filter(company=company)
                    last_payment = all_payroll.last().bulk_id
                    # get last payment and check if it is the same with the current month
                    last_payment_ins = company_data.filter(bulk_id=last_payment).first()
                    if last_payment_ins:
                        # print(last_payment_ins, "last_payment_ins")
                        last_pay_date = f"{last_payment_ins.payroll_year}-{last_payment_ins.payroll_month}-01"
                        last_parts = last_pay_date.split("-")
                        # Convert the month name to numerical value
                        last_date_month = month_map[last_parts[1].lower()]
                        # Create a datetime object
                        last_date_object = datetime(
                            int(last_parts[0]), last_date_month, int(last_parts[2])
                        )
                        # print(last_date_object.date(), "last_date_object.date()", datetime.now().date(), "datetime.now().date()")
                        if last_date_object.date() != datetime.now().date():
                            all_pay_count = (
                                all_payroll.aggregate(Count("id"))["id__count"] or 0
                            )
                            print(all_pay_count, "all_pay_count > ", const_pay_count)
                            if all_pay_count >= const_pay_count:
                                # print("PASS 3")
                                first_payment = all_payroll.first().bulk_id
                                # print(first_payment, "first_payment")
                                first_payment_ins = company_data.filter(
                                    bulk_id=first_payment
                                ).first()
                                # print(first_payment_ins, "first_payment_ins")
                                first_pay_date = f"{first_payment_ins.payroll_year}-{first_payment_ins.payroll_month}-01"
                                # Split the date string into parts
                                parts = first_pay_date.split("-")

                                # Convert the month name to numerical value
                                month = month_map[parts[1].lower()]

                                # Create a datetime object
                                date_object = datetime(
                                    int(parts[0]), month, int(parts[2])
                                )

                                # Calculate the difference in months
                                # print(date_object, "date_object", datetime.now(), "datetime.now()")
                                delta = relativedelta(datetime.now(), date_object)
                                total_months = delta.years * 12 + delta.months
                                # print(total_months, "total month")
                                # print(all_pay_count, " <= ", int(total_months))
                                # check if all salary count is less than the current month
                                if all_pay_count <= int(total_months):
                                    # print("PASS 4")

                                    total_net_pay = employee_payable * all_pay_count
                                    sum_payroll = (
                                        all_payroll.aggregate(Sum("payable_amount"))[
                                            "payable_amount__sum"
                                        ]
                                        or 0
                                    )

                                    # print(total_net_pay, " total net pay > " , "sum payroll ",sum_payroll)
                                    # check if the mean of total employee payroll is less than the total net pay
                                    if total_net_pay >= sum_payroll:
                                        # print("PASS 5")
                                        try:
                                            mean_payroll = sum_payroll / all_pay_count
                                        except ZeroDivisionError:
                                            mean_payroll = 0

                                        company_employee.employee_instant_daily_wage = (
                                            True
                                        )
                                        company_employee.set_instant_wage_amount = (
                                            round_amount(mean_payroll)
                                        )
                                        company_employee.employee_instant_wage_status = (
                                            "OFF"
                                        )
                                        company_employee.save()
                                    else:
                                        pass
                                else:
                                    pass
                            else:
                                pass
                        else:
                            pass
                    else:
                        pass

                else:
                    pass
            else:
                pass

    return "INSTANT WAGE CHECKER FOR EMPLOYEES"


@shared_task
def send_money_one_click_account_task(
    user,
    one_click_type,
    account_ins,
    disburse_pin,
    account_number,
    account_name,
    bank_name,
    bank_code,
    amount,
    narration,
    charge_fee,
    one_click_id,
):
    from payroll_app.models import OneClickTransaction

    all_payment = []
    payed = OneClickTransaction.objects.filter(transaction_id=one_click_id).first()
    if payed:
        if not payed.disbursed:
            user_ins = User.objects.get(id=user)
            account_inst = AccountSystem.objects.get(id=account_ins)

            # new send money
            send_money = Transaction.vfd_funds_transfer(
                bank_code=bank_code,
                bank_name=bank_name,
                account_name=account_name,
                account_number=account_number,
                narration=narration,
                amount=amount,
                user=user_ins,
                account=account_inst,
                charge_fee=charge_fee
            )
            all_payment.append(send_money)
            if isinstance(send_money, str):
                payed.status_code = "00"
                payed.disbursed = True
                payed.status = "SUCCESSFUL"
                payed.save()

            else:
                payed.status_code = "04"
                payed.status = "FAILED"
                payed.save()

    return f"SEND MONEY ONE CLICK DONE {all_payment}"


# @shared_task
# def create_company_payroll_settings(company, user):
#     from payroll_app.models import CompanyPayrollSettings
#     payroll_setting_data = CompanyPayrollSettings.objects.filter(company=company).first()
#     if payroll_setting_data:
#         basic_data = SalaryComponentSettings.objects.filter(company=company,salary_name="Basic").first()
#         if basic_data:
#            pass
#         else:
#             SalaryComponentSettings.objects.create(
#                     company=company,
#                     employer=user,
#                     salary_name= "Basic",
#                     default_component=True,
#                 )
#         transport_data = SalaryComponentSettings.objects.filter(company=company,salary_name="Transport").first()
#         if transport_data:
#             pass
#         else:
#             SalaryComponentSettings.objects.create(
#                 company=company,
#                 employer=user,
#                 salary_name= "Transport",
#                 default_component=True,
#             )
#         housing_data = SalaryComponentSettings.objects.filter(company=company,salary_name="Housing").first()
#         if housing_data:
#             pass
#         else:
#             SalaryComponentSettings.objects.create(
#                 company=company,
#                 employer=user,
#                 salary_name= "Housing",
#                 default_component=True,
#             )
#     else:
#         CompanyPayrollSettings.objects.create(
#             company=company,
#             employer=user,
#             company_name=company.company_name,
#             is_active=True,
#             )
#         basic_data = SalaryComponentSettings.objects.filter(company=company,salary_name="Basic").first()
#         if basic_data:
#            pass
#         else:
#             SalaryComponentSettings.objects.create(
#                     company=company,
#                     employer=user,
#                     salary_name= "Basic",
#                     default_component=True,
#                 )
#         transport_data = SalaryComponentSettings.objects.filter(company=company,salary_name="Transport").first()
#         if transport_data:
#             pass
#         else:
#             SalaryComponentSettings.objects.create(
#                 company=company,
#                 employer=user,
#                 salary_name= "Transport",
#                 default_component=True,
#             )
#         housing_data = SalaryComponentSettings.objects.filter(company=company,salary_name="Housing").first()
#         if housing_data:
#             pass
#         else:
#             SalaryComponentSettings.objects.create(
#                 company=company,
#                 employer=user,
#                 salary_name= "Housing",
#                 default_component=True,
#             )

#     return "PAYROLL SETTING AND DEFAULT COMPONENTS CREATED"


@shared_task
def send_payroll_payment_notification_email(
    recipient: str,
    subject: str,
    template_dir: str,
    use_template=True,
    body=None,
    **substitute,
):
    if use_template:
        TEMPLATE_DIR = os.path.join("templates", f"{template_dir}")
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitute)
    else:
        template = None
    try:
        requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_APIKEY}"),
            data={
                "from": "Liberty Pay <<EMAIL>>",
                "to": f"<{recipient}>",
                "subject": f"{subject}",
                "html": f"""{template}""" if use_template else None,
                "text": body if not use_template else None,
            },
        )

        return "EMAIL SENT"
    except Exception as e:
        print("failed to send email", e)
        return "EMAIL FAILED"


@shared_task
def send_payroll_approval_notification_email(
    recipient: str,
    subject: str,
    template_dir: str,
    use_template=True,
    body=None,
    **substitute,
):
    if use_template:
        TEMPLATE_DIR = os.path.join("templates", f"{template_dir}")
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitute)
    else:
        template = None
    try:
        requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_APIKEY}"),
            data={
                "from": "Liberty Pay <<EMAIL>>",
                "to": f"<{recipient}>",
                "subject": f"{subject}",
                "html": f"""{template}""" if use_template else None,
                "text": body if not use_template else None,
            },
        )

        return "EMAIL SENT"
    except Exception as e:
        print("failed to send email", e)
        return "EMAIL FAILED"
    
@shared_task
def send_automated_pfa(
    company_id, 
    payroll_date, 
    company_details_id,
    user_id,
    account_id,
    all_payable_id

):
    from payroll_app.apis.pfa_schedule import pension_data
    from payroll_app.models import PayrollTable, PensionFundAdminSettings, SendPensionData
    from accounting.utils import categorize_transaction
    
    # send_pfa_email = pension_data(company_id, payroll_date, company_details_id, all_payable_id)
    company_ins = Company.objects.get(id=company_id)
    base_qs = PayrollTable.objects.filter(id__in=all_payable_id, company__id=company_id, payroll_deleted=False, employee__pension_fund_admin__isnull=False)
    user_ins = User.objects.get(id=user_id)
    account_inst = AccountSystem.objects.get(id=account_id)
    
    pension_account_ins = AccountSystem.objects.filter(
                    company=company_ins, account_type="PENSION"
                ).first()
    unique_pfa = base_qs.values('employee__pension_fund_admin').distinct()
    # get the constant table
    const_table = ConstantTable.get_constant_instance()
    skip_pension_id = const_table.skip_pension_id
    skip_company_pension_id = const_table.skip_company_pension_id

    for pfa in unique_pfa:
        pfa_id = pfa['employee__pension_fund_admin']

        ### Skip Liberty Assured Leadway Administrator
        if(
            skip_company_pension_id is not None
            and skip_pension_id is not None
        ):
            if (company_id == skip_company_pension_id and pfa_id == skip_pension_id):
                continue
        
        get_pfa = PensionFundAdminSettings.objects.filter(id=pfa_id).first()
        # all_employees = base_qs.filter(employee__pension_fund_admin=get_pfa, employee__pension_pin__isnull=False, pension_amount__gt=0)

        all_employees = base_qs.filter(
            employee__pension_fund_admin=get_pfa, 
            employee__pension_pin__isnull=False
        ).exclude(employee__pension_pin="").filter(pension_amount__gt=0)
        total_amount = all_employees.aggregate(Sum("pension_amount"))["pension_amount__sum"] or 0
        # Create the pension list
        pension_list = [
            {
                "last_name": this_employee.employee.employee_last_name,
                "first_name": this_employee.employee.employee_first_name,
                "pension_amount": this_employee.pension_amount,
                "pension_pin": this_employee.employee.pension_pin,
            }
            for this_employee in all_employees
        ]
        send_pension = SendPensionData.objects.create(
                company = company_ins,
                pension_list = pension_list,
                pfa=get_pfa,
                payroll_date=payroll_date
            )
        if total_amount > 0:
            send_money = Wallet.send_pension_money_via_buddy(
                narration=f"{send_pension.id}-{pfa_id}",
                amount=(total_amount + 150),
                user=user_ins,
                payout_type="PAYROLL",
                account_inst=account_inst,
                receiver=pension_account_ins,
                charge_fee=0
            )

            send_money_status = send_money.get("succeded", False)
            if send_money_status is True:                    
                send_pfa_money = Transaction.vfd_funds_transfer(
                    bank_code=get_pfa.pfa_bank_code,
                    bank_name=get_pfa.pfa_bank_name,
                    account_name=get_pfa.pfa_account_name,
                    account_number=get_pfa.pfa_account_number,
                    narration=f"{company_ins.company_name} Pension Schedule",
                    amount=total_amount,
                    user=user_ins,
                    account=pension_account_ins,
                    pension_data_id=send_pension.id
                )
                if isinstance(send_pfa_money, str):
                    transaction_instance = Transaction.objects.filter(transaction_ref=send_pfa_money).first()
                    if transaction_instance:
                        try:
                            categorize_transaction(
                                transaction_instance,
                                source="hr",
                                transaction_type="regulatory_payments_pensions"
                            )
                        except Exception as e:
                            logger.info(f"AUTOMATE PENSION PAYMENT -> {str(e)}")
    return f"PFA FOR {company_ins.company_name} SUCCESSFUL"

@shared_task
def generate_paybox_id():
    from payroll_app.models import CompanyEmployeeList
    get_all_user = CompanyEmployeeList.objects.all()
    for employee in get_all_user:
        paybox_id = generate_readable_employee_id(employee.id)
        employee.paybox_id = paybox_id
        employee.save()
    
    return f"GENERATED PAYBOX ID FOR ALL EMPLOYEES"

@shared_task
def generate_staff_id():
    from payroll_app.models import CompanyEmployeeList  

    all_employees = CompanyEmployeeList.objects.filter(employee_staff_id__isnull=True, company__isnull=False)
    for employee in all_employees:
        get_staff_id = CompanyEmployeeList.add_generate_staff_id(date=employee.created_at, company=employee.company)
        employee.employee_staff_id = get_staff_id
        employee.save()
    return "SUCCESSFULLY GENERATED STAFF IDS"

@shared_task
def manual_send_automated_pfa(
    payroll_date, 
    user_id,
    account_id,
    pension_list,
    employer_code,
    company_name,
    company_email,
    hr_email
):

    send_pfa_email = manual_pension_data(payroll_date, company_name,  pension_list, user_id, account_id, employer_code, company_email, hr_email)

    return f"MANUAL PFA FOR {company_name} SUCCESSFUL"

@shared_task
def manual_send_automated_pfa_no_payment(
    payroll_date, 
    pension_list,
    employer_code,
    company_name,
    company_email,
    hr_email
):
    send_pfa_email = manual_pension_data_no_payment(payroll_date, company_name,  pension_list, employer_code, company_email, hr_email)
    return f"MANUAL PFA FOR {company_name} SUCCESSFUL"

@shared_task
def automate_send_pfa(
    payroll_date, 
    pfa_id,
    pension_list,
    company_id,
    beneficiary_account_number,
    transaction_type,
    payment_method,
    transaction_date,
    amount,
    beneficiary_account_name,
    transaction_ref,
    pension_data_id,
    session_id
):
    from payroll_app.models import  CompanyPayrollSettings

    payroll_settings_ins = CompanyPayrollSettings.objects.filter(company__id=company_id).first()
    this_company = Company.objects.get(id=company_id)
    if payroll_settings_ins:
        if payroll_settings_ins.hr_email:
            hr_email = payroll_settings_ins.hr_email
        else:
            hr_email = this_company.user.email
        if payroll_settings_ins.company_name:
            company_name = payroll_settings_ins.company_name
        else:
            company_name = this_company.company_name
        employer_code = payroll_settings_ins.employer_pension_code
    else:
        hr_email = this_company.user.email
        employer_code = ""
        company_name = this_company.company_name
    company_email = this_company.user.email
    send_pfa_email = update_single_pfa_pension_data(
        payroll_date, 
        company_name, 
        pension_list,
        pfa_id, 
        employer_code, 
        company_email, 
        hr_email,
        beneficiary_account_number,
        transaction_type,
        payment_method,
        transaction_date,
        amount,
        beneficiary_account_name,
        transaction_ref,
        pension_data_id,
        session_id,
        company_id
    )

    return f"SINGLE PFA FOR {company_name} SUCCESSFUL"

@shared_task
def instant_wage_payment_reminder():
    from payroll_app.models import InstantWagePayroll, CompanyPayrollSettings

    file_path = os.path.join(settings.BASE_DIR, 'payroll_app/media/')

    # Get the start and end dates for the past week (Monday to Friday)
    date_for_filtering = timezone.localdate() if timezone else timezone.now().date()

    all_unpaid_instant_wage = InstantWagePayroll.objects.filter(company_paid=False, employee_due_payment=True)
    all_company_instant_wage = all_unpaid_instant_wage.values('company').distinct()
    for company_instant_wage in all_company_instant_wage:
        this_company = company_instant_wage["company"]
        wage_company = Company.objects.filter(id=this_company).first()
        if not wage_company:
            continue
        payroll_settings_ins = CompanyPayrollSettings.objects.filter(company=wage_company).first()
        if payroll_settings_ins:
            hr_email = payroll_settings_ins.hr_email
            if payroll_settings_ins.company_name:
                this_company_name = payroll_settings_ins.company_name
            else:
                this_company_name = None
        else:
            hr_email = ""
            this_company_name = None
        
        company_name = this_company_name if this_company_name else wage_company.company_name

        # remove last payment reminder for this company
        last_file = os.path.join(f"{file_path}{company_name}_instant_wage_payment_reminder_{date_for_filtering}.xlsx") 
        my_file = Path(last_file) 
        if my_file.is_file(): 
            os.remove(last_file)
        try:
            os.mkdir(file_path)
        except Exception:
            pass 

        this_company_instant_wage = all_unpaid_instant_wage.filter(company=wage_company)
        ##########
        # Extract required fields
        # Annotate with Coalesce to handle None fields
        data = this_company_instant_wage.annotate(
            email=Coalesce('receiver__email', Value('Unknown')),
        ).values( 
            "email", 
            "amount"
        )
        # Create a DataFrame from the data
        instant_wage_data = pd.DataFrame(list(data))
        
        # Optionally, rename columns
        instant_wage_data.rename(columns={
            "email": "Email",
            "amount": "Amount"
        }, inplace=True)

        # Export DataFrame to Excel
        excel_file = f'{file_path}{company_name}_instant_wage_payment_reminder_{date_for_filtering}.xlsx'
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            instant_wage_data.to_excel(writer, index=False, sheet_name='Weekly_Report', header=True)

        instant_wage_data.to_excel(f"{file_path}{company_name}_instant_wage_payment_reminder_{date_for_filtering}.xlsx", index=False, header=True)
        with open(last_file, "rb") as excel_file:
            weekly_report = excel_file.read()
            excel_file.close()

        send_email_with_attachment(
            file = weekly_report,
            template_dir="instant_wage_reminder.html",
            file_name = f"{file_path}{company_name}_instant_wage_payment_reminder_{date_for_filtering}.xlsx",
            subject=f"{company_name} Weekly Report for {date_for_filtering}",
            recipient=wage_company.user.email,
            company_name=wage_company.company_name,
            sender_email=hr_email if hr_email else wage_company.user.email,
            cc_email="<EMAIL>",
        )
        file_name = f"{file_path}{company_name}_instant_wage_payment_reminder_{date_for_filtering}.xlsx"
        try:
            os.remove(f"{file_name}") ## Delete file when done
        except PermissionError:
            pass

    return "REMINDED ALL INSTANT WAGE OWING COMPANIES"

@shared_task
def auto_start_instant_wage():
    from payroll_app.models import CompanyEmployeeList, CompanyPayrollSettings
    # Constants for the current date
    start_date = datetime.today()
    month = start_date.month
    year = start_date.year
    month_days = calendar.monthrange(year, month)[1]
    instant_net_wage_date = start_date.day

    all_company = CompanyPayrollSettings.objects.filter(authorize_instant_wage=True).values_list('company', flat=True).distinct()
    for this_company in all_company:
        company = Company.objects.filter(id=this_company).first()
        if not company:
            continue
        all_active_employees = CompanyEmployeeList.objects.filter(
            company=company,
            is_active=True,
            is_deleted=False,
            employee_instant_daily_wage=True,
            set_instant_wage_amount__gt=0,
        ).exclude(employee_instant_wage_status="SUCCESSFUL")

        # Process each employee
        employees_to_update = []
        for employee in all_active_employees:
            try:
                # Calculate wage-related amounts
                daily_wage_amount = round_amount(employee.set_instant_wage_amount / month_days)
                collected_wage = round_amount(instant_net_wage_date * daily_wage_amount)
                current_daily_wage = round_amount(employee.set_instant_wage_amount - collected_wage)
                net_available_wage = collected_wage

                # Update employee attributes
                employee.employee_instant_wage_status = "SUCCESSFUL"
                employee.employee_instant_daily_wage_date = start_date
                employee.employee_instant_daily_wage_amount = daily_wage_amount
                employee.employee_instant_daily_remaining_wage_amount = current_daily_wage
                employee.employee_instant_daily_collected_wage_amount = collected_wage
                employee.employee_instant_net_available_wage = net_available_wage

                employees_to_update.append(employee)
            except ZeroDivisionError:
                continue

        # Bulk update employees in the database
        if employees_to_update:
            CompanyEmployeeList.objects.bulk_update(
                employees_to_update,
                fields=[
                    'employee_instant_wage_status',
                    'employee_instant_daily_wage_date',
                    'employee_instant_daily_wage_amount',
                    'employee_instant_daily_remaining_wage_amount',
                    'employee_instant_daily_collected_wage_amount',
                    'employee_instant_net_available_wage',
                ]
            )