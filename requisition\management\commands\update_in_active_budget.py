from django.core.management.base import BaseCommand
# from django.db.models import Sum
from django.utils import timezone

from requisition.models import Budget, Expense, BudgetAllocation


# from requisition.tasks import toggle_budget_to_inactive


class Command(BaseCommand):
    help = 'This is my custom management command'

    def handle(self, *args, **options):
        # budgets = Budget.objects.filter(is_active=False, is_deleted=False)
        # for budget in budgets:
        #     allocations = BudgetAllocation.objects.filter(budget=budget).aggregate(
        #         sum_amount=Sum('amount'))["sum_amount"] or 0
        #
        # all_expense = \ Expense.objects.filter(budget=budget, status="SUCCESSFUL").aggregate(sum_amount=Sum(
        # 'expense_amount'))[ "sum_amount"] or 0
        #
        #     to_update_budget = Budget.objects.get(id=budget.id)
        #     to_update_budget.running_balance = float(allocations) - float(all_expense)
        #     to_update_budget.save()
        #     print("Updated budget successfully................................................................")
        # print("DONE :: Finished ::")

        budgets = Budget.objects.filter(is_active=False, is_deleted=False)
        allocations = BudgetAllocation.objects.filter(budget__in=budgets, is_active=True, is_deleted=False)
        allocations.update(is_active=False, updated_at=timezone.now())
        print("UPDATED::")
