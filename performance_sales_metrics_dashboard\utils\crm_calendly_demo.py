from django.db.models import Count
from performance_sales_metrics_dashboard.models import ProductVerticals, Lead, BookADemo, TeamMember
from requisition.helpers.enums import UserRole, UserStatus

def create_demo(
        product_verticals:list,
        start_time,
        validated_data: dict, 
    ):

    all_verticals = ProductVerticals.objects.all()
    
    try:
        vertical_list = [
                            {"vertical": f"{vertical.name}", "key": f"{vertical.pk}"} for vertical in all_verticals
                        ]
        chosen_vertical = [int(vertical["key"]) for vertical in vertical_list if vertical["vertical"] in product_verticals]
    except Exception as e:
        print("An error occurred while trying to get verticals!", str(e))
    
    try:
        lead_instance = Lead.create_lead_online(
            product_vertical=chosen_vertical,
            validated_data=validated_data,
        )
        lead_details = {
            "lead_email": lead_instance.company_email, 
            "lead_name": lead_instance.contact_name, 
            "lead_phone_num": lead_instance.contact_phone_number
        }
    except Exception as e:
        return {
                "message": "An error occurred while trying to create a lead!",
                "error": str(e),
            }
    

    try:
        free_sales_officer = TeamMember.objects.annotate(
            num_leads=Count('leads')
        ).filter(role=UserRole.SALES_OFFICER, is_active=True).order_by('num_leads').first()

        if free_sales_officer:
            lead_instance.sales_officer_in_charge = free_sales_officer
            lead_instance.save()

            demo_instance = BookADemo.objects.create(
                team_member=free_sales_officer,
                lead=lead_instance,
                start_time=start_time,
                is_assigned=True
            )

            return {
                "message": "Lead created and demo booked successfully!",
                "lead_details": lead_details,
                "demo_details": {
                    "demo_id": demo_instance.pk,
                    "sales_officer": free_sales_officer.email,
                    "start_time": demo_instance.start_time
                }
            }
        else:
            return {
                "message": "No available Sales Officer to assign the lead to!"
            }
    except Exception as e:
        return {
            "message": "An error occurred while trying to assign a lead to a sales officer and book a demo!",
            "error": str(e),
        }