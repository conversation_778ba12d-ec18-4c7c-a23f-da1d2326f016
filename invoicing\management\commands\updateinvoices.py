from django.core.management.base import BaseCommand

from invoicing.models import Invoice


class Command(BaseCommand):
    help = "MODIFY INVOICE STATUS."

    def handle(self, *args, **kwargs):
        count = 0
        invoices = Invoice.objects.filter(due=False, paid=False)
        for invoice in invoices:
            invoice.paid = True
            invoice.save()

            count += 1

            if count == 4500:
                break
            else:
                pass
