from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import (
    AccountTypeListView,
    AccountViewSet,
    BalanceSheetReportView,
    JournalEntryViewSet,
    JournalLineViewSet,
    ProfitAndLossReportView,
    TrialBalanceReportView
)

router = DefaultRouter()
router.register(r"accounts", AccountViewSet, basename="accounts")
router.register(r"journal-lines", JournalLineViewSet, basename="journal-lines")
router.register(r"journals", JournalEntryViewSet, basename="journals")

urlpatterns = [
    path("", include(router.urls)),
    path("account-types/", AccountTypeListView.as_view(), name="account-type-list"),
    path(
        "report/profile-and-loss/<str:company_id>/",
        ProfitAndLossReportView.as_view(),
        name="profit-and-loss",
    ),
    path(
        "report/balance-sheet/<str:company_id>/",
        BalanceSheetReportView.as_view(),
        name="balance-sheet",
    ),
    path(
        "report/trial-balance/<str:company_id>/",
        TrialBalanceReportView.as_view(),
        name="trial-balance",
    )
]
