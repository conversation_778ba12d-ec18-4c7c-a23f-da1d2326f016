from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from finance_system.resources import *
# Register your models here.

class CompanyManualBankResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyManualBankResource
    search_fields = search_fields = ["company__company_name", "account_number", "company_owner__email"]
    list_filter = ["is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class JournalResourceAdmin(ImportExportModelAdmin):
    resource_class = JournalResource
    search_fields = search_fields = ["company__company_name", "journal_name", "journal_reference_no"]
    list_filter = ["is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class AccountTypeResourceAdmin(ImportExportModelAdmin):
    resource_class = AccountTypeResource
    search_fields = search_fields = ["account_type_name"]
    list_filter = ["is_active",]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class ChartOfAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = ChartOfAccountResource
    search_fields = search_fields = ["chart_of_account_name", "account_type__account_type_name"]
    list_filter = ["is_deleted",]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(CompanyManualBank, CompanyManualBankResourceAdmin)
admin.site.register(Journal, JournalResourceAdmin)
admin.site.register(AccountType, AccountTypeResourceAdmin)
admin.site.register(ChartOfAccount, ChartOfAccountResourceAdmin)