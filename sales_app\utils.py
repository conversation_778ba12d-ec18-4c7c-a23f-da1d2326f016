import math

from rest_framework import serializers


# Create your utility function(s) here.
def get_vat_value(
    vat: float,
    amount: float,    
):
    return math.ceil((vat / 100) * float(amount))


def validate_customer(customer_id: str):
    from sales_app.models import Customer

    """
    Validate the existence of a customer by its ID.
    Returns:
        Customer: The customer object if it exists.
    Raises:
        serializers.ValidationError: If the customer with the provided ID does not exist.
    """
    try:
        customer = Customer.objects.get(id=customer_id)
    except Customer.DoesNotExist:
        raise serializers.ValidationError({"message": "provide a valid customer ID."})
    return customer


def validate_batch_id(batch_id: str):
    from sales_app.models import SalesTransaction

    """
    Validate the existence of a sales transaction by its batch ID.
    Args:
        batch_id (str): The unique identifier of the sales transaction to validate.
    Returns:
        batch_id(str): The validated batch ID.
    Raises:
        serializers.ValidationError: If no record matches the provided ID.
    """
    sales_transactions = SalesTransaction.objects.filter(batch_id=batch_id)
    if not sales_transactions.exists():
        raise serializers.ValidationError({"message": f"invalid sales batch ID."})
    return batch_id


def validate_number(number: int):
    """
    Validate the number provided to be of positive value.
    Args:
        number (int): The number to validate.
    Returns:
        number (int): The validated number.
    Raises:
        serializers.ValidationError: If the number is invalid.
    """
    if number <= 0:
        raise serializers.ValidationError("A valid integer is required.")
    return True


def validate_amount(amount: float):
    """
    Validate the amount provided to be of positive value.
    Args:
        amount (int): The amount to validate.
    Returns:
        amount (int): The validated amount.
    Raises:
        serializers.ValidationError: If the amount is invalid.
    """
    if amount < 0:
        raise serializers.ValidationError("A valid amount is required.")
    return True
