from datetime import datetime, timedelta
import json
import pprint
import random

from dateutil import parser
from django.db import IntegrityError
from django.db.models import Q, Prefetch
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
import pytz
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
# from django.db import transaction
# from django.db.models import Max
# from django.http import Http404
from rest_framework.response import Response
from rest_framework.views import APIView

from core.auth.custom_auth import CustomUserAuthentication
from core.models import User
from core.pagenator import CustomPagination
from core.tasks import send_email
from performance_sales_metrics_dashboard.tasks import schedule_bulk_emails, send_single_mails, sync_emails_with_db, \
    retrieve_emails
from performance_sales_metrics_dashboard.utils import google_calendar_api
from performance_sales_metrics_dashboard.utils.email_drafts import save_to_drafts
from performance_sales_metrics_dashboard.utils.gmail_integration import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from performance_sales_metrics_dashboard.utils.gmail_outbox import send_email_with_gmail
from performance_sales_metrics_dashboard.utils.parse_dates import get_emails_within_range, get_upper_date_limit
from performance_sales_metrics_dashboard.utils.retrieve_emails import get_emails_from_server
from performance_sales_metrics_dashboard.utils.utils import encrypt_password
from performance_sales_metrics_dashboard.utils.zoom_integration import ZoomAPIntegration
from performance_sales_metrics_dashboard.utils.zoom_meeting import schedule_demo
from requisition.helpers.enums import UserRole, UserStatus
from requisition.models import Company, TeamMember, Team
from requisition.serializers import ListOfCompanySerializer, PaginatedTeamMembersSerializer, TeamMemberSerializer, \
    TeamSerializer
from .enums import Lead_status
from .models import Activity, Category, Emails, GmailAccount, Lead, Merchants, Notes, Pipeline, SalesLead, \
    SalesOfficer, Stage, Task, ProductVerticals, BookADemo, BookADemo, WorkEmail, NewsLetterSubscription
from .performance_sales_metrics_dashboard_permissions import IsSalesOfficer, IsSalesLead, IsAdmin
from .serializers import ActivitySerializer, GoogleAuthSerializer, BookADemoSerializer, BookADemoSerializer, \
    BookDemoSerializer, \
    CompanyIdSerializer, DeletedEmailSerializer, \
    DraftEmailSerializer, GmailSerializer, GoogleAuthSerializer, GoogleMailSerializer, MailInboxSerializer, \
    ProductVerticalsSerializer, ReadEmailSerializer, SaveDraftSerializer, \
    ScheduleZoomToGoogleSerializer, \
    ScheduledEmailSerializer, SentEmailSerializer, ReadProductVerticalsSerializer, WorkEmailSerializer, \
    NewsLetterSubscriptionSerializer
from .serializers import CreateLeadSerializer, OnboardCompanySerializer, \
    PipelineSerializer, ReadLeadSerializer, RefreshEmailSerializer, SalesLeadSerializer, SalesOfficerSerializer, \
    CreateStageSerializer, SendBulkEmailSerializer, TaskSerializer, NotesSerializer, ReadStageSerializer, \
    CategorySerializer, \
    ReadPipelineSerializer, ReadActivitySerializer, ReadTaskSerializer, ReadNotesSerializer, ReadCategorySerializer, \
    CheckFreeBusySerializer, CreateCalendarSerializer, EventsSerializer, InboxSerializer, \
    SendEmailSerializer, EmailSerializer, ReadStageAndLeadSerializer, ReadSalesOfficerSerializer, \
    ReadSalesLeadSerializer
from .utils.google_calendar_api import schedule_event_on_google_calendar, retrieve_event_from_google_calendar, \
    create_calendar, retrieve_calendar, check_free_busy_status
from .utils.book_a_demo import book_demo
from .utils.sent_emails import save_sent_emails
from .utils.read_emails import save_read_emails
from .utils.parse_dates import get_time_max
from celery.result import AsyncResult
from django.core.cache import cache


# from .utils import stage_notification
# from .models import SalesTeam, Appointment


def pagination_factor(self, request, pagination_class, instance, serializer):
    page = pagination_class.paginate_queryset(instance, request, view=self)
    serialized_results = serializer(page, many=True)
    paginated_data = pagination_class.get_paginated_response(serialized_results.data)
    return paginated_data


class UserStatusConfirmationAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        user = request.user
        user_id = request.GET.get("user_id")
        company_instances = Company.objects.filter(user=user)
        company_names = list(company_instances.values_list("name", flat=True))

        user_data = {
            "user_id": user.id,
            "user_full_name": user.get_full_name(),
            "user_first": user.first_name,
            "user_last": user.last_name,
            "user_phone_number": user.phone_no,
            "user_email": user.email,
            "user_bvn_first_name": user.bvn_first_name,
            "user_bvn_last_name": user.bvn_last_name,
            "user_state": user.state,
            "user_lga": user.lga,
            "Companies": company_names
        }

        return Response(
            {
                "message": "User exists.",
                "data": user_data,
            },
            status=status.HTTP_200_OK
        )


class AdminStatus(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        user = request.user
        team_id = request.GET.get("team_id")
        team_data = []
        admin_profile_listing = [UserRole.ADMIN, UserRole.OWNER, UserRole.SALES_SUPER_ADMIN, UserRole.SUPERVISOR]

        try:
            team_instance = Team.objects.get(id=team_id)
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if user:
            user_data = {
                "user_id": user.id,
                "user_email": user.email,
                "user_full_name": user.get_full_name(),
                "user_first": user.first_name,
                "user_last": user.last_name,
                "user_bvn_first_name": user.bvn_first_name,
                "user_bvn_last_name": user.bvn_last_name,
                "user_state": user.state,
                "user_lga": user.lga
            }

            try:
                team_member_instance = TeamMember.objects.get(member=user.id,
                                                              team=team_instance,
                                                              role__in=admin_profile_listing,
                                                              status=UserStatus.ACTIVE,
                                                              is_deleted=False)
                serialize_team_member_instance = TeamMemberSerializer(team_instance)

                return Response(
                    {
                        "message": "User is an admin.",
                        "data": [
                            {
                                "team_role": team_member_instance.role,
                                "team_id": team_instance.id,
                                "team_name": team_instance.team_name,
                                "company_id": team_instance.company.id,
                                "company_name": team_instance.company.company_name,
                                "user_data": user_data,
                                "team_member_data": serialize_team_member_instance.data
                            }
                        ]
                    },
                    status=status.HTTP_200_OK
                )

            except TeamMember.DoesNotExist:
                team_member_instance = TeamMember.objects.get(member=user.id,
                                                              team=team_instance,
                                                              status=UserStatus.ACTIVE,
                                                              is_deleted=False)
                serialize_team_member_instance = TeamMemberSerializer(team_instance)
                return Response(
                    {
                        "message": "User is not an Admin.",
                        "data": [
                            {
                                "team_role": team_member_instance.role,
                                "team_id": team_instance.id,
                                "team_name": team_instance.team_name,
                                "company_id": team_instance.company.id,
                                "company_name": team_instance.company.company_name,
                                "user_data": user_data,
                                "team_member_data": serialize_team_member_instance.data}
                        ]
                    },
                    status=status.HTTP_200_OK
                )


class NewsLetterSubscriptionAPIView(APIView):
    serializer_class = NewsLetterSubscriptionSerializer

    def get(self, request):
        serializer = self.serializer_class
        email = request.GET.get("email")

        try:
            subscriber_instance = NewsLetterSubscription.objects.get(email=email)
        except NewsLetterSubscription.DoesNotExist:
            return Response(
                {"message": "Email does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )
        serialized_result = serializer(subscriber_instance)
        return Response(
            {
                "message": "Subscriber exists",
                "data": serialized_result.data
            },
            status=status.HTTP_200_OK
        )

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data.get("email")

        try:
            old_subscriber = NewsLetterSubscription.objects.get(email=email)
            return Response(
                {"message": "You have previously subscribed to our News Letter."},
                status=status.HTTP_200_OK
            )
        except NewsLetterSubscription.DoesNotExist:
            pass

        try:
            new_subscriber = NewsLetterSubscription.create(serializer.validated_data)
        except:
            return Response(
                {"message": "Something went wrong, please try again"},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response(
            {"message": "Thank you for subscribing."},
            status=status.HTTP_200_OK
        )


class GetTeamByCompany(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            if team_id:
                team_instance = Team.objects.filter(id=team_id, member=user, company=company_instance)
            else:
                team_instance = Team.objects.filter(member=user, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        number_of_teams = team_instance.count()
        serialized_results = TeamSerializer(team_instance, many=True)
        return Response(
            {
                "message": "Teams retrieved.",
                "number_of_teams": number_of_teams,
                "data": serialized_results.data
            },
            status=status.HTTP_200_OK
        )


class AdminPipelineOverviewAPIView(APIView):
    permission_classes = [IsAdmin]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request):
        # category = request.GET.get("category_id")
        # team = request.GET.get("team_id")
        # param = request.GET.get("param")
        user = request.user
        query = Q()
        paginated_data = {}
        category_with_stage = []
        leads_in_category = []

        try:
            # user = User.objects.get(user=user)
            # stage_instance = Stage.objects.get(category=category_instance)
            # serialize_stage = SuperAdminStage(stage_instance)
            # category_with_stage = {"Category":category_instance}
            # {category: [] for category in default_categories}
            # category_with_stage = {category: [] for category in default_categories}
            company_instance = Company.objects.get(company__user=user)
            category_instance = Category.objects.filter(company=company_instance)

            for category in category_instance:
                # stage_instance = Stage.objects.filter(category=category_instance)
                # serialize_stage = SuperAdminStage(stage_instance, many=True)

                # stages = Stage.objects.filter(category=category_instance).order_by('-created_at').prefetch_related(
                #     Prefetch(
                #         'lead_set', queryset=Lead.objects.only('id', 'contact_name').order_by('-id'), to_attr='leads'
                #     )
                # )

                # serialized_stages = ReadStageAndLeadSerializer(stages, many=True, context={'leads': True})

                # data = {
                #     "Category": category.name,
                #     "Stages": serialized_stages.data
                # }

                # category_with_stage.append(data)

                lead_instances = Lead.objects.filter(stage__category=category)
                serialized_lead = ReadLeadSerializer(lead_instances, many=True)
                data = {
                    "Category": category.name,
                    "all_leads_in_category_data": serialized_lead.data,
                    # "Stages": serialized_stages.data
                }
                category_with_stage.append(data)
        except User.DoesNotExist:
            return Response(
                {
                    "message": "User does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        return Response(
            {
                "message": "Data Retrieved Successfully.",
                "data": category_with_stage
            },
            status=status.HTTP_200_OK
        )

        # if pipeline_id:
        #     try:


class GetLeadsInATeam(APIView):
    permission_classes = [IsAdmin]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        category_id = request.GET.get("category_id")
        admin_profile_listing = [UserRole.ADMIN, UserRole.OWNER, UserRole.SALES_SUPER_ADMIN, UserRole.SUPERVISOR]
        query = Q()
        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            team_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=team_instance,
                                               serializer=TeamSerializer
                                               )
            return Response(
                {
                    "message": "Team does not exist",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            admin_instance = TeamMember.objects.get(
                member=user,
                team=team_instance,
                role__in=admin_profile_listing,
                status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            admin_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=admin_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Admin does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if category_id:
            try:
                category_instance = Category.objects.get(id=category_id, team=team_instance)

                lead_instances = Lead.objects \
                    .filter(sales_officer_in_charge__team=team_instance, stage__category=category_instance) \
                    .order_by('-created_at')

            except Category.DoesNotExist:
                category_instance = []
                paginated_data = pagination_factor(self=self,
                                                   pagination_class=self.pagination_class,
                                                   request=request,
                                                   instance=category_instance,
                                                   serializer=ReadCategorySerializer
                                                   )
                return Response(
                    {
                        "message": "Category does not exist",
                        "paginated_results": paginated_data.data
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            except Lead.DoesNotExist:
                leads_instances = []
                paginated_data = pagination_factor(self=self,
                                                   pagination_class=self.pagination_class,
                                                   request=request,
                                                   instance=leads_instances,
                                                   serializer=ReadLeadSerializer
                                                   )
                return Response(
                    {
                        "message": "Lead does not exist.",
                        "paginated_results": paginated_data.data
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        else:
            try:
                lead_instances = Lead.objects \
                    .filter(sales_officer_in_charge__team=team_instance) \
                    .order_by('-created_at')
            except Lead.DoesNotExist:
                leads_instances = []
                paginated_data = pagination_factor(self=self,
                                                   pagination_class=self.pagination_class,
                                                   request=request,
                                                   instance=leads_instances,
                                                   serializer=ReadLeadSerializer
                                                   )

                return Response(
                    {
                        "message": "Leads do not exist in this teams CRM.",
                        "paginated_results": paginated_data.data
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        page = self.pagination_class.paginate_queryset(lead_instances, request, view=self)
        if page is not None:
            serialized_results = ReadLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


# from .tasks import sync_emails


# User = get_user_model()

# class SalesLeadOnboardingAPIView(APIView):
#     permission_classes = [IsAuthenticated]
#
#     def post(self, request):
#         user = request.user
#         serializer = SalesLeadSerializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         product_verticals = serializer.validated_data['product_verticals']
#
#         sales_lead, created = SalesLead.objects.get_or_create(
#             user=user,
#             defaults={
#                 'name': user.get_full_name(),
#                 'email': user.email,
#                 'product_verticals': product_verticals,
#             }
#         )
#
#         if not created:
#             return Response({"detail": "Sales lead already exists."}, status=status.HTTP_400_BAD_REQUEST)
#
#         return Response(serializer.data, status=status.HTTP_201_CREATED)
class SalesLeadOnboardingAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request):
        sales_lead_id = request.GET.get("sales_lead_id")
        param = request.GET.get("param")
        paginated_data = {}
        query = Q()

        if sales_lead_id:

            try:
                sales_lead_instance = SalesLead.objects.get(id=sales_lead_id)
                serialized_results = ReadSalesLeadSerializer(sales_lead_instance)
                return Response(
                    {"message": "Sales Lead Found", "data": serialized_results.data},
                    status=status.HTTP_200_OK
                )
            except SalesLead.DoesNotExist:
                return Response(
                    {"message": "Sales Lead does not exist.", "data": []
                     },
                    status=status.HTTP_404_NOT_FOUND
                )

        elif param:
            query &= Q(name__icontains=param) | Q(phone_number__icontains=param) | Q(email__icontains=param) | \
                     Q(product_verticals__icontains=param) | Q(company__icontains=param)

            sales_lead_instance = SalesLead.objects.filter(query).order_by('-created_at')

        else:
            sales_lead_instance = SalesLead.objects.all().order_by('-created_at')

        total_number_of_sales_lead = sales_lead_instance.count()
        if total_number_of_sales_lead == 0:
            page = self.pagination_class.paginate_queryset(sales_lead_instance, request, view=self)
            serialized_results = ReadSalesLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Sales Lead Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )

        page = self.pagination_class.paginate_queryset(sales_lead_instance, request, view=self)
        if page is not None:
            serialized_results = ReadSalesLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = SalesLeadSerializer(data=request.data)

        if serializer.is_valid():
            company_id = request.data.get("company")
            try:
                company_instance = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.", "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            sales_lead_instance = SalesLead.create_sales_lead(user=user, validated_data=serializer.validated_data)
            serialized_sales_lead_instance = ReadSalesLeadSerializer(sales_lead_instance)

            return Response(
                {
                    "message": "Sales Lead created successfully.",
                    "data": serialized_sales_lead_instance.data
                },
                status=status.HTTP_201_CREATED
            )

    def put(self, request, pk):
        sales_lead_instance = get_object_or_404(SalesLead, pk=pk)

        serializer = ReadSalesLeadSerializer(instance=sales_lead_instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "message": "Successful",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK)

        return Response(
            {
                "message": serializer.errors,
                "data": []
            },
            status=status.HTTP_200_OK
        )


# class SalesLeadOnboardingAPIView(APIView):
#     permission_classes = [IsAuthenticated]
#
#     def post(self, request, *args, **kwargs):
#         serializer = SalesLeadSerializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#
#         SalesLead.create_sales_lead(
#             user=request.user,
#             **serializer.validated_data
#         )
#
#         return Response({"detail": "Sales Lead successfully onboarded."}, status=status.HTTP_201_CREATED)


# class SalesOfficerOnboardingAPIView(APIView):
#     # permission_classes = [IsAuthenticated]
#
#     def post(self, request):
#         user = request.user
#         sales_lead_id = request.data.get('sales_lead_id')
#         sales_lead = get_object_or_404(SalesLead, id=sales_lead_id)
#
#         referral_code = generate_random_referral_code(6)
#         product_vertical = sales_lead.product_verticals
#
#         serializer = SalesOfficerSerializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#
#         sales_officer, created = SalesOfficer.objects.get_or_create(
#             user=user,
#             sales_lead=sales_lead,
#             defaults={
#                 'name': user.get_full_name(),
#                 'email': user.email,
#                 'product_vertical': product_vertical,
#                 'referral_code': referral_code,
#
#             }
#         )
#
#         if not created:
#             return Response({"detail": "Sales officer already exists."}, status=status.HTTP_400_BAD_REQUEST)
#
#         return Response(serializer.data, status=status.HTTP_201_CREATED)

#
# class SalesOfficerOnboardingAPIView(APIView):
#     # permission_classes = [IsAuthenticated]
#
#     def post(self, request):
#         user = request.user
#         sales_lead_id = request.data.get('sales_lead_id')
#
#         try:
#             sales_officer = SalesOfficer.create_sales_officer(user, sales_lead_id, **request.data)
#         except ValidationError as e:
#             return Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
#
#         serializer = SalesOfficerSerializer(sales_officer)
#         return Response(serializer.data, status=status.HTTP_201_CREATED)


class SalesOfficerOnboardingAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request):
        sales_officer_id = request.GET.get("sales_officer_id")
        param = request.GET.get("param")
        paginated_data = {}
        query = Q()

        if sales_officer_id:

            try:
                sales_officer_instance = SalesOfficer.objects.get(id=sales_officer_id)
                serialized_results = ReadSalesOfficerSerializer(sales_officer_instance)
                return Response(
                    {"message": "Sales Officer Found", "data": serialized_results.data},
                    status=status.HTTP_200_OK
                )

            except SalesOfficer.DoesNotExist:
                return Response(
                    {"message": "Sales Officer does not exist.", "data": []
                     },
                    status=status.HTTP_404_NOT_FOUND
                )

        elif param:
            query &= Q(name__icontains=param) | Q(phone_number__icontains=param) | Q(email__icontains=param)

            sales_officer_instance = SalesOfficer.objects.filter(query).order_by('-created_at')

        else:
            sales_officer_instance = SalesOfficer.objects.all().order_by('-created_at')

        total_number_of_sales_officers = sales_officer_instance.count()
        if total_number_of_sales_officers == 0:
            page = self.pagination_class.paginate_queryset(sales_officer_instance, request, view=self)
            serialized_results = ReadSalesOfficerSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Sales Officer Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )

        page = self.pagination_class.paginate_queryset(sales_officer_instance, request, view=self)
        if page is not None:
            serialized_results = ReadSalesOfficerSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )

    def post(self, request):
        user = request.user
        serializer = SalesOfficerSerializer(data=request.data)

        if serializer.is_valid():
            sales_lead_id = request.data.get("sales_lead")
            try:
                sales_lead_instance = SalesLead.objects.get(id=sales_lead_id)
            except SalesLead.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Lead does not exist.", "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            sales_officer_instance = SalesOfficer.create_sales_officer(user=user,
                                                                       validated_data=serializer.validated_data)
            serialized_sales_officer_instance = ReadSalesOfficerSerializer(sales_officer_instance)
            return Response(
                {
                    "message": "Sales Officer created successfully.",
                    "data": serialized_sales_officer_instance.data
                },
                status=status.HTTP_201_CREATED
            )

    def put(self, request, pk):
        sales_officer_instance = get_object_or_404(SalesOfficer, pk=pk)

        serializer = ReadSalesOfficerSerializer(instance=sales_officer_instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "message": "Successful",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK)

        return Response(
            {
                "message": serializer.errors,
                "data": []
            },
            status=status.HTTP_200_OK
        )


class DeleteSalesOfficerAPIView(APIView):
    permission_classes = [IsAuthenticated, IsSalesLead]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def delete(self, request):
        user = request.user
        sales_officer_id = request.GET.get("sales_officer_id")

        if sales_officer_id:

            try:
                sales_lead_instance = SalesLead.objects.get(user=user)
            except SalesLead.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Lead does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = SalesOfficer.objects.get(id=sales_officer_id)

            except SalesOfficer.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            sales_officer_instance.delete()
            return Response({"message": "Sales Officer has been deleted"}, status=status.HTTP_204_NO_CONTENT)
        return Response(
            {
                "message": "",
                "data": []
            },
            status=status.HTTP_400_BAD_REQUEST
        )


class SalesOfficerOnboardingCompanyListCreateAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        referral_code = request.data.get('referral_code')
        company_id = request.data.get('company_id')

        if not referral_code or not company_id:
            return Response({"detail": "Referral code and company ID are required."},
                            status=status.HTTP_400_BAD_REQUEST)

        company = get_object_or_404(Company, id=company_id)
        sales_officer = get_object_or_404(SalesOfficer, referral_code=referral_code)

        if company.sales_officer:
            return Response({"detail": "Company is already onboarded by a sales officer."},
                            status=status.HTTP_400_BAD_REQUEST)

        company.sales_officer = sales_officer
        company.referral_code = sales_officer.referral_code
        company.product_vertical = sales_officer.product_vertical
        company.save()

        merchant = Merchants.objects.create(company=company, sales_officer=sales_officer)
        merchant.create_revenue_lines()

        serializer = OnboardCompanySerializer(company)
        return Response({"status": "Success", "data": serializer.data}, status=status.HTTP_200_OK)


# class ProspectAPIView(APIView):
#     permission_classes = [IsAuthenticated]
#     # permission_classes = [IsSalesOfficer]
#     # permission_classes = [IsSalesLead]
#     authentication_classes = [CustomUserAuthentication]
#     pagination_class = CustomPagination
#
#     def get(self, request, *args, **kwargs):
#         user = request.user
#
#         prospect_id = request.GET.get("lead_id")
#
#         try:
#             sales_officer_instance = SalesOfficer.objects.get(user=user)
#         except SalesOfficer.DoesNotExist:
#             return Response(
#                 {"message": "Sales Officer does not exist."},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         user = request.user
#
#         prospect_id = request.GET.get("lead_id")
#
#         try:
#             sales_officer_instance = SalesOfficer.objects.get(user=user)
#         except SalesOfficer.DoesNotExist:
#             return Response(
#                 {"message": "Sales Officer does not exist."},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#
#         if prospect_id:
#             try:
#                 # prospect_instance = Lead.objects.get(id=prospect_id, entity=Entity.PROSPECT)
#                 prospect_instance = Lead.objects.get(id=prospect_id, sales_officer=sales_officer_instance,
#                                                      entity=Entity.PROSPECT)
#                 # prospect_instance = Lead.objects.get(id=prospect_id, entity=Entity.PROSPECT)
#                 prospect_instance = Lead.objects.get(id=prospect_id, sales_officer=sales_officer_instance,
#                                                      entity=Entity.PROSPECT)
#                 serialized_results = ReadLeadSerializer(prospect_instance)
#                 return Response(
#                     {"message": "Prospect Found", "data": serialized_results.data},
#                     status=status.HTTP_200_OK
#                 )
#
#             except Lead.DoesNotExist:
#                 return Response(
#                     {"message": "Prospect does not exist."},
#                     status=status.HTTP_404_NOT_FOUND
#                 )
#
#         else:
#             # prospect_instances = Lead.objects.filter(entity=Entity.PROSPECT)
#             prospect_instances = Lead.objects.filter(sales_officer=sales_officer_instance, entity=Entity.PROSPECT)
#             serialized_results = ReadLeadSerializer(prospect_instances, many=True)
#             total_number_of_leads = prospect_instances.count()
#             return Response(
#                 {
#                     "Number of results": total_number_of_leads,
#                     "Results": serialized_results.data
#                 },
#                 status=status.HTTP_200_OK)
#

class LeadAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        lead_id = request.GET.get("lead_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if lead_id:
            try:
                lead_instance = Lead.objects.get(id=lead_id, sales_officer_in_charge=sales_officer_instance)
                serialized_results = ReadLeadSerializer(lead_instance)
                return Response(
                    {
                        "message": "Lead Found",
                        "data": serialized_results.data}
                    ,
                    status=status.HTTP_200_OK
                )

            except Lead.DoesNotExist:
                return Response(
                    {"message": "Lead does not exist.", "data": []
                     },
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(
            {
                "message": "Lead cannot be null",
                "data": []
            },
            status=status.HTTP_200_OK
        )

    def post(self, request):
        user = request.user
        company_id = request.data.get("company_id")
        team_id = request.data.get("team_id")
        pipeline_id = request.data.get("pipeline")
        stage_id = request.data.get("stage")
        product_vertical_id = request.data.get("product_vertical")
        list_of_approved_verticals = []
        list_of_unapproved_verticals = []

        serializer = CreateLeadSerializer(data=request.data)

        if serializer.is_valid():
            if pipeline_id:
                try:
                    pipeline_instance = Pipeline.objects.get(id=pipeline_id)
                except Pipeline.DoesNotExist:
                    return Response(
                        {"message": "Pipeline does not exist.", "data": []},
                        status=status.HTTP_404_NOT_FOUND
                    )

                try:
                    stage_instance = Stage.objects.get(id=stage_id, pipeline=pipeline_id)
                except Stage.DoesNotExist:
                    return Response(
                        {"message": "Stage is either null or does not exist.", "data": []},
                        status=status.HTTP_404_NOT_FOUND
                    )

            try:
                company_instance = Company.objects.get(id=company_id)
                team_instance = Team.objects.get(id=team_id, company=company_instance)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            except Team.DoesNotExist:
                return Response(
                    {
                        "message": "Team does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {"message": "Sales Officer does not exist.", "data": []
                     },
                    status=status.HTTP_404_NOT_FOUND
                )

            if product_vertical_id:
                for vertical in product_vertical_id:
                    try:
                        product_vertical_instance = ProductVerticals.objects.get(id=vertical)
                        list_of_approved_verticals.append(product_vertical_instance)
                    except ProductVerticals.DoesNotExist:
                        list_of_unapproved_verticals.append(vertical)

            lead_instance = Lead.create_lead(sales_officer=sales_officer_instance,
                                             product_vertical=list_of_approved_verticals,
                                             validated_data=serializer.validated_data)
            serialized_lead_instance = ReadLeadSerializer(lead_instance)

            return Response(
                {
                    "message": "Lead created successfully.",
                    "vertical_ids_that_do_not_exist": list_of_unapproved_verticals,
                    "data": serialized_lead_instance.data,
                },
                status=status.HTTP_201_CREATED)

        else:
            return Response(
                {
                    "message": "The data entered is incorrect, please try again.",
                    "error_details": serializer.errors,
                    "data": []
                },
                status=status.HTTP_400_BAD_REQUEST
            )

    def patch(self):
        pass

    def put(self, request, pk):
        lead_instance = get_object_or_404(Lead, pk=pk)

        serializer = ReadLeadSerializer(instance=lead_instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "message": "Successful",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK)

        return Response(
            {
                "message": serializer.errors,
                "data": []
            },
            status=status.HTTP_200_OK
        )

    def delete(self, request):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        lead_id = request.GET.get("lead_id")

        if lead_id:

            try:
                company_instance = Company.objects.get(id=company_id)
                team_instance = Team.objects.get(id=team_id, company=company_instance)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            except Team.DoesNotExist:
                return Response(
                    {
                        "message": "Team does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                lead_instance = Lead.objects.get(id=lead_id, sales_officer_in_charge=sales_officer_instance)
            except Lead.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            lead_instance.delete()
            return Response({"message": "Lead has been deleted"}, status=status.HTTP_204_NO_CONTENT)
        return Response(
            {
                "message": "Something went wrong. Please try again.",
                "data": []
            },
            status=status.HTTP_400_BAD_REQUEST
        )


class GetMultipleLeadAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        lead_status = request.GET.get("lead_status")
        lead_onboard_status = request.GET.get("lead_onboard_status")
        param = request.GET.get("param")
        query = Q()
        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )
        except Team.DoesNotExist:
            team_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=team_instance,
                                               serializer=TeamSerializer
                                               )

            return Response(
                {
                    "message": "Team does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            sales_officer_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=sales_officer_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )

        if lead_status:
            lead_instance = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance, status=lead_status) \
                .order_by('-created_at')

        elif lead_onboard_status:
            lead_instance = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance,
                                                onboard_status=lead_onboard_status).order_by('-created_at')

        elif param:
            query &= Q(id__icontains=param) | Q(contact_name__icontains=param) | \
                     Q(contact_email__icontains=param) | Q(contact_phone_number__icontains=param) | \
                     Q(company_name__icontains=param) | Q(company_email__icontains=param) | \
                     Q(company_phone_number__icontains=param)

            lead_instance = Lead.objects.filter(query, sales_officer_in_charge=sales_officer_instance). \
                order_by('-created_at')

        else:
            lead_instance = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance).order_by('-created_at')

        total_number_of_leads = lead_instance.count()
        if total_number_of_leads == 0:
            page = self.pagination_class.paginate_queryset(lead_instance, request, view=self)
            serialized_results = ReadLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Lead Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )

        page = self.pagination_class.paginate_queryset(lead_instance, request, view=self)
        if page is not None:
            serialized_results = ReadLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


class LeadStatusAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request):

        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False)
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.", "data": []
                },
                status=status.HTTP_200_OK
            )

        total_leads = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance).count()
        pending_leads = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance,
                                            status=Lead_status.PENDING).count()
        closed_leads = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance,
                                           status=Lead_status.CLOSED).count()
        active_leads = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance,
                                           status=Lead_status.ACTIVE).count()
        drop_off = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance,
                                       status=Lead_status.DROP_OFF).count()
        awaiting = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance,
                                       status=Lead_status.AWAITING).count()

        data = {
            "total_leads": total_leads,
            "pending_leads": pending_leads,
            "closed_leads": closed_leads,
            "active_leads": active_leads,
            "drop_off": drop_off,
            "awaiting": awaiting,

        }

        return Response(
            {"message": "Pipeline exists", "data": data},
            status=status.HTTP_200_OK
        )


class GetLeadsInAPipeline(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        pipeline_id = request.GET.get("pipeline_id")
        param = request.GET.get("param")
        query = Q()
        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            team_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=team_instance,
                                               serializer=TeamSerializer
                                               )

            return Response(
                {
                    "message": "Team does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            sales_officer_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=sales_officer_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
        except Pipeline.DoesNotExist:
            pipeline_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=pipeline_instance,
                                               serializer=ReadPipelineSerializer
                                               )

            return Response(
                {
                    "message": "Pipeline does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            lead_instances = Lead.objects.filter(pipeline=pipeline_instance).order_by('-created_at')

        except Lead.DoesNotExist:
            leads_instances = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=leads_instances,
                                               serializer=ReadLeadSerializer
                                               )

            return Response(
                {
                    "message": "Lead does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if param:
            query &= Q(id__icontains=param) | Q(contact_name__icontains=param) | \
                     Q(contact_email__icontains=param) | Q(contact_phone_number__icontains=param) | \
                     Q(company_name__icontains=param) | Q(company_email__icontains=param) | \
                     Q(company_phone_number__icontains=param)

            lead_instances = Lead.objects.filter(query, pipeline=pipeline_instance).order_by('-created_at')

        page = self.pagination_class.paginate_queryset(lead_instances, request, view=self)
        if page is not None:
            serialized_results = ReadLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


class GetLeadsNotInAPipeline(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            team_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=team_instance,
                                               serializer=TeamSerializer
                                               )

            return Response(
                {
                    "message": "Team does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            sales_officer_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=sales_officer_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        lead_instance = Lead.objects.filter(sales_officer_in_charge=sales_officer_instance, pipeline=None) \
            .order_by('-created_at')
        serialized_results = ReadLeadSerializer(lead_instance, many=True)

        total_number_of_leads = lead_instance.count()
        if total_number_of_leads == 0:
            page = self.pagination_class.paginate_queryset(lead_instance, request, view=self)
            serialized_results = ReadLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Lead Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )

        paginator = self.pagination_class
        page = paginator.paginate_queryset(lead_instance, request)
        if page is not None:
            serialized_results = ReadLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


class GetExpiredLeads(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        pipeline_id = request.GET.get("pipeline_id")

        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            team_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=team_instance,
                                               serializer=TeamSerializer
                                               )

            return Response(
                {
                    "message": "Team does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            sales_officer_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=sales_officer_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if pipeline_id:
            try:
                pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)

                lead_instance = Lead.objects \
                    .filter(sales_officer_in_charge=sales_officer_instance,
                            pipelne=pipeline_instance,
                            status=Lead_status.DROP_OFF) \
                    .order_by('-created_at')

            except Pipeline.DoesNotExist:
                pipeline_instance = []
                paginated_data = pagination_factor(self=self,
                                                   pagination_class=self.pagination_class,
                                                   request=request,
                                                   instance=pipeline_instance,
                                                   serializer=ReadPipelineSerializer
                                                   )

                return Response(
                    {
                        "message": "Pipeline does not exist.",
                        "paginated_results": paginated_data.data
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            except Lead.DoesNotExist:
                lead_instance = []
                paginated_data = pagination_factor(self=self,
                                                   pagination_class=self.pagination_class,
                                                   request=request,
                                                   instance=lead_instance,
                                                   serializer=ReadLeadSerializer
                                                   )

                return Response(
                    {
                        "message": "Pipeline does not exist.",
                        "paginated_results": paginated_data.data
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        else:
            lead_instance = Lead.objects \
                .filter(sales_officer_in_charge=sales_officer_instance, status=Lead_status.DROP_OFF) \
                .order_by('-created_at')
        serialized_results = ReadLeadSerializer(lead_instance, many=True)

        total_number_of_leads = lead_instance.count()
        if total_number_of_leads == 0:
            page = self.pagination_class.paginate_queryset(lead_instance, request, view=self)
            serialized_results = ReadLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Lead Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )

        paginator = self.pagination_class
        page = paginator.paginate_queryset(lead_instance, request)
        if page is not None:
            serialized_results = ReadLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


class SalesOfficerUploadLeadsViaFileAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def post(self, request):
        user = request.user
        company_id = request.data.get("company_id")
        csv_file = request.data.get("csv_file")
        pipeline_id = request.data.get("pipeline_id")
        stage_id = request.data.get("stage_id")
        product_vertical_id = request.data.get("product_vertical_id")
        list_of_approved_verticals = []
        list_of_unapproved_verticals = []

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if product_vertical_id:
            for vertical in product_vertical_id:
                try:
                    product_vertical_instance = ProductVerticals.objects.get(id=product_vertical_id)
                    list_of_approved_verticals.append(product_vertical_instance)
                except ProductVerticals.DoesNotExist:
                    list_of_unapproved_verticals.append(product_vertical_id)

        if pipeline_id:

            try:
                pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
            except Pipeline.DoesNotExist:
                return Response(
                    {
                        "message": "Pipeline does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            if stage_id:

                try:
                    stage_instance = Stage.objects.get(id=stage_id, pipeline=pipeline_instance)
                except Stage.DoesNotExist:
                    return Response(
                        {
                            "message": "Stage does not exist",
                            "data": []
                        },
                        status=status.HTTP_404_NOT_FOUND
                    )

            else:
                try:
                    stage_instance = Stage.objects.get(pipeline=pipeline_instance, order=1)
                except Stage.DoesNotExist:
                    return Response(
                        {
                            "message": "Pipeline does not have any stage",
                            "data": []
                        },
                        status=status.HTTP_404_NOT_FOUND
                    )

            lead_creation_progress = Lead.create_lead_using_csv_data_upload(
                csv_file=csv_file,
                sales_officer=sales_officer_instance,
                pipeline=pipeline_instance,
                stage=stage_instance,
                product_vertical=list_of_approved_verticals
            )
            stage_instance.update_lead_count()

            return Response(
                {
                    "message": "Process complete.",
                    "Progress Report": lead_creation_progress,
                },
                status=status.HTTP_201_CREATED
            )

        elif csv_file:

            lead_creation_progress = Lead.create_lead_using_csv_data_upload(
                csv_file=csv_file,
                sales_officer=sales_officer_instance,
                pipeline=None,
                stage=None,
                product_vertical=list_of_approved_verticals
            )

            return Response(
                {
                    "message": "Process complete.",
                    "progress_report": lead_creation_progress,
                },
                status=status.HTTP_201_CREATED
            )

        else:
            return Response(
                {
                    "message": "The data entered is incorrect, please try again.",
                    "data": []
                },
                status=status.HTTP_200_OK
            )


class QualifyLeadAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def put(self, request):
        user = request.user
        company_id = request.data.get("company_id")
        lead_ids = request.data.get("lead_ids")
        number_of_qualified_leads = 0
        unqualified_leads = []
        lead_data = []

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.", "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        for lead_id in lead_ids:

            try:
                lead_instance = Lead.objects.get(id=lead_id, sales_officer_in_charge=sales_officer_instance,
                                                 status=Lead_status.PENDING)
                lead_instance.status = Lead_status.ACTIVE
                lead_instance.save()

                number_of_qualified_leads += 1

                serialized_result = ReadLeadSerializer(lead_instance)
                lead_data.append(serialized_result.data)

            except Lead.DoesNotExist:
                unqualified_leads.append(lead_id)

        return Response(
            {
                "message": f"Process Complete. Proceed to add leads to a pipeline.",
                "data": {
                    "Number of Qualified Leads": number_of_qualified_leads,
                    "Lead Data": lead_data,
                    "Number of Unqualified Leads": len(unqualified_leads),
                    "List of failed qualifications": unqualified_leads,
                },
            },
            status=status.HTTP_200_OK)


class MoveLeadToPipelineAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def put(self, request):
        user = request.user
        company_id = request.data.get("company_id")
        team_id = request.data.get("team_id")
        lead_ids = request.data.get("lead_ids")
        pipeline_id = request.data.get("pipeline_id")
        stage_id = request.data.get("stage_id")
        number_of_leads_moved = 0
        list_of_leads_moved = []
        leads_already_in_a_pipeline = []
        lead_not_moved = []

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
        except Pipeline.DoesNotExist:
            return Response(
                {
                    "message": "Pipeline does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if stage_id:
            try:
                stage_instance = Stage.objects.get(id=stage_id, pipeline=pipeline_instance)
            except Stage.DoesNotExist:
                return Response(
                    {
                        "message": "Stage does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        else:
            try:
                stage_instance = Stage.objects.get(pipeline=pipeline_instance, order=1)
            except Stage.DoesNotExist:
                return Response(
                    {
                        "message": "Pipeline does not have any stage.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        for lead_id in lead_ids:
            try:
                lead_instance = Lead.objects.get(id=lead_id, sales_officer_in_charge=sales_officer_instance)

                if lead_instance.pipeline:
                    leads_already_in_a_pipeline.append(lead_id)
                    continue

                if lead_instance.status == Lead_status.PENDING:
                    lead_instance.status = Lead_status.ACTIVE

                lead_instance.pipeline = pipeline_instance
                lead_instance.stage = stage_instance
                lead_instance.save()
                activate_lead_stage = lead_instance.set_active_stage_start_and_expiry_date()
                increase_lead_count_in_new_stage = lead_instance.stage.update_lead_count()

                serialized_result = ReadLeadSerializer(lead_instance)
                list_of_leads_moved.append(serialized_result.data)

                number_of_leads_moved += 1

            except Lead.DoesNotExist:
                lead_not_moved.append(lead_id)

        return Response(
            {
                "message": f"Lead has been moved to {pipeline_instance.name} pipeline, "
                           f"{stage_instance.name}  stage",
                "data": {
                    "number_of_leads_moved": number_of_leads_moved,
                    "list_of_lead_data": list_of_leads_moved,
                    "list_of_leads_already_in_a_pipeline": leads_already_in_a_pipeline,
                    "number_of_failed_migrations": len(lead_not_moved),
                    "list_of_failed_lead_ids": lead_not_moved,
                }
            },
            status=status.HTTP_200_OK
        )


class MoveLeadToNextStageInPipelineAPIView(APIView):
    # permission_classes = [IsAuthenticated]
    permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def put(self, request):
        user = request.user
        lead_ids = request.data.get("lead_id")
        stage_id = request.data.get("stage_id")
        company_id = request.data.get("company_id")
        team_id = request.data.get("team_id")
        paginated_data = {}
        moved_leads = []
        unmatched_ids = []
        new_stage_instance = None

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        for lead_id in lead_ids:
            try:
                lead_instance = Lead.objects.get(id=lead_id, sales_officer_in_charge=sales_officer_instance,
                                                 status=Lead_status.ACTIVE)

                pipeline_instance = lead_instance.pipeline
                if pipeline_instance is not None:
                    old_stage_instance = lead_instance.stage

                    if stage_id:
                        try:
                            new_stage_instance = Stage.objects.get(id=stage_id, pipeline=pipeline_instance)

                            # lead_instance.stage = new_stage_instance
                            # activate_lead_stage = lead_instance.set_active_stage_start_and_expiry_date()
                            # lead_instance.save()
                            # decrease_lead_count_in_old_stage = old_stage_instance.update_lead_count()
                            # increase_lead_count_in_new_stage = new_stage_instance.update_lead_count()

                        except Stage.DoesNotExist:
                            return Response(
                                {
                                    "message": "Stage does not exist.",
                                    "data": []
                                },
                                status=status.HTTP_404_NOT_FOUND
                            )

                    else:

                        try:
                            old_stage_order = old_stage_instance.order
                            new_stage_order = old_stage_order + 1
                            new_stage_instance = Stage.objects.get(pipeline=pipeline_instance, order=new_stage_order)

                            # lead_instance.stage = new_stage_instance
                            # activate_lead_stage = lead_instance.set_active_stage_start_and_expiry_date()
                            # lead_instance.save()
                            # decrease_lead_count_in_old_stage = old_stage_instance.update_lead_count()
                            # increase_lead_count_in_new_stage = new_stage_instance.update_lead_count()

                        except Stage.DoesNotExist:
                            return Response(
                                {
                                    "message": "There is no other stage after this.",
                                    "data": []
                                },
                                status=status.HTTP_404_NOT_FOUND
                            )

                    lead_instance.stage = new_stage_instance
                    activate_lead_stage = lead_instance.set_active_stage_start_and_expiry_date()

                    lead_instance.save()
                    serialize_lead_instance = ReadLeadSerializer(lead_instance)

                    decrease_lead_count_in_old_stage = old_stage_instance.update_lead_count()
                    increase_lead_count_in_new_stage = new_stage_instance.update_lead_count()

                    moved_leads.append(serialize_lead_instance.data)

            except Lead.DoesNotExist:
                unmatched_ids.append(lead_id)

        if len(moved_leads) == 0:
            return Response(
                {
                    "message": f"No Leads were moved to any stage",
                    "Leads_successfully_moved": moved_leads,
                    "Unmatched_leads": unmatched_ids,
                },
                status=status.HTTP_204_NO_CONTENT
            )

        return Response(
            {
                "message": f"Leads has been moved to the {new_stage_instance.name} stage",
                "Leads_successfully_moved": moved_leads,
                "Unmatched_leads": unmatched_ids,
            },
            status=status.HTTP_200_OK
        )


# TODO: Read and clean up the code below.
class LeadProgression(APIView):
    """
    API endpoint for managing deal progression within stages.

    Methods:
    - post(request): Move deals to a specified stage within a pipeline.
    - delete(request): Remove deals from stages.

    Returns:
    - Response: Result of deal progression operations.
    """

    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    @staticmethod
    def post(request, *args, **kwargs):
        """
        Move deals to a specified stage within the pipeline.

        Args:
        - request: HTTP request object containing stage_id and a list of deal_id(s).

        Returns:
        - Response: Result of moving deals to the specified stage.
        """
        sales_officer_id = request.data.get("sales_officer_id")
        stage_id = request.data.get("stage_id")
        lead_ids = request.data.get("lead_ids", [])
        existing_leads = []
        non_existent_leads = []

        try:
            pipeline_instance = Pipeline.objects.get(sales_officer=sales_officer_id)
            stage_instance = Stage.objects.get(pipeline=pipeline_instance)

        except Pipeline.DoesNotExist:
            return Response(
                {
                    "message": "Pipeline does not exist",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        # try:
        #     stage_instance = Stage.objects.get(stage_id=stage_id)
        #
        # except Stage.DoesNotExist:
        #     return Response(
        #         {"message": "Stage does not exist"},
        #         status=status.HTTP_404_NOT_FOUND
        #     )

        for lead_id in lead_ids:
            try:
                lead_instance = Lead.objects.get(id=lead_id)
                existing_leads.append(lead_instance)
            except Lead.DoesNotExist:
                non_existent_leads.append(lead_id)

        for lead in existing_leads:
            try:
                # old_stage_id = lead_instance.stage.id
                lead.stage = stage_instance

                if lead.trail is None:
                    lead.trail = []

                # Update trail field
                trail_entry = {
                    "Event": "progression",
                    "Timestamp": timezone.now().isoformat(),
                    "Lead Name": lead.contact_name,
                    "Stage": stage_instance.name,
                    # "moved_by": deal_contact
                }
                lead.trail.append(trail_entry)
                lead.save()

                # Send email notification if enabled for the stage
                # if stage_instance.email_notifications_enabled:
                #     stage_notification(stage_instance, lead)

            except Lead.DoesNotExist:
                continue

        # Get the deal progression count
        # lead_count = Lead.deal_progression_count(id=lead_id)
        return Response(
            {
                "message": f"{existing_leads} has been moved to {stage_instance.name} stage",
                # "Leads"
                # "deal_count": lead_count
            },
            status=status.HTTP_200_OK
        )

    def delete(self, request):
        """
        Remove deals from stages.

        Args:
        - request: HTTP request object containing deal_ids.

        Returns:
        - Response: Result of removing candidates from stages.
        """

        lead_ids = request.data.get("lead_ids", [])

        lead_names = []
        stages = []
        for lead_id in lead_ids:
            try:
                lead = Lead.objects.get(id=lead_id)
                old_stage = lead.stage
                lead.status = Lead_status.DROP_OFF
                lead.save()
                lead_names.append(lead.contact_name)
                stages.append(lead.stage)

                # Update counts for the stages
                if old_stage:
                    old_stage = Stage.objects.get(id=old_stage.id)
                    # old_stage.deal_count = F("deal_count") - 1
                    old_stage.save()

            except Lead.DoesNotExist:
                continue

        return Response({"message": f"{lead_names} has been dropped off from {stages.title}"})

    pass


class PipelineAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        pipeline_id = request.GET.get("pipeline_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if pipeline_id:
            try:
                pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
                serialized_pipeline = ReadPipelineSerializer(pipeline_instance)
                pipeline_stages = Stage.objects.filter(pipeline=pipeline_instance)
                serialized_stages = ReadStageSerializer(pipeline_stages, many=True)
                number_of_stages_in_pipeline = pipeline_stages.count()

                total_lead_in_pipeline = Lead.objects.filter(pipeline=pipeline_instance).count()
                pending_leads = Lead.objects.filter(pipeline=pipeline_id, status=Lead_status.PENDING).count()
                closed_leads = Lead.objects.filter(pipeline=pipeline_id, status=Lead_status.CLOSED).count()
                active_leads = Lead.objects.filter(pipeline=pipeline_id, status=Lead_status.ACTIVE).count()
                drop_off = Lead.objects.filter(pipeline=pipeline_id, status=Lead_status.DROP_OFF).count()
                awaiting = Lead.objects.filter(pipeline=pipeline_id, status=Lead_status.AWAITING).count()

                data = {
                    "Pipeline": serialized_pipeline.data,
                    "Number of Stages": number_of_stages_in_pipeline,
                    "Stages": serialized_stages.data,
                    "Statistics": {
                        "total_leads_in_pipeline": total_lead_in_pipeline,
                        "pending_leads": pending_leads,
                        "closed_leads": closed_leads,
                        "active_leads": active_leads,
                        "drop_off": drop_off,
                        "awaiting": awaiting,
                    }
                }
                return Response(
                    {
                        "message": "Pipeline exists",
                        "data": data
                    },
                    status=status.HTTP_200_OK
                )
            except Pipeline.DoesNotExist:
                return Response(
                    {
                        "message": "Pipeline does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(
            {
                "message": "Pipeline cannot be null",
                "data": []
            },
            status=status.HTTP_200_OK
        )

    def post(self, request):
        user = request.user
        company_id = request.data.get("company_id")
        team_id = request.data.get("team_id")
        serializer = PipelineSerializer(data=request.data)

        if serializer.is_valid():

            try:
                company_instance = Company.objects.get(id=company_id)
                team_instance = Team.objects.get(id=team_id, company=company_instance)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            except Team.DoesNotExist:
                return Response(
                    {
                        "message": "Team does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance: TeamMember = TeamMember.objects.get(
                    member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response({"message": "Sales Officer does not exist.", "data": []
                                 },
                                status=status.HTTP_404_NOT_FOUND
                                )

            try:
                pipeline_instance = Pipeline.create(sales_officer_instance, serializer.validated_data)
            except IntegrityError:
                return Response(
                    {"error": f"A Pipeline with this name already exists by {sales_officer_instance.member.username}."},
                    status=status.HTTP_400_BAD_REQUEST
                )
            serialized_pipeline_instance = ReadPipelineSerializer(pipeline_instance)

            return Response(
                {
                    "message": "Pipeline created successfully.",
                    "data": serialized_pipeline_instance.data,
                },
                status=status.HTTP_201_CREATED)

        else:
            return Response(
                {
                    "message": "The data entered is incorrect, please try again.",
                    "error_details": serializer.errors,
                    "data": []
                },
                status=status.HTTP_200_OK
            )

    def patch(self):
        pass

    def put(self, request, pk):
        company_id = request.data.get("company_id")
        team_id = request.data.get("team_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        # Get the existing Pipeline instance, or return a 404 error if it doesn't exist
        pipeline_instance = get_object_or_404(Pipeline, pk=pk, sales_officer__team=team_instance)

        # Deserialize the incoming data and apply it to the existing instance
        serializer = PipelineSerializer(instance=pipeline_instance, data=request.data, partial=True)

        # Check if the new data is valid
        if serializer.is_valid():
            # Save the updated instance
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)

        # Return validation errors if the data is not valid
        return Response(
            {
                "error_details": serializer.errors,
                "data": []
            },
            status=status.HTTP_200_OK
        )

    def delete(self, request):
        user = request.user
        pipeline_id = request.GET.get("pipeline_id")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")

        if pipeline_id:

            try:
                company_instance = Company.objects.get(id=company_id)
                team_instance = Team.objects.get(id=team_id, company=company_instance)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            except Team.DoesNotExist:
                return Response(
                    {
                        "message": "Team does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)

            except Pipeline.DoesNotExist:
                return Response(
                    {
                        "message": "Pipeline does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            pipeline_instance.delete()
            return Response({"message": "Pipeline has been deleted"}, status=status.HTTP_204_NO_CONTENT)


class GetMultiplePipelineAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        param = request.GET.get("param")
        query = Q()
        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            team_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=team_instance,
                                               serializer=TeamSerializer
                                               )

            return Response(
                {
                    "message": "Team does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            sales_officer_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=sales_officer_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if param:
            query &= Q(id__icontains=param) | Q(name__icontains=param) | \
                     Q(description__icontains=param) | Q(type__icontains=param) | \
                     Q(is_default__icontains=param)

            pipeline_instance = Pipeline.objects \
                .filter(query, sales_officer=sales_officer_instance) \
                .order_by('-created_at')

        else:
            pipeline_instance = Pipeline.objects.filter(sales_officer=sales_officer_instance).order_by('-created_at')

        total_number_of_pipelines = pipeline_instance.count()
        if total_number_of_pipelines == 0:
            page = self.pagination_class.paginate_queryset(pipeline_instance, request, view=self)
            serialized_results = ReadPipelineSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Pipeline Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )
            # return Response(
            #     {
            #         "message": "No Pipeline Found",
            #         "data": []
            #     },
            #     status=status.HTTP_200_OK
            #     )

        page = self.pagination_class.paginate_queryset(pipeline_instance, request)
        if page is not None:
            serialized_results = ReadPipelineSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


class StagesAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        stage_id = request.GET.get("stage_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if stage_id:
            try:
                stage_instance = Stage.objects.get(id=stage_id, pipeline__sales_officer=sales_officer_instance)
                serialized_results = ReadStageSerializer(stage_instance)
                return Response(
                    {"message": f"{stage_instance.name} Stage exists", "data": serialized_results.data},
                    status=status.HTTP_200_OK
                )

            except Stage.DoesNotExist:
                return Response(
                    {
                        "message": "Stage does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(
            {
                "message": "Stage cannot be null",
                "data": []
            },
            status=status.HTTP_200_OK
        )

    def post(self, request):
        user = request.user
        serializer = CreateStageSerializer(data=request.data)

        if serializer.is_valid():
            pipeline_id = request.data.get("pipeline")
            category_id = request.data.get("category")
            company_id = request.data.get("company_id")
            team_id = request.data.get("team_id")

            try:
                company_instance = Company.objects.get(id=company_id)
                team_instance = Team.objects.get(id=team_id, company=company_instance)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            except Team.DoesNotExist:
                return Response(
                    {
                        "message": "Team does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                if pipeline_id:
                    Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
                else:
                    return Response(
                        {
                            "message": "Pipeline id cannot be null",
                            "data": []
                        },
                        status=status.HTTP_406_NOT_ACCEPTABLE
                    )
            except Pipeline.DoesNotExist:
                return Response(
                    {
                        "message": "Pipeline does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                if category_id:
                    Category.objects.get(id=category_id, team=team_instance)

                    existing_stage_with_category = Stage.objects.filter(
                        pipeline=pipeline_id,
                        category=category_id,
                        # pipeline__sales_officer=sales_officer_instance
                    )
                    if existing_stage_with_category:
                        return Response(
                            {
                                "message": "Stage not created. A stage with the category selected already exists "
                                           "in this pipeline. Kindly select another Category",
                                "data": []
                            },
                            status=status.HTTP_200_OK
                        )
                else:
                    return Response(
                        {
                            "message": "Category id cannot be null",
                            "data": []
                        },
                        status=status.HTTP_200_OK
                    )
            except Category.DoesNotExist:
                return Response(
                    {
                        "message": "Category does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            stage_instance = Stage.create(serializer.validated_data)
            serialized_stage_instance = ReadStageSerializer(stage_instance)

            return Response(
                {
                    "message": "Stage created successfully.",
                    "data": serialized_stage_instance.data,
                },
                status=status.HTTP_201_CREATED)

        else:
            return Response(
                {
                    "message": "The data entered is incorrect, please try again.",
                    "error_details": serializer.errors
                },
                status=status.HTTP_200_OK)

    def patch(self):
        pass

    def put(self, request, pk):
        company_id = request.data.get("company_id")
        team_id = request.data.get("team_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        stage_instance = get_object_or_404(Stage, pk=pk, pipeline__sales_officer__team__company=company_instance)

        serializer = CreateStageSerializer(instance=stage_instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_200_OK)

    def delete(self, request):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        stage_id = request.GET.get("stage_id")

        if stage_id:

            try:
                company_instance = Company.objects.get(id=company_id)
                team_instance = Team.objects.get(id=team_id, company=company_instance)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            except Team.DoesNotExist:
                return Response(
                    {
                        "message": "Team does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                stage_instance = Stage.objects.get(id=stage_id, pipeline__sales_officer=sales_officer_instance)
            except Stage.DoesNotExist:
                return Response(
                    {
                        "message": "Stage does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            stage_instance.delete()
            return Response({"message": "Stage has been deleted"}, status=status.HTTP_204_NO_CONTENT)


class GetMultipleStagesAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        stage_id = request.GET.get("stage_id")
        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            team_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=team_instance,
                                               serializer=TeamSerializer
                                               )

            return Response(
                {
                    "message": "Team does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            sales_officer_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=sales_officer_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            stage_instance = Stage.objects.filter(pipeline__sales_officer=sales_officer_instance) \
                .order_by('-created_at')
        except Stage.DoesNotExist:
            stage_instance = []

            total_number_of_stages = stage_instance.count(None)

            if total_number_of_stages == 0:
                page = self.pagination_class.paginate_queryset(stage_instance, request, view=self)
                serialized_results = ReadStageSerializer(page, many=True)
                paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Stage Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        page = self.pagination_class.paginate_queryset(stage_instance, request, view=self)
        if page is not None:
            serialized_results = ReadStageSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


class GetStagesByPipeline(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request, *args, **kwargs):
        user = request.user
        pipeline_id = request.GET.get("pipeline_id")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
        except Pipeline.DoesNotExist:
            return Response(
                {
                    "message": "Pipeline does not exist",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        pipeline_stages = Stage.objects.filter(pipeline=pipeline_instance).order_by('-created_at')
        number_of_stages_in_pipeline = pipeline_stages.count()
        serialized_stages = ReadStageAndLeadSerializer(pipeline_stages, many=True)

        return Response(
            {
                "message": f"Stages in {pipeline_instance.name}",
                "Number of Data": number_of_stages_in_pipeline,
                "data": serialized_stages.data,
            },
            status=status.HTTP_200_OK)


class GetLeadsInStagesByPipeline(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        pipeline_id = request.GET.get("pipeline_id")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.", "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
        except Pipeline.DoesNotExist:
            return Response(
                {
                    "message": "Pipeline does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        # Prefetch the leads for each stage within the pipe using prefetch_related.
        stages = Stage.objects.filter(pipeline=pipeline_instance).order_by('-created_at').prefetch_related(
            Prefetch(
                'lead_set', queryset=Lead.objects.only('id', 'contact_name').order_by('-id'), to_attr='leads'
            )
        )

        serialized_stages = ReadStageAndLeadSerializer(stages, many=True, context={'leads': True})

        return Response(
            {
                "message": "Successful",
                "number_of_stages": stages.count(),
                "data": serialized_stages.data,
            },
            status=status.HTTP_200_OK
        )


class GetLeadsByStage(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request, *args, **kwargs):
        user = request.user
        stage_id = request.GET.get("stage_id")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.", "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            stage_instance = Stage.objects.get(id=stage_id, pipeline__sales_officer=sales_officer_instance)
            # stage_instance.update_lead_count()
            serialized_stage = ReadStageSerializer(stage_instance)
            lead_instance = Lead.objects.filter(stage=stage_instance).order_by('-created_at')
            number_of_lead_in_stage = stage_instance.lead_count
            serialized_leads = ReadLeadSerializer(lead_instance, many=True)

        except Stage.DoesNotExist:
            return Response(
                {
                    "message": "Stage does not exist",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        return Response(
            {
                "Stage": serialized_stage.data,
                "number_of_lead_in_stage": number_of_lead_in_stage,
                "Leads": serialized_leads.data
            },
            status=status.HTTP_200_OK)


class ActivityAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        activity_id = request.GET.get("activity_id")
        company_id = request.GET.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.", "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if activity_id:
            try:
                activity_instance = Activity.objects.get(id=activity_id, sales_officer=sales_officer_instance)
                serialized_results = ReadActivitySerializer(activity_instance)
                return Response(
                    {"message": "Activity Found", "data": serialized_results.data},
                    status=status.HTTP_200_OK
                )

            except Activity.DoesNotExist:
                return Response(
                    {
                        "message": "Activity does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(
            {
                "message": "Activity cannot be null",
                "data": []
            },
            status=status.HTTP_406_NOT_ACCEPTABLE
        )

    def post(self, request):
        user = request.user
        company_id = request.data.get("company_id")

        serializer = ActivitySerializer(data=request.data)

        if serializer.is_valid():

            try:
                company_instance = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            lead_id = request.data.get("lead")

            try:
                lead_instance = Lead.objects.get(id=lead_id)
            except Lead.DoesNotExist:
                return Response(
                    {
                        "message": "Lead does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            activity_instance = Activity.create(sales_officer_instance, serializer.validated_data)
            serialized_activity_instance = ReadActivitySerializer(activity_instance)

            return Response(
                {
                    "message": "Activity created successfully.",
                    "data": serialized_activity_instance.data,
                },
                status=status.HTTP_201_CREATED)

        else:
            return Response(
                {
                    "message": "The data entered is incorrect, please try again.",
                    "error_details": serializer.errors
                },
                status=status.HTTP_200_OK
            )

    def patch(self):
        pass

    def put(self, request, pk):
        activity_instance = get_object_or_404(Activity, pk=pk)

        serializer = ActivitySerializer(instance=activity_instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "message": "Successful",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK
            )
        return Response(
            {
                "message": "Successful",
                "error_details": serializer.errors
            },
            status=status.HTTP_200_OK
        )

    def delete(self, request):
        user = request.user
        activity_id = request.GET.get("activity_id")
        company_id = request.GET.get("company_id")

        if activity_id:

            try:
                company_instance = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                activity_instance = Activity.objects.get(id=activity_id, sales_officer=sales_officer_instance)

            except Activity.DoesNotExist:
                return Response(
                    {
                        "message": "Activity does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            activity_instance.delete()
            return Response({"message": "Activity has been deleted"}, status=status.HTTP_204_NO_CONTENT)


class GetMultipleActivityAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        param = request.GET.get("param")
        query = Q()
        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            sales_officer_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=sales_officer_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if param:
            query &= Q(title__icontains=param) | Q(description__icontains=param)
            activity_instance = Activity.objects.filter(query).order_by('-created_at')

        else:
            activity_instance = Activity.objects.filter(sales_officer=sales_officer_instance).order_by('-created_at')

        total_number_of_activities = activity_instance.count()
        if total_number_of_activities == 0:
            page = self.pagination_class.paginate_queryset(activity_instance, request, view=self)
            serialized_results = ReadActivitySerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Activity Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )

        page = self.pagination_class.paginate_queryset(activity_instance, request)
        if page is not None:
            serialized_results = ReadActivitySerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


# TODO: Read and clean up the code below.
# TODO: Test code out
class RecentActivitiesView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request):
        # if not lead_id:
        #     return Response(
        #         {"error": "merchant_id is required"},
        #         status=status.HTTP_400_BAD_REQUEST
        #     )

        user = request.user
        company_id = request.GET.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        lead_id = Lead.objects.GET.get("lead_id")

        try:
            lead = Lead.objects.get(id=lead_id, sales_officer_in_charge=sales_officer_instance)
        except Lead.DoesNotExist:
            return Response(
                {"Message": "Lead does not exist.", "data": []},
                status=status.HTTP_404_NOT_FOUND
            )

        last_week = timezone.now() - timedelta(days=7)

        # Get activities for the merchant created within the last 7 days
        activities = Activity.objects.filter(
            lead=lead,
            created_at__gte=last_week
        )

        # Get tasks for the activities created within the last 7 days
        tasks = Task.objects.filter(
            activity__in=activities,
            activity__created_at__gte=last_week
        )

        serialized_lead = ReadLeadSerializer(lead)
        serialized_activities = ReadActivitySerializer(activities, many=True)
        tasks_serializer = TaskSerializer(tasks, many=True)

        return Response({
            "Lead Data": serialized_lead.data,
            "Activities": serialized_activities.data,
            "Tasks": tasks_serializer.data
        }, status=status.HTTP_200_OK)


class TaskAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        task_id = request.GET.get("task_id")
        company_id = request.GET.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.", "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if task_id:
            try:
                task_instance = Task.objects.get(id=task_id, activity__sales_officer=sales_officer_instance)
                serialized_results = ReadTaskSerializer(task_instance)
                return Response(
                    {"message": "Task Found", "Task Data": serialized_results.data},
                    status=status.HTTP_200_OK
                )

            except Task.DoesNotExist:
                return Response(
                    {
                        "message": "Task does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(
            {
                "message": "Task cannot be null",
                "data": []
            },
            status=status.HTTP_200_OK
        )

    def post(self, request):
        user = request.user
        company_id = request.data.get("company_id")
        serializer = TaskSerializer(data=request.data)

        if serializer.is_valid():
            try:
                company_instance = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            activity_id = request.data.get("activity")

            try:
                if activity_id:
                    activity_instance = Activity.objects.get(id=activity_id, sales_officer=sales_officer_instance)
                else:
                    return Response(
                        {
                            "message": "Activity id cannot be null",
                            "data": []
                        },
                        status=status.HTTP_200_OK
                    )

            except Activity.DoesNotExist:
                return Response(
                    {
                        "message": "Activity does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            task_instance = Task.create(serializer.validated_data)
            serialized_task_instance = ReadTaskSerializer(task_instance)

            return Response(
                {
                    "message": "Task created successfully.",
                    "data": serialized_task_instance.data,
                },
                status=status.HTTP_201_CREATED)

        else:
            return Response(
                {
                    "message": "The data entered is incorrect, please try again.",
                    "error_details": serializer.errors
                },
                status=status.HTTP_200_OK
            )

    def delete(self, request):
        user = request.user
        task_id = request.GET.get("task_id")
        company_id = request.GET.get("company_id")

        if task_id:

            try:
                company_instance = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                task_instance = Task.objects.get(id=task_id, activity__sales_officer=sales_officer_instance)

            except Task.DoesNotExist:
                return Response(
                    {
                        "message": "Task does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            task_instance.delete()
            return Response({"message": "Task has been deleted"}, status=status.HTTP_204_NO_CONTENT)


class GetMultipleTaskAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        company_id = request.GET.get("company_id")
        param = request.GET.get("param")
        paginated_data = {}
        query = Q()

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            sales_officer_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=sales_officer_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if param:
            query &= Q(title__icontains=param) | Q(description__icontains=param)
            task_instance = Pipeline.objects.filter(query).order_by('-created_at')

        else:
            task_instance = Task.objects.filter(activity__sales_officer=sales_officer_instance).order_by('-created_at')

        total_number_of_tasks = task_instance.count()
        if total_number_of_tasks == 0:
            page = self.pagination_class.paginate_queryset(task_instance, request, view=self)
            serialized_results = ReadTaskSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Task Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_200_OK
            )
        page = self.pagination_class.paginate_queryset(queryset=task_instance, request=request, view=self)
        if page is not None:
            serialized_results = ReadTaskSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


# TODO: Read and clean up the code below.
# TODO: Test code out
class RecentAndUpcomingTasksView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request):
        # if not lead_id:
        #     return Response(
        #         {
        #             "error": "lead_id is required"
        #         },
        #         status=status.HTTP_400_BAD_REQUEST
        #     )

        user = request.user
        company_id = request.GET.get("company_id")

        try:
            try:
                company_instance = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        lead_id = Lead.objects.GET.get("lead_id")

        try:
            lead = Lead.objects.get(id=lead_id, sales_officer_in_charge=sales_officer_instance)
        except Lead.DoesNotExist:
            return Response(
                {
                    "message": "Lead does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        # lead_serializer = CreateLeadSerializer(lead)

        activity = Activity.objects.get(lead=lead_id)

        try:
            tasks = Task.objects.filter(activity=activity)
            upcoming_tasks = []
            recent_tasks = []

            for task in tasks:

                deadline = task.deadline
                task_serializer = ReadTaskSerializer(task)

                if deadline > timezone.now():
                    upcoming_tasks.append(task_serializer.data)
                else:
                    recent_tasks.append(task_serializer.data)

            return Response(
                {
                    "Recent tasks": recent_tasks,
                    "Upcoming tasks": upcoming_tasks,
                },
                status=status.HTTP_200_OK
            )
        except Task.DoesNotExist:
            return Response(
                {
                    "message": "There are no upcoming tasks",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )


class NotesAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        notes_id = request.GET.get("notes")
        company_id = request.GET.get("company_id")

        try:

            try:
                company_instance = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.", "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if notes_id:
            try:
                note_instance = Notes.objects.get(id=notes_id, sales_officer=sales_officer_instance)
                serialized_results = ReadNotesSerializer(note_instance)
                return Response(
                    {"message": "Note Found", "data": serialized_results.data},
                    status=status.HTTP_200_OK
                )

            except Notes.DoesNotExist:
                return Response(
                    {
                        "message": "Note does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(
            {
                "message": "Note cannot be null",
                "data": []
            },
            status=status.HTTP_200_OK
        )

    def post(self, request):
        user = request.user
        company_id = request.data.get("company_id")

        serializer = NotesSerializer(data=request.data)

        if serializer.is_valid():

            try:
                company_instance = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                sales_officer_instance = TeamMember.objects.get(
                    member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "Sales Officer does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            note_instance = Notes.create(sales_officer_instance, serializer.validated_data)
            serialized_note_instance = ReadNotesSerializer(note_instance)

            return Response(
                {
                    "message": "Note created successfully.",
                    "data": serialized_note_instance.data,
                },
                status=status.HTTP_201_CREATED)

        else:
            return Response(
                {
                    "message": "The data entered is incorrect, please try again.",
                    "error_details": serializer.errors
                },
                status=status.HTTP_200_OK
            )

    def patch(self):
        pass

    def put(self, request, pk):
        task_instance = get_object_or_404(Task, pk=pk)

        serializer = TaskSerializer(instance=task_instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "message": "Successful",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK
            )
        return Response(
            {
                "message": "Successful",
                "error_details": serializer.errors
            },
            status=status.HTTP_200_OK
        )

    def delete(self):
        pass


class GetMultipleNotesAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        notes_id = request.GET.get("notes")
        company_id = request.GET.get("company_id")
        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            company_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=company_instance,
                                               serializer=ListOfCompanySerializer
                                               )

            return Response(
                {
                    "message": "Company does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            sales_officer_instance = []
            paginated_data = pagination_factor(self=self,
                                               pagination_class=self.pagination_class,
                                               request=request,
                                               instance=sales_officer_instance,
                                               serializer=PaginatedTeamMembersSerializer
                                               )

            return Response(
                {
                    "message": "Sales Officer does not exist.",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            notes_instance = Notes.objects.filter(sales_officer=sales_officer_instance).order_by('-created_at')
        except Notes.DoesNotExist:
            notes_instance = []

            total_number_of_notes = notes_instance.count(None)
            if total_number_of_notes == 0:
                page = self.pagination_class.paginate_queryset(notes_instance, request, view=self)
                serialized_results = ReadStageSerializer(page, many=True)
                paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

            return Response(
                {
                    "message": "No Note Found",
                    "paginated_results": paginated_data.data
                },
                status=status.HTTP_404_NOT_FOUND
            )

        page = self.pagination_class.paginate_queryset(queryset=notes_instance, request=request, view=self)
        if page is not None:
            serialized_results = ReadNotesSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


class EmailAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def post(self, request):

        # user = request.user

        serializer = EmailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        recipient = serializer.validated_data.get("recipient")
        subject = serializer.validated_data.get("subject")
        body = serializer.validated_data.get("body")
        # contact_name = serializer.validated_data.get("contact_name")
        sales_officer_id = serializer.validated_data.get("sales_officer_id")
        sales_lead_id = serializer.validated_data.get("sales_lead_id")
        user_id = serializer.validated_data.get("user_id")
        lead_id = serializer.validated_data.get("lead_id")

        try:
            sales_officer = SalesOfficer.objects.get(id=sales_officer_id)
            name = sales_officer.name
        except SalesOfficer.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer not found",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        try:
            sales_lead = SalesLead.objects.get(id=sales_lead_id)
            name = sales_lead.name
        except SalesLead.DoesNotExist:
            return Response(
                {
                    "message": "Sales Lead not found",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            sender = User.objects.get(id=user_id)
            name = sender.first_name
        except User.DoesNotExist:
            return Response(
                {
                    "message": "User not found",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        try:
            merchant = Lead.objects.get(id=lead_id)
            # recipient = lead.contact_email
            lead = merchant.contact_name
        except Merchants.DoesNotExist:
            return Response(
                {
                    "message": "Merchant not found",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        send_email.delay(
            recipient=recipient,
            subject=subject,
            body=body,
            template_dir="send_email.html",
            name=name,
            lead=lead,
        )

        return Response(
            {
                "message": "Email sent successfully"
            },
            status=status.HTTP_200_OK
        )


class SendEmailView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def post(self, request):
        user = request.user
        serializer = SendEmailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data
        subject = serializer_data.get("subject")
        message = serializer_data.get("message")
        cc = serializer_data.get("cc")
        bcc = serializer_data.get("bcc")
        recipient_email = serializer_data.get("recipient_email")
        company_id = serializer_data.get("company_id")
        date = datetime.now()
        timezone = pytz.timezone('Africa/Lagos')
        date = date.astimezone(timezone).replace(microsecond=0)
        senders_name = user.first_name + " " + user.last_name

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
            sales_officer_id = sales_officer_instance.id
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist",
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sender = GmailAccount.objects.get(user_id=user.id)
            sender_email = sender.gmail_address

            send_email_with_gmail.delay(
                gmail_address=sender_email,
                subject=subject,
                message=message,
                senders_name=senders_name,
                cc=cc,
                bcc=bcc,
                recipient_email=recipient_email,
                date=date,
                team_member=sales_officer_instance.id,
            )
            if sender_email == None:
                try:
                    sender = WorkEmail.objects.get(user_id=user.id)
                    sender_email = sender.work_email

                    send_single_mails.delay(
                        user_id=user.id,
                        subject=subject,
                        message=message,
                        sender_email=sender_email,
                        senders_name=senders_name,
                        cc=cc,
                        bcc=bcc,
                        recipient_email=recipient_email,
                        date=date,
                        team_member=sales_officer_instance.id,
                    )
                    return Response(
                        {
                            "message": "Liberty mail sent!"
                        }, status=status.HTTP_200_OK
                    )
                except WorkEmail.DoesNotExist:
                    return Response(
                        {
                            "message": "User does not have a registered Liberty email!",
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )
            return Response(
                {
                    "message": "Google mail sent!",
                },
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred.",
                    "error": str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ScheduleBulkEmailView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def post(self, request):
        user = request.user
        serializer = SendBulkEmailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data
        company_id = serializer_data.get("company_id")
        subject = serializer_data.get("subject")
        message = serializer_data.get("message")
        cc = serializer_data.get("cc")
        bcc = serializer_data.get("bcc")
        recipient_emails = serializer_data.get("recipient_emails")
        scheduled_time = serializer_data.get("scheduled_time")
        current_timezone = datetime.now().astimezone().tzinfo
        updated_scheduled_time = datetime.strptime(scheduled_time, "%Y-%m-%d %H:%M:%S").replace(microsecond=0,
                                                                                                tzinfo=current_timezone)
        senders_name = user.first_name + " " + user.last_name

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales Officer does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        if updated_scheduled_time > timezone.now():
            try:
                sender = GmailAccount.objects.get(user_id=user.id)
                sender_email = sender.gmail_address
                schedule_bulk_emails.delay(
                    user_id=user.id,
                    team_member=sales_officer_instance.id,
                    subject=subject,
                    message=message,
                    sender_email=sender_email,
                    senders_name=senders_name,
                    cc=cc,
                    bcc=bcc,
                    recipient_emails=recipient_emails,
                    scheduled_time=scheduled_time,
                    gmail_address=sender_email,
                    date=updated_scheduled_time,
                )
                if sender_email == None:
                    try:
                        sender = WorkEmail.objects.get(user_id=user.id)
                        sender_email = sender.work_email
                    except WorkEmail.DoesNotExist:
                        return Response(
                            {
                                "message": "User does not have a registered Liberty email!",
                            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                        )

                    schedule_bulk_emails.delay(
                        user_id=user.id,
                        team_member=sales_officer_instance.id,
                        subject=subject,
                        message=message,
                        sender_email=sender_email,
                        senders_name=senders_name,
                        cc=cc,
                        bcc=bcc,
                        recipient_emails=recipient_emails,
                        scheduled_time=scheduled_time,
                        gmail_address=None,
                        date=updated_scheduled_time,
                    )
                return Response(
                    {
                        "message": "Bulk emails scheduled successfully",
                    }, status=status.HTTP_200_OK
                )
            except Exception as e:
                return Response(
                    {
                        "message": "An error occurred.",
                        "error": str(e)
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        else:
            return Response(
                {
                    "error": "The scheduled date must be at a future time.",
                }, status=status.HTTP_400_BAD_REQUEST
            )


class RefreshEmailView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def post(self, request):
        user = request.user
        serializer = RefreshEmailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data
        company_id = serializer_data.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist.",
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sync_emails_with_db.delay(
                user_id=user.id,
                sales_officer=sales_officer_instance.id,
            )
            return Response(
                {
                    "message": "Emails refreshed"
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred.",
                    "error": str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InboxView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request):
        user = request.user
        company_id = request.GET.get("company_id")

        try:
            company_isntance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_isntance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            inbox = Emails.objects.filter(is_inbox=True)
            number_of_inbox_emails = inbox.count()
            serialized_inbox = MailInboxSerializer(inbox, many=True)

            return Response(
                {
                    "message": "Emails retrieved successfully",
                    "inbox": serialized_inbox.data,
                    "number_of_items": number_of_inbox_emails
                }
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred.",
                    "error": str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RetrieveEmailView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request):
        user = request.user
        email_date_range = request.GET.get("date")
        company_id = request.GET.get("company_id")

        email_date = datetime.strptime(email_date_range, "%Y-%m-%d").date()
        email_range = get_upper_date_limit(email_date)

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "error": "Sales officer does not exist",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            mails = get_emails_within_range(email_date, email_range)
            serialized_emails = MailInboxSerializer(mails, many=True)
            total_number_of_mails = mails.count()

            return Response(
                {
                    "message": "Emails retrieved successfully",
                    "mailbox": serialized_emails.data,
                    "number_of_emails": total_number_of_mails
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {
                    "message": "An error occurred",
                    "error": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetSentEmailsView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = [CustomPagination]

    def get(self, request):
        user = request.user
        company_id = request.GET.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist.",
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sent_emails = Emails.objects.filter(is_sent=True)
            number_of_emails_sent = sent_emails.count()
            serialized_sent_emails = SentEmailSerializer(sent_emails, many=True)
            if sent_emails:
                return Response(
                    {
                        "message": "Sent emails retrieved successfully.",
                        "sent_emails": serialized_sent_emails.data,
                        "number_of_items": number_of_emails_sent,
                    }, status=status.HTTP_200_OK
                )
        except Emails.DoesNotExist:
            return Response(
                {
                    "message": "You have not sent any emails yet.",
                }, status=status.HTTP_400_BAD_REQUEST
            )


class GetScheduledEmailsView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = [CustomPagination]

    def get(self, request):
        user = request.user
        company_id = request.GET.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            scheduled_emails = Emails.objects.filter(is_scheduled=True)
            num_of_scheduled_emails = scheduled_emails.count()
            serialized_scheduled_emails = ScheduledEmailSerializer(scheduled_emails, many=True)
            return Response(
                {
                    "message": "Scheduled emails retrieved.",
                    "scheduled_emails": serialized_scheduled_emails.data,
                    "number_of_items": num_of_scheduled_emails,
                }
            )
        except Emails.DoesNotExist:
            return Response(
                {
                    "message": "You have not scheduled any emails yet!",
                }, status=status.HTTP_400_BAD_REQUEST
            )


class DraftEmailsView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_classes = [CustomPagination]

    def get(self, request):
        user = request.user
        company_id = request.GET.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            drafts = Emails.objects.filter(is_draft=True)
            number_of_drafts = drafts.count()
            serialized_drafts = DraftEmailSerializer(drafts, many=True)
            return Response(
                {
                    "message": "Drafts retrieved successfully.",
                    "drafts": serialized_drafts.data,
                    "number_of_items": number_of_drafts,
                }, status.HTTP_200_OK
            )
        except Emails.DoesNotExist:
            return Response(
                {
                    "message": "Draft does not exist.",
                }, status=status.HTTP_400_BAD_REQUEST
            )

    def post(self, request):
        user = request.user
        serializer = SaveDraftSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data
        subject = serializer_data.get("subject")
        content = serializer_data.get("content")
        cc = serializer_data.get("cc")
        bcc = serializer_data.get("bcc")
        recipients = serializer_data.get("recipients")
        company_id = serializer_data.get("company_id")
        current_date = datetime.now()
        current_timezone = timezone.now().astimezone().tzinfo
        updated_date = current_date.strftime("%Y-%m-%d %H:%M:%S")
        updated_date = datetime.strptime(updated_date, "%Y-%m-%d %H:%M:%S")
        tz_date = updated_date.astimezone(current_timezone)
        senders_name = user.first_name + " " + user.last_name

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sender = GmailAccount.objects.get(user_id=user.id)
            sender_email = sender.gmail_address

            if sender_email == None:
                try:
                    sender = WorkEmail.objects.get(user_id=user.id)
                    sender_email = sender.work_email
                except WorkEmail.DoesNotExist:
                    return Response(
                        {
                            "message": "Work email does not exist for the user!",
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            drafts = save_to_drafts(
                sales_officer=sales_officer_instance.id,
                is_draft=True,
                date=tz_date,
                subject=subject,
                sender=sender_email,
                senders_name=senders_name,
                cc=cc,
                bcc=bcc,
                recipients=recipients,
                content=content,
            )
            serialized_drafts = DraftEmailSerializer(drafts, many=False)

            return Response(
                {
                    "message": "Email saved as draft.",
                    "draft": serialized_drafts.data,
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occured while attempting to save email as draft.",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DeleteEmailsView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_classes = [CustomPagination]

    def get(self, request):
        user = request.user
        company_id = request.GET.get("company_id")
        try:
            company_instance = Company.objects.get(id=company_id)
        except Exception as e:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except Exception as e:
            return Response(
                {
                    "message": "Sales officer does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            deleted_emails = Emails.objects.filter(is_deleted=True)
            num_deleted_emails = deleted_emails.count()
            serialized_deleted_mails = DeletedEmailSerializer(deleted_emails, many=True)
            return Response(
                {
                    "message": "Deleted mails retrieved.",
                    "deleted_emails": serialized_deleted_mails.data,
                    "number_of_items": num_deleted_emails,
                }, status=status.HTTP_200_OK
            )
        except Emails.DoesNotExist:
            return Response(
                {
                    "message": "There are no deleted emails.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

    def delete(self, request):
        user = request.user
        company_id = request.data.get("company_id")
        email_id = request.data.get("email_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            deleted_email_instance = Emails.objects.get(id=email_id)
            deleted_email_instance.soft_delete()
            return Response(
                {
                    "message": "Email deleted.",
                }, status=status.HTTP_200_OK
            )
        except Emails.DoesNotExist:
            return Response(
                {
                    "message": "Email does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )


class ReadEmailsView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_classes = [CustomPagination]

    def post(self, request):
        user = request.user
        email_id = request.data.get("email_id")
        company_id = request.data.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist",
                    "data": []
                }, status=status.HTTP_200_OK
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, user_role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            emails = Emails.objects.filter(id=email_id)
            # mark_as_read = save_read_emails(

            # )
            return Response(
                {
                    "message": "Email read.",
                    "emails": emails
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "An error occured.": e,
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    # def get(self, request):
    #     user = request.user       
    #     company_id = request.GET.get("company_id")

    #     try:
    #         company_instance = Company.objects.get(id=company_id)
    #     except Exception as e:
    #         return Response(
    #             {
    #                 "message": "Company does not exist.",
    #                 "data": []
    #             }, status=status.HTTP_200_OK
    #         )

    #     try:
    #         sales_officer_instance = TeamMember.objects.get(
    #             member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
    #             is_deleted=False
    #                 )
    #     except Exception as e:
    #         return Response(
    #             {
    #                 "message": "Sales officer does not exist.",
    #             }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
    #         )

    #     try:
    #         read_mail = Emails.objects.get(is_inbox=True)
    #         serialized_read_mail = ReadEmailSerializer(read_mail, many=True)
    #         return Response(
    #             {
    #                 "message": "Emails retrieved successfully.",
    #                 "Inbox": serialized_read_mail.data,
    #                 "status": "read",
    #             }, status.HTTP_200_OK
    #         )
    #     except Emails.DoesNotExist:
    #         return Response(
    #             {
    #                 "message": "You do not have any mails in your inbox.",
    #             }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
    #         )


class EventsView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def post(self, request):
        serializer = EventsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data
        attendees = serializer_data.get("attendees")
        summary = serializer_data.get("summary")
        description = serializer_data.get("description")
        location = serializer_data.get("location")
        start_date = serializer_data.get("start_date")
        end_date = serializer_data.get("end_date")
        calendarIds = serializer_data.get("calendarIds")
        company_id = serializer_data.get("company_id")
        user = request.user

        end_date = google_calendar_api.end_date

        deadline = str(end_date)

        deadline = deadline[0:25]

        deadline = parser.isoparse(end_date)

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                }, status=status.HTTP_200_OK
            )
        try:
            sales_officer = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist",
                    "data": []
                },
                status=status.HTTP_200_OK
            )
        try:
            meeting = schedule_event_on_google_calendar(
                attendees=attendees,
                summary=summary,
                description=description,
                location=location,
                start_date=start_date,
                end_date=end_date,
                calendarIds=calendarIds,
            )
            return Response(
                {
                    "message": "Event created successfully",
                    "Event": meeting,
                },
                status=status.HTTP_201_CREATED
            )
        except Exception as error:
            return Response(
                {
                    "message": error,
                    "data": []
                },
                status=status.HTTP_200_OK
            )

        # new_event = Task.create_event(
        #     sales_officer_id = serializer.validated_data["sales_officer_id"],
        #     meeting = meeting,
        #     attendees = serializer.validated_data["attendees"],
        #     deadline = deadline
        # )

    def get(self, request):
        try:
            calendarIds = request.GET.get("calendarIds")
            summary = retrieve_event_from_google_calendar(calendarIds)
            return Response(
                {
                    "Message": "Here are your events",
                    "Calendar summary": summary
                }, status=status.HTTP_200_OK
            )
        except Exception as error:
            return Response(
                {
                    "An error occurred": error
                }
            )


class CreateCalendarView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def post(self, request):
        serializer = CreateCalendarSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data
        calendar_title = serializer_data.get("calendar_title")
        user = request.user
        company_id = request.data.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                }, status=status.HTTP_200_OK
            )

        try:
            sales_officer_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist",
                    "data": []
                },
                status=status.HTTP_200_OK
            )

        try:
            calendar = create_calendar(calendar_title=calendar_title)
            return Response(
                {
                    "Message": "Calendar created successfully",
                    "Calendar": calendar
                }, status=status.HTTP_200_OK
            )
        except Exception as error:
            return Response(
                {
                    "message": error,
                    "data": []
                },
                status=status.HTTP_200_OK
            )


class GetCalendarView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request):
        calendarIds = request.GET.get("calendarIds")
        try:
            calendar = retrieve_calendar(calendarIds=calendarIds)
            return Response(
                {
                    "message": "Calendar retrieved successfully",
                    "Calendar": calendar,
                }, status=status.HTTP_200_OK
            )
        except Exception as error:
            return Response(
                {
                    "message": error,
                    "data": []
                },
                status=status.HTTP_200_OK
            )

    def patch(self, request):
        pass

    def delete(self, request):
        calendarIds = request.GET.get("calendarIds")
        pass


class GetCategoryAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def get(self, request, *args, **kwargs):
        user = request.user
        category_id = request.GET.get("category_id")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "You are currently not attached to any company.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            team_member_instance = TeamMember.objects.get(member=user, team=team_instance,
                                                          status=UserStatus.ACTIVE, is_deleted=False)

            team_member_company = team_member_instance.team.company
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "You do not have the permission to view the category.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if category_id:
            try:
                category_instance = Category.objects.get(id=category_id, team=team_instance)
                serialized_results = ReadCategorySerializer(category_instance)
                return Response(
                    {"message": "Category Found", "Category Data": serialized_results.data},
                    status=status.HTTP_200_OK
                )
            except Category.DoesNotExist:
                return Response(
                    {
                        "message": "Category does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        else:
            category_instances = Category.objects.filter(team=team_instance)
            serialized_results = ReadCategorySerializer(category_instances, many=True)
            total_number_of_category = category_instances.count()
            return Response(
                {
                    "Number of results": total_number_of_category,
                    "Results": serialized_results.data
                },
                status=status.HTTP_200_OK
            )


class GetAllLeadsInACategory(APIView):
    permission_classes = [IsAdmin]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        category_id = request.GET.get("category_id")
        param = request.GET.get("param")
        query = Q()
        paginated_data = {}

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "You are currently not attached to any company.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            team_member_instance = TeamMember.objects.get(member=user, team=team_instance,
                                                          status=UserStatus.ACTIVE, is_deleted=False)
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "You do not have the permission to view the category.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            category_instance = Category.objects.get(id=category_id, team=team_instance)
        except Category.DoesNotExist:
            return Response(
                {
                    "message": "Category does not exist.",
                    "data": []
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            lead_instances = Lead.objects.filter(stage__category=category_instance)
        except Lead.DoesNotExist:
            return Response(
                {
                    "message": "There are no Leads under this category.",
                    "data": []
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        if param:
            query &= Q(id__icontains=param) | Q(contact_name__icontains=param) | \
                     Q(contact_email__icontains=param) | Q(contact_phone_number__icontains=param) | \
                     Q(company_name__icontains=param) | Q(company_email__icontains=param) | \
                     Q(company_phone_number__icontains=param)

            # lead_instances = Lead.objects.filter(query, category__stage=category_instance).order_by('-created_at')
            lead_instances = Lead.objects.filter(query, stage__category=category_instance).order_by('-created_at')

        page = self.pagination_class.paginate_queryset(lead_instances, request, view=self)
        if page is not None:
            serialized_results = ReadLeadSerializer(page, many=True)
            paginated_data = self.pagination_class.get_paginated_response(serialized_results.data)

        return Response(
            {
                "message": "Successful",
                "paginated_results": paginated_data.data
            },
            status=status.HTTP_200_OK
        )


class CategoryAPIView(APIView):
    # permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    permission_classes = [IsAdmin]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination

    def post(self, request):
        user = request.user
        company_id = request.data.get("company_id")
        team_id = request.data.get("team_id")
        category_name = request.data.get("name")
        serializer = CategorySerializer(data=request.data)

        if serializer.is_valid():
            try:
                company_instance = Company.objects.get(id=company_id)
                team_instance = Team.objects.get(id=team_id, company=company_instance)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "You are currently not attached to any company.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            except Team.DoesNotExist:
                return Response(
                    {
                        "message": "Team does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                admin_instance = TeamMember.objects.get(member=user, team=team_instance,
                                                        status=UserStatus.ACTIVE, is_deleted=False)
                print("Company Name", company_instance.company_name, "\n\n")
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "You do not have the permission to create a category.",
                        "data": []
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            try:
                if category_name:
                    category_instance = Category.create(company=company_instance,
                                                        validated_data=serializer.validated_data)
                    serialized_category_instance = ReadCategorySerializer(category_instance)

                    return Response(
                        {
                            "message": "Category created successfully.",
                            "data": serialized_category_instance.data,
                        },
                        status=status.HTTP_201_CREATED
                    )
                else:
                    return Response(
                        {
                            "message": "Category Name cannot be null",
                            "data": []
                        },
                        status=status.HTTP_406_NOT_ACCEPTABLE
                    )

            except:
                return Response(
                    {
                        "message": "An error occurred. Please try again",
                        "data": []
                    },
                    status=status.HTTP_408_REQUEST_TIMEOUT
                )

        else:
            return Response(
                {
                    "message": "The data entered is incorrect, please try again.",
                    "error_details": serializer.errors,
                    "data": []
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # if serializer.is_valid():
        #     category_name = serializer.validated_data.get("name")
        #     Category.create(serializer.validated_data)
        #     return Response(
        #             {"message": "Category created successfully."},
        #             status=status.HTTP_201_CREATED
        #         )
        #
        # else:
        #     return Response(
        #         {
        #             "message": "The data entered is incorrect, please try again.",
        #             "error_details": serializer.errors,
        #             "data": []
        #         },
        #         status=status.HTTP_200_OK
        #

    def put(self, request, pk):
        category_instance = get_object_or_404(Category, pk=pk)

        serializer = CategorySerializer(instance=category_instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "message": "Successful",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK
            )
        return Response(
            {
                "message": "Successful",
                "error_details": serializer.errors
            },
            status=status.HTTP_200_OK
        )

    def delete(self, request):
        company_id = request.data.get("company_id")
        team_id = request.data.get("team_id")
        category_id = request.GET.get("category_id")

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "You are currently not attached to any company.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if category_id:
            try:
                category_instance = Category.objects.get(id=category_id, team=team_instance)
            except Category.DoesNotExist:
                return Response(
                    {
                        "message": "Category does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            category_instance.delete()
            return Response({"message": "Category has been deleted"}, status=status.HTTP_204_NO_CONTENT)


class GetProductVerticalsAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        user = request.user
        product_vertical_id = request.GET.get("product_vertical_id")
        company_id = request.GET.get("company_id")
        team_id = request.GET.get("team_id")
        param = request.GET.get("param")
        query = Q()

        try:
            company_instance = Company.objects.get(id=company_id)
            team_instance = Team.objects.get(id=team_id, company=company_instance)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except Team.DoesNotExist:
            return Response(
                {
                    "message": "Team does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            team_member_instance = TeamMember.objects.get(member=user, team=team_instance,
                                                          status=UserStatus.ACTIVE, is_deleted=False)

            team_member_company = team_member_instance.team.company

        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "User does not exist.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            company_instance = Company.objects.get(id=team_member_company.id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "You are currently not attached to any company.",
                    "data": []
                },
                status=status.HTTP_404_NOT_FOUND
            )

        if product_vertical_id:
            try:
                product_vertical_instance = ProductVerticals.objects.get(id=product_vertical_id, team=team_instance)
                serialized_results = ReadProductVerticalsSerializer(product_vertical_instance)
                return Response(
                    {"message": "Product Vertical Found", "Product Vertical Data": serialized_results.data},
                    status=status.HTTP_200_OK
                )
            except ProductVerticals.DoesNotExist:
                return Response(
                    {
                        "message": "Product Vertical does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

        elif param:
            query &= Q(name__icontains=param)
            # product_vertical_instance = ProductVerticals.objects.filter(query)
            product_vertical_instance = ProductVerticals.objects.filter(query, team=team_instance)

        else:
            product_vertical_instance = ProductVerticals.objects.filter(
                company=team_member_company,
                team=team_member_instance.team
            )

        serialized_results = ReadProductVerticalsSerializer(product_vertical_instance, many=True)
        total_number_of_product_verticals = product_vertical_instance.count()
        return Response(
            {
                "Number of results": total_number_of_product_verticals,
                "Results": serialized_results.data
            },
            status=status.HTTP_200_OK)


class ProductVerticalsAPIView(APIView):
    permission_classes = [IsAdmin]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def post(self, request):
        user = request.user
        # team_id = request.data.get("team_id")
        serializer = ProductVerticalsSerializer(data=request.data)

        if serializer.is_valid():

            company_id = serializer.validated_data.get("company_id")
            team_id = serializer.validated_data.get("team_id")
            name = serializer.validated_data.get("name")

            try:
                team_member_instance = TeamMember.objects.get(member=user, status=UserStatus.ACTIVE, is_deleted=False)
                team_member_team = team_member_instance.team
                team_member_company = team_member_instance.team.company
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "You do not have the permission to create a category.",
                        "data": []
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            try:
                if team_id:
                    team_instance = Team.objects.get(id=team_id)
                else:
                    team_instance = Team.objects.get(id=team_member_team.id)
            except Team.DoesNotExist:
                return Response(
                    {
                        "message": "Team does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                if company_id:
                    company_instance = Company.objects.get(id=company_id)
                else:
                    company_instance = Company.objects.get(id=team_member_company.id)

            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "You are currently not attached to any company.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            product_vertical_instance = ProductVerticals.create(team=team_instance,
                                                                validated_data=serializer.validated_data)
            serialized_product_vertical_instance = ProductVerticalsSerializer(product_vertical_instance)

            return Response(
                {
                    "message": "Product Vertical created successfully.",
                    "data": serialized_product_vertical_instance.data,
                },
                status=status.HTTP_201_CREATED)

        else:
            return Response(
                {
                    "message": "The data entered is incorrect, please try again.",
                    "error_details": serializer.errors,
                    "data": []
                },
                status=status.HTTP_200_OK
            )

    def put(self, request, pk):
        product_vertical_instance = get_object_or_404(ProductVerticals, pk=pk)

        serializer = ProductVerticalsSerializer(instance=product_vertical_instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "message": "Successful",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK
            )
        return Response(
            {
                "error_details": serializer.errors,
                "data": []
            },
            status=status.HTTP_200_OK
        )

    def delete(self, request):
        user = request.user
        company_id = request.GET.get("company_id")
        team_id = request.data.get("team_id")
        product_vertical_id = request.GET.get("product_vertical_id")

        if product_vertical_id:
            try:
                company_instance = Company.objects.get(id=company_id)
                team_instance = Team.objects.get(id=team_id, company=company_instance)
            except Company.DoesNotExist:
                return Response(
                    {
                        "message": "Company does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            except Team.DoesNotExist:
                return Response(
                    {
                        "message": "Team does not exist.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                admin_instance = TeamMember.objects.get(
                    member=user, team=team_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                    is_deleted=False
                )
            except TeamMember.DoesNotExist:
                return Response(
                    {
                        "message": "user is not an Admin.",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            try:
                product_vertical_instance = ProductVerticals.objects.get(id=product_vertical_id)
            except ProductVerticals.DoesNotExist:
                return Response(
                    {
                        "message": "Product Vertical does not exist",
                        "data": []
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            product_vertical_instance.delete()
            return Response({"message": "Product Vertical has been deleted"}, status=status.HTTP_204_NO_CONTENT)
        return Response(
            {
                "message": "Something went wrong. Please try again.",
                "data": []
            },
            status=status.HTTP_400_BAD_REQUEST
        )


class CheckSalesOfficerAvailabilityView(APIView):
    def post(self, request):
        timeMin = request.data.get("timeMin")
        timeMax = request.data.get("timeMax")
        calendars = request.data.get("calendars")

        try:
            free_busy_status = ZoomAPIntegration.check_schedule(timeMin=timeMin, timeMax=timeMax, calendars=calendars)
            if isinstance(free_busy_status, dict) and "error" in free_busy_status:
                return Response(
                    free_busy_status, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            else:
                return Response(
                    free_busy_status, status=status.HTTP_200_OK
                )
        except Exception as error:
            return Response(
                {
                    "An error": error,
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ZoomBearerTokenView(APIView):

    def post(self, request):
        """
        Endpoint to generate Zoom bearer token.
        """
        bearer_token = ZoomAPIntegration.generate_bearer_token()

        try:
            if bearer_token:
                return Response(
                    {
                        "message": "Bearer token generated successfully.",
                        "Bearer_Token": bearer_token,
                    }, status=status.HTTP_200_OK
                )
        except Exception as e:
            return Response(
                {
                    "message": "An error occured",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ZoomAccessTokenView(APIView):

    def post(self, request):
        """
        Endpoint to generate Zoom access token.
        """
        access_token = ZoomAPIntegration.generate_access_token()

        try:
            if access_token:
                return Response(
                    {
                        "message": "Access token generated successfully.",
                        "Access Token:": access_token,
                    }, status=status.HTTP_200_OK
                )
        except Exception as e:
            return Response(
                {
                    "message": "An error occured.",
                    "error": str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # if isinstance(access_token, dict) and 'error' in access_token:
        #     return Response(
        #         access_token, 
        #         status=status.HTTP_500_INTERNAL_SERVER_ERROR
        #     )
        # else:
        #     return Response(
        #         {"access_token": access_token}, 
        #         status=status.HTTP_200_OK
        #     )


class GetAllSalesOfficersView(APIView):

    # time_now = datetime.now()
    def get(self, request):

        try:
            all_sales_officers = [member.email for member in TeamMember.objects.all()]
            all_sales_officers_id = [member.id for member in TeamMember.objects.all()]
            print("ALL_SALES_OFFICERS::::", all_sales_officers, "\n\n\n")
            cache.set(key="sales_officers", value=all_sales_officers, timeout=60 * 45)
            cache.set(key="sales_officers_ids", value=all_sales_officers_id, timeout=60 * 45)
            # cache.set(key="calendarIds", value=all_sales_officers, timeout=60*45)

            cached_sales_officers = cache.get("sales_officers")
            cached_sales_officers_ids = cache.get("sales_officers_ids")
            # cached_calendar_ids = cache.get("calendarIds") 

            calendarIds = []
            for i in all_sales_officers:
                calendarIds.append({"id": i})
            cache.set(key="calendarIds", value=calendarIds, timeout=60 * 45)
            print("CALENDAR_IDS::::", calendarIds, "\n\n\n")
            # for i, j in zip(cached_sales_officers, cached_sales_officers_ids):
            #     calendarIds.append({"id": i, "email": j})

            return Response(
                {
                    "message": "Sales officers retrieved successfully.",
                    "sales_officers": all_sales_officers,
                    "cached_sales_officers": cached_sales_officers,
                    "sales_officers_ids": all_sales_officers_id,
                    "cached_sales_officers_ids": cached_sales_officers_ids,
                    "calendar_ids": calendarIds,
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while trying to retrieve sales officers.",
                    "error": str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CreateLeadOnlineView(APIView):
    def post(self, request):
        serializer = ReadLeadSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        company_name = serializer_data.get("company_name")
        company_email = serializer_data.get("company_email")
        contact_name = serializer_data.get("contact_name")
        contact_phone_number = serializer_data.get("contact_phone_number")
        country = serializer_data.get("country")
        how_did_you_hear_about_us = serializer_data.get("how_did_you_hear_about_us")
        employee_head_count = serializer_data.get("employee_head_count")
        monthly_transaction_amount_estimate = serializer_data.get("monthly_transaction_amount_estimate")
        product_vertical = serializer_data.get("product_vertical")

        try:
            sales_officers_names_emails = [
                {"name": f"{member.member.first_name} {member.member.last_name}", "email": member.email} for member in
                TeamMember.objects.all()]

            cache.set(key="sales_officers_names_emails", value=sales_officers_names_emails, timeout=60 * 45)

        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while attempting to retrieve sales officers.",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        payload = {
            "company_name": company_name,
            "work_email": company_email,
            "full_name": contact_name,
            "phone_number": contact_phone_number,
            "country": country,
            "how_did_you_hear_about_us": how_did_you_hear_about_us,
            "employee_head_count": employee_head_count,
            "monthly_transaction_amount_estimate": monthly_transaction_amount_estimate,
        }

        try:
            lead_details = Lead.create_lead_online(
                product_vertical=product_vertical,
                validated_data=payload,
            )
            cache.set(value=lead_details, key="lead_instance", timeout=60 * 45)

            if lead_details:
                cache.set(key="contact_name", value=contact_name, timeout=60 * 45)

            serialized_lead_details = ReadLeadSerializer(lead_details)

            return Response(
                {
                    "message": "Lead created successfully.",
                    "lead_details": serialized_lead_details.data,
                }, status=status.HTTP_201_CREATED
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while attempting to save lead demo details.",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CheckFreeBusyStatusView(APIView):
    pagination_class = CustomPagination

    def post(self, request):
        serializer = CheckFreeBusySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data
        timeMin = serializer_data.get("timeMin")
        calendarIds = cache.get("calendarIds")
        timeMax = get_time_max(timeMin=timeMin)

        try:
            free_busy_status = check_free_busy_status(
                calendarIds=calendarIds,
                timeMin=timeMin,
                timeMax=timeMax,
            )

            calendar_status = free_busy_status["calendars"]
            filtered_dict = {email: data for email, data in calendar_status.items() if
                             'errors' not in data and data['busy'] == []}
            dict_key = filtered_dict.keys()
            mail = [key for key in dict_key]

            if len(mail) > 0 and len(mail) < 2:
                free_sales_officer = mail[0]
                print("FREE_SALES_OFFICER::::", free_sales_officer, "\n\n\n")
                cache.set(value=free_sales_officer, key="sales_officer", timeout=60 * 45)
                free_officer = cache.get("sales_officer")
                print("FREE_SALES_OFFICER::::", free_officer, "\n\n\n")
            else:
                free_sales_officers = []
                for i in mail:
                    free_sales_officers.append(i)
                    num_of_so = len(free_sales_officers)
                    random_so = random.randint(1, num_of_so)
                    free_sales_officer = free_sales_officers[random_so]
                    print("FREE_SALES_OFFICER")
                    cache.set(value=free_sales_officer, key="sales_officer", timeout=60 * 45)

            # free_sales_officers = []
            # for i in filtered_dict.items():
            #     if len(filtered_dict) == 1:
            #        print("FILTERED_DICT::::", filtered_dict.keys(), "\n\n\n")
            #        print("DATA_TYPE_FILTERED_DICT::::", type(filtered_dict.keys()), "\n\n\n")
            #     else:
            #         free_sales_officers.append(i)
            #         num_of_so = len(free_sales_officers)
            #         random_so = random.randint(1, num_of_so)
            #         chosen_so = free_sales_officers[random_so]
            #         print("CHOSEN_SALES_OFFICER::::", chosen_so, "\n\n\n")
            # print("FREE_SALES_OFFICERS::::", free_sales_officers, "\n\n\n")
            return Response(
                {
                    "message": "Status retrieved",
                    "status": free_busy_status,
                    "available_sales_officer": free_sales_officer,
                }, status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred",
                    "error": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ZoomMeetView(APIView):
    pagination_class = CustomPagination

    def post(self, request):
        """
        Endpoint to create a new Zoom meeting.
        """
        start_time = request.data.get('start_time')
        cache.set(key="start_time", value=start_time, timeout=60 * 45)
        lead_email = cache.get("company_email")
        sales_officer = cache.get("sales_officer")
        cached_meeting_invitees = [lead_email, sales_officer]
        cache.set(key="attendees", value=cached_meeting_invitees, timeout=60 * 45)

        # meeting_invitees.append(lead_email)
        # meeting_invitees.append(sales_officer)
        meeting_invitees = []
        for meeting_invitee in cached_meeting_invitees:
            meeting_invitees.append({
                "email": meeting_invitee,
                # "email": sales_officer,
            })
        cache.set(key="meeting_invitees", value=meeting_invitees, timeout=60 * 45)

        if not start_time:
            return Response(
                {"error": "You must specify the start time to schedule a zoom meeting."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            zoom_meeting_details = ZoomAPIntegration.create_zoom_meeting(
                start_time=start_time,
                meeting_invitees=meeting_invitees,
            )
            cache.set(value=zoom_meeting_details, key="zoom_meeting_details", timeout=60 * 45)

            return Response(
                {
                    "message": "Zoom meeting created successfully",
                    "meeting_details": zoom_meeting_details
                }, status=status.HTTP_201_CREATED
            )
        except Exception as e:
            return Response(
                {
                    "An error occurred": str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get(self, request):
        """
        Endpoint to get a Zoom meeting.
        """

        # Extract the meetingId from the query parameters
        try:
            meeting_id = request.GET.get('meetingId')

        # if not meeting_id:
        # return JsonResponse({'error': 'meetingId is required'}, status=400)
        except Exception as e:
            return Response(
                {
                    "message": "An error occured while trying to get Zoom meeting.",
                    'error': str(e),
                }, status=status.HTTP_400_BAD_REQUEST
            )

        # try:
        #     bearer_token = zoom_integration.generate_bearer_token()
        # except Exception as e:
        #     return Response(
        #         {
        #             "error": str(e)
        #         }
        #     )

        try:
            # Call the get_zoom_meeting function
            meeting_info = ZoomAPIntegration.get_zoom_meeting(meeting_id)
            if isinstance(meeting_info, dict) and "error" in meeting_info:
                return Response(
                    meeting_info, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            else:
                return Response(
                    meeting_info, status=status.HTTP_200_OK
                )
        except Exception as e:
            return Response(
                {
                    "An Error occurred": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ScheduleZoomMeetingToGoogleView(APIView):
    def post(self, request):
        serializer = ScheduleZoomToGoogleSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        start_date = serializer_data.get("start_date")

        try:
            end_date = get_time_max(start_date)
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while attempting to get timeMax.",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        attendees = cache.get("meeting_invitees")
        print("GOOGLE_ATTENDEES::::", attendees, "\n\n\n")
        sales_officer_calendar_id = cache.get("sales_officer")
        lead_email = cache.get("company_email")
        lead_name = cache.get("contact_name")
        zoom_meeting_details = cache.get("zoom_meeting_details")
        lead_zoom_meeting_details = {
            "meeting_topic": zoom_meeting_details["meeting_topic"],
            "meeting_start_time": zoom_meeting_details["meeting_start_time"],
            "meeting_duration": zoom_meeting_details["meeting_duration"],
            "meeting_password": zoom_meeting_details["meeting_password"],
            "meeting_join_url": zoom_meeting_details["meeting_join_url"],
        }
        json_zoom_meeting = json.dumps(lead_zoom_meeting_details)

        try:
            zoom_meeting = schedule_event_on_google_calendar(
                attendees=attendees,
                start_date=start_date,
                end_date=end_date,
                calendarIds=sales_officer_calendar_id,
                zoom_meeting_details=zoom_meeting_details,
            )

            send_email.delay(
                recipient=lead_email,
                subject="PayBox360 Demo",
                zoom_meeting_details=json_zoom_meeting,
                template_dir="zoom_meeting_notification.html",
                lead_name=lead_name,
            )

            return Response(
                {
                    "message": "Zoom meeting scheduled successfully on google calendar.",
                    "meeting_details": zoom_meeting,
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occured while trying to save zoom meeting details to google calendar.",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LeadDemoDetailsView(APIView):
    def post(self, request):
        calendarIds = cache.get("calendarIds")
        start_time = request.data.get("start_time")
        parsed_start_time = datetime.strptime(start_time, "%Y-%m-%dT%H:%M:%S.000Z")
        sales_officers_names_emails = cache.get("sales_officers_names_emails")
        calendarIds = [{"id": member["email"]} for member in sales_officers_names_emails]

        try:
            demo_details = schedule_demo(timeMin=start_time, calendarIds=calendarIds)
            sales_officer = cache.get("sales_officer")
            name_of_sales_officer = [entry.get("name") for entry in sales_officers_names_emails if
                                     entry.get("email") == sales_officer]
            name_of_sales_officer = name_of_sales_officer[0]
            sales_officer = TeamMember.objects.get(email=sales_officer, status=UserStatus.ACTIVE,
                                                   is_deleted=False
                                                   )
            final_demo_details = {
                "sales_officer": name_of_sales_officer,
                "web_conferencing_details": demo_details["meeting_join_url"],
                "time_zone": "Lagos/WAT",
                "meeting_join_url": demo_details["meeting_join_url"],
                "meeting_start_time": demo_details["meeting_start_time"]
            }
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred.",
                    "error": str(e),
                }, status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        try:
            online_lead_details = cache.get("lead_instance")
            online_lead_details.sales_officer_in_charge = sales_officer
            online_lead_details.save()
            serialized_online_lead = ReadLeadSerializer(online_lead_details)

            demo_details = BookADemo.create(
                team_member=sales_officer,
                lead=online_lead_details,
                start_time=parsed_start_time,
            )

            serialized_demo_details = BookADemoSerializer(demo_details)

            return Response(
                {
                    "message": "Lead demo details saved successfully.",
                    "demo_details": serialized_demo_details.data,
                    "online_lead_details": serialized_online_lead.data,
                    "final_demo_details": final_demo_details,
                }, status=status.HTTP_201_CREATED
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while attempting to save lead demo details.",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class WorkEmailSetupView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = WorkEmailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        user = request.user
        print("USER:::::", user, "\n\n\n\n\n")
        # work_email = request.data.get("work_email")
        # password = request.data.get("password")
        # company_id = request.data.get("company_id")
        work_email = serializer_data.get("work_email")
        password = serializer_data.get("password")
        company_id = serializer_data.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            user_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False,
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist!",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            password = encrypt_password(password)
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while trying to encrypt email password!",
                    "error": str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        try:
            work_email, created = WorkEmail.objects.update_or_create(
                user=user,
                defaults={
                    "work_email": work_email,
                    "password": password,
                }
            )
            return Response(
                {
                    "message": "Work email setup successfully!",
                    "created": created,
                }, status=status.HTTP_200_OK
            )
        except WorkEmail.DoesNotExist:
            return Response(
                {
                    "message": "An error occurred while trying to save work email!"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetAuthUrlView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = GoogleAuthSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        user = request.user
        gmail_address = serializer_data.get("gmail_address")
        company_id = serializer_data.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            user_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False,
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist!",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            oauth_handler = GmailOAuthHandler()
            auth_url = oauth_handler.get_authorization_url(gmail_address)

            return Response(
                {
                    "authorization_url": auth_url,
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "OAuth2 flow failed!",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GmailSetupView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        token_data = request.data.get("token_data")
        serializer = GmailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        user = request.user
        gmail_address = serializer_data.get("gmail_address")
        company_id = serializer_data.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            user_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False,
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist!",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            oauth_handler = GmailOAuthHandler()
            gmail_detail = oauth_handler.store_tokens(user=user, gmail_address=gmail_address, token_data=token_data)
            serialized_gmail_detail = GoogleMailSerializer(gmail_detail)

            return Response(
                {
                    "gmail_detail": serialized_gmail_detail.data,
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "OAuth2 flow failed!",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetValidTokenView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = GoogleAuthSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        user = request.user
        gmail_address = serializer_data.get("gmail_address")
        company_id = serializer_data.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            user_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False,
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist!",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            oauth_handler = GmailOAuthHandler()
            valid_token = oauth_handler.get_valid_token(gmail_address)
            return Response(
                {
                    "valid_token": valid_token,
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "OAuth2 flow failed!",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RefreshTokenView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = GoogleAuthSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        user = request.user
        gmail_address = serializer_data.get("gmail_address")
        company_id = serializer_data.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            user_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False,
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist!",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            oauth_handler = GmailOAuthHandler()
            refreshed_token = oauth_handler.refresh_access_tokens(gmail_address)
            return Response(
                {
                    "refreshed_token": refreshed_token,
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "OAuth2 flow failed!",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class Oauth2StringView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = GoogleAuthSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        user = request.user
        gmail_address = serializer_data.get("gmail_address")
        company_id = serializer_data.get("company_id")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {
                    "message": "Company does not exist.",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            user_instance = TeamMember.objects.get(
                member=user, team__company=company_instance, role=UserRole.SALES_OFFICER, status=UserStatus.ACTIVE,
                is_deleted=False,
            )
        except TeamMember.DoesNotExist:
            return Response(
                {
                    "message": "Sales officer does not exist!",
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        try:
            oauth_handler = GmailOAuthHandler()
            oauth2_string = oauth_handler.get_oauth2_string(gmail_address)
            return Response(
                {
                    "oauth2_string": oauth2_string
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "OAuth2 flow failed!",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FetchTokenDataView(APIView):
    def post(self, request):
        code = request.data.get("code")

        try:
            oauth_handler = GmailOAuthHandler()
            token_data = oauth_handler.fetch_tokens(code)
            return Response(
                {
                    "message": "Token data retrieved successfully!",
                    "token_data": token_data,
                }, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while attempting to get token data",
                    "error": str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AuthCodeView(APIView):
    def get(self, request):
        print(request.query_params)
        params = request.query_params
        code_dict = params.dict()
        print("CODE_DICT", code_dict, "\n\n\n\n\n")
        state = code_dict.get("state")
        code = code_dict.get("code")

        try:
            oauth_handler = GmailOAuthHandler()
            token_data = oauth_handler.fetch_tokens(authorization_code=code)
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred!",
                    "error": str(e) 
                }
            )

        return Response(
                {
                    "message": "Successful!",
                    # "token_data": token_data,
                    "state": state,
                    "code": code,
                }, status=status.HTTP_200_OK
            )


class TestAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsSalesOfficer]
    # permission_classes = [IsSalesLead]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination()

    def get(self, request, *args, **kwargs):
        category_id = request.GET.get("category_id")
        category_instance = Category.objects.get(id=category_id)
        lead_instances = Lead.objects.filter(pipeline__stage__category=category_id)
        serialized_lead = ReadLeadSerializer(lead_instances, many=True)

        return Response(
            {"message": f"Leads from all stages relating to {category_instance.name} ", "data": serialized_lead.data},
            status=status.HTTP_200_OK
        )

    # def get(self, request, *args, **kwargs):
    #     pipeline_id = request.data.get("pipeline_id")
    #     stages = Stage.objects.filter(pipeline=pipeline_id)
    #     number_of_stages = Stage.objects.filter(pipeline=pipeline_id).count()
    #     serialized_data = StageSerializer(stages, many=True)
    #     return Response(
    #         {"message": "Pipeline is Valid", "Total Number of Stages in Pipeline": number_of_stages,
    #          "data": serialized_data.data, },
    #         status=status.HTTP_200_OK
    #     )

    # def get_lead_or_leads(self, request, *args, **kwargs):
    # def get_prospect_or_prospects(self, request, *args, **kwargs):
    #     lead_id = request.data.get("id")
    #
    #     if lead_id:
    #         try:
    #             lead_instance = Lead.objects.get(id=lead_id, entity=Entity.PROSPECT)
    #             serialized_results = ReadLeadSerializer(lead_instance)
    #             return Response(
    #                 {"message": "Lead Found", "data": serialized_results.data},
    #                 status=status.HTTP_200_OK
    #             )
    #
    #         except Lead.DoesNotExist:
    #             return Response(
    #                 {
    #                   "message": "Lead does not exist.",
    #                   "data": []
    #                 },
    #                     status=status.HTTP_404_NOT_FOUND
    #                 )
    #
    #     else:
    #         lead_instances = Lead.objects.filter(entity=Entity.PROSPECT)
    #         serialized_results = ReadLeadSerializer(lead_instances, many=True)
    #         total_number_of_leads = lead_instances.count()
    #         return Response(
    #             {
    #                 "Number of results": total_number_of_leads,
    #                 "Results": serialized_results.data
    #             },
    #             status=status.HTTP_200_OK)

    # def get(self, request, *args, **kwargs):
    # lead_id = request.data.get("id")
    #
    # if lead_id:
    #     try:
    #         lead_instance = Lead.objects.get(id=lead_id, entity=Entity.PROSPECT)
    #         serialized_results = ReadProspectSerializer(lead_instance)
    #         return Response(
    #             {"message": "Lead Found", "data": serialized_results.data},
    #             status=status.HTTP_200_OK
    #         )
    #
    #     except Lead.DoesNotExist:
    #         return Response(
    #             {"message": "Lead does not exist."},
    #             status=status.HTTP_404_NOT_FOUND
    #         )
    #
    # else:
    #     lead_instances = Lead.objects.filter(entity=Entity.PROSPECT)
    #     serialized_results = ReadProspectSerializer(lead_instances, many=True)
    #     total_number_of_leads = lead_instances.count()
    #     return Response(
    #         {
    #             "Number of results": total_number_of_leads,
    #             "Results": serialized_results.data
    #         },
    #         status=status.HTTP_200_OK)
    pass

# class MerchantListCreateAPIView(APIView):
#     def get(self, request):
#         merchants = Company.objects.all()
#         serializer = MerchantSerializer(merchants, many=True)
#         return Response(serializer.data)
#
#     def post(self, request):
#         serializer = MerchantSerializer(data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
#
#
# class MerchantDetailAPIView(APIView):
#     def get_object(self, pk):
#         try:
#             return Merchant.objects.get(pk=pk)
#         except Merchant.DoesNotExist:
#             raise Http404
#
#     def get(self, request, pk):
#         merchant = self.get_object(pk)
#         serializer = MerchantSerializer(merchant)
#         return Response(serializer.data)
#
#     def put(self, request, pk):
#         merchant = self.get_object(pk)
#         serializer = MerchantSerializer(merchant, data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
#
#     def delete(self, request, pk):
#         merchant = self.get_object(pk)
#         merchant.delete()
#         return Response(status=status.HTTP_204_NO_CONTENT)
#
#
# class SystemUsageListCreateAPIView(APIView):
#     def get(self, request):
#         system_usages = SystemUsageMetrics.objects.all()
#         serializer = SystemUsageSerializer(system_usages, many=True)
#         return Response(serializer.data)
#
#     def post(self, request):
#         serializer = SystemUsageSerializer(data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
#
#
# class SystemUsageDetailAPIView(APIView):
#     def get_object(self, pk):
#         try:
#             return SystemUsageMetrics.objects.get(pk=pk)
#         except SystemUsageMetrics.DoesNotExist:
#             raise Http404
#
#     def get(self, request, pk):
#         system_usage = self.get_object(pk)
#         serializer = SystemUsageSerializer(system_usage)
#         return Response(serializer.data)
#
#     def put(self, request, pk):
#         system_usage = self.get_object(pk)
#         serializer = SystemUsageSerializer(system_usage, data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
#
#     def delete(self, request, pk):
#         system_usage = self.get_object(pk)
#         system_usage.delete()
#         return Response(status=status.HTTP_204_NO_CONTENT)
#
#
# class HeadSalesLeadListCreateAPIView(APIView):
#     def get(self, request):
#         head_sales_leads = HeadSalesLead.objects.all()
#         serializer = HeadSalesLeadSerializer(head_sales_leads, many=True)
#         return Response(serializer.data)
#
#     def post(self, request):
#         serializer = HeadSalesLeadSerializer(data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
#
#
# class HeadSalesLeadDetailAPIView(APIView):
#     def get_object(self, pk):
#         try:
#             return HeadSalesLead.objects.get(pk=pk)
#         except HeadSalesLead.DoesNotExist:
#             raise Http404
#
#     def get(self, request, pk):
#         head_sales_lead = self.get_object(pk)
#         serializer = HeadSalesLeadSerializer(head_sales_lead)
#         return Response(serializer.data)
#
#     def put(self, request, pk):
#         head_sales_lead = self.get_object(pk)
#         serializer = HeadSalesLeadSerializer(head_sales_lead, data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
#
#     def delete(self, request, pk):
#         head_sales_lead = self.get_object(pk)
#         head_sales_lead.delete()
#         return Response(status=status.HTTP_204_NO_CONTENT)
# class GetLedsInStagesByPipeline(APIView):
#     permission_classes = [IsAuthenticated]
#     # permission_classes = [IsSalesOfficer]
#     # permission_classes = [IsSalesLead]
#     authentication_classes = [CustomUserAuthentication]
#     pagination_class = CustomPagination
#
#     def get(self, request, *args, **kwargs):
#         user = request.user
#         pipeline_id = request.GET.get("pipeline_id")
#
#         try:
#             sales_officer_instance = SalesOfficer.objects.get(user=user)
#         except SalesOfficer.DoesNotExist:
#             return Response(
#                 {"message": "Sales Officer does not exist."},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#
#         try:
#             pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
#         except Pipeline.DoesNotExist:
#             return Response({"message": "Pipeline does not exist"}, status=status.HTTP_404_NOT_FOUND)
#
#
#         teeter_totter = []
#         pipeline_stages = Stage.objects.filter(pipeline=pipeline_instance).order_by('-created_at')
#         # number_of_stages_in_pipeline = pipeline_stages.count()
#         # serialized_stages = ReadStageSerializer(pipeline_stages, many=True)
#
#         # leads_in_stage = Lead.objects.filter(stage=pipeline_stages).select_related('stage')
#
#         # leads_in_stage = (
#         #     Lead.objects.filter(stage__in=pipeline_stages)
#         #     .select_related('stage')
#         # )
#         # serialized_stages = ReadLeadSerializer(leads_in_stage, many=True)
#         # print(serialized_stages.data, "\n\n")
#
#         for stage in pipeline_stages:
#             # leads_in_stage = Lead.objects.filter(stage=stage).select_related('stage')
#             try:
#                 leads_in_stage = (
#                     Lead.objects.get(stage=stage)
#                     .select_related('stage')
#                 )
#                 # serialized_stages = ReadLeadSerializer(leads_in_stage, many=True)
#                 serialized_stages = ReadStageAndLeadSerializer(leads_in_stage, many=True)
#                 teeter_totter.append(serialized_stages)
#                 print(serialized_stages.data, "\n\n")
#             except:
#                 pass
#
#         return Response(
#             {
#                 "message": f"Stages in {pipeline_instance.name}",
#                 # "Number of Data": number_of_stages_in_pipeline,
#                 # "data": serialized_stages.data,
#                 # "data": serialized_stages.data
#                 "data": teeter_totter
#             },
#             status=status.HTTP_200_OK)

# class GetLedsInStagesByPipeline(APIView):
#     permission_classes = [IsAuthenticated]
#     # permission_classes = [IsSalesOfficer]
#     # permission_classes = [IsSalesLead]
#     authentication_classes = [CustomUserAuthentication]
#     pagination_class = CustomPagination
#
#     def get(self, request, *args, **kwargs):
#         user = request.user
#         pipeline_id = request.GET.get("pipeline_id")
#
#         try:
#             sales_officer_instance = SalesOfficer.objects.get(user=user)
#         except SalesOfficer.DoesNotExist:
#             return Response(
#                 {"message": "Sales Officer does not exist."},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#
#         try:
#             pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
#         except Pipeline.DoesNotExist:
#             return Response({"message": "Pipeline does not exist"}, status=status.HTTP_404_NOT_FOUND)
#
#         pipeline_stages = Stage.objects.filter(pipeline=pipeline_instance).order_by('-created_at')
#
#         true_that = []
#         for stage in pipeline_stages:
#             try:
#                 leads_in_stage = (
#                     Lead.objects.get(stage=stage)
#                     .select_related('stage')
#                 )
#                 serialized_stages = ReadLeadSerializer(leads_in_stage, many=True)
#                 true_that.append(serialized_stages)
#
#             except:
#                 pass
#
#         return Response(
#             {
#                 # "message": f"Stages in {pipeline_instance.name}",
#                 # "data": serialized_stages.data
#                 "data": true_that
#             },
#             status=status.HTTP_200_OK)
#
# from django.db.models import Prefetch
#
# class GetLedsInStagesByPipeline(APIView):
#     permission_classes = [IsAuthenticated]
#     authentication_classes = [CustomUserAuthentication]
#     pagination_class = CustomPagination()
#
#     def get(self, request, *args, **kwargs):
#         user = request.user
#         pipeline_id = request.GET.get("pipeline_id")
#
#         try:
#             sales_officer_instance = SalesOfficer.objects.get(user=user)
#         except SalesOfficer.DoesNotExist:
#             return Response(
#                 {"message": "Sales Officer does not exist."},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#
#         try:
#             pipeline_instance = Pipeline.objects.get(id=pipeline_id, sales_officer=sales_officer_instance)
#         except Pipeline.DoesNotExist:
#             return Response({"message": "Pipeline does not exist"}, status=status.HTTP_404_NOT_FOUND)
#
#         # Use prefetch_related to fetch stages and their associated leads in a single query.
#         pipeline_stages = Stage.objects.filter(pipeline=pipeline_instance).order_by('-created_at') \
#             .prefetch_related(
#                 Prefetch(
#                     'lead_set',  # Assuming 'lead_set' is the default related_name for Lead's ForeignKey to Stage
#                     queryset=Lead.objects.only('id', 'contact_name', 'created_at'),  # Load only necessary fields for performance
#                     to_attr='leads'
#                 )
#             )
#
#         number_of_stages_in_pipeline = pipeline_stages.count()
#         serialized_stages = ReadStageSerializer(pipeline_stages, many=True, context={'leads': True})
#
#         return Response(
#             {
#                 # "message": f"Stages in {pipeline_instance.name}",
#                 "Number of Data": number_of_stages_in_pipeline,
#                 "data": serialized_stages.data,
#             },
#             status=status.HTTP_200_OK
#         )
