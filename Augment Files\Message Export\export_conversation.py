#!/usr/bin/env python
"""
Conversation Export Script

This script exports a conversation with a specific ID to a file.
Usage: python export_conversation.py <conversation_id> [output_file]
"""

import sys
import os
import json
import datetime
import argparse
from pathlib import Path

def setup_argparse():
    """Set up command line argument parsing."""
    parser = argparse.ArgumentParser(description='Export a conversation to a file.')
    parser.add_argument('conversation_id', help='The ID of the conversation to export')
    parser.add_argument('-o', '--output', help='Output file path (default: conversation_<id>.json)')
    parser.add_argument('-f', '--format', choices=['json', 'txt', 'md', 'html'], default='json',
                        help='Output format (default: json)')
    parser.add_argument('-p', '--pretty', action='store_true', help='Pretty print JSON output')
    return parser

def find_conversation(conversation_id):
    """
    Find a conversation by its ID.
    
    This is a placeholder function. In a real implementation, this would query
    a database or API to retrieve the conversation data.
    """
    # Placeholder for demonstration purposes
    # In a real implementation, you would query your data source
    
    # Example conversation data structure
    conversation = {
        "id": conversation_id,
        "timestamp": datetime.datetime.now().isoformat(),
        "participants": ["User", "Assistant"],
        "messages": [
            {
                "sender": "User",
                "content": "Hello, can you help me with something?",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=5)).isoformat()
            },
            {
                "sender": "Assistant",
                "content": "Of course! I'm here to help. What do you need assistance with?",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=4)).isoformat()
            },
            {
                "sender": "User",
                "content": "I'm trying to understand how to export conversations.",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=3)).isoformat()
            },
            {
                "sender": "Assistant",
                "content": "I can definitely help with that. Let me explain the process...",
                "timestamp": (datetime.datetime.now() - datetime.timedelta(minutes=2)).isoformat()
            }
        ]
    }
    
    return conversation

def export_as_json(conversation, output_file, pretty=False):
    """Export conversation as JSON."""
    with open(output_file, 'w', encoding='utf-8') as f:
        if pretty:
            json.dump(conversation, f, indent=2)
        else:
            json.dump(conversation, f)
    return output_file

def export_as_text(conversation, output_file):
    """Export conversation as plain text."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"Conversation ID: {conversation['id']}\n")
        f.write(f"Timestamp: {conversation['timestamp']}\n")
        f.write(f"Participants: {', '.join(conversation['participants'])}\n\n")
        
        for message in conversation['messages']:
            f.write(f"{message['sender']} ({message['timestamp']}):\n")
            f.write(f"{message['content']}\n\n")
    return output_file

def export_as_markdown(conversation, output_file):
    """Export conversation as Markdown."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"# Conversation {conversation['id']}\n\n")
        f.write(f"**Timestamp:** {conversation['timestamp']}\n")
        f.write(f"**Participants:** {', '.join(conversation['participants'])}\n\n")
        
        for message in conversation['messages']:
            f.write(f"### {message['sender']} ({message['timestamp']})\n\n")
            f.write(f"{message['content']}\n\n")
    return output_file

def export_as_html(conversation, output_file):
    """Export conversation as HTML."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("<!DOCTYPE html>\n<html>\n<head>\n")
        f.write("<meta charset='utf-8'>\n")
        f.write(f"<title>Conversation {conversation['id']}</title>\n")
        f.write("<style>\n")
        f.write("body { font-family: Arial, sans-serif; margin: 20px; }\n")
        f.write(".message { margin-bottom: 20px; }\n")
        f.write(".sender { font-weight: bold; }\n")
        f.write(".timestamp { color: #666; font-size: 0.8em; }\n")
        f.write(".content { margin-top: 5px; }\n")
        f.write("</style>\n</head>\n<body>\n")
        
        f.write(f"<h1>Conversation {conversation['id']}</h1>\n")
        f.write(f"<p><strong>Timestamp:</strong> {conversation['timestamp']}</p>\n")
        f.write(f"<p><strong>Participants:</strong> {', '.join(conversation['participants'])}</p>\n")
        
        for message in conversation['messages']:
            f.write("<div class='message'>\n")
            f.write(f"<div class='sender'>{message['sender']}</div>\n")
            f.write(f"<div class='timestamp'>{message['timestamp']}</div>\n")
            f.write(f"<div class='content'>{message['content']}</div>\n")
            f.write("</div>\n")
        
        f.write("</body>\n</html>")
    return output_file

def main():
    """Main function to export a conversation."""
    parser = setup_argparse()
    args = parser.parse_args()
    
    conversation_id = args.conversation_id
    output_format = args.format
    
    # Set default output filename if not provided
    if not args.output:
        output_file = f"conversation_{conversation_id}.{output_format}"
    else:
        output_file = args.output
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Find the conversation
    try:
        conversation = find_conversation(conversation_id)
    except Exception as e:
        print(f"Error finding conversation: {e}")
        sys.exit(1)
    
    # Export the conversation in the specified format
    try:
        if output_format == 'json':
            export_as_json(conversation, output_file, args.pretty)
        elif output_format == 'txt':
            export_as_text(conversation, output_file)
        elif output_format == 'md':
            export_as_markdown(conversation, output_file)
        elif output_format == 'html':
            export_as_html(conversation, output_file)
        
        print(f"Conversation exported to {output_file}")
    except Exception as e:
        print(f"Error exporting conversation: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
