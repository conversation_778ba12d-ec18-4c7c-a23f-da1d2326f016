import sys
from django.core.management.commands.test import Command as TestCommand
import coverage


class Command(TestCommand):
    def handle(self, *test_labels, **options):
        # Start coverage
        cov = coverage.Coverage(source=["."])
        cov.start()

        # Run tests
        super().handle(*test_labels, **options)

        # Stop coverage and save data
        cov.stop()
        cov.save()

        # Report the coverage
        cov.report(file=sys.stdout)

        # Optional: Generate an HTML report
        # cov.html_report(directory='htmlcov')
