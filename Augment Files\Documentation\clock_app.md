# Clock App Documentation

## Overview

The Clock app manages time tracking, attendance, and work scheduling for companies on the Paybox360 platform. It allows businesses to monitor employee work hours, breaks, overtime, and attendance while providing tools for shift management and meeting coordination.

## Core Features

### 1. Time Tracking

- Clock in/out functionality
- Work hour calculation
- Break management
- Overtime tracking

### 2. Attendance Management

- Daily attendance records
- Attendance sheet generation
- Absence tracking
- Late arrival monitoring

### 3. Task Management

- Task assignment and tracking
- Work activity monitoring
- Productivity measurement
- Task completion reporting

### 4. Location Tracking

- Work location monitoring
- Geofencing capabilities
- Remote work verification
- Location history

### 5. Shift Management

- Shift creation and assignment
- Shift scheduling
- Shift rotation management
- Schedule compliance tracking

### 6. Meeting Coordination

- Company meeting scheduling
- Meeting attendance tracking
- Meeting notifications
- Virtual meeting links

## How It Works

1. **Clock Setup**: Companies configure time tracking settings and policies
2. **Shift Assignment**: Employees are assigned to specific shifts
3. **Clock In/Out**: Employees record work start and end times
4. **Break Tracking**: Break periods are recorded during work hours
5. **Attendance Monitoring**: System tracks attendance patterns and exceptions
6. **Reporting**: Work hours and attendance data are compiled into reports

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `Clock` | Time tracking records | `clock_in`, `clock_out`, `status` | User, Company |
| `Break` | Break time records | `break_start`, `break_end` | Clock |
| `Location` | Work location data | `latitude`, `longitude`, `address` | Clock |
| `OverTime` | Overtime records | `hours`, `approval_status` | User, Company |
| `Record` | Daily attendance | `date`, `status`, `hours_worked` | User, Company |
| `Task` | Work activities | `title`, `description`, `status` | User |
| `Screenshot` | Work verification | `image`, `timestamp` | Clock |
| `Shift` | Work schedules | `start_time`, `end_time`, `days` | Company |
| `AttendanceSheet` | Attendance summary | `month`, `year`, `status` | Company |
| `CompanyBranchInvite` | Branch invitations | `email`, `status` | Company |
| `CompanyMeeting` | Meeting records | `title`, `start_time`, `end_time` | Company |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `calculate_hours_worked` | Computes work duration | `models.py` (Clock) | Used in attendance reporting |
| `verify_location` | Validates work location | `utils.py` | Called during clock in/out |
| `generate_attendance_sheet` | Creates attendance reports | `services.py` | Used in attendance views |
| `process_overtime` | Calculates overtime hours | `models.py` (OverTime) | Used in payroll integration |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/clock-app/clock-in/` | Record work start | POST | `ClockInAPIView` |
| `/clock-app/clock-out/` | Record work end | POST | `ClockOutAPIView` |
| `/clock-app/breaks/` | Manage break time | POST/GET | `BreakAPIView` |
| `/clock-app/attendance/` | View attendance | GET | `AttendanceAPIView` |
| `/clock-app/shifts/` | Manage shifts | POST/GET | `ShiftAPIView` |
| `/clock-app/meetings/` | Manage meetings | POST/GET | `CompanyMeetingAPIView` |

### Integration Points

- **Payroll App**: For overtime and attendance data
- **Core App**: For user authentication and permissions
- **Geolocation Services**: For location verification
- **Notification System**: For alerts and reminders

## Key Considerations

### 1. Time Accuracy

- **Responsible App**: Clock app
- **Key Functions**:
  - Precise timestamp recording in Clock model
  - Time zone handling for global companies
  - Time calculation algorithms in `calculate_hours_worked`

### 2. Location Verification

- **Responsible App**: Clock app
- **Key Functions**:
  - Geofencing in `verify_location`
  - Location history tracking in Location model
  - Address resolution from coordinates

### 3. Attendance Compliance

- **Responsible App**: Clock app
- **Key Functions**:
  - Attendance policy enforcement
  - Late arrival detection
  - Absence tracking and categorization

### 4. Shift Management

- **Responsible App**: Clock app
- **Key Functions**:
  - Shift assignment in Shift model
  - Schedule conflict detection
  - Shift rotation algorithms

### 5. Overtime Calculation

- **Responsible App**: Clock app
- **Key Functions**:
  - Overtime threshold configuration
  - Approval workflow in OverTime model
  - Integration with payroll for compensation