from django.core.management.base import BaseCommand
from django.conf import settings
from datetime import datetime
import pytz
from django.contrib.auth import get_user_model
from requisition import models as req
from performance_sales_metrics_dashboard import models as perf
from account import models as acc
from account import enums

User = get_user_model()

class Command(BaseCommand):
    help = 'Calculate and update revenue lines based on existing transactions.'

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        # User ID to filter companies and merchants
        user_id = "8656c269-fcfa-4d3d-8b51-7ca04c15e83a"

        # Get all companies related to the specified user
        companies = req.Company.objects.filter(user__id=user_id)
        print("COMPANIES :::::::::::::::::", companies)
        if not companies.exists():
            self.stdout.write(self.style.ERROR(
                "No companies found in the database for the specified user."))
            return

        for company in companies:
            self.stdout.write(self.style.SUCCESS(f"Processing company: {company.company_name}"))

            # Get all users associated with this company
            users_in_company = acc.User.objects.filter(id=user_id)
            print("USER:::::::::::::::::", users_in_company)

            # Get all successful transactions for the specified user in this company
            transactions = acc.Transaction.objects.filter(
                user__in=users_in_company,
                status=enums.TransactionStatus.SUCCESSFUL  # Filter for successful transactions
            )
            print("TRANSACTION::::::::::::::::::::::", transactions)

            if not transactions.exists():
                self.stdout.write(self.style.WARNING(
                    f"No successful transactions found for company: {company.company_name}"))
                continue

            # Get all product verticals
            product_verticals = perf.ProductVerticals.objects.all()
            print("P_VERTICALS::::::::::::::::", product_verticals)

            # Dictionary to hold the total amount per vertical
            vertical_amounts = {}
            print("VERTICALS AMOUNT::::::::::::::::::", product_verticals)
            # Match transactions with product verticals
            for txn in transactions:
                self.stdout.write(self.style.SUCCESS(
                    f"Processing transaction: {txn.id} for amount: {txn.amount}"))

                # Match the first four letters of payout_type with product verticals
                for vertical in product_verticals:
                    if txn.payout_type[:4] == vertical.name[:4]:
                        if vertical not in vertical_amounts:
                            vertical_amounts[vertical] = 0.00
                        vertical_amounts[vertical] += txn.amount
                        self.stdout.write(self.style.SUCCESS(
                            f"Matched vertical: {vertical.name} with amount: {txn.amount}"))

            if not vertical_amounts:
                self.stdout.write(self.style.WARNING(
                    f"No matching verticals found for transactions in company: {company.company_name}"))
                continue

            # Create or update revenue lines
            merchant = company.merchants.first()
            if not merchant:
                self.stdout.write(self.style.ERROR(
                    f"No merchants found for company: {company.company_name}"))
                continue

            for vertical, amount in vertical_amounts.items():
                revenue_line, created = perf.RevenueLines.objects.get_or_create(
                    merchant=merchant,
                    vertical=vertical,
                    defaults={'amount': amount}
                )
                if created:
                    self.stdout.write(self.style.SUCCESS(
                        f"Created revenue line for vertical: {vertical.name} with amount: {amount}"))
                else:
                    revenue_line.amount += amount
                    revenue_line.save()
                    self.stdout.write(self.style.SUCCESS(
                        f"Updated revenue line for vertical: {vertical.name} with new amount: {revenue_line.amount}"))

        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"Revenue lines updated successfully in {(END_TIME - START_TIME).total_seconds()} seconds"))
