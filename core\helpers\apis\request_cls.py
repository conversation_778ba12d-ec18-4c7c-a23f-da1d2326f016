import json
from dataclasses import dataclass

import requests
from django.conf import settings

@dataclass
class LibertyPayPlus:
    base_url = settings.LIBERTY_PAY_PLUS_BASE_URL

    @classmethod
    def request_user_details(cls, auth_token):
        url = f"{cls.base_url}/agency/user/get_user_details/"
        print("URL::::::::", url)
        payload = ""
        headers = {'Authorization': f'Bearer {auth_token}'}
        try:
            res = requests.request("GET", url, headers=headers, data=payload)
            if res.status_code == 200:
                return {"status": True, "response": res.json()}
            else:
                return {"status": False, "response": res.text}

        except requests.exceptions.RequestException as err:

            return {"status": False, "response": err}

    @classmethod
    def login_and_password_verification(cls, email, password):
        payload = json.dumps({
            "email": email,
            "password": str(password),
            "device_type": "MOBILE"
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", f"{cls.base_url}/user/login/create/", headers=headers, data=payload)

        # print(response.text)
        if response.status_code == 200:
            return {"status": True, "data": response.json()}
        else:
            return {"status": False, "data": response.json()}

    @classmethod
    def verify_transaction_pin(cls, auth_token, transaction_pin):

        payload = json.dumps({
            "transaction_pin": f"{transaction_pin}"
        })
        headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", f"{cls.base_url}/agency/user/verify_transaction_pin/", headers=headers,
                                    data=payload)
        return response.status_code

    @classmethod
    def send_money_bank_transfer(cls,
                                 account_number,
                                 auth_token,
                                 account_name,
                                 bank_code,
                                 bank_name,
                                 amount,
                                 from_account_number,
                                 transaction_pin,
                                 transaction_ref
                                 ):
        url = f"{cls.base_url}/send/send_money_bank_account/"

        payload = json.dumps({
            "from_wallet_type": "COLLECTION",
            "data": [
                {
                    "account_number": f"{account_number}",
                    "account_name": f"{account_name}",
                    "bank_code": f"{bank_code}",
                    "bank_name": f"{bank_name}",
                    "amount": int(amount),
                    "narration": "Requisition",
                    "is_beneficiary": "False",
                    "save_beneficiary": "True",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                    "ledger_commission": 20,
                    "commission_type": None,
                    "customer_reference": f"{transaction_ref}",
                    "from_account_number": f"{from_account_number}"

                }
            ],
            "total_amount": 100,
            "total_amount_with_charge": 130,
            "transaction_pin": f"{transaction_pin}"
        })
        headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        return response.json()
      

    @classmethod
    def fetch_account_name(cls, account_number, bank_code, auth_token):

        url = f"{cls.base_url}/send/fetch_account_name/"

        payload = json.dumps({
            "account_number": f"{account_number}",
            "bank_code": f"{bank_code}"
        })
        headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        return {"status": True, "data": response.json()}

    @classmethod
    def send_buddy(cls, buddy_phone_number, amount, auth_token, transaction_ref, transaction_pin):

        url = f"{cls.base_url}/send/send_money_paybuddy/"

        payload = json.dumps(
            {
                "from_wallet_type": "COLLECTION",
                "to_wallet_type": "COLLECTION",
                "data": [
                    {
                        "buddy_phone_number": f"{buddy_phone_number}",
                        "amount": int(amount),
                        "narration": "Requisition",
                        "is_beneficiary": "False",
                        "save_beneficiary": "True",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                        "customer_reference": f"{transaction_ref}"

                    }
                ],
                "transaction_pin": f"{transaction_pin}"
            })
        headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        return response.json()

    @classmethod
    def send_payroll_buddy(cls, buddy_phone_number, amount, auth_token, transaction_ref, narration, transaction_pin):

        url = f"{cls.base_url}/send/send_money_paybuddy/"

        payload = json.dumps(
            {
                "from_wallet_type": "COLLECTION",
                "to_wallet_type": "COLLECTION",
                "data": [
                    {
                        "buddy_phone_number": f"{buddy_phone_number}",
                        "amount": int(amount),
                        "narration": narration,
                        "is_beneficiary": "False",
                        "save_beneficiary": "True",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                        "customer_reference": f"{transaction_ref}"

                    }
                ],
                "transaction_pin": f"{transaction_pin}"
            })
        headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
        response = requests.request("POST", url, headers=headers, data=payload)

        return response.json()

    @classmethod
    def expected_user_wallet_details(cls, auth_token):
        liberty_pay_user_details = cls.request_user_details(auth_token=auth_token)
        if liberty_pay_user_details.get("status"):
            user_data = liberty_pay_user_details.get("response")
            # pprint(user_data)
            """
                    :Sample response
                    user_data = {
                    "accounts_data": [
                        {
                            "account_type": "OTHERS",
                            "true_account_type": "CORPORATE",
                            "bank_name": "VFD Microfinance Bank",
                            "account_number": "**********",
                            "account_name": "Tech Zonev1-Dev35",
                            "other_balance": 50000
                        },
                        {
                            "account_type": "OTHERS",
                            "true_account_type": "CORPORATE",
                            "bank_name": "VFD Microfinance Bank",
                            "account_number": "**********",
                            "account_name": "Tech Zonev1-Dev34",
                            "other_balance": 40000.0
                        },
                        {
                            "account_type": "OTHERS",
                            "true_account_type": "MAIN",
                            "bank_name": "VFD Microfinance Bank",
                            "account_number": "**********",
                            "account_name": "Tech Zonev1-Dev34",
                            "other_balance": 40000.0
                        }
                    ],
                    "wallets_data": [
                        {
                            "wallet_type": "SPEND",
                            "available_balance": 0.0,
                            "hold_balance": 0.0
                        },
                        {
                            "wallet_type": "COLLECTION",
                            "available_balance": 4716.43,
                            "hold_balance": 0.0
                        }
                    ]
                }

                    """

            # sum all corporate balance to get main wallet balance
            # -----------------------------------------------------------
            account_data = user_data.get("accounts_data")
            total_corporate_balance = sum(
                data.get("other_balance") for data in account_data if data.get("true_account_type") == "CORPORATE")

            wallet_data = user_data.get("wallets_data")

            main_bal = next(
                (data.get("available_balance") for data in wallet_data if data.get("wallet_type") == "COLLECTION"), 0)

            expected_wallet_details = {
                "main_balance": main_bal,
                "total_corporate_balance": total_corporate_balance,
                "total_main_balance": (main_bal - total_corporate_balance),
                "user_data": user_data
            }

            return expected_wallet_details

        else:
            return None

    @classmethod
    def login(cls, email, password):

        url = f"{cls.base_url}/user/login/create/"

        payload = json.dumps({
            "email": f"{email}",
            "password": f"{password}",
            "device_type": "MOBILE"
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        return response.json()

    @classmethod
    def verify_password(cls, auth_token, password):

        url = f"{cls.base_url}/agency/user/verify_password/"

        payload = json.dumps({
            "otp_value": f"{password}"
        })
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {auth_token}'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        return response

    @classmethod
    def verify_requisition_transaction(cls, transaction_ref):
        url = f"{cls.base_url}/accounts/fetch_trans_escrow_id?escrow_id={transaction_ref}"

        headers = {
            'Authorization': f'token {settings.LIBERTY_PAY_CORPORATE_TOKEN} ',
            'Content-Type': 'application/json'
        }
        payload = ""
        response = requests.request("GET", url, headers=headers, data=payload)
        return response.json()

    @classmethod
    def fetch_liberty_pay_users(cls):
        url = f"{cls.base_url}/agency/loans/all_user_details/"

        payload = {}
        headers = {"Authorization": f"Bearer {settings.LIBERTY_PAY_CORPORATE_TOKEN}"}

        resp = requests.request("GET", url, headers=headers, data=payload)
        response = resp.json()

        return response

    @classmethod
    def get_user_kyc_details(cls, email, bvn_number):
        url = f"{cls.base_url}/api/admin/get-user-kyc-details"

        payload = json.dumps({
            "user-email": f"{email}",
            "bvn-number": f"{bvn_number}"
        })
        headers = {"Authorization": f"Bearer {settings.LIBERTY_PAY_CORPORATE_TOKEN}",
                   'Content-Type': 'application/json'}

        resp = requests.request("POST", url, headers=headers, data=payload)
        # print(resp)
        # print(resp.status_code)
        if resp.status_code == 200:
            response = resp.json()
            return response
        else:
            return None

    @classmethod
    def request_username_exist(cls, auth_token, username):
        url = f"{cls.base_url}/agency/user/fetch_sign_up_username/?username={username}"
        payload = ""
        headers = {'Authorization': f'Bearer {auth_token}'}
        try:
            res = requests.request("GET", url, headers=headers, data=payload)
            return res.json()
        except requests.exceptions.RequestException as err:
            return {"status": False, "response": err}