from requisition.helpers.enums import User<PERSON><PERSON>
from requisition.models import TeamMember
from ..models import Emails

def save_to_drafts(
        is_draft, 
        sales_officer, 
        date, 
        subject,
        sender, 
        senders_name, 
        cc,
        bcc,
        recipients, 
        content, 
    ):
    try:
        date_sent = Emails.objects.get(date=date)
        Emails.objects.get(date=date_sent.date)
            # continue
    except Emails.DoesNotExist:
    # except:
        sales_officer_instance = TeamMember.objects.get(
                id=sales_officer, role=UserRole.SALES_OFFICER
            )
        draft_email = Emails.save_as_draft(
            is_draft=is_draft,
            sales_officer=sales_officer_instance, 
            date=date, 
            subject=subject, 
            sender=sender,
            senders_name=senders_name,
            cc=cc,
            bcc=bcc,
            recipients=recipients, 
            content=content, 
        )
        return draft_email
    return None


