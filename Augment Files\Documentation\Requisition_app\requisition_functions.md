# Requisition App Technical Documentation

## Overview

This document provides detailed technical documentation of functions within the requisition app, including cross-references and other technical details. The requisition app is a spend management system that allows companies to manage, approve, and disburse funds for various business expenses.

## Core Functions

### 1. Requisition Management Functions

| Function                           | Purpose                                                    | Location                        | Line     | Cross-References                                                                   |
| ---------------------------------- | ---------------------------------------------------------- | ------------------------------- | -------- | ---------------------------------------------------------------------------------- |
| `requisition_disbursement`         | Processes fund transfers to approved requisitions          | `models.py` (Requisition class) | Line 978 | Called in `Disbursement` view (Line 217), `SmsDisbursement` view                   |
| `purchase_ident_with_budget_check` | Validates procurement requests against budget availability | `utils.py`                      | Line 30  | Used in `PurchaseIndentSerializerIn`, `ApprovePurchaseIndentSerializer` (Line 173) |
| `create_ident_products`            | Creates product items for purchase indents                 | `utils.py`                      | Line 14  | Used in procurement creation serializers (Line 165)                                |
| `process_invoice_file_2`           | Extracts data from invoice files using OCR                 | `helpers/receipt_extract.py`    | Line 63  | Called in `ReadExpenseReceipt` view                                                |
| `prompt_engineering`               | Processes OCR text to extract structured invoice data      | `helpers/receipt_extract.py`    | Line 3   | Called by `process_invoice_file_2` (Line 74)                                       |
| `ocr_and_extract_text`             | Performs OCR on image data                                 | `helpers/receipt_extract.py`    | Line 45  | Called by `process_invoice_file_2` (Line 73)                                       |

### 2. Budget Management Functions

| Function                       | Purpose                                           | Location                             | Line     | Cross-References                                     |
| ------------------------------ | ------------------------------------------------- | ------------------------------------ | -------- | ---------------------------------------------------- |
| `percentage_spent`             | Calculates budget usage percentage                | `models.py` (BudgetAllocation class) | Line 487 | Used in budget reporting views                       |
| `percentage_off_budget`        | Calculates percentage of budget allocation        | `models.py` (BudgetAllocation class) | Line 478 | Used in budget reporting                             |
| `amount_spent_percentage`      | Calculates percentage of requisition amount spent | `models.py` (Category class)         | Line 598 | Used in expense tracking                             |
| `get_current_allocated_amount` | Retrieves current budget allocation               | `models.py` (Budget class)           | Line 352 | Used in `purchase_ident_with_budget_check` (Line 35) |

### 3. Utility Functions

| Function                    | Purpose                                    | Location   | Line     | Cross-References                       |
| --------------------------- | ------------------------------------------ | ---------- | -------- | -------------------------------------- |
| `format_phone_number`       | Standardizes phone number format           | `utils.py` | Line 79  | Used in supplier onboarding (Line 127) |
| `get_previous_next_item`    | Gets previous and next items in a queryset | `utils.py` | Line 60  | Used in list views for pagination      |
| `extract_from_excel_or_csv` | Extracts data from uploaded files          | `utils.py` | Line 100 | Used in bulk upload features           |
| `onboard_supplier`          | Creates supplier records                   | `utils.py` | Line 120 | Used in supplier management            |
| `is_supplier_check`         | Validates supplier existence               | `utils.py` | Line 195 | Used in procurement validation         |

### 4. Integration Functions

| Function                         | Purpose                              | Location                           | Line     | Cross-References                          |
| -------------------------------- | ------------------------------------ | ---------------------------------- | -------- | ----------------------------------------- |
| `create_contact`                 | Creates contact in Brevo CRM         | `helpers/brevo_crm.py`             | Line 53  | Used for marketing integration            |
| `send_buddy`                     | Sends money via Liberty Pay          | `core/helpers/apis/request_cls.py` | Line 52  | Used in disbursement process              |
| `verify_requisition_transaction` | Verifies transaction status          | `core/helpers/apis/request_cls.py` | Line 152 | Used in transaction verification          |
| `iterator_helper_to_get_values`  | Helper for data extraction           | `helpers/func.py`                  | Line 5   | Used in various data processing functions |
| `get_company_verification_count` | Tracks company verification attempts | `helpers/func.py`                  | Line 15  | Used in company verification process      |

## API Views

### 1. Requisition Management Views

| View                      | Purpose                          | URL                                 | HTTP Method | Line      | Key Functions                                                    |
| ------------------------- | -------------------------------- | ----------------------------------- | ----------- | --------- | ---------------------------------------------------------------- |
| `CreateRequisition`       | Creates fund requests            | `/requisition/create_requisition/`  | POST        | Line 1025 | Validates request data, creates requisition record               |
| `Disbursement`            | Processes fund disbursements     | `/requisition/disbursement/`        | POST        | Line 1217 | Calls `requisition_disbursement`, handles transaction processing |
| `BulkDisbursementAPIView` | Processes multiple disbursements | `/requisition/bulk-disbursement/`   | POST        | Line 1198 | Handles bulk transaction processing                              |
| `DeclineRequsition`       | Declines fund requests           | `/requisition/decline_requisition/` | POST        | Line 1350 | Updates requisition status, sends notifications                  |
| `ListFilterRequisition`   | Lists and filters requisitions   | `/requisition/requisitions/`        | GET         | Line 1380 | Provides filtered requisition data                               |
| `GetRequisitionDashboard` | Provides dashboard data          | `/requisition/req_dashboard/`       | GET         | Line 1450 | Aggregates requisition statistics                                |

### 2. Expense Management Views

| View                 | Purpose                     | URL                               | HTTP Method | Line      | Key Functions                                         |
| -------------------- | --------------------------- | --------------------------------- | ----------- | --------- | ----------------------------------------------------- |
| `ExpenseDashboard`   | Provides expense statistics | `/requisition/expense_dashboard/` | GET         | Line 2150 | Aggregates expense data, calculates statistics        |
| `ExpenseList`        | Lists expense records       | `/requisition/expense_list/`      | GET         | Line 2200 | Filters and returns expense records                   |
| `TeamExpenseList`    | Lists team expenses         | `/requisition/team_expense_list/` | GET         | Line 2250 | Filters expenses by team                              |
| `RecordExpense`      | Creates expense records     | `/requisition/record_expense/`    | POST        | Line 2300 | Creates and validates expense data                    |
| `ReadExpenseReceipt` | Extracts data from receipts | `/requisition/receipt_reader/`    | POST        | Line 2350 | Uses `process_invoice_file_2` to extract receipt data |

### 3. Budget Management Views

| View                     | Purpose                   | URL                                      | HTTP Method | Line      | Key Functions                      |
| ------------------------ | ------------------------- | ---------------------------------------- | ----------- | --------- | ---------------------------------- |
| `CreateBudget`           | Creates budget records    | `/requisition/create_budget/`            | POST        | Line 1600 | Creates and validates budget data  |
| `ListBudget`             | Lists budget records      | `/requisition/budgets/`                  | GET         | Line 1650 | Filters and returns budget records |
| `EditBudget`             | Updates budget records    | `/requisition/edit_budget/`              | PUT         | Line 1700 | Updates budget data                |
| `CreateBudgetCategories` | Creates budget categories | `/requisition/create_budget_categories/` | POST        | Line 1750 | Creates budget category records    |
| `ListBudgetCategories`   | Lists budget categories   | `/requisition/budget_categories/`        | GET         | Line 1800 | Returns budget category data       |

## Integration Points

### 1. External API Integrations

| Integration  | Purpose                                           | Key Functions                                  | Location                                |
| ------------ | ------------------------------------------------- | ---------------------------------------------- | --------------------------------------- |
| VFD Bank API | Fund transfers to external accounts               | `vfd_funds_transfer`                           | `account/models.py` (Transaction class) |
| WhisperSMS   | SMS notifications for approvals and disbursements | `sms_notifications`                            | `requisition/helpers/send_sms.py`       |
| AWS S3       | Document storage for receipts and invoices        | `upload_file_aws_s3_bucket`                    | `core/tasks.py`                         |
| Brevo CRM    | Marketing automation and contact management       | `create_contact`                               | `requisition/helpers/brevo_crm.py`      |
| Liberty Pay  | Payment processing                                | `send_buddy`, `verify_requisition_transaction` | `core/helpers/apis/request_cls.py`      |

### 2. Internal Module Dependencies

| Module      | Dependent Modules | Key Integration Points                |
| ----------- | ----------------- | ------------------------------------- |
| Requisition | Account           | Wallet balance checks, fund transfers |
| Requisition | Core              | Email notifications, file storage     |
| Requisition | Payroll           | Employee data for team members        |
| Requisition | Stock Inventory   | Product and supplier management       |

## Data Flow Diagrams

### 1. Requisition Creation and Approval Flow

```
Team Member -> CreateRequisition -> Requisition (PENDING) -> Approval -> Requisition (APPROVED) -> Disbursement -> Fund Transfer
```

### 2. Invoice Processing Flow

```
Upload Invoice -> ReadExpenseReceipt -> process_invoice_file_2 -> OCR Processing -> Data Extraction -> Expense Record
```

### 3. Budget Check Flow

```
Purchase Indent -> purchase_ident_with_budget_check -> Budget Validation -> Approval/Rejection
```

## Performance Considerations

1. **OCR Processing**: The `process_invoice_file_2` function performs resource-intensive operations and may impact performance with large files
2. **Budget Calculations**: Functions like `percentage_spent` perform database aggregations that may be expensive with large datasets
3. **Bulk Operations**: The bulk disbursement functionality processes multiple transactions and should be monitored for performance
