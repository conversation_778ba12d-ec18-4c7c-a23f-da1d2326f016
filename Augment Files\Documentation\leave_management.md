# Leave Management App Documentation

## Overview

The Leave Management app handles employee leave requests, approvals, and leave balance tracking for companies on the Paybox360 platform. It provides a structured workflow for requesting time off, managing approvals, and maintaining accurate leave records.

## Core Features

### 1. Leave Request Management

- Leave request creation and submission
- Multiple leave type support
- Request approval workflow
- Request status tracking

### 2. Leave Balance Tracking

- Leave entitlement management
- Balance calculation and updates
- Accrual rules configuration
- Leave usage monitoring

### 3. Leave Calendar

- Team leave calendar view
- Absence visualization
- Schedule planning
- Conflict detection

### 4. Approval Workflow

- Multi-level approval process
- Approval notifications
- Request comments and feedback
- Delegation of approval authority

### 5. Leave Policy Management

- Company-specific leave policies
- Leave type configuration
- Eligibility rules
- Holiday calendar management

## How It Works

1. **Policy Setup**: Companies configure leave types and policies
2. **Entitlement Assignment**: Leave balances are allocated to employees
3. **Request Submission**: Employees submit leave requests
4. **Approval Process**: Managers review and approve/reject requests
5. **Balance Update**: System updates leave balances upon approval
6. **Calendar Integration**: Approved leaves appear on team calendars

## Technical Details

### Models

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|--------------|
| `LeaveType` | Leave categories | `name`, `days_allowed`, `is_paid` | Company |
| `LeaveRequest` | Time off requests | `start_date`, `end_date`, `status` | User, LeaveType |
| `LeaveBalance` | Available leave days | `entitled_days`, `used_days` | User, LeaveType |
| `LeaveApproval` | Approval records | `status`, `comments`, `approved_at` | LeaveRequest, User |
| `Holiday` | Company holidays | `date`, `name`, `is_recurring` | Company |
| `LeavePolicy` | Policy configuration | `accrual_frequency`, `carryover_limit` | Company, LeaveType |

### Key Functions

| Function | Purpose | Location | Cross-References |
|----------|---------|----------|------------------|
| `calculate_leave_days` | Computes business days | `utils.py` | Used in request processing |
| `update_leave_balance` | Adjusts available days | `models.py` (LeaveBalance) | Called after approval |
| `check_leave_conflicts` | Detects scheduling issues | `services.py` | Used during request validation |
| `generate_leave_calendar` | Creates calendar view | `services.py` | Used in calendar endpoints |

### API Endpoints

| Endpoint | Purpose | HTTP Method | View |
|----------|---------|------------|------|
| `/leave-management/requests/` | Manage leave requests | POST/GET | `LeaveRequestViewSet` |
| `/leave-management/approvals/` | Process approvals | POST | `LeaveApprovalView` |
| `/leave-management/balances/` | View leave balances | GET | `LeaveBalanceView` |
| `/leave-management/calendar/` | View team calendar | GET | `LeaveCalendarView` |
| `/leave-management/types/` | Manage leave types | POST/GET | `LeaveTypeViewSet` |

### Integration Points

- **Clock App**: For attendance correlation
- **Payroll App**: For paid leave processing
- **Notification System**: For approval alerts
- **Calendar Services**: For schedule integration

## Key Considerations

### 1. Leave Calculation Accuracy

- **Responsible App**: Leave Management app
- **Key Functions**:
  - Business day calculation in `calculate_leave_days`
  - Holiday exclusion logic
  - Half-day and partial leave handling

### 2. Policy Enforcement

- **Responsible App**: Leave Management app
- **Key Functions**:
  - Balance validation before approval
  - Policy rule application in LeavePolicy model
  - Minimum notice period enforcement

### 3. Approval Workflow Management

- **Responsible App**: Leave Management app
- **Key Functions**:
  - Multi-level approval routing
  - Approval delegation handling
  - Status tracking in LeaveRequest model

### 4. Balance Maintenance

- **Responsible App**: Leave Management app
- **Key Functions**:
  - Accrual processing based on policy
  - Year-end balance adjustments
  - Leave balance audit trail

### 5. Calendar Synchronization

- **Responsible App**: Leave Management app
- **Key Functions**:
  - Team availability visualization
  - Resource planning integration
  - Schedule conflict detection