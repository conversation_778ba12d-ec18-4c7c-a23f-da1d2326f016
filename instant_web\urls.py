from django.urls import path

from instant_web import views
from instant_web.views import DeleteInstantWebAPIView

urlpatterns = [
    path("company_available_stock", views.CompanyAvailableStockAPIView.as_view()),
    path("sales_stock_summary_cards_data/", views.SalesAndStockSummaryCardsDataAPIView.as_view(), name="sales-stock-summary-cards-data"),
    path(
        "company_available_categories",
        views.CompanyAvailableCategoriesAPIView.as_view(),
    ),
    path('instant-web-categories/<uuid:branch_id>/', views.GetInstantWebCategoriesByBranch.as_view(), name='instant-web-categories'),
    path("company_item_details", views.CompanyItemDetailsAPIView.as_view()),
    path(
        "instant_web/",
        views.InstantWebAPIView.as_view(),
        name="create_instant_web",
    ),

    path(
        "retrieve_instant_web/",
        views.ViewInstantWebAPIView.as_view(),
        name="retrieve_instant_web",
    ),

    path(
        "retrieve_instant_web_by_url/",
        views.FetchInstantWebByDomainAPIView.as_view(),
        name="retrieve_instant_web_by_url"),


    path(
        "retrieve_company_instant_web/",
        views.CompanyInstantWebsAPIView.as_view(),
        name="retrieve_instant_web",
    ),

    path(
        "update_instant_web/",
        views.EditStoreAPIView.as_view(),
        name="update_instant_web",
    ),

    path(
        'delete_instant_web/',
        DeleteInstantWebAPIView.as_view(),
        name='delete_instant_web'
    ),

    path(
            "get_buyers/",
            views.BuyersByCompanyView.as_view(),
            name="get_buyers",
        ),
    path('analytics/', views.AnalyticsView.as_view(), name='analytics'),
    path('transaction-analysis/', views.TransactionAnalysisView.as_view(), name='transaction-analysis'),
    path('create-qrcode/', views.CreateQRCodeView.as_view(), name='create_qrcode'),
    path('qrcodes/', views.QRCodeListView.as_view(), name='qr_code_list'),
    path('qrcodes/<uuid:pk>/', views.QRCodeDetailView.as_view(), name='qr_code_detail'),
    path('qr-codes/<uuid:pk>/link/', views.ScanQRCodeView.as_view(), name='qr-code-link'),


    path('tables/', views.TableListView.as_view(), name='table-list'),
    path('tables/create/', views.CreateTableView.as_view(), name='table-create'),
    path('tables/<uuid:pk>/', views.TableDetailView.as_view(), name='table-detail'),
]
