from uuid import UUID
from django.contrib.auth import get_user_model
from django.core.management import BaseCommand

from account.helpers.liberty_credi import LibertyCrediMgr
from account.helpers.vbank_handler import VfdBankMgr
from account.models import AccountSystem, Transaction, Wallet
from account.tasks import (
    debited_or_pending_bank_transfer_reversal_without_initiating_payout,
    one_off_vfd_failed_transaction_reversal,
    vfd_failed_transaction_reversal,
)

User = get_user_model()


class Command(BaseCommand):
    def handle(self, *args, **options):
        # acct = AccountSystem.objects.get(id="b3f45d07-c9d7-4c75-8cfe-85efefc06af5")
        # print(acct, "\n\n")
        # send_money = Transaction.vfd_funds_transfer(
        #     bank_code="999999",
        #     bank_name="VFD",
        #     account_name="Roland",
        #     account_number="**********",
        #     narration="Test transfer funds",
        #     amount=100,
        #     account=acct,
        #     user=acct.user,
        # )
        # # wallet_instance = Wallet.objects.get(id="0eb820ca-6b83-4727-8422-5cdfec47b79a")
        # # transaction_instance = Transaction.objects.get(id=4)
        # # k = Wallet.wallet_reversal(transaction_instance=transaction_instance)
        # # print(k)
        # # reversal_result = one_off_vfd_failed_transaction_reversal()
        # # v = VfdBankMgr()
        # # enquiry = v.vfd_account_enquiry()
        # # # enq2 = v.get_fromClient_toClient_data(
        # # #     account_number="**********", bank_code="999999"
        # # # )
        # # send_money = vfd_failed_transaction_reversal()
        # # send_money = Transaction.objects.get(id=60)._vfd_initiate_payout_internal_account_to_external()
        # print(send_money)
        # liberty = LibertyCrediMgr()
        # notification_result = liberty.notify_float_deduction(
        #     amount=20000,
        # )
        # print(notification_result)
        pass
