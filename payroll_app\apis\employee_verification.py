import base64
import requests
import json
from django.conf import settings
from fuzzywuzzy import fuzz
from datetime import datetime
from dateutil.relativedelta import relativedelta
import string
import secrets

def is_utility_bill_expired(utility_bill_date, allowed_utility_month):

    # Convert the date string to a datetime object
    date_obj = datetime.strptime(utility_bill_date, '%Y-%m-%d')

    # Get the current date
    current_date = datetime.now()

    # Calculate the date 6 months ago from today
    six_months_ago = current_date - relativedelta(months=allowed_utility_month)

    # Check if the date is more than 6 months ago
    if date_obj < six_months_ago:
        is_expired = True
    else:
        is_expired = False
    return is_expired

def standardize_address(address):
    address = address.upper().replace('.', '').replace(',', '')
    address = ' '.join(address.split())  # remove extra spaces
    return address

def address_match_percentage(utility_bill_address, user_home_address):
    standardized_address1 = standardize_address(utility_bill_address)
    standardized_address2 = standardize_address(user_home_address)

    # Compare addresses
    similarity_ratio = fuzz.ratio(standardized_address1, standardized_address2)

    return similarity_ratio

def base_64_image_encoder(image):
    encoded_string = base64.b64encode(image.read())
    data = encoded_string.decode('utf-8')
    return data


def validate_utility_bill_with_chagpt(image):
    """
    Makes a call to OpenAI's API to validate the ID document type using an image.
    """
    # Encode the image to base64
    try:
        base64_image = base_64_image_encoder(image)
    except Exception as e:
        return {"status": False,  "message": str(e), "image_data": ""}

    # Get the list of API keys from settings, split by comma
    api_key = str(settings.CHAT_GPT_API)

    # OpenAI API request headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    # Construct the request payload
    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"What document type of Utility Bill is this in Nigeria?"
                        "You have to extract information from it to return a json only data, below are the keys needed to be extracted if found otherwise keep N/A\n"
                        "Your entire response/output is going to consist of a single JSON object {}, and you will NOT wrap it within JSON md markers or in a list\n"
                        '''Example of the response structure:\n
                        {\n
                            "Date": "The date when the expense was incurred. (do correction in spelling of month if required and do not include time) the format for the date should be "YYYY-MM-DD",\n
                            "Vendor": "The name of the vendor or supplier from whom the goods or services were purchased (this information can also extract from URL and might be before .com or .org and not include address in it)",\n
                            "Address": "The location where the expense occurred (e.g address)".\n
                        }\n\
                        '''
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ],
        # "max_tokens": 300
    }

    try:
        # Make the API call
        response = requests.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload)
        # Check if the response was successful
        if response.status_code == 200:
            response_data = response.json()

            # The response content might be a string, so parse it into JSON if necessary
            content = response_data.get("choices", [{}])[0].get("message", {}).get("content", {})
            if isinstance(content, str):
                try:
                    content = json.loads(content)  # Parse the string as JSON
                except json.JSONDecodeError:
                    return {"status": False,  "message": "an error occurred, try again!!!", "image_data": ""}
            
            content['status'] = True
            content['message'] = 'success'
            content['image_data'] = f"data:image/jpeg;base64,{base64_image}"
            return content # Return the parsed JSON response

        else:
            print(f"Failed to validate document type with API key {api_key}. Status code: {response.status_code}")
            print(f"Response: {response.text}")
    
    except requests.RequestException as e:
        # Log any request errors and try the next key
        print(f"Error during API call with API key {api_key}: {e}")

    # If all API keys fail, return an unknown document type error
    return {"status": False,  "message": "an error occurred, try again!!!", "image_data": ""}

def generate_guarantor_invite_id():
    from payroll_app.models import VerificationData

    alphabet = string.ascii_letters + string.digits
    loop_condition = True
    while loop_condition:
        guarantor_invite_id = "".join(secrets.choice(alphabet) for i in range(8))
        if (
            any(c.isupper() for c in guarantor_invite_id)
            and any(c.isupper() for c in guarantor_invite_id)
            and sum(c.isdigit() for c in guarantor_invite_id) >= 6
        ):
            loop_condition = False

    guarantor_invite_id = guarantor_invite_id.lower()
    if VerificationData.objects.filter(guarantor_invite_id=guarantor_invite_id).exists():
        generate_guarantor_invite_id()

    return guarantor_invite_id


def send_verification_sms(phone, invite_id):
    message = f"To verify your Guarantee, verify your data with the link https://www.home.paybox360.com/payroll/guarantor-verification/{invite_id}"
    url = "https://whispersms.xyz/transactional/send"
    headers = {
        "Authorization": f"Api_key {settings.SEEDS_PENNIES_SMS_WHISPER_API_KEY}",
        "Content-Type": "application/json",
    }
    payload = json.dumps(
        {
            "receiver": f"{phone}",
            "template": "4e08c548-54ba-453e-8cad-4876713a3f05",
            "place_holders": {"message": message},
        }
    )

    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        res = response.json()

        new_resp = {
            "receiver": phone,
            "whisper_resp": res,
            "message": f"SMS SENT TO {phone}",
            "payload": payload,
            "sent": True,
        }

    except requests.exceptions.RequestException as e:
        res = "WHISPER IS DOWN"

        new_resp = {
            "receiver": phone,
            "whisper_resp": f"{e}",
            "message": res,
            "payload": payload,
            "sent": False,
        }
    return new_resp