import datetime
import pya<PERSON>gu<PERSON>
from PIL import Image
from io import BytesIO
from .models import Screenshot, Record

record = Record.objects.filter(date_created__date = datetime.now().date()).last()

def capture_screenshot(record):
    screenshot = pyautogui.screenshot()
    image_bytes = BytesIO()
    screenshot.save(image_bytes, format="PNG")
    screenshot_data = image_bytes.getvalue()

    screenshot_entry = Screenshot(
        record=record,
        screenshot=screenshot_data
    )
    screenshot_entry.save()


def format_datetime(datetime_obj):
    return datetime_obj.strftime('%Y-%m-%d %H:%M:%S')