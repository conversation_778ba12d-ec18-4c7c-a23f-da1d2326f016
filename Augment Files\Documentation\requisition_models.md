# Requisition App Models Documentation

## Overview

This document provides detailed technical documentation of the models within the requisition app, including their relationships, key fields, and cross-references.

## Core Models

### 1. Company Model

| Field | Type | Purpose | Related Models |
|-------|------|---------|----------------|
| `company_name` | CharField | Name of the company | - |
| `company_email` | EmailField | Official email address | - |
| `company_phone` | Char<PERSON>ield | Contact phone number | - |
| `user` | ForeignKey | Owner of the company | User |
| `is_verified` | BooleanField | Verification status | - |
| `is_active` | BooleanField | Active status | - |
| `industry` | ForeignKey | Industry classification | CompanyIndustry |

**Key Methods:**
- `__str__`: Returns company name
- `get_teams`: Returns all teams in the company

**Cross-References:**
- Referenced by Team, Budget, Requisition, Expense models
- Used in company verification process

### 2. Team Model

| Field | Type | Purpose | Related Models |
|-------|------|---------|----------------|
| `team_name` | CharField | Name of the team | - |
| `company` | ForeignKey | Company the team belongs to | Company |
| `team_type` | CharField | Type of team (Department, Project, etc.) | - |
| `is_active` | BooleanField | Active status | - |
| `created_at` | DateTimeField | Creation timestamp | - |

**Key Methods:**
- `__str__`: Returns team name
- `get_members`: Returns all members in the team

**Cross-References:**
- Referenced by TeamMember, Requisition, Budget, Expense models
- Used in budget allocation and requisition creation

### 3. TeamMember Model

| Field | Type | Purpose | Related Models |
|-------|------|---------|----------------|
| `team` | ForeignKey | Team the member belongs to | Team |
| `member` | ForeignKey | User who is a member | User |
| `role` | CharField | Role in the team | - |
| `is_active` | BooleanField | Active status | - |
| `created_at` | DateTimeField | Creation timestamp | - |

**Key Methods:**
- `__str__`: Returns member name and team
- `can_disburse`: Checks if member can disburse funds

**Cross-References:**
- Referenced by Requisition model
- Used in permission checks for disbursement

### 4. Budget Model

| Field | Type | Purpose | Related Models |
|-------|------|---------|----------------|
| `budget_name` | CharField | Name of the budget | - |
| `budget_amount` | DecimalField | Total budget amount | - |
| `company` | ForeignKey | Company the budget belongs to | Company |
| `team` | ForeignKey | Team the budget is allocated to | Team |
| `start_date` | DateField | Budget start date | - |
| `end_date` | DateField | Budget end date | - |
| `is_active` | BooleanField | Active status | - |

**Key Methods:**
- `__str__`: Returns budget name
- `get_current_allocated_amount`: Calculates current allocation
- `get_budget_utilization`: Calculates budget usage

**Cross-References:**
- Referenced by BudgetAllocation, Requisition models
- Used in budget checks for requisitions

### 5. Requisition Model

| Field | Type | Purpose | Related Models |
|-------|------|---------|----------------|
| `user` | ForeignKey | User who created the requisition | User |
| `company` | ForeignKey | Company the requisition belongs to | Company |
| `team` | ForeignKey | Team the requisition belongs to | Team |
| `member` | ForeignKey | Team member who created the requisition | TeamMember |
| `request_amount` | DecimalField | Requested amount | - |
| `request_reason` | TextField | Reason for the request | - |
| `requisition_category` | CharField | Category of the requisition | - |
| `status` | CharField | Status (PENDING, APPROVED, DECLINED) | - |
| `is_disbursed` | BooleanField | Disbursement status | - |
| `approved_at` | DateTimeField | Approval timestamp | - |
| `disbursed_at` | DateTimeField | Disbursement timestamp | - |

**Key Methods:**
- `__str__`: Returns requisition ID
- `team_name`: Returns team name
- `requisition_disbursement`: Processes fund transfers

**Cross-References:**
- Referenced by Category, Expense models
- Used in disbursement process

### 6. PurchaseIndent Model

| Field | Type | Purpose | Related Models |
|-------|------|---------|----------------|
| `team` | ForeignKey | Team the indent belongs to | Team |
| `requested_by` | ForeignKey | User who created the indent | User |
| `supplier` | ForeignKey | Supplier for the products | Supplier |
| `estimated_cost` | DecimalField | Estimated cost | - |
| `approval_status` | CharField | Status (PENDING, APPROVED, DECLINED) | - |
| `approved_by` | ForeignKey | User who approved the indent | User |
| `approved_at` | DateTimeField | Approval timestamp | - |

**Key Methods:**
- `__str__`: Returns indent ID
- `products_count`: Returns number of products

**Cross-References:**
- Referenced by IndentProduct, ProcurementPurchaseOrder models
- Used in procurement process

### 7. Expense Model

| Field | Type | Purpose | Related Models |
|-------|------|---------|----------------|
| `user` | ForeignKey | User who recorded the expense | User |
| `company` | ForeignKey | Company the expense belongs to | Company |
| `team` | ForeignKey | Team the expense belongs to | Team |
| `expense_amount` | DecimalField | Expense amount | - |
| `expense_category` | CharField | Category of the expense | - |
| `requisition` | ForeignKey | Related requisition | Requisition |
| `status` | CharField | Status (PENDING, SUCCESSFUL) | - |
| `receipt` | TextField | Receipt data | - |

**Key Methods:**
- `__str__`: Returns expense ID
- `amount_spent_percentage`: Calculates percentage of requisition spent

**Cross-References:**
- Used in expense reporting and budget tracking

## Model Relationships

### 1. Company Relationships

```
Company --1:N--> Team
Company --1:N--> Budget
Company --1:N--> Requisition
Company --1:N--> Expense
```

### 2. Team Relationships

```
Team --1:N--> TeamMember
Team --1:N--> Requisition
Team --1:N--> Budget
Team --1:N--> PurchaseIndent
Team --1:N--> Expense
```

### 3. Budget Relationships

```
Budget --1:N--> BudgetAllocation
Budget --1:N--> Requisition
```

### 4. Requisition Relationships

```
Requisition --1:N--> Category
Requisition --1:N--> Expense
```

### 5. PurchaseIndent Relationships

```
PurchaseIndent --1:N--> IndentProduct
PurchaseIndent --1:1--> ProcurementPurchaseOrder
```

## Database Considerations

1. **Indexes**: Primary keys and foreign keys are indexed for performance
2. **Constraints**: Foreign key constraints ensure data integrity
3. **Cascades**: Deletion of parent records may cascade to child records

## Cross-Model Functions

| Function | Purpose | Models Involved |
|----------|---------|-----------------|
| `requisition_disbursement` | Processes fund transfers | Requisition, AccountSystem, Transaction |
| `purchase_ident_with_budget_check` | Validates against budget | PurchaseIndent, Budget, BudgetAllocation |
| `percentage_spent` | Calculates budget usage | BudgetAllocation, Requisition |
| `get_current_allocated_amount` | Retrieves allocation | Budget, BudgetAllocation |

## Signal Handlers

| Signal | Purpose | Models Affected |
|--------|---------|-----------------|
| `post_save` on Requisition | Sends notifications | Requisition |
| `post_save` on TeamMember | Updates permissions | TeamMember |
| `post_save` on Company | Creates default team | Company, Team |
| `post_save` on PurchaseIndent | Updates budget | PurchaseIndent, Budget |

## Performance Considerations

1. **Query Optimization**: Complex queries involving multiple models should be optimized
2. **Denormalization**: Some data is denormalized for performance (e.g., company in Requisition)
3. **Caching**: Frequently accessed data should be cached (e.g., budget calculations)
