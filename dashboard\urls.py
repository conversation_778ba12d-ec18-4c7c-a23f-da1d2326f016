from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    path('average-transaction-stats/', views.AverageTransactionStatsView.as_view(), name='average-transaction-stats'),
    path('customers/overview/', views.CustomerOverviewView.as_view(), name='customer-overview'),
    path('transactions/stats/', views.TransactionStatsView.as_view(), name='transaction-stats'),
    path('financials/metrics/', views.FinancialMetricsView.as_view(), name='financial-metrics'),
    path('transactions/comparative/', views.TransactionComparativeView.as_view(), name='transaction-comparative'),
    # path('transactions/type-metrics/', views.TransactionTypeMetricsView.as_view(), name='transaction-type-metrics'),
    # path('transactions/amount-metrics/', views.TransactionAmountMetricsView.as_view(), name='transaction-amount-metrics'),
    # path('traffic/sources/', views.TrafficSourceView.as_view(), name='traffic-sources'),
    path('total-transfer/', views.TotalTransferView.as_view(), name='total-transfer'),
    path('transaction-metrics/', views.TransactionMetricsView.as_view(), name='transaction-metrics'),
    path('sales-stats/', views.SalesStatsView.as_view(), name='sales-stats'),
    path('transaction-line-chart/', views.TransactionLineChartView.as_view(), name='transaction-line-chart'),
    path('transaction-comparative-charts/', views.TransactionComparativeChartsView.as_view(), name='transaction-comparative-charts'),
    path('transaction-status-line-chart/', views.TransactionStatusLineChartView.as_view(), name='transaction-status-line-chart'),
    path('sessions-by-traffic-source/', views.SessionByTrafficSourceView.as_view(), name='sessions-by-traffic-source'),
    path('wallet-balance/', views.WalletBalanceView.as_view(), name='wallet-balance'),
    path('transaction-status-metrics/', views.TransactionStatusMetricsView.as_view(), name='transaction-status'),
] 