Requisition App
Company
    •	Description: Represents a company in the system
    •	Inherits From: BaseModel
    •	Fields:
        o	user (ForeignKey to User)
        o	company_name (CharField)
        o	company_email (EmailField)
        o	company_phone (CharField)
        o	company_address (TextField)
        o	company_logo (TextField)
        o	industry (CharField)
        o	company_size (CharField)
        o	company_wallet_type (CharField)
        o	overall_running_budget (DecimalField)
        o	is_active (BooleanField)
        o	is_deleted (BooleanField)
    •	Dependencies:
        o	User (ForeignKey)
    •	Third-Party Dependencies: None
    •	Creation Criteria:
        o	Valid User instance (company owner)
        o	Company name
        o	Industry (optional)
        o	Company size (optional)